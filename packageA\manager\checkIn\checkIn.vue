<template>
	<view>

		<view class="ordingBox">
			<p class="title">入住信息</p>
			<view class="msgBox">
				<view class="msgItem">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">销售类型</text></p>
					<picker @change="bindChange" :value="changeIndex" range-key="name" :range="roomStatusBox">
						<view class="pickerBox">
							{{roomStatusBox[changeIndex].name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<view class="msgItem">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">销售规则</text></p>
					<picker @change="bindChange1" :value="changeIndex1" range-key="name" :range="roomSaleList">
						<view class="pickerBox">
							{{roomSale.name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<view class="msgItem">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">入住类型</text></p>
					<picker @change="bindChange2" :value="changeIndex2" range-key="name" :range="stayTypeList">
						<view class="pickerBox">
							{{stayTypeList[changeIndex2].name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<view class="msgItem">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">联系人</text></p>
					<view class=""
						style="height: 38rpx;width: 400rpx;padding-left: 20rpx;display: flex;align-items: center;">
						<input v-model="linkman" trim="all" placeholder="请输入联系人" @input="searchInfo" style="border: 1px solid #eee;padding: 10rpx 4rpx;height: 40rpx;">
						</input>
					</view>
				</view>


				<view class="msgItem">
					<p><text style="font-size: 34rpx;">手机号</text></p>
					<view class=""
						style="height: 38rpx;width: 400rpx;padding-left: 20rpx;display: flex;align-items: center;">
						<uni-easyinput type="number" @blur="chooseInfo" v-model="linkphone" trim="all"
							placeholder="请输入联系电话" @input="searchInfo1"></uni-easyinput>
					</view>
				</view>

				<!-- 会员展示 -->
				<view class="manListBox" v-if="manList.length > 0&&ifShow">
					<scroll-view scroll-y="true" style="max-height: 320rpx;">
						<view class="infoBox" style="" v-for="(item, index) in manList" :key="index"
							@click="chooseName(item)">
							<text>{{item.name?item.name:'暂无'}}</text>
							<text>({{item.grade_name?item.grade_name:'散客'}})</text>
							<text>{{item.phone?item.phone:'暂无'}}</text>
							<text style="font-size: 30rpx;color: #5b900b;">选择</text>
						</view>
					</scroll-view>
				</view>

				<view class="msgItem">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">价格方案</text></p>
					<picker @change="bindChange3" :value="changeIndex3" range-key="name" :range="priceTypeList">
						<view class="pickerBox">
							{{priceTypeList[changeIndex3].name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<view class="msgItem" style="border: 2px dashed #2c7f08;" v-if="manInfo&&ifShow1">
					<view class="" style="width: 100%;display: flex;flex-wrap: wrap;font-size: 26rpx;padding:0 20rpx;">
						<p style="width: 50%;">姓名:{{manInfo.name}}</p>
						<p style="width: 50%;">会员等级:{{manInfo.grade_name}}</p>
						<p style="width: 50%;">电话:{{manInfo.phone}}</p>
						<p style="width: 50%;">会员余额:{{manInfo.balance}}</p>
					</view>
				</view>

				<view class="msgItem" v-if="unitList.length > 0">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">单位</text></p>
					<picker @change="bindChange4" :value="changeIndex4" range-key="intermediary_name" :range="unitList">
						<view class="pickerBox">
							{{unitList[changeIndex4].intermediary_name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<view class="msgItem" v-if="mediatorList.length > 0">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">中介</text></p>
					<picker @change="bindChange5" :value="changeIndex5" range-key="intermediary_name"
						:range="mediatorList">
						<view class="pickerBox">
							{{mediatorList[changeIndex5].intermediary_name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>
				<!-- 预抵时间 -->
				<!-- <view class="msgItem">
						<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">预抵时间</text></p>
						<view class="" style="width: 480rpx;">
							<uni-datetime-picker :start="startTime" type="datetime" v-model="datetimesingle"
								@change="changeLog" />
						</view>
					</view> -->

				<!-- 入住时长 -->
				<view class="">
					<!-- 小时 -->
					<view class="msgItem"
						v-if="this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room'">
						<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">入住时长</text></p>
						<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<text>{{roomSale.stay_time}}小时</text>
						</view>
					</view>
					<!-- 天数 -->
					<view class="msgItem" v-if="roomSale.sign=='standard'">
						<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">入住时长</text></p>
						<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<uni-number-box :min="1" v-model="dayCounts" @change="dayChange"></uni-number-box>天
						</view>
					</view>
					<!-- 月租 -->
					<view class="msgItem" v-if="roomSale.sign=='long_standard'">
						<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">入住时长</text></p>
						<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<uni-number-box :min="1" v-model="monthCounts" @change="monthChange"></uni-number-box>月
						</view>
					</view>
				</view>

				<!-- 离店时间 -->
				<view class="msgItem">
					<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
							style="font-size: 34rpx;">离店时间</text></p>
					<view class="" v-if="roomSale.sign=='hour'||roomSale.sign=='conference_room'"
						style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
						<text>{{datetimesingle1}}</text>
					</view>

					<view class="" style="width: 480rpx;" v-if="roomSale.sign=='standard'">
						<uni-datetime-picker :start="endTime" type="datetime" v-model="datetimesingle1"
							@change="changeLog1" />
					</view>
					<view class="" v-if="roomSale.sign=='long_standard'"
						style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
						<text>{{datetimesingle1}}</text>
					</view>
				</view>

				<!-- 订单来源 -->
				<view class="msgItem">
					<p><text style="color: red;">*</text><text style="font-size: 34rpx;">订单来源</text></p>
					<picker @change="bindChange6" :value="changeIndex6" range-key="source_name" :range="billSource">
						<view class="pickerBox">
							{{billSource[changeIndex6].source_name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<!-- 外部订单号 -->
				<view class="msgItem">
					<p><text style="font-size: 34rpx;">外部订单号</text></p>
					<input type="text" placeholder="请输入外部订单号" class="msgInput" v-model="outOrderNum" style="">
				</view>
				<!-- 备注 -->
				<view class="msgItem">
					<p><text style="font-size: 34rpx;">备注</text></p>
					<input type="text" placeholder="请输入备注" class="msgInput" v-model="memo" style="">
				</view>
			</view>
		</view>

		<view class="roomBox">
			<p class="title">房间信息</p>
			<view class="room" v-for="(item, index) in roomList" :key="index">
				<view class="room_content">
					<view class="item">
						<text>房号:{{item.room_number}}</text>
					</view>
					<view class="item">
						<text>房型:{{item.room_type_name}}</text>
					</view>

					<view class="item1">
						<text>价格:</text>
						<view class="" style="width: 260rpx;">
							<uni-easyinput type="digit" v-model="item.room_prices.room_price[0].room_price" trim="all"
								@change="changePrice(item)" :clearable="false"></uni-easyinput>
						</view>

						<text style="font-size: 22rpx;color: blue;padding-left: 6rpx;"
							@click="changePrice(item)">多日房价</text>
					</view>
					<view class="item1">
						<text>押金:</text>
						<view class="" style="width: 260rpx;">
							<uni-easyinput type="digit" v-model="item.room_prices.cash_pledge" trim="all"
								:clearable="false"></uni-easyinput>
						</view>

					</view>
					<view class="item3" style="">
						<view class="serviceBox"
							:style="item1.checked?'background:#5c79e3;border:1px solid #5c79e3;color:#cad9f4':''"
							v-for="(item1, index1) in item.room_prices.room_service" :key="index1"
							@click="chooseService({'item':item,'item1':item1})">
							<text>{{item1.service_name}}</text>
							<text style="padding-left: 6rpx;">￥{{item1.price}}</text>
						</view>
					</view>


					<view class="item1">
						<view class="" @click="exchangeRoom(item)"
							style="height: 50rpx;width: 100rpx;border-radius: 10rpx;background: #2074f1;color: #fff;display: flex;align-items: center;justify-content: center;">
							<text>换房</text>
						</view>
						<view class="" @click="deleRoom(item)"
							style="height: 50rpx;width: 100rpx;border-radius: 10rpx;background: #f10004;color: #fff;display: flex;align-items: center;justify-content: center;margin: 0 10rpx;">
							<text>删除</text>
						</view>
					</view>

					<view class=""
						style="display: flex;flex-direction: column;justify-content: center;width: 100%;margin-top: 14rpx;background: #dfdfdf;padding: 10rpx;">
						<view class="item_msg" v-for="(item2, index2) in item.user_info" :key="index2">
							<input type="text" v-model="item2.name" style="width: 140rpx;font-size: 24rpx;"
								class="item_msg_inpt" placeholder="姓名"
								@input="searchInfo2({'item':item,'index2':index2})">
							<view class="msgItem" @click.stop="getGender({'item':item,'index2':index2})">
								<picker @change.stop="bindChangeGender" :value="changeIndexGender" range-key="name"
									:range="genderList">
									<view class="pickerBox">
										{{item2.gender==0?'保密':(item2.gender==1?'男':'女')}}
										<view class="icon-down"
											style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
										</view>
									</view>
								</picker>
							</view>
							<input type="number" v-model="item2.phone" style="width: 300rpx;font-size: 24rpx;"
								class="item_msg_inpt" placeholder="电话"
								@input="searchInfo3({'item':item,'index2':index2})">
							<view class="msgItem" @click.stop="getGender({'item':item,'index2':index2})">
								<picker @change.stop="bindChangeCard" :value="changeIndexCard" range-key="name"
									:range="cardList">
									<view class="pickerBox" style="width: 142rpx;">
										{{item2.identification_type==1?'身份证':(item2.identification_type==2?'港澳通行证':(item2.identification_type==3?'驾驶证':(item1.identification_type==4?'军官证':(item1.identification_type==5?'护照':'台湾身份证'))))}}
									</view>
								</picker>
							</view>
							<input type="idcard" v-model="item2.identification_number"
								style="width: 320rpx;font-size: 24rpx;" class="item_msg_inpt" placeholder="证件号码">
							<view class="icon-tianjia" style="font-size: 40rpx;margin:0 20rpx;"
								@click="addUserMsg(item)">
							</view>
							<image src="../../../static/images/jian.png" style="width: 44rpx;height:44rpx"
								@click="reduceUserMsg({'item':item,'index2':index2})" mode=""
								v-if="item.user_info.length>1"></image>
						</view>

						<!-- 会员展示 -->
						<view class="manListBox" v-if="manCheckInList.length > 0&&ifShow2">
							<scroll-view scroll-y="true" style="max-height: 320rpx;">
								<view class="infoBox" style="" v-for="(item3,index3) in manCheckInList" :key="index3"
									@click="chooseCheckMan({'item3':item3,'index':index})">
									<text>{{item3.name?item3.name:'暂无'}}</text>
									<text>({{item3.grade_name?item3.grade_name:'散客'}})</text>
									<text>{{item3.phone?item3.phone:'暂无'}}</text>
									<text style="font-size: 30rpx;color: #5b900b;">选择</text>
								</view>
							</scroll-view>
						</view>

					</view>

				</view>
			</view>
			<view class="item2" style="background: #eee;color:#2074f1" @click="selectRoom()">
				<text>添加房间</text>
			</view>
		</view>

		<!-- 价格 -->
		<view class="priceBox">
			<p class="title">订单价格</p>
			<view class="priceContent">
				<view style="display: flex;align-items: center;">
					<p style="width: 120rpx;">房费:</p> <text>{{roomPrice}}</text>
				</view>
				<view style="display: flex;align-items: center;">
					<p style="width: 120rpx;">套餐费:</p> <text>{{servicePrice}}</text>
				</view>
				<view style="display: flex;align-items: center;">
					<p style="width: 120rpx;">押金:</p> <text>{{cashPrice}}</text>
				</view>
				<view style="display: flex;align-items: center;margin-top: 30rpx;">
					<p style="width: 120rpx;">优惠券:</p><text v-if="chooseCouponse"
						style="margin-right: 30rpx;">-{{chooseCouponsePrice}}</text> <text
						style="font-size: 22rpx;color: #2c7f08;">{{couponsList.length?couponsList.length:0}}张可用</text>
					<text style="padding-left: 40rpx;color: blue;" @click="chooseCoupon">选择</text>
				</view>
			</view>
		</view>

		<view class="" style="height: 140rpx;width: 100%;"></view>

		<!-- 应收款 -->
		<view class="submitContent" style="">
			<view class="" :style="{color: themeColor.main_color}" style="font-size: 40rpx;font-weight: 600;">
				<p>应收款:￥{{(roomPrice*1 + servicePrice*1 + cashPrice*1 - chooseCouponsePrice*1).toFixed(2)}}</p>
			</view>
			<view class=""
				style="width: 120rpx;height: 60rpx;border-radius: 10rpx;padding: 10rpx;color: #fff;text-align: center;"
				:style="{background: themeColor.main_color}" @click="surePay">
				提交
			</view>
		</view>


		<!-- 选择优惠券弹窗 -->
		<m-popup :show="popCoupon" @closePop="closePopCoupon">
			<mBossChooseCoupons :coupType="1" :list="couponsList" :limitNum="couponLimit" @getCouponIfo="getInfo">
			</mBossChooseCoupons>
		</m-popup>

		<!-- 价格弹窗 -->
		<m-popup :show="popPrice" @closePop="closepopPrice">
			<view class="" style="height: 80vh;width: 100%;padding: 30rpx;">
				<scroll-view scroll-y="true" style="height:100%;position: relative;">
					<view class="" v-for="(item3, index3) in pricesBox.room_price" style="margin-top: 30rpx;"
						:key="index3">

						<view class=""
							style="display: flex;align-items: center;height: 50rpx;justify-content: space-between;">
							<text>{{item3.date}}:</text>
							<view class="" style="width: 400rpx;height: 44rpx;display: flex;align-items: center;">
								<view class="" style="width: 260rpx;">
									<uni-easyinput type="digit" v-model="item3.room_price" trim="all"
										@change="changeDatePrice" :clearable="false"></uni-easyinput>
								</view>

								<text style="font-size: 28rpx;color: #2c7f08;padding-left: 20rpx;"
									@click="dayPrice(item3.room_price)" v-if="index3==0">同步房价</text>
							</view>


						</view>

					</view>
					<view class=""
						style="width: 100%;height: 60rpx;display: flex;align-items: center;justify-content: center;margin-top: 80rpx;">
						<view class=""
							style="width: 480rpx;height: 70rpx;border-radius: 30rpx;background-color: #2c7f08;color: #fff;display: flex;align-items: center;justify-content: center;"
							@click="closepopPrice">
							<text>确认</text>
						</view>

					</view>
				</scroll-view>

			</view>
		</m-popup>

		<!-- 选房弹窗 -->
		<mCheckInRooms :roomList="selectRoomList" :ids="chooseIds" :num="itemPrice" :rooms="chooseRooms" :poprc="showRc"
			@closeZj="closeRc" @sureRc="getRcIds"></mCheckInRooms>

		<!-- 换房弹窗 -->
		<mExchangeRoom :roomList="selectRoomList" :ids="chooseIds" :num="itemPrice" :rooms="chooseRooms"
			:poprc="showExRc" @closeZj="closeExRc" @sureRc="getExRcIds"></mExchangeRoom>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	import mCheckInRooms from '../../components/m-checkInRooms.vue'
	import mExchangeRoom from '../../components/m-exChangeRoom.vue'
	import mBossChooseCoupons from '../../components/m-chooseBossCoupons.vue'
	export default {
		data() {
			return {
				//销售类型
				changeIndex: 0,
				//销售规则
				changeIndex1: 0,
				//入住类型
				changeIndex2: 0,
				// 联系人
				linkman: '',
				paramsMan: {
					search_word: '',
					page: 1,
					limit: 10
				},
				linkphone: '',
				paramsPhone: {
					search_word: '',
					page: 1,
					limit: 10
				},
				ifShow: true,
				ifShow1: true,
				ifShow2: true,
				manList: [],
				manInfo: null,
				// 价格方案
				changeIndex3: 0,
				ifGrade: '',
				roomStatusBox: [], //销售类型
				roomSaleList: [], //销售规则
				roomSale: null,
				//入住类型
				stayTypeList: [{
					id: 1,
					name: '正常'
				}, {
					id: 2,
					name: '免费'
				}, {
					id: 3,
					name: '自用'
				}],
				priceTypeList: [{
					id: 1,
					name: '门市价',
					status: 1
				}, {
					id: 2,
					name: '会员',
					status: 1
				}, {
					id: 3,
					name: '单位',
					status: 1
				}, {
					id: 4,
					name: '中介',
					status: 1
				}],
				//单位
				changeIndex4: 0,
				unitList: [],
				// 中介
				changeIndex5: 0,
				mediatorList: [],
				// 预抵时间
				dayCounts: 1,
				monthCounts: 1,
				datetimesingle: '',
				startTime: '',
				datetimesingle1: '',
				endTime: '',
				// 订单来源
				billSource: [],
				changeIndex6: 0,
				// 外部单号
				outOrderNum: '',
				//备注
				memo: '',
				params: {
					end_time: '',
					room_clear_status: [],
					room_record_status: [],
					room_sale_type: '',
					start_time: '',
					times: ''
				},

				// 房型列表
				roomTypeList: [],
				// 房间列表
				roomList: [],
				service: '', //选择的服务

				//价格弹窗
				popPrice: false,
				pricesBox: [],
				itemPrice: null, //暂时存储点击的item

				// 优惠券弹窗
				popCoupon: false,

				// 房态
				params1: {
					room_clear_status: [],
					room_record_status: [],
					floor_id: '',
					building_id: "",
					room_type_id: "",
					room_number: "",
					room_sale_type: "",
					grade_id: "",
					intermediaries_id: "",
					start_time: "",
					end_time: ""
				},
				selectRoomList: [],
				showRc: false,
				chooseIds: [],
				chooseRooms: [],

				// 价格
				roomPrice: 0,
				servicePrice: 0,
				cashPrice: 0,
				couponsList: [],
				chooseCouponse: null,
				chooseCouponsePrice: 0,
				couponLimit: 0,

				genderList: [{
					id: 0,
					name: '保密'
				}, {
					id: 1,
					name: '男'
				}, {
					id: 2,
					name: '女'
				}],
				cardList: [],
				changeIndexGender: 0,
				changeIndexCard: 0,
				chooseConn: null,
				chooseRoom: null,
				msgIndex: 0,
				showExRc: false,
				leave_time: '',
				manCheckInList: [],
				manCheckIndex: 0
			};
		},
		components: {
			mCheckInRooms,
			mBossChooseCoupons,
			mExchangeRoom
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager', 'shopSetting', 'gradeList']),
			...mapState('room', ['roomInfo']),
		},
		watch: {
			manInfo: {
				handler(newVal, oldVal) {
					if (this.manInfo && this.manInfo.upgrade_growth_value != -1) {
						let a = [{
							id: 1,
							name: '门市价',
							status: 1
						}, {
							id: 2,
							name: '会员',
							status: 1
						}, {
							id: 3,
							name: '单位',
							status: 1
						}, {
							id: 4,
							name: '中介',
							status: 1
						}]

						this.priceTypeList = a
						this.priceTypeList[1].status = 1
						this.changeIndex3 = 1

						// 查询优惠券
						this.$iBox
							.http('bossGetUserCoupon1', {
								common_code: this.manInfo.common_code,
								page: 1,
								limit: 1000
							})({
								method: 'post'
							})
							.then(res => {
								this.couponsList = res.data.list
							})

						let leave_time = ''
						this.gradeList.filter(item => {
							if (this.manInfo.grade == item.id) {
								item.right_itererest.forEach(item1 => {
									if (item1.sign == 'yctf') {
										leave_time = item1.value
									}
								})
							}
						})

						if (this.roomSale.sign == 'standard') {
							this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss'))
								.format('YYYY-MM-DD') + ` ${leave_time}:00`
							console.log(leave_time, 'leave_time');
						} else if (this.roomSale.sign == 'long_standard') {
							this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss'))
								.format('YYYY-MM-DD') + ` ${leave_time}:00`
							console.log(leave_time, 'leave_time');
						}

					} else {

						let a = [{
							id: 1,
							name: '门市价',
							status: 1
						}, {
							id: 2,
							name: '单位',
							status: 1
						}, {
							id: 3,
							name: '中介',
							status: 1
						}]
						this.priceTypeList = a
						this.changeIndex3 = 0
					}
				},
				immediate: true,
				deep: true
			},
			linkphone() {
				if (this.linkphone.length != 11) {
					this.manInfo = null
				}
				console.log(this.roomList, 'watch');
				if (this.roomList.length > 0) {
					if (this.manInfo) {
						this.roomList[0].user_info[0].name = this.manInfo.name
						this.roomList[0].user_info[0].phone = this.manInfo.phone
						this.roomList[0].user_info[0].identification_type = this.manInfo.identification_type
						this.roomList[0].user_info[0].identification_number = this.manInfo.identification_number
						this.roomList[0].user_info[0].gender = this.manInfo.gender
					} else {
						this.roomList[0].user_info[0].name = this.linkman
						this.roomList[0].user_info[0].phone = this.linkphone
					}

				}
			},
			linkman() {
				if (this.roomList.length > 0) {
					if (this.manInfo) {
						this.roomList[0].user_info[0].name = this.manInfo.name
						this.roomList[0].user_info[0].phone = this.manInfo.phone
						this.roomList[0].user_info[0].identification_type = this.manInfo.identification_type
						this.roomList[0].user_info[0].identification_number = this.manInfo.identification_number
						this.roomList[0].user_info[0].gender = this.manInfo.gender
					} else {
						this.roomList[0].user_info[0].name = this.linkman
						this.roomList[0].user_info[0].phone = this.linkphone
					}

				}
			},
			changeIndex3() {
				if (this.priceTypeList[this.changeIndex3].name != '会员') {
					this.ifShow = false
					this.ifShow1 = false
					this.couponsList = []
				} else if (this.priceTypeList[this.changeIndex3].name == '会员') {
					this.ifShow = true
					this.ifShow1 = true
					this.params.grade_id = this.manInfo.grade
					this.getRoomType()


				} else {
					this.ifShow = true
					this.ifShow1 = true
					this.couponsList = []
				}
			},
			roomList: {
				handler(newVal, oldVal) {
					console.log(newVal, 'room', this.roomSale, this.dayCounts);
					let roomPrice = 0
					let servicePrice = 0
					let cashPrice = 0
					newVal.forEach(item => {
						item.room_prices.room_price.forEach(price => {
							roomPrice += (price.room_price * 1)
						})

						item.room_prices.room_service.forEach(item1 => {
							if (item1.checked) {
								servicePrice += item1.price * (this.roomSale.sign == 'hour' || this
									.roomSale.sign == 'conference_room' ? 1 : (this.roomSale
										.sign == 'standard' ? this.dayCounts : this.monthCounts))
							}
						})
						cashPrice += item.room_prices.cash_pledge * (this.roomSale.sign == 'hour' || this.roomSale
							.sign == 'conference_room' ? 1 : (this.roomSale
								.sign == 'standard' ? this.dayCounts : this.monthCounts))
					})
					this.roomPrice = roomPrice.toFixed(2)
					this.servicePrice = servicePrice.toFixed(2)
					this.cashPrice = cashPrice.toFixed(2)
				},
				deep: true,
				immediate: true

			},
			roomPrice: {
				handler(newVal, oldVal) {

					this.couponLimit = newVal * 1 + this.servicePrice * 1
					console.log('价格变化', this.couponLimit);
				},
				deep: true,
				immediate: true
			},
			servicePrice: {
				handler(newVal, oldVal) {
					this.couponLimit = newVal * 1 + this.roomPrice * 1
					console.log('价格变化', this.couponLimit);
				},
				deep: true,
				immediate: true
			},
			roomSale() {
				console.log(this.manInfo, 'this.manInfo');
				// 查询酒店设置
				this.$iBox
					.http('getShopSetting', {
						shop_id: this.roomSale.shop_id
					})({
						method: 'post'
					})
					.then(res => {

						let enter_time = res.data.filter(item => {

							return item.sign == 'enter_time'
						})[0].property.value

						enter_time = Number(enter_time.split(':')[0])
						if (this.manInfo && this.manInfo.upgrade_growth_value != -1) {
							console.log(this.manInfo, 'this.manInfo1');
							let leave_time = ''
							this.gradeList.filter(item => {
								if (this.manInfo.grade == item.id) {
									item.right_itererest.forEach(item1 => {
										if (item1.sign == 'yctf') {
											leave_time = item1.value
										}
									})
								}
							})



							if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
									'YYYY-MM-DD HH:mm:ss').add(
									this.roomSale.stay_time, "hours")).format('YYYY-MM-DD HH:mm:ss')
							} else if (this.roomSale.sign == 'standard') {
								// 判断是否为凌晨
								if (0 <= this.$moment().get('hours') && this.$moment().get('hours') <= 6) {

									this.datetimesingle1 = this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss')
										.format('YYYY-MM-DD') + ` ${leave_time}:00`
								} else {
									this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
											'YYYY-MM-DD HH:mm:ss').add(
											this.roomSale.stay_time, "days")).format('YYYY-MM-DD') +
										` ${leave_time}:00`
								}


							} else {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
									'YYYY-MM-DD HH:mm:ss').add(
									this.roomSale.stay_time, "months")).format('YYYY-MM-DD') + ` ${leave_time}:00`
							}

						} else {
							if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
									'YYYY-MM-DD HH:mm:ss').add(
									this.roomSale.stay_time, "hours")).format('YYYY-MM-DD HH:mm:ss')
							} else if (this.roomSale.sign == 'standard') {
								// 判断是否为凌晨
								if (0 <= this.$moment().get('hours') && this.$moment().get('hours') < 6) {

									this.datetimesingle1 = this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss')
										.format('YYYY-MM-DD') + ` ${this.leave_time}:00`
								} else {
									this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
											'YYYY-MM-DD HH:mm:ss').add(
											this.roomSale.stay_time, "days")).format('YYYY-MM-DD') +
										` ${this.leave_time}:00`
								}


							} else {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
										'YYYY-MM-DD HH:mm:ss').add(
										this.roomSale.stay_time, "months")).format('YYYY-MM-DD') +
									` ${this.leave_time}:00`
							}
							console.log(this.datetimesingle1, 'this.datetimesingle1');
						}
					})


			},
			changeIndexGender() {
				this.connectBill.forEach(item => {
					if (this.chooseConn.id == item.id) {
						item.userMsg.forEach((item1, index1) => {
							if (this.msgIndex == index1) {
								item1.gender = this.genderList[this.changeIndex1].id
							}

						})
					}
				})
			},
			changeIndexCard() {
				this.connectBill.forEach(item => {
					if (this.chooseConn.id == item.id) {
						item.userMsg.forEach((item1, index1) => {
							if (this.msgIndex == index1) {
								item1.identification_type = this.cardList[this.changeIndex2].id
							}
						})
					}
				})
			}
		},
		onLoad(options) {
			// 获取离店时间设置
			this.leave_time = this.shopSetting.filter(item => {
				return item.sign == 'leave_time'
			})[0].property.value
			this.$iBox
				.http('getRoomSellType', {})({
					method: 'post'
				})
				.then(res => {
					// res.data.forEach(item => {
					// 	this.roomStatusBox.push(item)
					// })
					this.roomStatusBox = res.data
					this.$iBox
						.http('getRoomSaleType', {
							sell_type: res.data[0].id,
							status: 1
						})({
							method: 'post'
						})
						.then(res => {
							this.roomSaleList = res.data
							this.roomSale = res.data[0]

							// 订单来源
							this.$iBox
								.http('getBillSource', {})({
									method: 'post'
								})
								.then(res => {
									this.billSource = res.data
								})
							this.datetimesingle = this.$moment(this.$moment().unix() * 1000).format(
								'YYYY-MM-DD HH:mm:ss')

							this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
									'YYYY-MM-DD HH:mm:ss').add(1, "days")).format('YYYY-MM-DD') +
								` ${this.leave_time}:00`
							this.startTime = this.$moment().format('YYYY-MM-DD')
							console.log('小于12', this.roomSale.sign);

							if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
								this.endTime = this.$moment(this.$moment(this.startTime, 'YYYY-MM-DD HH:mm:ss')
									.add(roomSale.stay_time,
										"hours")).format('YYYY-MM-DD HH:mm:ss')
							} else if (this.roomSale.sign == 'standard') {
								// 判断是否是凌晨
								this.endTime = this.$moment(this.$moment(this.startTime, 'YYYY-MM-DD HH:mm:ss')
									.add(this.dayCounts, "days")).format('YYYY-MM-DD HH:mm:ss')


							} else {
								this.endTime = this.$moment(this.$moment(this.startTime, 'YYYY-MM-DD HH:mm:ss')
									.add(this.monthCounts,
										"months")).format('YYYY-MM-DD HH:mm:ss')
							}

							this.params = {
								end_time: this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss').unix(),
								room_clear_status: [1, 2, 3],
								room_record_status: [3, 5],
								room_sale_type: this.roomSale.id,
								start_time: this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').unix(),
								times: this.dayCounts
							}
							this.$iBox
								.http('getUsableRoomType', this.params)({
									method: 'post'
								})
								.then(res => {
									let a = []

									res.data.forEach(item => {
										a.push(item)
										item.room_prices.room_service.forEach((item1, index1) => {
											if (index1 == 0) {
												item1.checked = true
											} else {
												item1.checked = false
											}

										})
									})

									this.roomTypeList = JSON.parse(JSON.stringify(a))
									console.log(this.roomInfo, 'this.roomInfo');
									if (this.roomInfo) {
										a.forEach(item => {
											if (item.id == this.roomInfo.room_type_id) {
												this.roomInfo.room_prices = item.room_prices
											}
										})

										let price = JSON.parse(JSON.stringify(this.roomInfo))
										let userInfo = []
										let msg = {
											name: '',
											gender: 0,
											phone: '',
											identification_type: 1,
											identification_number: ''
										}
										userInfo.push(msg)
										price.user_info = userInfo

										this.roomList.push(price)
									} else {
										this.roomList = []
									}

									// 查询证件类型
									this.$iBox.http('getIdentificationTypeList', {

										})({
											method: 'post'
										})
										.then(res => {
											this.cardList = res.data
										})

								})
						})
				})


		},
		methods: {
			getRoomType() {
				this.$iBox
					.http('getUsableRoomType', this.params)({
						method: 'post'
					})
					.then(res => {
						let a = []
						res.data.forEach(item => {
							item.room_prices.room_service.forEach((item1, index1) => {
								if (index1 == 0) {
									item1.checked = true
								} else {
									item1.checked = false
								}

							})
							a.push(item)
						})

						console.log(this.roomList, 'this.roomList');
						this.roomTypeList = JSON.parse(JSON.stringify(a))
						let b = JSON.parse(JSON.stringify(this.roomList))
						a.forEach(item => {
							b.forEach(item1 => {
								if (item.id == item1.room_type_id) {
									if (!item1.user_info || item1.user_info.length == 0) {
										let userInfo = []
										let msg = {
											name: '',
											gender: 0,
											phone: '',
											identification_type: 1,
											identification_number: ''
										}
										userInfo.push(msg)
										item1.user_info = userInfo
										console.log('change');
									}
									item1.room_prices = item.room_prices
								}
							})

						})
						// 重新复制刷新数据
						this.roomList = b
						this.roomList.forEach(item1 => {
							let roomPrice = 0
							let servicePrice = 0
							let cashPrice = 0
							item1.room_prices.room_price.forEach(price => {
								roomPrice += (price.room_price * 1)
							})

							item1.room_prices.room_service.forEach(item2 => {
								if (item2.checked) {
									servicePrice += item2.price * (this.roomSale.sign == 'hour' || this
										.roomSale.sign == 'conference_room' ? 1 : (
											this.roomSale.sign == 'standard' ? this.dayCounts :
											this
											.monthCounts))
								}
							})
							cashPrice += item1.room_prices.cash_pledge * (this.roomSale.sign == 'hour' || this
								.roomSale.sign == 'conference_room' ? 1 : (this
									.roomSale.sign == 'standard' ? this.dayCounts : this.monthCounts))

							this.roomPrice = roomPrice.toFixed(2)
							this.servicePrice = servicePrice.toFixed(2)
							this.cashPrice = cashPrice.toFixed(2)
						})

					})
			},
			changePrice(e) {
				this.itemPrice = e
				this.pricesBox = e.room_prices
				this.roomList.forEach(item => {
					if (item.id == e.id) {
						item.room_prices = e.room_prices
					}
				})
				console.log(this.roomList, 'ee');
				this.popPrice = true
			},
			closepopPrice() {
				this.popPrice = false
			},
			chooseService(e) {
				this.roomList.forEach(item => {
					if (item.id == e.item.id) {
						item.room_prices.room_service.forEach(item1 => {
							if (item1.id == e.item1.id) {
								item1.checked = true
							} else {
								item1.checked = false
							}
						})
					}
				})
			},
			chooseInfo() {
				// console.log(this.manList, 'this.manList');
				if (this.manList.length == 1) {
					this.manInfo = this.manList[0]
					this.linkman = this.manInfo.name
				}

			},

			searchInfo(e) {
				
				this.$iBox.debounce(() => {
					this.paramsMan.search_word = this.linkman
					this.$iBox
						.http('searchUser', this.paramsMan)({
							method: 'post'
						})
						.then(res => {
							this.manList = res.data.list
							console.log(this.manList,'dddddddddddd');
							this.ifShow = true
						})
				}, 1500,false);
				
			},
			searchInfo1(e) {
				console.log(e, '输入');
				this.$iBox.debounce(() => {
					this.paramsPhone.search_word = this.linkphone
					this.$iBox
						.http('searchUser', this.paramsPhone)({
							method: 'post'
						})
						.then(res => {
							this.manList = res.data.list
							console.log(this.manList,'dddddddddddd');
							this.ifShow = true
						})
				}, 1500, false);
			},
			searchInfo2(e) {
				console.log(e, '输入');
				this.paramsMan.search_word = e.item.user_info[e.index2].name
				this.$iBox.debounce(() => {
					this.$iBox
						.http('searchUser', this.paramsMan)({
							method: 'post'
						})
						.then(res => {
							this.manCheckInList = res.data.list
							this.manCheckIndex = e.index2
							this.ifShow2 = true
						})
				}, 1500, false);
				
			},
			searchInfo3(e) {
				console.log(e, '输入');
				this.paramsMan.search_word = e.item.user_info[e.index2].name
				this.$iBox.debounce(() => {
					this.$iBox
						.http('searchUser', this.paramsMan)({
							method: 'post'
						})
						.then(res => {
							this.manCheckInList = res.data.list
							this.manCheckIndex = e.index2
							this.ifShow2 = true
						})
				}, 1500, false);
				
				
			},
			chooseCheckMan(e) {
				console.log(e);
				for (var i = 0; i < this.roomList.length; i++) {
					if (i == e.index) {
						this.roomList[e.index].user_info[this.manCheckIndex].name = e.item3.name
						this.roomList[e.index].user_info[this.manCheckIndex].phone = e.item3.phone
						this.roomList[e.index].user_info[this.manCheckIndex].identification_number = e.item3
							.identification_number
						this.roomList[e.index].user_info[this.manCheckIndex].gender = e.item3.gender
						this.roomList[e.index].user_info[this.manCheckIndex].identification_type = e.item3
							.identification_type
					}
					break;
				}

				this.ifShow2 = false


			},
			bindChange(e) {
				console.log(this.roomSale, 'this.roomSale');
				this.roomSaleList = []
				this.changeIndex = e.detail.value
				this.changeIndex1 = 0
				this.$iBox
					.http('getRoomSaleType', {
						sell_type: this.roomStatusBox[this.changeIndex].id,
						status: 1
					})({
						method: 'post'
					})
					.then(res => {
						this.roomSaleList = res.data
						this.roomSale = this.roomSaleList[this.changeIndex1]
						this.params.room_sale_type = this.roomSale.id
						this.getRoomType()
					})


			},
			bindChange1(e) {
				this.changeIndex1 = e.detail.value
				this.roomSale = this.roomSaleList[this.changeIndex1]
				this.params.room_sale_type = this.roomSale.id
				this.getRoomType()
			},
			bindChange2(e) {

				this.changeIndex2 = e.detail.value
			},
			bindChange3(e) {
				this.changeIndex3 = e.detail.value
				if (this.priceTypeList[e.detail.value].name == '单位') {
					this.$iBox
						.http('getIntermediaryList', {
							type: 1
						})({
							method: 'post'
						})
						.then(res => {
							this.mediatorList = []
							res.data.forEach(item => {
								this.unitList.push(item)
							})

							this.params.intermediaries_id = this.unitList[0].id
							this.getRoomType()

						})
				} else if (this.priceTypeList[e.detail.value[0]].name == '中介') {
					this.$iBox
						.http('getIntermediaryList', {
							type: 2
						})({
							method: 'post'
						})
						.then(res => {
							this.unitList = []
							res.data.forEach(item => {
								this.mediatorList.push(item)
							})
							this.params.intermediaries_id = this.mediatorList[0].id
							this.getRoomType()
						})
				} else {
					this.unitList = []
					this.mediatorList = []
				}
			},
			bindChange4(e) {
				this.changeIndex4 = e.detail.value
				this.params.intermediaries_id = this.unitList[this.changeIndex4].id
				this.getRoomType()
			},
			bindChange5(e) {
				console.log(e);
				this.changeIndex5 = e.detail.value
				this.params.intermediaries_id = this.mediatorList[this.changeIndex5].id
				this.getRoomType()
			},
			bindChange6(e) {
				console.log(e);
				this.changeIndex6 = e.detail.value
			},
			chooseName(e) {
				this.manList = []
				this.linkman = e.name
				this.linkphone = e.phone
				this.manInfo = e
				console.log(e, '会员信息');

			},
			// changeLog(e) {
			// 	this.datetimesingle = e
			// 	if (this.manInfo) {
			// 		let leave_time = ''
			// 		this.gradeList.filter(item => {
			// 			if (this.manInfo.grade == item.id) {
			// 				item.right_itererest.forEach(item1 => {
			// 					if (item1.sign == 'yctf') {
			// 						leave_time = item1.value
			// 					}
			// 				})
			// 			}
			// 		})

			// 		if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
			// 			this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this.dayCounts,
			// 				"hours")).format('YYYY-MM-DD HH:mm:ss')
			// 		} else if (this.roomSale.sign == 'standard') {
			// 			this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this.dayCounts,
			// 					"days"))
			// 				.format('YYYY-MM-DD') + ` ${leave_time}:00`
			// 		} else {
			// 			this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this.dayCounts,
			// 				"months")).format('YYYY-MM-DD') + ` ${leave_time}:00`
			// 		}

			// 	} else {

			// 		if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
			// 			this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this.dayCounts,
			// 				"hours")).format('YYYY-MM-DD HH:mm:ss')
			// 		} else if (this.roomSale.sign == 'standard') {
			// 			this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this.dayCounts,
			// 					"days"))
			// 				.format('YYYY-MM-DD') + ` ${this.leave_time}:00`
			// 		} else {
			// 			this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this.dayCounts,
			// 				"months")).format('YYYY-MM-DD') + ` ${this.leave_time}:00`
			// 		}
			// 	}
			// 	this.params.start_time = this.$moment(this.datetimesingle,'YYYY-MM-DD HH:mm:ss').unix()
			// 	this.params.end_time = this.$moment(this.datetimesingle1,'YYYY-MM-DD HH:mm:ss').unix()
			// 	this.params.times = this.dayCounts
			// 	this.getRoomType()
			// },
			changeLog1(e) {
				this.datetimesingle1 = e
				if (this.roomSale.sign == 'standard') {
					let s = this.$moment(this.datetimesingle.split(' ')[0])
					let e = this.$moment(this.datetimesingle1.split(' ')[0])
					console.log(s, e, this.datetimesingle, this.datetimesingle1);
					let c = e.diff(s, 'days')
					this.dayCounts = c
				}

				this.params.end_time = this.$moment(this.datetimesingle1).unix()
				this.params.times = this.dayCounts
				this.getRoomType()
			},
			dayChange(e) {
				this.dayCounts = e
				if (this.manInfo) {
					let leave_time = ''
					this.gradeList.filter(item => {
						if (this.manInfo.grade == item.id) {
							item.right_itererest.forEach(item1 => {
								if (item1.sign == 'yctf') {
									leave_time = item1.value
								}
							})
						}
					})
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"days")).format('YYYY-MM-DD') + ` ${leave_time}:00`
				} else {
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"days")).format('YYYY-MM-DD') + ` ${this.leave_time}:00`
				}
				this.params.start_time = this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params.end_time = this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params.times = this.dayCounts
				this.getRoomType()
			},
			monthChange(e) {
				if (this.manInfo) {
					let leave_time = ''
					this.gradeList.filter(item => {
						if (this.manInfo.grade == item.id) {
							item.right_itererest.forEach(item1 => {
								if (item1.sign == 'yctf') {
									leave_time = item1.value
								}
							})
						}
					})
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"months")).format('YYYY-MM-DD') + ` ${leave_time}:00`

				} else {
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"months")).format('YYYY-MM-DD') + ` ${this.leave_time}:00`

				}
				this.params.start_time = this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params.end_time = this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params.times = this.dayCounts
				this.getRoomType()
			},
			changeDatePrice() {

				this.roomList.forEach(item => {
					if (item.id == this.itemPrice.id) {
						item.room_prices = this.pricesBox
					}
				})
				// console.log(this.roomList);
			},
			dayPrice(e) {
				console.log(e, this.pricesBox);
				this.pricesBox.room_price.forEach(item => {
					item.room_price = e
				})
			},
			selectRoom() {
				// console.log(e);
				this.params1.start_time = this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params1.end_time = this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params1.room_sale_type = this.roomSale.id
				this.params1.intermediaries_id = this.unitList.length > 0 && this.mediatorList.length == 0 ? this.unitList[
					this.changeIndex4].id : (this.unitList.length == 0 && this.mediatorList.length > 0 ? this
					.mediatorList[this.changeIndex5].id : '')
				console.log(this.priceTypeList[this.changeIndex3].name);
				if (this.priceTypeList[this.changeIndex3].name == '会员') {
					this.params1.grade_id = this.manInfo ? this.manInfo.grade : ''
				}
				this.$iBox.http('selectRoom', this.params1)({
					method: 'post'
				}).then(res => {
					this.selectRoomList = res.data
					this.roomList.forEach(item => {
						this.chooseIds = []
						this.chooseRooms = []

						let selectRoomList = JSON.parse(JSON.stringify(this.selectRoomList))
						selectRoomList.forEach(item4 => {
							if (item4.building == item.building_id) {
								item4.floor_list.forEach(item1 => {
									if (item1.floor == item.floor_name) {
										item1.room_list = item1.room_list.filter(item2 => {
											return item2.id != item.id
										})
									}
								})
							}
						})
						this.selectRoomList = selectRoomList

					})
					console.log(this.roomList, this.selectRoomList, 'this.roomList');
					this.showRc = true
				})
			},
			closeRc() {
				this.showRc = false
			},
			getRcIds(e) {
				console.log(e, 'sure1', this.roomList);
				let rooms = e.rooms
				this.roomTypeList.forEach(item => {
					rooms.forEach(item1 => {
						if (item.id == item1.room_type_id) {
							let userInfo = []
							let msg = {
								name: '',
								gender: 0,
								phone: '',
								identification_type: 1,
								identification_number: ''
							}
							userInfo.push(msg)
							item1.user_info = userInfo
							item1.room_prices = item.room_prices
						}
					})

				})

				this.roomList = [...this.roomList, ...rooms]


				if (this.manInfo) {
					this.roomList[0].user_info[0].name = this.manInfo.name
					this.roomList[0].user_info[0].phone = this.manInfo.phone
					this.roomList[0].user_info[0].identification_type = this.manInfo.identification_type
					this.roomList[0].user_info[0].identification_number = this.manInfo.identification_number
					this.roomList[0].user_info[0].gender = this.manInfo.gender
				} else {
					this.roomList[0].user_info[0].name = this.linkman
					this.roomList[0].user_info[0].phone = this.linkphone
				}



				console.log(e, 'sure', this.roomList);
			},
			deleteRoom(e) {
				let roomList = JSON.parse(JSON.stringify(this.roomList))
				roomList.forEach(item => {
					if (item.id == e.item.id) {
						item.room_list = item.room_list.filter(item1 => {
							return item1.id != e.item1.id
						})
					}
				})
				this.roomList = roomList
				console.log(this.roomList, 'dd', e);
			},
			chooseCoupon() {
				this.popCoupon = true
			},
			closePopCoupon() {
				this.popCoupon = false
			},
			getInfo(e) {
				console.log(e);
				this.chooseCouponse = e
				this.chooseCouponsePrice = e.discounts
				this.popCoupon = false
			},
			surePay() {
				this.$iBox.throttle1(() => {
					this.checkIn()
				}, 2000);
			},
			bindChangeGender(e) {
				console.log(e, 'rrr');
				this.changeIndexGender = e.detail.value[0]
			},
			bindChangeCard(e) {
				this.changeIndexCard = e.detail.value[0]
			},
			getGender(e) {
				this.chooseConn = e.item
				this.msgIndex = e.index2
			},
			addUserMsg(e) {
				let roomList = this.roomList
				roomList.forEach(item => {
					if (e.id == item.id) {
						let msg = {
							name: '',
							gender: 0,
							phone: '',
							identification_type: 1,
							identification_number: ''
						}
						item.user_info.push(msg)
					}
				})
				this.roomList = JSON.parse(JSON.stringify(roomList))
			},
			reduceUserMsg(e) {
				let roomList = this.roomList
				roomList.forEach(item => {
					if (e.item.id == item.id) {
						item.user_info.splice(e.index2, 1)
					}
				})
				this.roomList = JSON.parse(JSON.stringify(roomList))
			},
			exchangeRoom(e) {
				console.log(e);
				this.chooseRoom = e
				this.params1.start_time = this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params1.end_time = this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss').unix()
				this.params1.room_sale_type = this.roomSale.id
				this.params1.intermediaries_id = this.unitList.length > 0 && this.mediatorList.length == 0 ? this.unitList[
					this.changeIndex4].id : (this.unitList.length == 0 && this.mediatorList.length > 0 ? this
					.mediatorList[this.changeIndex5].id : '')
				console.log(this.priceTypeList[this.changeIndex3].name);
				if (this.priceTypeList[this.changeIndex3].name == '会员') {
					this.params1.grade_id = this.manInfo ? this.manInfo.grade : ''
				}
				this.$iBox.http('selectRoom', this.params1)({
					method: 'post'
				}).then(res => {
					this.selectRoomList = res.data
					this.roomList.forEach(item => {
						this.chooseIds = []
						this.chooseRooms = []

						let selectRoomList = JSON.parse(JSON.stringify(this.selectRoomList))
						selectRoomList.forEach(item4 => {
							if (item4.building == item.building_id) {
								item4.floor_list.forEach(item1 => {
									if (item1.floor == item.floor_name) {
										item1.room_list = item1.room_list.filter(item2 => {
											return item2.id != item.id
										})
									}
								})
							}
						})
						this.selectRoomList = selectRoomList

					})
					console.log(this.roomList, this.selectRoomList, 'this.roomList');
					this.showExRc = true
				})
			},
			getExRcIds(e) {
				let room = e.rooms[0]
				let room_list = JSON.parse(JSON.stringify(this.roomList))
				for (var i = 0; i < room_list.length; i++) {
					if (this.chooseRoom.id == room_list[i].id) {
						room.room_prices = this.chooseRoom.room_prices
						room.user_info = this.chooseRoom.user_info
						room_list[i] = room
					}
				}
				this.roomList = room_list
				console.log(this.roomList, 'room');
			},
			closeExRc() {
				this.showExRc = false
			},
			deleRoom(e) {
				let roomList = this.roomList.filter(item => {
					return item.id != e.id
				})
				this.roomList = JSON.parse(JSON.stringify(roomList))
			},
			checkIn() {
				console.log(this.roomList, 'room');
				if (!this.linkman) {
					uni.showToast({
						icon: 'none',
						title: '请完善联系人信息'
					})
					return
				}



				let params = {
					memo: this.memo,
					bill_source: this.billSource[this.changeIndex6].id,
					enter_time_plan: this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').unix(),
					price_project: this.priceTypeList[this.changeIndex3].id,
					room_list: [],
					room_sale_type: this.roomSale.id,
					stay_type: this.stayTypeList[this.changeIndex2].id,
					times: this.roomSale.sign == 'conference_room' || this.roomSale.sign == 'hour' ? this.roomSale
						.stay_time : (this.roomSale.sign == 'standard' ? this.dayCounts : this.monthCounts),
					user_info: {
						link_man: this.linkman,
						link_phone: this.linkphone,
						common_code: this.priceTypeList[this.changeIndex3].name == '会员' ? this.manInfo.common_code : ''
					},
					intermediary_id: this.unitList.length > 0 && this.mediatorList.length == 0 ? this.unitList[
						this.changeIndex4].id : (this.unitList.length == 0 && this.mediatorList.length > 0 ? this
						.mediatorList[this.changeIndex5].id : ''),
					user_coupon_id: this.chooseCouponse ? this.chooseCouponse.id : '',
					other_bill_code: this.outOrderNum
				}
				let room_list = []
				this.roomList.forEach(item => {
					let custom_room_price = []
					item.room_prices.room_price.forEach(item1 => {
						let price = {
							date: item1.date,
							price: item1.room_price
						}
						custom_room_price.push(price)
					})

					let room = {
						custom_price: {
							custom_cash_pledge: item.room_prices.cash_pledge,
							custom_room_price: custom_room_price,
						},
						room_id: item.id,
						room_service_selected: item.room_prices.room_service.filter(
							service => {
								return service.checked == true
							})[0].id,
						room_type_id: item.room_type_id,
						user_info: item.user_info
					}
					room_list.push(room)



				})
				params.room_list = room_list

				if (this.roomSale.sign == 'hour') {
					this.$iBox.http('HourcheckIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title: '提示',
								content: '办理成功!',
								cancelText: '返回',
								confirmText: '查看订单',
								success: res => {
									if (res.confirm) {
										uni.navigateTo({
											url: '../bill/billList/billList'
										})
									} else {
										uni.navigateBack({})
									}
								}
							})
						})
				} else if (this.roomSale.sign == 'standard') {
					this.$iBox.http('checkIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title: '提示',
								content: '办理成功!',
								cancelText: '返回',
								confirmText: '查看订单',
								success: res => {
									if (res.confirm) {
										uni.navigateTo({
											url: '../bill/billList/billList'
										})
									} else {
										uni.navigateBack({})
									}
								}
							})
						})
				} else if (this.roomSale.sign == 'long_standard') {
					this.$iBox.http('LongStandardcheckIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title: '提示',
								content: '办理成功!',
								cancelText: '返回',
								confirmText: '查看订单',
								success: res => {
									if (res.confirm) {
										uni.navigateTo({
											url: '../bill/billList/billList'
										})
									} else {
										uni.navigateBack({})
									}
								}
							})
						})
				} else {
					this.$iBox.http('ConferencecheckIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title: '提示',
								content: '办理成功!',
								cancelText: '返回',
								confirmText: '查看订单',
								success: res => {
									if (res.confirm) {
										uni.navigateTo({
											url: '../bill/billList/billList'
										})
									} else {
										uni.navigateBack({})
									}
								}
							})
						})
				}




			}

		}
	}
</script>

<style lang="scss" scoped>
	.ordingBox {
		width: 96%;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin: 20rpx auto;

		.title {
			font-size: 36rpx;
			font-weight: 600;
			padding: 20rpx 0;
		}

		.msgBox {
			border-top: 1px solid #c9c9c9;

			.msgItem {
				display: flex;
				align-items: center;
				padding: 20rpx 0;

				.msgInput {
					border: 1px solid #ece8e8;
					height: 36rpx;
					width: 340rpx;
					border-radius: 4px;
					padding: 4px 10px;
					font-size: 30rpx;
					margin-left: 36rpx;
				}

				.pickerBox {
					margin-left: 16rpx;
					position: relative;
					height: 60rpx;
					width: 380rpx;
					border-radius: 14rpx;
					border: 1px solid #eee;
					display: flex;
					padding: 0 20rpx;
					font-size: 30rpx;
					align-items: center;

					.arrow {
						animation-name: to_bottom_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					.arrow_ac {
						animation-name: to_up_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					/* 箭头动画 */

					@keyframes to_up_show {
						0% {
							transform: rotate(0);
						}

						50% {
							transform: rotate(90deg);
						}

						100% {
							transform: rotate(180deg);
						}
					}

					@keyframes to_bottom_show {
						0% {
							transform: rotate(180deg);
							animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
						}

						50% {
							transform: rotate(90deg);
							animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
						}

						100% {
							transform: rotate(0deg);
						}
					}
				}
			}


			.manListBox {
				min-height: 80rpx;
				max-height: 500rpx;
				background: #eee;
				padding: 0 20rpx;
				font-size: 26rpx;

				.infoBox {
					display: flex;
					width: 100%;
					justify-content: space-between;
					align-items: center;
					padding: 20rpx 0;
					border-bottom: 1px solid #c9c9c9;
				}
			}
		}

	}

	.roomBox {
		width: 96%;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin: 20rpx auto;

		.title {
			font-size: 36rpx;
			font-weight: 600;
			padding: 20rpx 0;
		}

		.manListBox {
			min-height: 80rpx;
			max-height: 500rpx;
			background: #eee;
			padding: 0 20rpx;
			font-size: 26rpx;

			.infoBox {
				display: flex;
				width: 100%;
				justify-content: space-between;
				align-items: center;
				padding: 20rpx 0;
				border-bottom: 1px solid #c9c9c9;
			}
		}

		.item2 {
			font-size: 28rpx;
			width: 500rpx;
			line-height: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin: 10rpx auto;
			padding: 14rpx 0;
			border: 1px solid #eee;
			border-radius: 10rpx;
			font-size: 34rpx;
			font-weight: 600;
		}

		.room {
			width: 100%;
			padding: 20rpx 0;
			border: 1px solid #c9c9c9;
			margin: 10rpx 0;
			border-radius: 10rpx;

			.room_content {
				display: flex;
				align-items: center;
				flex-wrap: wrap;

				.item {
					font-size: 28rpx;
					width: 33%;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					padding-left: 10rpx;
					// justify-content: center;
					padding: 0 20rpx;
					margin: 10rpx 0;
				}

				.item1 {
					font-size: 28rpx;
					width: 100%;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					// justify-content: center;
					padding: 0 20rpx;
					margin: 10rpx 0;
				}

				.item2 {
					font-size: 28rpx;
					width: 500rpx;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx auto;
					padding: 14rpx 0;
					border: 1px solid #eee;
					border-radius: 10rpx;
					font-size: 34rpx;
					font-weight: 600;
				}

				.item3 {
					font-size: 28rpx;
					width: 90%;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					margin: 10rpx auto;
					padding: 14rpx;
					border: 1px solid #eee;
					border-radius: 10rpx;

					.serviceBox {
						width: fit-content;
						padding: 4rpx;
						border: 1px solid #d7d7d7;
						color: #c9c9c9;
						border-radius: 6rpx;
						font-size: 24rpx;
						margin-right: 10rpx;
					}

					.roomSelcet {
						display: flex;
						flex-direction: column;
						align-items: center;
						position: relative;

						.close {
							position: absolute;
							top: -16rpx;
							right: -16rpx;
							font-size: 30rpx;
							color: #ff0000;
							width: 32rpx;
							height: 32rpx;
						}
					}
				}

				.item_msg {
					width: 640rpx;
					display: flex;
					flex-wrap: wrap;
					align-items: center;
					margin: 4rpx 0;

					.item_msg_inpt {
						border: 1px solid #eee;
						padding: 0 10rpx;
						border-radius: 4rpx;
						height: 40rpx;
						background: #fff;
						margin: 4rpx;
					}

					.msgItem {
						display: flex;
						align-items: center;
						padding: 10rpx 0;

						.pickerBox {
							position: relative;
							height: 43rpx;
							width: 110rpx;
							border-radius: 4rpx;
							border: 1px solid #eee;
							display: flex;
							font-size: 30rpx;
							align-items: center;
							background-color: #fff;
							padding-left: 6rpx;
							font-size: 26rpx;

							.arrow {
								animation-name: to_bottom_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}

							.arrow_ac {
								animation-name: to_up_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}


						}
					}
				}
			}
		}
	}

	.priceBox {
		width: 96%;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin: 20rpx auto;

		.title {
			font-size: 36rpx;
			font-weight: 600;
			padding: 20rpx 0;
		}
	}

	.picker-view_box {
		position: relative;
		height: 650rpx;
		width: 100vw;

		.picker-view {
			height: 600rpx;
			width: 100vw;

			.item {
				width: 100%;
				align-items: center;
				justify-content: center;
				text-align: center;
			}
		}
	}

	.linkBox {
		height: 500rpx;
		width: 100%;
		padding: 30rpx;

		.linkSearch {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 20rpx;
		}
	}

	.submitContent {
		box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
		position: fixed;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 24rpx;
		background: #fff;
		z-index: 3;
	}
</style>