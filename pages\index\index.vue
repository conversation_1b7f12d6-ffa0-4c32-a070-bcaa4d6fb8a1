<template>
	<view class="page">
		<view class="mainPage" style="position: fixed;top:0;z-index: -1;width: 750rpx;height: 40vh;"
			:style="mainBg?'':'background: linear-gradient(180deg, '+ themeColor.bg_main_color+'40'+' 0%, '+ '#F5F5F5 100%)'">
			<image v-if="mainBg" style="position: absolute;top: 0;right: 0;width: 100%;height: 100%;" :src="mainBg"
				mode=""></image>
		</view>
		<view class="" style="z-index:9;padding: 0 32rpx;" :style="{width:menuButtonInfo.left+'px'}">
			<view class="" :style="{marginTop:searchBarTop + 'px',height:searchBarHeight + 'px',opacity:opacity}"
				style="display: flex;align-items: center;width: 100%;z-index: 9;position: relative;justify-content: space-between;">
				<view class="" style="width: fit-content;height: 76rpx;" @click="toLogin">
					<p style="font-size: 30rpx;" :style="{color:themeColor.text_main_color}">
						{{userInfo.grade_info?userInfo.grade_info.grade_name:'请注册会员'}}
					</p>
					<p style="font-size: 24rpx;" :style="{color:themeColor.text_main_color}">专属折扣·免押金·积分兑换</p>
				</view>
				<view class="" style="display: flex;flex-direction: column;align-items: center;" @click="toCoupon">
					<view class="icon-lingquanzhongxin" style="font-size: 40rpx;">

					</view>
					<p :style="{color:themeColor.text_main_color}" style="font-size: 20rpx;">领优惠</p>
				</view>
			</view>

		</view>
		<view class="" :style="ifSwiper?'height:64rpx':'height: 30rpx;'"></view>
		<m-couponToast :img="pop.image" :show="pop.status" :url="pop.link"></m-couponToast>
		<view class="" :key="index" v-for="(item, index) in diyModel">
			<m-swiper :swiper_list="item.property.list" :width="item.property.width" :height="item.property.height"
				v-if="item.sign=='slideshow'"></m-swiper>
			<m-hotelCard :mode="item.property.style" v-if="item.sign=='book_room_1'"></m-hotelCard>
			<m-banner :img_url="item.property.bg_image" v-if="item.sign=='coupon_1'"></m-banner>
			<m-gridBox :show="true" :height="item.property.height" :mode="item.property.style"
				:grid_list="item.property.list" v-if="item.sign=='card_1'"></m-gridBox>
			<m-roomList :typesList='types_list' :mode="item.property.style"
				v-if="hackReset&&item.sign=='room_type'"></m-roomList>
			<m-searchAuto v-if="hackReset&&item.sign=='query_order'" @showPhone="showLogin"></m-searchAuto>
			<m-hotelDetailCard v-if="item.sign=='hotel_info'" :mode="item.property"></m-hotelDetailCard>
			<fanxian v-if="item.sign=='booking_share'"></fanxian>
		</view>
		<m-copyRight></m-copyRight>
		<!-- 自助入住卡片 -->
		<view class="autoCard" style="background-color: #FFFFFF;" v-if="ifShowTime">
			<p style="width: 100%;font-size: 32rpx;margin-top: 20rpx;text-align: center;"
				:style="{color:themeColor.text_main_color}">温馨提示</p>
			<p style="width: 100%;font-size: 32rpx;margin-top: 20rpx;text-align: center;"
				:style="{color:themeColor.text_main_color}">您有正在入住的订单,点击进入房卡页开锁！</p>
			<view class=""
				style="height: 80rpx;width: 100%;display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;margin-top: 30rpx;">
				<view class="" @click="closeShowTime"
					:style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}"
					style="display: flex;align-items: center;justify-content: center;height: 80rpx;width: 275rpx;border-radius: 60rpx;">
					关闭
				</view>
				<view class="" @click="toCard" :style="{color:themeColor.bg_color,background:themeColor.bg_main_color}"
					style="display: flex;align-items: center;justify-content: center;height: 80rpx;width: 275rpx;border-radius: 60rpx;">
					去房卡页
				</view>
			</view>
		</view>

		<m-popup :show="activePop" mode="center" :closeable="true" @closePop="closeActive">
			<view class="member-gift-container">
				<!-- 顶部庆祝区域 -->
				<view class="celebration-header">
					<view class="gift-icon">🎉</view>
					<text class="congratulations-text">恭喜您已获得以下会员权益！</text>
					<view class="sparkle-effects">
						<text class="sparkle">✨</text>
						<text class="sparkle">⭐</text>
						<text class="sparkle">💎</text>
					</view>
				</view>

				<!-- 会员卡区域 -->
				<view class="member-card">
					<view class="card-background">
						<view class="card-pattern"></view>
						<view class="card-shine"></view>
					</view>

					<view class="card-content">
						<!-- 会员等级 -->
						<view class="member-level-section">
							<text class="level-label">会员等级</text>
							<text class="level-value">{{memberGiftInfo?memberGiftInfo.grade_name: '**'}}</text>
						</view>

						<!-- 有效期 -->
						<view class="validity-section">
							<text class="validity-label">有效期至</text>
							<text
								class="validity-value">{{memberGiftInfo?memberGiftInfo.usable_month+'个月': '**个月'}}</text>
						</view>

						<!-- 价值 -->
						<view class="validity-section">
							<text class="validity-label">价值</text>
							<text class="validity-value">{{memberGiftInfo?memberGiftInfo.amount+'元': '*元'}}</text>
						</view>
					</view>
				</view>

				<!-- 权益说明 -->
				<view class="benefits-section">
					<text class="benefits-title">专属权益</text>
					<view class="benefits-list">
						<view class="benefit-item">
							<text class="benefit-icon">🎯</text>
							<text class="benefit-text">专属折扣优惠</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">💳</text>
							<text class="benefit-text">免押金入住</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">🎁</text>
							<text class="benefit-text">积分兑换礼品</text>
						</view>
					</view>
				</view>
			</view>
		</m-popup>

		<m-tabbar :list="tabbar"></m-tabbar>
		<!-- <official-account></official-account> -->
		<!-- tabbar安全区域 -->
		<m-login v-if="hackReset1&&if_login" @loginTo="loginSucess" :customStyle="cusStyle"></m-login>
		<!-- <m-needAuthor v-if="hackReset&&!if_login"></m-needAuthor> -->
		<view style="height: 120rpx;"></view>
	</view>
</template>

<script>
	import fanxian from '@/pages/myCenter/components/fanxian.vue'
	// const plugin1 = requirePlugin("myPlugin");
	var log = require('../../plugins/log.js')
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				types_list: [],
				img1: '',
				img2: '',
				diyModel: [],
				hackReset: true,
				hackReset1: true,
				ifShowTime: false,
				seconds: 15,
				autoRoomSetting: false,
				if_login: false,
				cusStyle: null,
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				menuButtonInfo: null,
				mainBg: '',
				ifSwiper: true,
				activePop: false,
				// 会员赠送弹窗相关
				memberGiftInfo: null
			}
		},
		components: {
			fanxian
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting', 'setting','hotelList']),
			...mapState('food', ['tableId']),
		},
		watch: {},
		async onLoad(options) {
			await this.$onLaunched;
			const systemInfo = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.menuButtonInfo = menuButtonInfo
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo.statusBarHeight + 44;

			let du = ''
			let scene = wx.getEnterOptionsSync()

			if (scene.query.scene && (scene.scene == 1047 || scene.scene == 1048 || scene.scene == 1049 || scene
					.scene == 1007 || scene.scene == 1008)) {
				let query = decodeURIComponent(scene.query.scene)
				//解析参数
				if (query.includes("du")) {
					du = this.$iBox.linkFormat(query, "du")
					// 更新分销人
					if (du) {
						this.$iBox.http('updateDistributionUserId', {
							distribution_user_id: du
						})({
							method: 'post'
						}).then(res => {
							let hotel = this.hotelList.filter(item=>{
								return item.id == res.data.shop_id
							})[0]
							console.log(hotel,'hotel');
							this.getHotel(hotel)

						})
					}

				}
			}
			

	

		},
		async onShow() {
			await this.$onLaunched;
			
			// 获取自助入住设置，
			this.autoRoomSetting = this.shopSetting.filter(item => {
				return item.sign == 'self_check_in'
			})[0].property.status
			
			if (this.autoRoomSetting) {
				// // 查询订单,如果订单数量为1则自动跳转选房页，多于1则选择订单
				this.$iBox.http('getUserRoomBillList', {
					search_word: this.searchKey
				})({
					method: 'post'
				}).then(res => {
					if (res.data != 'no_login_bill' && res.data.length > 0) {
						if (res.data[0].team_id) {
							if (res.data[0].room_id) {
								// uni.switchTab({
								// 	url: '/pages/myRoom/myRoom'
								// })
								this.ifShowTime = true
							} else {
								uni.navigateTo({
									url: '/packageB/teamCheckIn/teamCheckIn'
								})
							}
			
						} else {
							this.ifShowTime = true
							// uni.switchTab({
							// 	url: '/pages/myRoom/myRoom'
							// })
						}
			
					}
				})
			}
			
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			// this.toLogin()
			this.cusStyle = {
				zindex: 1001
			}
			console.log(this.$moment(1750305599 * 1000).format('YYYY-MM-DD') + ' ' + '08' + ':00')
			uni.showLoading({
				title: '加载中...'
			})
			this.$iBox.http('getHomePageUi', {
				path: 'pages/index/index',
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {
				console.log('index2');
				this.diyModel = res.data
				uni.hideLoading()
				// 判断是否是选择城市模式，判断组件
				res.data.forEach(item => {
					if (item.sign == 'book_room_1') {
						if (item.property.style == 4) {
							this.getCityModel(true)
						} else {
							this.getCityModel(false)
						}
					}
				})

				// 判断是否有主页背景图组建，直接获取

				this.mainBg = res.data.filter(item => {
					return item.sign == 'home_page_bg'
				})[0] ? res.data.filter(item => {
					return item.sign == 'home_page_bg'
				})[0].property.bg_image : ''

				this.ifSwiper = res.data.filter(item => {
					return item.sign == 'slideshow'
				})[0] ? false : true
			})


		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			...mapActions('hotel', ['getHotelList', 'getHotel', 'getCityModel', 'getSaleTypes']),
			toCard() {
				uni.switchTab({
					url: '/pages/myRoom/myRoom'
				})
			},
			closeShowTime() {
				this.ifShowTime = false
			},
			closeActive() {
				this.activePop = false
			},
			toLogin() {
				
				this.hackReset1 = false
				this.$nextTick(() => {
					this.hackReset1 = true
				})
				
				let set = this.setting.filter(item => {
					return item.sign == 'auto_register_member'
				})
				//set=1,手动注册 set=2授权手机号自动注册
				if (set[0].property) {
					let a = set[0].property.value
					if (a == 2) {
						console.log(this.userInfo,'检查登录');
						if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo
							.grade_info.upgrade_growth_value > -1) {
							// 查询活动
							this.$iBox.http('getActive', {
								shop_id: this.hotel.id
							})({
								method: 'post'
							}).then(res => {
								res.data.forEach(item => {
									if (item.member_setting_id) {
										// 如果有会员赠送活动，直接弹窗
										this.activePop = true;
										this.memberGiftInfo = item.member_setting;
									}
								})
							})
				
						} else {
							this.if_login = true
						}
				
					} else if (a == 1) {
						// this.pop = true
						if (this.userInfo.grade_info && this.userInfo
							.grade_info.upgrade_growth_value > -1) {
							// 查询活动
							this.$iBox.http('getActive', {
								shop_id: this.hotel.id
							})({
								method: 'post'
							}).then(res => {
								res.data.forEach(item => {
									if (item.member_setting_id) {
										// 如果有会员赠送活动，直接弹窗
										this.activePop = true;
										this.memberGiftInfo = item.member_setting;
									}
								})
							})
				
						} else {
							uni.navigateTo({
								url:'/packageA/memberInfo/memberInfo'
							})
						}
					}
				}

			},
			toCoupon() {
				console.log('kj');
				uni.navigateTo({
					url: '/packageA/couponCenter/couponCenter'
				})
			},
			closeLogin(e) {
				this.if_login = false
			},
			loginSucess() {

				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true

					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					if (set[0].property) {
						let a = set[0].property.value
						if (a == 2) {
							if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
								.upgrade_growth_value > -1) {
								this.if_login = false
								// 查询活动
								this.$iBox.http('getActive', {
									shop_id: this.hotel.id
								})({
									method: 'post'
								}).then(res => {
									res.data.forEach(item => {
										if (item.member_setting_id) {
											// 如果有会员赠送活动，直接弹窗
											this.activePop = true;
											this.memberGiftInfo = item.member_setting;
										}
									})
								})

							} else {
								this.if_login = true
							}

						} else if (a == 1) {
							// this.pop = true
							if (this.userInfo.grade_info && this.userInfo
								.grade_info.upgrade_growth_value > -1) {
								// 查询活动
								this.$iBox.http('getActive', {
									shop_id: this.hotel.id
								})({
									method: 'post'
								}).then(res => {
									res.data.forEach(item => {
										if (item.member_setting_id) {
											// 如果有会员赠送活动，直接弹窗
											this.activePop = true;
											this.memberGiftInfo = item.member_setting;
										}
									})
								})
											
							} else {
								uni.navigateTo({
									url:'/packageA/memberInfo/memberInfo'
								})
							}
						}
					}


				})
			},
			showLogin(e) {
				console.log(e, 'login');
				if (e == 'no_login') {
					this.if_login = true
					this.hackReset1 = false
					this.$nextTick(() => {
						this.hackReset1 = true
					})
				}
			},
		},

		onShareAppMessage: function(res) {
			// 1.返回节点对象
			let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
			let currentPage = pages[pages.length - 1]; //获取当前页面的对象
			let url = currentPage.route //当前页面url

			return {

				path: url
			};
		},
		onPullDownRefresh() {
			this.getProjectList(this.params)
			setTimeout(function() {
				uni.stopPullDownRefresh();
			}, 1000);
		},
		// // 上拉加载
		onReachBottom() {


		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.tabbr_icon {
		width: 42rpx;
		height: 42rpx;
	}

	.search_box {
		padding: 20rpx;
		background-color: #FFFFFF;
		margin: 0 auto;
	}

	.tips_box {

		padding: 16rpx 30rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 26rpx;
	}

	.sub_box {
		padding: 0 20rpx;

		.solt_box {
			display: flex;
			flex-wrap: wrap;

			.solt_inner {
				margin-top: 20rpx;
				padding-right: 18rpx;
				font-size: 26rpx;
				color: #606266;
			}

			.sureLi {
				color: #007AFF;
			}
		}
	}

	.address_box {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		padding: 20rpx;
		font-size: 26rpx;
	}

	.address {
		color: #007AFF;
	}

	.swiper_box {
		padding: 0 20rpx;
	}

	.autoCard {
		position: fixed;
		bottom: 160rpx;
		width: 700rpx;
		height: 308rpx;
		left: 0;
		right: 0;
		margin: auto;
		color: #FFFFFF;
		border-radius: 20rpx;
		background: linear-gradient(7.48deg, #F8F9FF 20.24%, #C5D6FF 94.62%);
		border: 1px solid #D8DEFF;
		z-index: 10;
		display: flex;
		align-items: center;
		flex-direction: column;
		padding: 24rpx;
		/* 设置动态抖动动画 */
		// animation: shake 0.82s cubic-bezier(.36, .07, .19, .97) infinite;
		// transform-origin: center;
	}

	/* 使用CSS关键帧@keyframes定义动画效果 */
	@keyframes shake {

		10%,
		90% {
			transform: translate3d(-1px, 0, 0);
		}

		20%,
		80% {
			transform: translate3d(2px, 0, 0);
		}

		30%,
		50%,
		70% {
			transform: translate3d(-4px, 0, 0);
		}

		40%,
		60% {
			transform: translate3d(4px, 0, 0);
		}
	}

	/* 优化后的会员赠送弹窗样式 */
	.member-gift-container {
		width: 660rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 20rpx;
		overflow: hidden;
		position: relative;
	}

	.celebration-header {
		text-align: center;
		padding: 40rpx 30rpx 30rpx;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
		position: relative;
	}

	.celebration-header .gift-icon {
		font-size: 60rpx;
		margin-bottom: 15rpx;
		animation: bounce 1.5s infinite;
	}

	.celebration-header .congratulations-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	}

	.sparkle-effects {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
	}

	.sparkle {
		position: absolute;
		font-size: 24rpx;
		animation: sparkle 2s infinite;
	}

	.sparkle:nth-child(1) {
		top: 20rpx;
		left: 50rpx;
		animation-delay: 0s;
	}

	.sparkle:nth-child(2) {
		top: 30rpx;
		right: 60rpx;
		animation-delay: 0.5s;
	}

	.sparkle:nth-child(3) {
		bottom: 20rpx;
		left: 80rpx;
		animation-delay: 1s;
	}

	@keyframes sparkle {

		0%,
		100% {
			opacity: 0;
			transform: scale(0.5);
		}

		50% {
			opacity: 1;
			transform: scale(1.2);
		}
	}

	/* 会员卡样式 */
	.member-card {
		margin: 30rpx;
		height: 200rpx;
		border-radius: 20rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.2);
	}

	.card-background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
	}

	.card-pattern {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-image:
			radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx),
			radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx);
		background-size: 40rpx 40rpx;
	}

	.card-shine {
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
		animation: shine 3s infinite;
	}

	@keyframes shine {
		0% {
			transform: translateX(-100%) translateY(-100%) rotate(45deg);
		}

		100% {
			transform: translateX(100%) translateY(100%) rotate(45deg);
		}
	}

	.card-content {
		position: relative;
		z-index: 2;
		padding: 25rpx 30rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.member-level-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.level-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.9);
	}

	.level-value {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}

	.validity-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20rpx;
	}

	.validity-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.9);
	}

	.validity-value {
		font-size: 28rpx;
		font-weight: 500;
		color: #fff;
	}

	.privileges-badge {
		position: absolute;
		top: 25rpx;
		right: 30rpx;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10rpx);
		border-radius: 20rpx;
		padding: 8rpx 16rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
	}

	.badge-text {
		font-size: 20rpx;
		color: #fff;
		font-weight: bold;
	}

	/* 权益说明区域 */
	.benefits-section {
		padding: 30rpx;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20rpx);
	}

	.benefits-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		text-align: center;
	}

	.benefits-list {
		display: flex;
		flex-direction: column;
		gap: 15rpx;
	}

	.benefit-item {
		display: flex;
		align-items: center;
		padding: 15rpx 20rpx;
		background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
		border-radius: 15rpx;
		border: 1rpx solid #e1e8ff;
	}

	.benefit-icon {
		font-size: 32rpx;
		margin-right: 15rpx;
	}

	.benefit-text {
		font-size: 26rpx;
		color: #555;
		font-weight: 500;
	}

	/* 立即体验按钮 */
	.action-button {
		margin: 30rpx;
		background: linear-gradient(135deg, #ff6b6b 0%, #ff1744 100%);
		border-radius: 50rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 10rpx 30rpx rgba(255, 107, 107, 0.4);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
	}

	.action-button::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
		transition: left 0.5s;
	}

	.action-button:active::before {
		left: 100%;
	}

	.action-button:active {
		transform: scale(0.95);
		box-shadow: 0 5rpx 15rpx rgba(255, 107, 107, 0.6);
	}

	.button-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		position: relative;
		z-index: 1;
	}
</style>