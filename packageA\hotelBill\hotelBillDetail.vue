<template>
	<view class="billBOX">
		<view class="title_box"
			:style="{'background-image': 'linear-gradient(180deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
			<p style="font-size: 44rpx;font-weight: 600;" :style="{color:bg_color}">
				{{billStatus(billInfo.bill_status)}}
			</p>

			<view class="content">
				<view class="" v-if="billInfo.bill_status==2">
					<p>酒店正在确认订单，预定结果将会以短信通知您！</p>
					<p style="font-size: 22rpx;font-weight: 300;margin-top: 10rpx;"
						:style="{color:themeColor.com_color2}"
						v-if="(billInfo.bill_status==2||billInfo.bill_status==3)">
						{{billInfo.room_service.cancelable?`此订单在${timeFormate}${billInfo.room_service.before_end_time}点前可以免费取消哦!`:"此订单不支持取消服务 "}}
					</p>
				</view>

				<view class="" v-if="billInfo.bill_status==3">
					<p>酒店已确认订单，请携带本人证件照前往入住！</p>
					<p style="font-size: 22rpx;font-weight: 300;margin-top: 10rpx;"
						:style="{color:themeColor.com_color2}">
						{{billInfo.room_service.cancelable?`此订单在${timeFormate}${billInfo.room_service.before_end_time}点前可以免费取消哦!`:"此订单不支持取消服务 "}}
					</p>
				</view>



				<view class="" v-if="billInfo.bill_status==4">
					<p>您正在入住{{hotelInfo.shop_name}}!</p>

				</view>

				<view class="" v-if="billInfo.bill_status==5">
					<p>本次入住服务已完成!欢迎您的下次入住！</p>
				</view>

				<view class="" v-if="billInfo.bill_status==6">
					<p>正在申请取消，等待酒店确认！</p>
				</view>

				<view class="" v-if="billInfo.bill_status==7">
					<p>取消成功!欢迎您的下次入住！</p>
				</view>

				<view class="" v-if="billInfo.bill_status==9">
					<p>预定未到!欢迎您的下次入住！</p>
				</view>

				<view class="bottomBox">
					<view class="btnBoss" @click="cancleOrder" v-if="billInfo.bill_status==2||billInfo.bill_status==3"
						:style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}">
						申请取消
					</view>

					<view class="btnBoss" @click="rateOrder"
						v-if="billInfo.bill_status==5||billInfo.bill_status==9||billInfo.bill_status==10"
						:style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}">
						去评价
					</view>

					<view class="btnBoss" @click="modeService" v-if="billInfo.bill_status==4"
						:style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}">
						更多服务
					</view>
				</view>
			</view>
		</view>

		<!-- content -->
		<view class="content_box">
			<view class="price_box" v-if="thirdBill&&(billInfo.bill_source_sign=='wxxcx'||billInfo.bill_source_sign=='buru')">
				<view class="price_title">
					<view class="" style="padding-left: 6rpx;">
						<text style="font-weight: 600;font-size: 40rpx;">实付</text>
						<text :style="{color:themeColor.com_color2}">￥</text><text
							style="font-weight: 600;font-size: 40rpx;"
							:style="{color:themeColor.com_color2}">{{(billInfo.bill_amount - billInfo.amount_reduction).toFixed(2)}}</text>
							<text style="margin-left: 30rpx;">优惠金额:{{billInfo.amount_reduction}}元</text>
					</view>
					<!-- <text style="font-size: 24rpx;" :style="{color:themeColor.com_color2}">查看详情</text> -->
				</view>

			</view>
			<view class="hotelBox">
				<image :src="hotelInfo.cover_pic" style="height: 120rpx;width: 120rpx;border-radius: 8rpx;" mode="">
				</image>
				<view class="hotelInfo">
					<view class="" style="display: flex;align-items: center;justify-content: space-between;">
						<text style="font-size: 40rpx;font-weight: 500;">{{hotelInfo.shop_name}}</text>
						<view class="" style="display: flex;align-items: center;color: darkgrey;" @click="goPhone">
							<view class="icon-dianhua"></view>
							<text>联系酒店</text>
						</view>
					</view>

					<view class="content_rate">
						<text class="content_rate_num">{{hotelInfo.socre}}</text>
						<!-- <text>.0</text> -->
						<text style="padding-left: 10rpx;padding-right: 20rpx;">{{socreMate > 4?'很棒':'很好'}}</text>
						<m-rate :value="hotelInfo.socre" :size="20" :readonly="true"></m-rate>
						<view class="content_rate_bg"
							:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.main_color+'40,'+themeColor.com_color2+'40)'}">
						</view>
					</view>
					<view class="content_address" :style="{color:themeColor.text_title_color}">
						<text>{{hotelInfo.address}}</text>
					</view>
				</view>
			</view>

			<view class="roomInfo">
				<view class="nameBox">
					<text>{{billInfo.room_type_name}}{{billInfo.room_number?`(${billInfo.room_number})`:''}}</text>
				</view>
				<!-- 全日房 -->
				<view class="elseBox" v-if="billInfo.room_sale_type_sign=='standard'">
					<text style="">{{billInfo.enter_time_plan | moment}} 至 {{billInfo.leave_time_plan | moment}}</text>
					<text style="padding-left: 10rpx;">{{billInfo.stay_time+ '晚'}}</text>
					<text></text>
				</view>
				<!-- 时租房会议室 -->
				<view class="elseBox"
					v-if="billInfo.room_sale_type_sign=='hour'||billInfo.room_sale_type_sign=='conference_room'">
					<text style="">{{billInfo.enter_time_plan | moment}} </text>
					<text style="padding-left: 10rpx;">{{billInfo.room_sale_type_stay_time+ '小时'}}</text>
					<text></text>
				</view>
				<!-- 月租 -->
				<view class="elseBox" v-if="billInfo.room_sale_type_sign=='long_standard'">
					<text style="">{{billInfo.enter_time_plan | moment}} 至 {{billInfo.leave_time_plan | moment}}</text>
					<text style="padding-left: 10rpx;">{{billInfo.stay_time+ '月'}}</text>
					<text></text>
				</view>
				<view style="display: flex;flex-direction: column;margin-top: 20rpx;" v-if="billInfo.breakfast_record&&billInfo.breakfast_record.length > 0">
					<p style="font-size: 28rpx;font-weight: 600;">套餐详情:</p>
					<view style="font-size: 24rpx;display: flex;" v-for="item in billInfo.breakfast_record">
						<text>名称:{{item.name}}</text>
						<text style="margin-left: 20rpx;">{{item.content}}</text>
						<text style="margin-left: 10rpx;">{{item.use_name}}</text>
						<text style="margin-left: 10rpx;">{{item.date}}</text>
						<p v-if="item.qr" style="margin-left: 10rpx;color: blue;" @click="showFood">查看<text class="icon-erweima"></text> </p>
					</view>

				</view>
				<view class="manInfo">
					<view class="manInfo_title">
						<text class="manInfo_title1" style="padding-right: 60rpx;">入住人</text>
						<text>{{billInfo.link_man}}</text>
					</view>
					<view class="manInfo_title">
						<text class="manInfo_title1">联系手机</text>
						<text>{{billInfo.link_phone}}</text>
					</view>
					<view class="manInfo_title">
						<text class="manInfo_title1">入住说明</text>
						<text>{{billInfo.enter_time_plan | moment2}}</text>之后入住，
						<text>{{billInfo.leave_time_plan | moment2}}</text>之前退房
					</view>
				</view>
			</view>

			<view class="orderInfo">
				<p style="font-size: 40rpx;font-weight: 600;line-height: 60rpx;">订单信息</p>
				<view class="manInfo_title">
					<text class="manInfo_title1">订单号:</text>
					<text>{{billInfo.bill_code}}</text>
					<text style="margin-left: 20rpx;font-size: 30rpx;color: chocolate;" @click="copy">复制</text>
				</view>
				<view class="manInfo_title">
					<text class="manInfo_title1">订单来源:</text>
					<text>{{billInfo.bill_source_name}}</text>
				</view>
				<view class="manInfo_title">
					<text class="manInfo_title1">下单时间:</text>
					<text>{{billInfo.create_time | moment}}</text>
				</view>
			</view>
		</view>


		<view class="order-invoice">
			<view>订单发票</view>
			<view class="" style="display: flex;align-items: center;" @click="invoiceTo">
				<view>立即开票</view>
				<image src="/static/images/common_icon_jump_gold.png" class="jump-icon"></image>
			</view>
		</view>
		
		<view class="" @click="toAuto" style="width: 550rpx;display: flex;align-items: center;justify-content: center;height: 80rpx;margin: 20rpx auto;border-radius: 15rpx;" :style="{background:themeColor.main_color,color:themeColor.bg_color}" v-if="autoRoomSetting&&billInfo.bill_status==3">
			点击立即自助办理入住
		</view>
		
		<view class="" style="height: 200rpx;">

		</view>


	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				billInfo: {},
				hotelInfo: {},
				billId: '',
				thirdBill:1,
				autoRoomSetting:false
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotelList', 'unit', 'startDate', 'endDate', 'roomInfo', 'linkMan', 'shopSetting',
				'hotelBillDetail'
			]),
			timeFormate() {
				return this.$moment(this.billInfo.enter_time_plan * 1000).format('MM月DD日')
			},
			socreMate() {
				return Number(this.hotelInfo.socre) * 1
			}
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options, wx.getLaunchOptionsSync().query);
			if (options) {
				if (JSON.stringify(options.scene) !== "{}" && decodeURIComponent(options.scene).split('bill_id')
					.length == 2) {
					let obj = options.scene;
					let q = decodeURIComponent(obj).split('&');
					this.billId = q[0].split('=')[1];
				}

			}
			
			this.autoRoomSetting = this.shopSetting.filter(item => {
				return item.sign == 'self_check_in'
			})[0].property.status
			
			uni.showLoading({
				title: '加载中...'
			})
			if (this.billId) {
				// 查询订单
				this.$iBox.http('getRoomBillInfo', {
					bill_id: this.billId
				})({
					method: 'post'
				}).then(res => {
					this.billInfo = res.data
					this.hotelInfo = this.hotelList.filter(item => {
						return item.id == res.data.shop_id
					})[0]
					console.log(this.hotelInfo, 'this.hotelInfo');
					uni.hideLoading()
				})
			} else {
				console.log('cong');
				// 查询订单
				this.$iBox.http('getRoomBillInfo', {
					bill_id: this.hotelBillDetail.id
				})({
					method: 'post'
				}).then(res => {
					this.billInfo = res.data
					this.hotelInfo = this.hotelList.filter(item => {
						return item.id == res.data.shop_id
					})[0]
					console.log(this.hotelInfo, 'this.hotelInfo');
					uni.hideLoading()
				})
			}

		},
		async onShow() {
			await this.$onLaunched;
			this.thirdBill = this.shopSetting.filter(item => {
				return item.sign == 'third_bill_show_price'
			})[0].property.status
		},
		methods: {
			billStatus(e) {
				let a = ''
				if (e == 2) {
					a = '待确认'
				} else if (e == 3) {
					a = '待入住'
				} else if (e == 4) {
					a = '入住中'
				} else if (e == 5) {
					a = '已完成'
				} else if (e == 6) {
					a = '待取消'
				} else if (e == 7) {
					a = '已取消'
				} else if (e == 8) {
					a = '申请退房'
				} else if (e == 9) {
					a = '预定未到'
				} else if (e == 10) {
					a = '走结'
				} else if (e == 11) {
					a = '已完成'
				}
				return a
			},
			cancleOrder() {
				if (!this.billInfo.room_service.cancelable) {
					uni.showToast({
						icon: 'none',
						title: '此订单不支持取消',
						duration: 1000
					})

				} else {
					let time1 = this.$moment(this.billInfo.enter_time_plan*1000).format('yyyy-MM-DD')+ ' '+this.billInfo.room_service.before_end_time
					time1 = this.$moment(time1,'yyyy-MM-DD HH:mm').unix()
					
					
					let a = this.$moment().unix()
					if (a >time1) {
						uni.showToast({
							icon: 'none',
							title: '此订单已超过可取消时间',
							duration: 1000
						})
					} else {
						if (this.billInfo.room_sale_type_sign == "standard") {
							uni.showLoading({
								title: '正在申请取消中...'
							})
							this.$iBox.http('standardApplyCancelRoomBill', {
								bill_id: this.billInfo.id
							})({
								method: 'post'
							}).then(res => {
								uni.showToast({
									icon: 'none',
									title: '申请取消中，请等待酒店确认!',
									duration: 2000
								})
							})
						} else if (this.billInfo.room_sale_type_sign == "hour") {
							uni.showLoading({
								title: '正在申请取消中...'
							})
							this.$iBox.http('hourApplyCancelRoomBill', {
								bill_id: this.billInfo.id
							})({
								method: 'post'
							}).then(res => {
								uni.showToast({
									icon: 'none',
									title: '申请取消中，请等待酒店确认!',
									duration: 2000
								})
							})
						} else if (this.billInfo.room_sale_type_sign == "conference_room") {
							uni.showLoading({
								title: '正在申请取消中...'
							})
							this.$iBox.http('conferenceApplyCancelRoomBill', {
								bill_id: this.billInfo.id
							})({
								method: 'post'
							}).then(res => {
								uni.showToast({
									icon: 'none',
									title: '申请取消中，请等待酒店确认!',
									duration: 2000
								})
							})
						} else {
							uni.showLoading({
								title: '正在申请取消中...'
							})
							this.$iBox.http('longStandardApplyCancelRoomBill', {
								bill_id: this.billInfo.id
							})({
								method: 'post'
							}).then(res => {
								uni.showToast({
									icon: 'none',
									title: '申请取消中，请等待酒店确认!',
									duration: 2000
								})
							})
						}

					}
				}


			},
			rateOrder(e) {
				uni.navigateTo({
					url: '/pages/rating/myrate/myrate?id=' + this.billInfo.id
				})
			},
			goPhone() {
				uni.makePhoneCall({
					phoneNumber: this.hotelInfo.link_phone
				})
			},
			modeService() {
				uni.switchTab({
					url: '/pages/myRoom/myRoom'
				})
			},
			invoiceTo() {
				uni.navigateTo({
					url: '/packageA/invoiceBills/invoiceBills'
				})
			},
			toAuto(){
				uni.switchTab({
					url:'/pages/myRoom/myRoom'
				})
			},
			copy() {
				console.log();
				uni.setClipboardData({
					data: this.billInfo.bill_code,
					success: function(res) {
						console.log(res,'ds');
						uni.showToast({
							icon: 'success',
							title: '内容复制成功！'
						})
					}
				});
			},
			showFood(){
				uni.navigateTo({
					url:'/packageA/breakfastCard/breakfastCard?id=' + this.billInfo.id
				})
			}
		}

	}
</script>

<style lang="scss" scoped>
	.billBOX {
		background-color: #fbfafa;
		min-height: 100vh;

		.title_box {
			height: 480rpx;
			width: 100%;
			padding: 30rpx;

			.content {
				width: 700rpx;
				min-height: 240rpx;
				margin: 30rpx auto;
				background-color: #ffffff;
				border-radius: 10rpx;
				padding: 20rpx;
				font-weight: 600;
				font-size: 32rpx;

				.bottomBox {
					display: flex;
					align-items: center;
					margin-top: 16rpx;
					justify-content: flex-end;

					.btnBoss {
						margin-top: 30rpx;
						margin-left: 10rpx;
						width: fit-content;
						padding: 20rpx;
						height: 60rpx;
						border-radius: 10rpx;
						font-weight: 600;
						// border: 1px solid #b2b2b2;
						display: flex;
						align-items: center;
						justify-content: center;
						// color: #b6b6b6;
					}
				}
			}
		}

		.content_box {
			margin-top: -30rpx;
			border-top-left-radius: 30rpx;
			border-top-right-radius: 30rpx;
			background: #fbfafa;
			// height: 400rpx;
			width: 100%;
			border: 1px solid transparent;

			.price_box {
				height: 120rpx;
				width: 94%;
				margin: 10rpx auto;
				background-color: #ffffff;
				border-radius: 10rpx;
				position: relative;
				display: flex;
				align-items: center;
				padding: 20rpx;

				.price_title {
					display: flex;
					width: 100%;
					align-items: center;
					justify-content: space-between;
				}
			}


			.hotelBox {
				margin: 20rpx auto;
				border-radius: 30rpx;
				background: #ffffff;
				height: 200rpx;
				display: flex;
				width: 94%;
				align-items: center;

				border: 1px solid transparent;
				padding: 20rpx;

				.hotelInfo {
					display: flex;
					flex-direction: column;
					padding: 20rpx;
					width: 600rpx;

					.content_rate {
						display: flex;
						align-items: center;
						position: relative;
						margin-top: 4rpx;

						&_num {
							font-size: 34rpx;
							font-weight: 600;
							z-index: 3;
						}

						&_bg {
							position: absolute;
							width: 80rpx;
							height: 32rpx;
							border-radius: 20rpx;
							z-index: 1;
							bottom: -2rpx;
						}
					}

					.content_address {
						font-size: 28rpx;
						line-height: 46rpx;
						word-wrap: break-all;
					}
				}
			}

			// 房间详情
			.roomInfo {
				margin: 20rpx auto;
				border-radius: 30rpx;
				background: #ffffff;
				min-height: 200rpx;
				width: 94%;
				border: 1px solid transparent;
				padding: 20rpx;

				.nameBox {
					font-size: 40rpx;
					font-weight: 500;
				}

				.elseBox {
					font-size: 24rpx;
					color: #333;
				}

				.manInfo {
					margin-top: 20rpx;
					line-height: 60rpx;

					.manInfo_title {
						.manInfo_title1 {
							font-size: 28rpx;
							font-weight: 600;
							width: 190rpx;
							padding-right: 30rpx;
						}
					}
				}
			}

			// 订单信息
			.orderInfo {
				margin: 20rpx auto;
				border-radius: 30rpx;
				background: #ffffff;
				min-height: 200rpx;
				width: 94%;
				border: 1px solid transparent;
				padding: 20rpx;

				.manInfo_title {
					margin-top: 20rpx;

					.manInfo_title1 {
						font-size: 28rpx;
						font-weight: 600;
						width: 190rpx;
						padding-right: 30rpx;
					}
				}
			}
		}
	}

	.order-invoice {
		padding: 30rpx 40rpx;
		border-radius: 30rpx;
		box-shadow: 0 0 10rpx 0 rgba($color: #333, $alpha: 0.1);
		margin-bottom: 60rpx;
		background-color: #ffffff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		font-size: 30rpx;

	}

	.jump-icon {
		width: 24rpx;
		height: 48rpx;
		margin-left: 20rpx;
	}
</style>