/**
 * 初始化动画 Mixin
 * 为页面提供初始化动画的状态管理
 */
import globalAnimationManager from '@/utils/GlobalAnimationManager'

export default {
	data() {
		return {
			initAnimationVisible: false,
			initProgress: 0,
			initMessage: ''
		}
	},
	
	onLoad() {
		// 页面加载时恢复动画状态
		this.restoreAnimationState()
		// 注册状态变化监听
		this.registerAnimationListener()
	},
	
	onUnload() {
		// 页面卸载时移除监听
		this.unregisterAnimationListener()
	},
	
	methods: {
		// 恢复动画状态
		restoreAnimationState() {
			const state = globalAnimationManager.getState()
			this.initAnimationVisible = state.visible
			this.initProgress = state.progress
			this.initMessage = state.message
		},
		
		// 注册动画状态监听
		registerAnimationListener() {
			this.animationStateCallback = (type, state) => {
				this.initAnimationVisible = state.visible
				this.initProgress = state.progress
				this.initMessage = state.message
			}
			
			globalAnimationManager.onStateChange(this.animationStateCallback)
		},
		
		// 移除动画状态监听
		unregisterAnimationListener() {
			if (this.animationStateCallback) {
				globalAnimationManager.offStateChange(this.animationStateCallback)
				this.animationStateCallback = null
			}
		},
		
		// 显示初始化动画
		showInitAnimation() {
			return globalAnimationManager.show()
		},
		
		// 更新动画进度
		updateInitProgress(progress, message) {
			return globalAnimationManager.updateProgress(progress, message)
		},
		
		// 隐藏初始化动画
		hideInitAnimation() {
			return globalAnimationManager.hide()
		},
		
		// 获取动画状态
		getInitAnimationState() {
			return globalAnimationManager.getState()
		}
	}
}
