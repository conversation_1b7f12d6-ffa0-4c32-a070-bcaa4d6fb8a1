<template>
	<view>
		<view class="" style="height: 400rpx;width: 100vw;display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 50rpx;" @click="scanCode">
			<view class="icon-saoma" style="font-size: 300rpx;">
				
			</view>
			<p style="font-size: 44rpx;font-weight: 600;">点击扫码核销</p>
		</view>
		<!-- <view class="" v-if="foodCard" style="margin-top: 40rpx;padding: 30rpx;background: #ffffff;font-size: 34rpx;width: 700rpx;margin: 60rpx auto;display: flex;flex-direction: column;align-items: center;justify-content: space-around;min-height: 500rpx;">
			<p style="font-size: 44rpx;font-weight: 600;">扫描早餐券信息</p>
			<view class="item">
				<text>名称：</text>
				<text>{{foodCard.name}}</text>
			</view>
			<view class="item">
				<text>内容：</text>
				<text>{{foodCard.content}}</text>
			</view>
			<view class="item">
				<text>早餐券价值：</text>
				<text>{{foodCard.amount}}元</text>
			</view>
			<view class="item">
				<text>有效期：</text>
				<text>{{foodCard.date | moment1}}</text>
			</view>
			<view class="" style="width: 100vw;" @click="sure">
				<button style="background-color: cornflowerblue;color: #ffffff;width: 500rpx;">确定核销</button>
			</view>
		</view> -->
	</view>
</template>

<script>
	import { mapState, mapGetters, mapActions } from 'vuex';
	export default {
		data() {
			return {
				// foodCard:null,
				// card_id:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['hotel'])
		},
		methods: {
			scanCode(){
				wx.scanCode({
				  onlyFromCamera: true,
				  success :(res)=> {
				    console.log(res)
					let a = res.result
					// let a = "record_id=6&common_code=213213213&bill_id=3"
					let recode_id = a.split('=')[1]
					uni.showLoading({
						title:'加载中...'
					})
					this.$iBox
						.http('verificationBreakfastCoupon', {id:recode_id})({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title:'提示',
								content:'核销成功',
								showCancel:false,
								success() {
									
								}
							})
							uni.hideLoading()
						})
						.catch(function(error) {
							uni.hideLoading()
							console.log('33434', error);
						});
					
					
				  }
				})
			},
			// sure(){
			// 	uni.showLoading({
			// 		title:'加载中...'
			// 	})
			// 	this.$iBox
			// 		.http('checkBreakfastCoupon', {
			// 			id:this.foodCard.coupon_id,
			// 			bill_id:this.foodCard.bill_id
			// 			})({
			// 			method: 'post'
			// 		})
			// 		.then(res => {
			// 			uni.showModal({
			// 				title:'提示',
			// 				content:'核销成功',
			// 				showCancel:false,
			// 				success() {
								
			// 				}
			// 			})
			// 			uni.hideLoading()
			// 		})
			// 		.catch(function(error) {
			// 			console.log('33434', error);
			// 		});
			// }
		}
	}
</script>
	
<style>
	page {
		background: #f3f4f6;
	}
</style>
<style scoped lang="scss">

</style>
