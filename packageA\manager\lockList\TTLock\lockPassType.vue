<template>
	<view class="box">
		<m-tabs :list="list1" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="tab_click"
			:activeIndex="current" :config="{color:themeColor.text_main_color,
						  fontSize:30,
						  activeColor:themeColor.com_color1,
						  underLineColor:themeColor.com_color1,
						  underLineWidth:80,
						  underLineHeight:5}">
		</m-tabs>
		<view class="passBox" v-for="item in passType" @click="getPass(item)" v-if="current==0?item.keyboardPwdType==1:(current==1?item.keyboardPwdType==2:(current==2?item.keyboardPwdType==3:[5,6,7,8,9,10,11,12,13,14].includes(item.keyboardPwdType)))">
			<p style="font-size: 40rpx;font-weight: 600;">管理{{item.name}}密码</p>
			<p style="font-size: 24rpx;color: #909399;">{{item.remark}}</p>
			<p style="position: absolute;right: 30rpx;top: 10rpx;font-size: 40rpx;font-weight: 600;color: brown;">
				{{item.name[0]+item.name[1]}}</p>
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				list1: [],
				passType: [],
				current:0,
				
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		onLoad() { 
			this.list1 = []
			let a = {
						id: 0,
						name: '单次密码'
					}
			let b = {
						id: 1,
						name: '永久密码'
					}
					
			let c = {
						id: 2,
						name: '限期密码'
					}
					
			let d = {
						id: 3,
						name: '循环密码'
					}
			if(this.roleType('view_temp_password')){
				this.list1.push(a)
			}
			
			if(this.roleType('view_forever_password')){
				this.list1.push(b)
				
			}
			
			if(this.roleType('view_time_password')){
				this.list1.push(c)
				this.list1.push(d)
			}
			
			this.$iBox.http('getPasswordType', {
			})({
				method: 'post'
			}).then(res => {
				this.passType = res.data
			})
			
		},
		methods: {
			...mapActions('room',['getType']),
			getPass(e) {
				this.getType(e)
				uni.navigateTo({
					url: '/packageA/manager/lockList/TTLock/lockPassword'
				})
			},
			tab_click(e){
				this.current = e
			},
			roleType(e){
				let role= this.roles_list.filter(item => {
					return item.permission == e
				})
				
				if(role.length >0){
					return true
				}else{
					return false
				}
			},
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		.passBox {
			width: 90%;
			height: 200rpx;
			margin: 30rpx auto;
			border-radius: 20rpx;
			background: #FFFFFF;
			padding: 30rpx;
			box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
			position: relative;
		}


	}
</style>
