<template>
	<view style="height: 100vh;background-color: #fff;">
		<p style="padding: 20rpx 0 0 20rpx;font-size: 25rpx;">分销人ID：{{du}}</p>
		<view class="rechargeBox"
			:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.com_color1+'99,'+themeColor.com_color2+'99);color:'+themeColor.bg_color}">
			<p class="money" :style="{color:themeColor.bg_color}">{{userInfo.balance?userInfo.balance:0}}</p>
			<p>我的余额（元）</p>
			<view class="moneyList" @click="toList">
				<view class="icon-shouye" style="font-size: 34rpx;"></view>
				<text>余额记录</text>
			</view>
		</view>
		<p class="title">充值金额</p>
		<view class="listBox">
			<view class="list" v-for="item in rechargeList" @click="chooseItem(item)">
				<view class="listContent"
					:style="current==item.id?'border:1px solid '+themeColor.main_color+';background:'+themeColor.main_color+'33;color:'+themeColor.main_color:'border:1px solid #7b7b7b;background:#fff;color:#333'">
					<text style="font-size: 42rpx;font-weight: 600;">{{item.recharge_money}}元</text>
					<text style="font-size: 24rpx;" v-if="item.give_money>0">赠送{{item.give_money}}元</text>
					<text style="font-size: 24rpx;" v-if="item.give_point>0">赠送{{item.give_point}}积分</text>
				</view>
			</view>
		</view>

		<view class="btn_pay">
			<view class="btn" :style="'background:'+themeColor.main_color+';color:'+themeColor.bg_color" @click="sure">
				<text>确认充值</text>
			</view>
		</view>
		
		<m-popup :show="activePop" mode="center" :closeable="true" @closePop="closeActive">
			<view class="member-gift-container">
				<!-- 顶部庆祝区域 -->
				<view class="celebration-header">
					<view class="gift-icon">🎉</view>
					<text class="congratulations-text">恭喜您已获得以下会员权益！</text>
					<view class="sparkle-effects">
						<text class="sparkle">✨</text>
						<text class="sparkle">⭐</text>
						<text class="sparkle">💎</text>
					</view>
				</view>
		
				<!-- 会员卡区域 -->
				<view class="member-card">
					<view class="card-background">
						<view class="card-pattern"></view>
						<view class="card-shine"></view>
					</view>
		
					<view class="card-content">
						<!-- 会员等级 -->
						<view class="member-level-section">
							<text class="level-label">会员等级</text>
							<text class="level-value">{{memberGiftInfo?memberGiftInfo.grade_name: '**'}}</text>
						</view>
		
						<!-- 有效期 -->
						<view class="validity-section">
							<text class="validity-label">有效期至</text>
							<text class="validity-value">{{memberGiftInfo?memberGiftInfo.usable_month+'个月': '**个月'}}</text>
						</view>
						
						<!-- 价值 -->
						<view class="validity-section">
							<text class="validity-label">价值</text>
							<text class="validity-value">{{memberGiftInfo?memberGiftInfo.amount+'元': '*元'}}</text>
						</view>
					</view>
				</view>
		
				<!-- 权益说明 -->
				<view class="benefits-section">
					<text class="benefits-title">专属权益</text>
					<view class="benefits-list">
						<view class="benefit-item">
							<text class="benefit-icon">🎯</text>
							<text class="benefit-text">专属折扣优惠</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">💳</text>
							<text class="benefit-text">免押金入住</text>
						</view>
						<view class="benefit-item">
							<text class="benefit-icon">🎁</text>
							<text class="benefit-text">积分兑换礼品</text>
						</view>
					</view>
				</view>
			</view>
		</m-popup>

		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess" @closeToLogin="toCloseLogin"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				rechargeList: [],
				current: 0,
				payItem: {},
				if_login: false,
				hackReset: true,
				du: '',
				activePop: false,
				// 会员赠送弹窗相关
				memberGiftInfo: null
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'setting'])
		},
		watch: {},
		async onLoad(options) {
			await this.$onLaunched;

		},
		async onShow() {
			await this.$onLaunched;

			// 小程序二维码带参数,
			let du = ''
			let scene = wx.getEnterOptionsSync()
			if (scene.query.scene) {
				let query = decodeURIComponent(scene.query.scene)
				console.log(query, 'q');
				//解析参数
				if (query.includes("du")) {
					du = this.$iBox.linkFormat(query, "du")
					this.du = this.$iBox.linkFormat(query, "du")
					// 更新分销人
					if (du) {
						this.$iBox.http('updateDistributionUserId', {
							distribution_user_id: du
						})({
							method: 'post'
						}).then(res => {
							
							// 查询活动
							this.loginCheck()
						})

					}
				}

			}

			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			this.$iBox.http('getMemberRechargeSettingList', {})({
				method: 'post'
			}).then(res => {
				this.rechargeList = res.data
				this.current = res.data[0].id
				uni.hideLoading()
			})
		},
		methods: {
			closeActive(){
				this.activePop = false
			},
			toCloseLogin() {
				uni.showModal({
					title: '提示！',
					content: '为了获得更完整的会员服务请您授权您的手机号！',
					showCancel: false,
					success: res => {
						this.hackReset = false
						this.$nextTick(() => {
							this.hackReset = true
						})
						this.if_login = true
					}
				})
			},
			loginCheck(){
				let set = this.setting.filter(item => {
					return item.sign == 'auto_register_member'
				})
				if (set[0].property) {
					let a = set[0].property.value
					if (a == 2) {
						if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
							.upgrade_growth_value > -1) {
							this.if_login = false
							this.$iBox.http('getActive', {
								shop_id: this.hotel.id
							})({
								method: 'post'
							}).then(res => {
								res.data.forEach(item => {
									if (item.member_setting_id) {
										// 如果有会员赠送活动，直接弹窗
										this.activePop = true;
										this.memberGiftInfo = item.member_setting;
									}
								})
							})
						} else {
							this.if_login = true
						}
				
					} else if (a == 1) {
						// this.pop = true
						if (this.userInfo.phone) {
							this.if_login = false
							this.$iBox.http('getActive', {
								shop_id: this.hotel.id
							})({
								method: 'post'
							}).then(res => {
								res.data.forEach(item => {
									if (item.member_setting_id) {
										// 如果有会员赠送活动，直接弹窗
										this.activePop = true;
										this.memberGiftInfo = item.member_setting;
									}
								})
							})
						} else {
							this.if_login = true
						}
					}
				}
			},
			loginSucess() {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					this.loginCheck()
				})

			},
			chooseItem(e) {
				this.current = e.id
				this.payItem = e
			},
			sure() {
				//是否是会员
				if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
					1) {
					this.if_login = false
					this.$iBox.http('memberRecharge', {
						setting_id: this.current
					})({
						method: 'post'
					}).then(res => {
						if (res.data.bizCode == '0000') {
							// 随行付
							uni.requestPayment({
								provider: 'wxpay',
								AppId: res.data.payAppId,
								timeStamp: res.data.payTimeStamp,
								nonceStr: res.data.paynonceStr,
								package: res.data.payPackage,
								signType: res.data.paySignType,
								paySign: res.data.paySign,
								success: (res) => {
									uni.navigateTo({
										url: '/pages/resultsPage/resultsRecharge'
									})

								},
								fail: function(err) {
									uni.hideLoading()
								}
							});


						} else {
							// 微信支付
							uni.requestPayment({
								provider: 'wxpay',
								timeStamp: res.data.timeStamp,
								nonceStr: res.data.nonceStr,
								package: res.data.package,
								signType: 'MD5',
								paySign: res.data.paySign,
								success: (res) => {
									uni.navigateTo({
										url: '/pages/resultsPage/resultsRecharge'
									})

								},
								fail: function(err) {
									uni.hideLoading()
								}
							});
						}


					})

				} else {
					this.if_login = true
					this.hackReset = false
					this.$nextTick(() => {
						this.hackReset = true
					})
				}

			},
			toList() {
				uni.navigateTo({
					url: '/pages/recharge/rechargeList'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #fff;
	}

	.rechargeBox {
		margin: 40rpx auto;
		width: 690rpx;
		height: 280rpx;
		border-radius: 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: relative;
		box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;

		.money {
			font-size: 76rpx;
		}

		.moneyList {
			display: flex;
			align-items: center;
			position: absolute;
			right: 20rpx;
			top: 20rpx;
		}
	}

	.title {
		padding: 30rpx;
		font-weight: 500;
		font-size: 36rpx;
	}

	.listBox {
		padding: 0 30rpx;
		display: flex;
		flex-wrap: wrap;

		.list {
			width: 230rpx;
			height: 180rpx;
			display: flex;
			align-items: center;
			justify-content: center;

			.listContent {
				height: 160rpx;
				width: 210rpx;
				border-radius: 14rpx;
				display: flex;
				padding: 10rpx;
				flex-direction: column;
				align-items: center;
				justify-content: center;
			}

		}
	}

	.btn_pay {
		width: 100%;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: 30rpx;

		.btn {
			width: 80%;
			height: 100%;
			border-radius: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
	
	/* 使用CSS关键帧@keyframes定义动画效果 */
	@keyframes shake {
	
		10%,
		90% {
			transform: translate3d(-1px, 0, 0);
		}
	
		20%,
		80% {
			transform: translate3d(2px, 0, 0);
		}
	
		30%,
		50%,
		70% {
			transform: translate3d(-4px, 0, 0);
		}
	
		40%,
		60% {
			transform: translate3d(4px, 0, 0);
		}
	}
	
	/* 优化后的会员赠送弹窗样式 */
	.member-gift-container {
		width: 660rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 20rpx;
		overflow: hidden;
		position: relative;
	}
	
	.celebration-header {
		text-align: center;
		padding: 40rpx 30rpx 30rpx;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
		position: relative;
	}
	
	.celebration-header .gift-icon {
		font-size: 60rpx;
		margin-bottom: 15rpx;
		animation: bounce 1.5s infinite;
	}
	
	.celebration-header .congratulations-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
	}
	
	.sparkle-effects {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		pointer-events: none;
	}
	
	.sparkle {
		position: absolute;
		font-size: 24rpx;
		animation: sparkle 2s infinite;
	}
	
	.sparkle:nth-child(1) {
		top: 20rpx;
		left: 50rpx;
		animation-delay: 0s;
	}
	
	.sparkle:nth-child(2) {
		top: 30rpx;
		right: 60rpx;
		animation-delay: 0.5s;
	}
	
	.sparkle:nth-child(3) {
		bottom: 20rpx;
		left: 80rpx;
		animation-delay: 1s;
	}
	
	@keyframes sparkle {
		0%, 100% {
			opacity: 0;
			transform: scale(0.5);
		}
		50% {
			opacity: 1;
			transform: scale(1.2);
		}
	}
	
	/* 会员卡样式 */
	.member-card {
		margin: 30rpx;
		height: 200rpx;
		border-radius: 20rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.2);
	}
	
	.card-background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
	}
	
	.card-pattern {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-image:
			radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx),
			radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx);
		background-size: 40rpx 40rpx;
	}
	
	.card-shine {
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.3) 50%, transparent 70%);
		animation: shine 3s infinite;
	}
	
	@keyframes shine {
		0% {
			transform: translateX(-100%) translateY(-100%) rotate(45deg);
		}
		100% {
			transform: translateX(100%) translateY(100%) rotate(45deg);
		}
	}
	
	.card-content {
		position: relative;
		z-index: 2;
		padding: 25rpx 30rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}
	
	.member-level-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}
	
	.level-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.9);
	}
	
	.level-value {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
	}
	
	.validity-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-top: 20rpx;
	}
	
	.validity-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.9);
	}
	
	.validity-value {
		font-size: 28rpx;
		font-weight: 500;
		color: #fff;
	}
	
	.privileges-badge {
		position: absolute;
		top: 25rpx;
		right: 30rpx;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10rpx);
		border-radius: 20rpx;
		padding: 8rpx 16rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
	}
	
	.badge-text {
		font-size: 20rpx;
		color: #fff;
		font-weight: bold;
	}
	
	/* 权益说明区域 */
	.benefits-section {
		padding: 30rpx;
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(20rpx);
	}
	
	.benefits-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 20rpx;
		text-align: center;
	}
	
	.benefits-list {
		display: flex;
		flex-direction: column;
		gap: 15rpx;
	}
	
	.benefit-item {
		display: flex;
		align-items: center;
		padding: 15rpx 20rpx;
		background: linear-gradient(135deg, #f8f9ff 0%, #e8f2ff 100%);
		border-radius: 15rpx;
		border: 1rpx solid #e1e8ff;
	}
	
	.benefit-icon {
		font-size: 32rpx;
		margin-right: 15rpx;
	}
	
	.benefit-text {
		font-size: 26rpx;
		color: #555;
		font-weight: 500;
	}
	
	/* 立即体验按钮 */
	.action-button {
		margin: 30rpx;
		background: linear-gradient(135deg, #ff6b6b 0%, #ff1744 100%);
		border-radius: 50rpx;
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 10rpx 30rpx rgba(255, 107, 107, 0.4);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
	}
	
	.action-button::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
		transition: left 0.5s;
	}
	
	.action-button:active::before {
		left: 100%;
	}
	
	.action-button:active {
		transform: scale(0.95);
		box-shadow: 0 5rpx 15rpx rgba(255, 107, 107, 0.6);
	}
	
	.button-text {
		font-size: 32rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
		position: relative;
		z-index: 1;
	}
</style>