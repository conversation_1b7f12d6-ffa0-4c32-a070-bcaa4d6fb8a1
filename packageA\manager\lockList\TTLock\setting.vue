<template>
	<view class="box">
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">锁ID:</label>
			<view class="" style="width: 450rpx;">
				<text>{{lockInfo.lockId}}</text>
			</view>
		</view>

		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">锁别名:</label>
			<view class="" style="width: 450rpx;">
				<text>{{lockInfo.lockAlias}}</text>
			</view>
		</view>

		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">管理员钥匙:</label>
			<view class="" style="width: 450rpx;display: flex;align-items: center;justify-content: space-between;">
				<text>{{adminPwd}}</text>
				<text style="color: blueviolet;" @click="editPwd">修改</text>
			</view>
		</view>

		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">锁电量:</label>
			<view class="" style="width: 450rpx;">
				<text>{{lockInfo.electricQuantity}}</text>
			</view>
		</view>

		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">产品型号:</label>
			<view class="" style="width: 450rpx;">
				<text>{{lockInfo.modelNum}}</text>
			</view>
		</view>
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">硬件版本号:</label>
			<view class="" style="width: 500rpx;">
				<text>{{lockInfo.hardwareRevision}}</text>
			</view>
		</view>
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">固件版本号:</label>
			<view class="" style="width: 450rpx;">
				<text>{{lockInfo.firmwareRevision}}</text>
			</view>
		</view>
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">锁声音开关:</label>
			<view class="" style="width: 450rpx;display: flex;align-items: center;">
				<text>{{lockInfo.lockSound==0?'未知':(lockInfo.lockSound==1?'开启':'关闭')}}</text>
			</view>

		</view>
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">反锁开关:</label>
			<view class="" style="width: 450rpx;display: flex;align-items: center;">
				<text>{{lockInfo.privacyLock==0?'未知':(lockInfo.privacyLock==1?'开启':'关闭')}}</text>
			</view>
		</view>
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">防撬开关:</label>
			<view class="" style="width: 450rpx;display: flex;align-items: center;">
				<text>{{lockInfo.tamperAlert==0?'未知':(lockInfo.tamperAlert==1?'开启':'关闭')}}</text>
			</view>
		</view>
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">重置按键开关:</label>
			<view class="" style="width: 450rpx;display: flex;align-items: center;">
				<picker @change="bindPickerChange1" :value="resetButton" :range="resetButtonList">
					<view class=""
						style="width: 450rpx;display: flex;align-items: center;justify-content: space-between;">
						<view class="">{{resetButtonList[resetButton]}}</view>

						<text style="color: blueviolet;">设置</text>
					</view>

				</picker>
			</view>
		</view>
		<!-- 	<view class="nameBox">
			<label style="width: 200rpx;color:#909399">开门方向:</label>
			<view class="" style="width: 500rpx;display: flex;align-items: center;">
				<picker @change="bindPickerChange4" :value="openDirection" :range="openDirectionList" >
					<view class="" style="width: 500rpx;display: flex;align-items: center;justify-content: space-between;">
						<view class="">{{openDirectionList[openDirection]}}</view>
						
						<text style="color: blueviolet;">设置</text>
					</view>
					
				</picker>
			</view>
		</view> -->
		<!-- <view class="nameBox">
			<label style="width: 200rpx;color:#909399">常开模式:</label>
			<view class="" style="width: 500rpx;">
				<input type="text" placeholder="真实姓名(非必填)" v-model="name" />
			</view>
		</view>
		<view class="nameBox">
			<label style="width: 200rpx;color:#909399">常开模式自动开锁开关:</label>
			<view class="" style="width: 500rpx;">
				<input type="text" placeholder="真实姓名(非必填)" v-model="name" />
			</view>
		</view> -->
		<view class="nameBox">
			<label style="width: 250rpx;color:#909399">锁初始化时间:</label>
			<view class="" style="width: 450rpx;">
				<text>{{lockDate | moment1}}</text>
			</view>
		</view>
		<view class="nameBox">
			<label style="width: 250rpx;color:#909399">固件升级:</label>
			<view class="" style="width: 450rpx;">
				<text style="color: blueviolet;" @click="upLevel">升级</text>
			</view>
		</view>

		<!-- 修改密码弹窗 -->
		<m-popup :show="pop" @closePop="closePop" mode="center">
			<view class="ready_time" style="height: 760rpx;">
				<p style="font-weight: 600;font-size: 44rpx;padding: 30rpx 0;">修改密码</p>

				<view class="" style="display: flex;align-items: center;">
					<text style="padding-right: 14rpx;">新密码:</text>
					<input type="number" v-model="adminPwd" maxlength="9" placeholder="请输入6-9位密码"
						style="height: 80rpx;width: 500rpx;border-radius: 14rpx;border: 1px solid #525dff;padding: 0 10rpx;">
				</view>
				<button type="primary" @click="toModifyPasscode">修改密码</button>
			</view>
		</m-popup>
	</view>
</template>

<script>
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				lockInfo: null,
				resetButton: 0, //重置
				resetButtonList: ['未知', '开启', '关闭'], //重置,
				lockDate: '',
				adminPwd: '',
				pop: false,
				lockSettingInfo: null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		watch: {
			// lockInfo: {
			// 	handler(newData, oldData) {
			// 		this.lockSound = this.lockInfo.lockSound
			// 	},
			// 	immediate: true
			// }
		},
		onLoad() {
			this.getLockInfo()
			this.$iBox.http('getLockSetting', {
				page: 1,
				limit: 100
			})({
				method: 'post'
			}).then(resSetting => {
				this.lockSettingInfo = resSetting.data
			})

		},
		methods: {
			getLockInfo() {
				uni.showLoading({
					title: 'loading...'
				})
				this.$iBox.http('getLockDetailConf', {
					id: this.lockDetail.id
				})({
					method: 'post'
				}).then(res => {
					this.lockInfo = res.data
					this.lockDate = this.lockInfo.date / 1000
					this.adminPwd = this.lockInfo.noKeyPwd
					this.resetButton = this.lockInfo.resetButton
					uni.hideLoading()
				})
			},
			// 获取管理员密码
			// toGetAdminPasscode(event) {
			// 	let deviceId = ''
			// 	uni.showLoading({
			// 		state: `正在获取管理员密码`
			// 	})
			// 	plugin.getAdminPasscode(this.lockDetail.lock_data, res => {
			// 		if (res.errorCode === 10003) {
			// 			console.log("监控到设备连接已断开", res)
			// 		}
			// 	}, deviceId).then(res => {
			// 		uni.hideLoading({});
			// 		if (!!res.deviceId) deviceId = res.deviceId;
			// 		console.log(res,'guanliyu')
			// 		if (res.errorCode === 0) {
			// 			uni.showToast({
			// 				icon: 'none',
			// 				title: `获取管理员密码成功--原密码：${res.passcode}--`
			// 			})
			// 			this.adminPwd = res.passcode
			// 		} else {
			// 			uni.showToast({
			// 				icon: 'none',
			// 				title:  "获取管理员密码失败:" + res.errorMsg
			// 			})
			// 		}
			// 	}, deviceId)
			// },
			bindPickerChange1: function(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.resetButton = e.detail.value
				this.toSetLockConfig()
			},

			closePop() {
				this.pop = false
			},
			editPwd() {
				this.pop = true
			},
			// 获取锁开关配置(以重置功能为例)
			toSetLockConfig(event) {
				let deviceId = ''
				const lockConfigType = plugin.LockConfigType.RESET_BUTTON;
				const switchOn = this.resetButton == 1 ? true : false;
				uni.showLoading({
					title: `正在设置锁开关配置`
				})

				setLockConfig({
					configType,
					switchOn,
					lockData: ekeyInfo.lockData
				}).then(res => {
					wx.hideLoading();
					if (res.errorCode == 0) {
						let type = 0;
						switch (configType) {
							case 1:
								type = 3;
								break;
							case 2:
								type = 4;
								break;
							case 4:
								type = 2;
								break;
							case 16:
								type = 7;
								break;
							default:
								wx.hideLoading();
								return;
						};

						this.$iBox.http('updateLockeSetting', {
							id: this.lockDetail.id,
							type: 4,
							value: this.resetButton
						})({
							method: 'post'
						}).then(res => {

						})
					} else {
						wx.hideLoading();
						this.setData({
							state: `修改智能锁设置项失败：${res.errorMsg}`
						});
					}
				})

				// plugin.setLockConfig(lockConfigType, switchOn, this.lockDetail.lock_data, res => {
				// 	if (res.errorCode === 10003) {
				// 		console.log("监控到设备连接已断开", res)
				// 	}
				// }, deviceId).then(res => {
				// 	uni.hideLoading({});
				// 	if (!!res.deviceId) deviceId = res.deviceId;
				// 	console.log(res)
				// 	if (res.errorCode === 0) {
				// 		uni.showToast({
				// 			icon: 'none',
				// 			title: `设置锁开关配置成功--${lockConfigType}--${switchOn}--`
				// 		})

				// 		this.$iBox.http('updateLockeSetting', {
				// 			id: this.lockDetail.id,
				// 			type: 4,
				// 			value: this.resetButton
				// 		})({
				// 			method: 'post'
				// 		}).then(res => {

				// 		})

				// 	} else {
				// 		uni.showToast({
				// 			icon: 'none',
				// 			title: "请检测锁是否连接，设置锁开关配置失败:" + res.errorMsg
				// 		})
				// 	}
				// })
			},

			toModifyPasscode(event) {
				let deviceId = ''
				// const newPasscode = event.target.dataset.passcode;
				uni.showLoading({
					state: `正在获取管理员密码`
				})

				// 设置管理员密码
				modifyAdminPasscode({
					newPasscode: this.adminPwd,
					lockData: this.lockDetail.lock_data
				}).then(res => {
					wx.hideLoading();
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'none',
							title: `设置管理员密码成功--${JSON.stringify(res)}--`
						})
						this.$iBox.http('changeAdminKeyboardPwd', {
							id: this.lockDetail.id,
							password: this.adminPwd
						})({
							method: 'post'
						}).then(res => {
							this.getLockInfo()
						})
						this.pop = false
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'none',
							title: "请检测锁是否连接，设置管理员密码失败:" + res.errorMsg
						})
					}
				})

				// plugin.modifyAdminPasscode(this.adminPwd, this.lockDetail.lock_data, res => {
				// 	if (res.errorCode === 10003) {
				// 		console.log("监控到设备连接已断开", res)
				// 	}
				// }, deviceId).then(res => {
				// 	wx.hideLoading({});
				// 	if (!!res.deviceId) deviceId = res.deviceId;
				// 	console.log(res)
				// 	if (res.errorCode === 0) {
				// 		uni.showToast({
				// 			icon: 'none',
				// 			title: `设置管理员密码成功--${JSON.stringify(res)}--`
				// 		})
				// 		this.$iBox.http('changeAdminKeyboardPwd', {
				// 			id: this.lockDetail.id,
				// 			password: this.adminPwd
				// 		})({
				// 			method: 'post'
				// 		}).then(res => {
				// 			this.getLockInfo()
				// 		})
				// 		this.pop = false
				// 	} else {
				// 		uni.showToast({
				// 			icon: 'none',
				// 			title: "请检测锁是否连接，设置管理员密码失败:" + res.errorMsg
				// 		})
				// 	}
				// })
			},
			upLevel() {
				uni.showLoading({
					title: '固件正升级'
				})
				let deviceId = ''
				let packageInfo = {
					clientId: this.lockSettingInfo.client_id,
					accessToken: this.lockSettingInfo.access_token,
					lockId: this.lockInfo.lockId
				}

				plugin.enterDfuMode(packageInfo, this.lockDetail.lock_data, res => {
					if (res.errorCode === 0) {
						uni.showLoading({
							title: `${res.description}${res.progress?res.progress:''}`
						})
						if (res.description.includes('升级完成')) {
							uni.hideLoading()
						}
					}

					if (res.errorCode === 10003) {
						console.log("监控到设备连接已断开", res)
					}
				}, deviceId).then(res => {
					console.log(res, '升级');

					if (!!res.deviceId) deviceId = res.deviceId;
					if (res.errorCode === 0) {
						uni.hideLoading()
					} else {
						uni.showToast({
							icon: 'error',
							title: `${errorMsg}`
						})
					}
				})

			}

		}
	}
</script>

<style lang="scss" scoped>
	.box {
		width: 100vw;
		padding: 30rpx;
		background-color: #fff;

		.nameBox {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			border-bottom: 1px solid #e4e7ed;

			// justify-content: space-between;
		}

		.ready_time {
			height: 600rpx;
			width: 730rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
		}
	}
</style>