# 预定转入住数据同步问题修复

## 🐛 **问题描述**

在预定转入住功能中，当用户添加或删除入住人后再次点击确定，系统使用的仍然是旧的入住人数据，没有实时更新。

## 🔍 **问题分析**

### 数据流程
1. **数据源**: `connectBill` - 存储所有可选房间的完整信息
2. **选择数据**: `chooseConnBill` - 存储用户选择的房间信息
3. **操作函数**: `addUserMsg` 和 `reduceUserMsg` - 添加/删除入住人
4. **提交函数**: `sureCheckIn` - 使用 `chooseConnBill` 数据提交

### 问题根源
```javascript
// 数据流程问题：
connectBill (原始数据) 
    ↓ (checkboxBookToIn 选择时复制)
chooseConnBill (选择的数据)
    ↓ (sureCheckIn 使用)
提交到服务器

// 问题：addUserMsg/reduceUserMsg 只更新 connectBill，
// 但 chooseConnBill 没有同步更新！
```

### 具体场景
1. 用户选择房间 → `chooseConnBill` 获得初始数据
2. 用户添加入住人 → `addUserMsg` 更新 `connectBill`
3. 用户点击确定 → `sureCheckIn` 使用旧的 `chooseConnBill` 数据
4. **结果**: 新添加的入住人信息丢失

## 🛠️ **解决方案**

### 1. 添加数据同步函数
```javascript
// 同步 chooseConnBill 数据，确保添加/删除入住人后数据实时更新
syncChooseConnBill() {
    if (this.chooseConnBill && this.chooseConnBill.length > 0) {
        // 根据已选择的房间ID，从最新的 connectBill 中获取对应数据
        let updatedChooseConnBill = []
        this.chooseConnBill.forEach(chosenItem => {
            let updatedItem = this.connectBill.find(item => item.id === chosenItem.id)
            if (updatedItem) {
                updatedChooseConnBill.push(updatedItem)
            }
        })
        this.chooseConnBill = updatedChooseConnBill
        console.log('同步更新 chooseConnBill:', this.chooseConnBill)
    }
}
```

### 2. 修改 addUserMsg 函数
```javascript
addUserMsg(e) {
    let connectBill = JSON.parse(JSON.stringify(this.connectBill))
    connectBill.forEach(item => {
        if (e.id == item.id) {
            let msg = {
                name: '',
                gender: 0,
                phone: '',
                identification_type: 1,
                identification_number: ''
            }
            item.userMsg.push(msg)
        }
    })
    this.connectBill = JSON.parse(JSON.stringify(connectBill))
    
    // 同步更新 chooseConnBill 中对应的数据
    this.syncChooseConnBill()
}
```

### 3. 修改 reduceUserMsg 函数
```javascript
reduceUserMsg(e) {
    let connectBill = JSON.parse(JSON.stringify(this.connectBill))
    connectBill.forEach(item => {
        if (e.item.id == item.id) {
            item.userMsg.splice(e.index1, 1)
        }
    })
    this.connectBill = connectBill
    
    // 同步更新 chooseConnBill 中对应的数据
    this.syncChooseConnBill()
}
```

## ✅ **修复效果**

### 修复前
1. 用户选择房间 ✅
2. 用户添加入住人 ✅ (只更新 connectBill)
3. 用户点击确定 ❌ (使用旧的 chooseConnBill)
4. 新入住人信息丢失 ❌

### 修复后
1. 用户选择房间 ✅
2. 用户添加入住人 ✅ (更新 connectBill + 同步 chooseConnBill)
3. 用户点击确定 ✅ (使用最新的 chooseConnBill)
4. 新入住人信息正确提交 ✅

## 🧪 **测试建议**

### 测试场景
1. **添加入住人测试**:
   - 选择房间
   - 添加入住人
   - 填写入住人信息
   - 点击确定
   - 验证新入住人信息是否正确提交

2. **删除入住人测试**:
   - 选择房间
   - 删除入住人
   - 点击确定
   - 验证删除后的入住人信息是否正确

3. **混合操作测试**:
   - 选择房间
   - 添加入住人
   - 删除入住人
   - 再次添加入住人
   - 点击确定
   - 验证最终的入住人信息

4. **多房间测试**:
   - 选择多个房间
   - 对不同房间添加/删除入住人
   - 点击确定
   - 验证每个房间的入住人信息

### 验证方法
1. 在 `sureCheckIn` 函数开始处添加日志：
   ```javascript
   console.log('提交的入住人数据:', this.chooseConnBill)
   ```

2. 检查网络请求中的 `user_info` 字段是否包含最新的入住人信息

3. 验证服务器接收到的数据是否正确

## 📝 **注意事项**

1. **性能考虑**: `syncChooseConnBill` 函数会在每次添加/删除入住人时执行，对于大量房间的情况需要注意性能
2. **数据一致性**: 确保 `connectBill` 和 `chooseConnBill` 的数据结构完全一致
3. **错误处理**: 如果 `connectBill` 中找不到对应的房间数据，需要适当的错误处理
4. **向后兼容**: 修改不影响现有的选择房间逻辑

## 🚀 **后续优化建议**

1. 考虑使用 Vue 的响应式特性，通过 computed 属性自动计算 `chooseConnBill`
2. 可以考虑使用 Vuex 进行状态管理，避免数据同步问题
3. 添加数据验证，确保数据的完整性和一致性
