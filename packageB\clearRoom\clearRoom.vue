<template>
	<view>
		<m-tabs :list="list" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="tab_click"
			v-if="hackReset" :activeIndex="index" :config="{color:themeColor.main_color,
						  fontSize:30,
						  activeColor:themeColor.main_color,
						  underLineColor:themeColor.main_color,
						  underLineWidth:80,
						  underLineHeight:10}">
		</m-tabs>
		<view class="" style="display: flex;flex-direction: column;align-items: center;" v-if="index==0">
			<view class="" v-for="item in roomList" style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;padding: 18rpx 32rpx;
				display: flex;flex-direction: column;justify-content: space-between;position: relative;">

				<view style="display: flex;align-items: center;">
					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">生成时间:</p>
						<text>{{item.create_time | moment1}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">酒店备注:</p>
						<text>{{item.remark?item.remark:'暂无'}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;" v-if="item.user_remark">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">我的备注:</p>
						<text>{{item.user_remark?item.user_remark:'暂无'}}</text>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;justify-content: flex-end;">
					<view class=""
						style="width: fit-content;display: flex;justify-content: flex-end;margin-top: 20rpx;">
						<view class="" @click="addMsg(item)"
							style="height: 60rpx;min-width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
							:style="{border:'1px solid '+ themeColor.bg_main_color,color:themeColor.bg_main_color}">
							<text style="font-size: 28rpx;">添加留言</text>
						</view>
					</view>
					<view class=""
						style="width: fit-content;display: flex;justify-content: flex-end;margin-top: 20rpx;margin-left:10rpx;margin-right:10rpx;align-items: center;">
						<view class="" @click="openDoor(item)"
							style="height: 60rpx;min-width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
							:style="{border:'1px solid '+ themeColor.bg_main_color,color:themeColor.bg_main_color}">
							<text style="font-size: 28rpx;">开门清洁</text>
						</view>
					</view>
				</view>

			</view>
		</view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;" v-if="index==1">

			<view class="" v-for="item in roomList" style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;
				padding: 18rpx 32rpx;display: flex;flex-direction: column;justify-content: space-between;position: relative">
				<view class="" v-if="item.bill_id" @click="showCheck(item)"
					:style="{background:'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, ' +themeColor.bg_main1_color +' 100%)'}"
					style="position: absolute;top: 0;right: 0;z-index: 9;
				display: flex;align-items: center;justify-content: center;height: 80rpx;width: fit-content;padding: 6rpx 10rpx;border-bottom-left-radius: 16rpx;color: #FFFFFF;">
					核对商品
				</view>
				<view style="display: flex;align-items: center;">

					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">生成时间:</p>
						<text>{{item.create_time | moment1}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">酒店备注:</p>
						<text>{{item.remark?item.remark:'暂无'}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;" v-if="item.user_remark">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">我的备注:</p>
						<text>{{item.user_remark?item.user_remark:'暂无'}}</text>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;justify-content: flex-end;">
					<view class=""
						style="width: fit-content;display: flex;justify-content: flex-end;margin-top: 20rpx;">
						<view class="" @click="addMsg(item)"
							style="height: 60rpx;min-width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
							:style="{border:'1px solid '+ themeColor.bg_main_color,color:themeColor.bg_main_color}">
							<text style="font-size: 28rpx;">添加留言</text>
						</view>
					</view>
					<view class=""
						style="width: fit-content;display: flex;justify-content: flex-end;margin-top: 20rpx;margin-left:10rpx;margin-right:10rpx;align-items: center;">
						<view class="" @click="openDoor(item)"
							style="height: 60rpx;min-width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
							:style="{border:'1px solid '+ themeColor.bg_main_color,color:themeColor.bg_main_color}">
							<text style="font-size: 28rpx;">开门清洁</text>
						</view>
					</view>
					<view class=""
						style="width: fit-content;display: flex;justify-content: flex-end;margin-top: 20rpx;margin-left:10rpx;margin-right:10rpx;align-items: center;">
						<view class="" @click="peifu(item)" v-if="item.bill_id"
							style="height: 60rpx;min-width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
							:style="{border:'1px solid '+ themeColor.bg_main_color,color:themeColor.bg_main_color}">
							<text style="font-size: 28rpx;">物品赔付</text>
						</view>
					</view>
					<view class=""
						style="width: fit-content;display: flex;justify-content: flex-end;margin-top: 20rpx;">
						<view class="" @click="overClean(item)"
							style="height: 60rpx;width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
							:style="{background:'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, ' +themeColor.bg_main1_color +' 100%)'}">
							<text style="color: #FFFFFF;font-size: 28rpx;">打扫完成</text>
						</view>
					</view>
				</view>

			</view>
		</view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;" v-if="index==2">
			<view class="" v-for="item in roomList"
				style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;padding: 18rpx 32rpx;display: flex;flex-direction: column;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">生成时间:</p>
						<text>{{item.create_time | moment1}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">酒店备注:</p>
						<text>{{item.remark?item.remark:'暂无'}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;" v-if="item.user_remark">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">我的备注:</p>
						<text>{{item.user_remark?item.user_remark:'暂无'}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;" v-if="item.user_remark">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">打扫用时:</p>
						<text>{{((item.finish_time - item.start_time)/60).toFixed(0)}}分钟</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;" v-if="item.user_remark">
					<view class=""
						style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;">
						<p style="margin-right: 30rpx;">完成时间:</p>
						<text>{{item.finish_time | moment1}}</text>
					</view>
				</view>

				<view class="" style="width: 100%;display: flex;justify-content: flex-end;margin-top: 20rpx;">
					<view class=""
						style="height: 60rpx;width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
						:style="{background:themeColor.text_title_color}">
						<text style="color: #FFFFFF;font-size: 28rpx;">已完成</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 未分配 -->
		<m-popup :show="pop" @closePop="closePop" mode="bottom">
			<view class="" style="height: 80vh;border-radius: 32rpx;padding: 36rpx;">
				<view class="" style="margin-top: 60rpx;">
					<p style="margin-bottom: 10rpx;margin-bottom: 10rpx;">备注:</p>
					<uni-easyinput type="textarea" v-model="remark" placeholder="请输入备注说明"></uni-easyinput>
				</view>
				<view class=""
					style="position: absolute;bottom: 80rpx;left: 0;right: 0;margin: 0 auto;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="sure"
						style="display: flex;align-items: center;justify-content: center;border-radius: 48rpx;width: 500rpx;height: 80rpx;padding: 20rpx 0;color: #FFFFFF;"
						:style="{background:themeColor.main_color}">
						添加
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 门锁 -->
		<m-popup :show="popDoor" @closePop="closePopDoor" mode="bottom">
			<view class="" style="height: 30vh;border-radius: 32rpx;padding:60rpx 36rpx;">
				<ttLock v-if="ifTT()" :billDetail="room"></ttLock>
				<yyLock v-if="ifYY()" :billDetail="room"></yyLock>
			</view>
		</m-popup>

		<!-- 检查物品 -->
		<m-popup :show="popCheckGood" @closePop="closeCheck" mode="bottom">
			<view class="" style="height: 90vh;border-radius: 32rpx;padding:60rpx 36rpx;position: relative;">
				<view style="font-size: 40rpx;">房号：{{detail.room_number}}</view>
				<view class="" style="margin-top: 20rpx;font-size: 34rpx;">
					物品列表:(核对完成请勾选对应物品)
				</view>
				<view style="margin-top: 30rpx;color:brown;">未检查：</view>
				<view class="" style="display: flex;flex-wrap: wrap;margin-top: 20rpx;">
					<view class="">
						<checkbox-group @change="checkChange">
							<view class="" style="display: flex;flex-wrap: wrap;">
								<view v-for="item in goodsList" style="margin-right: 20rpx;margin-top: 20rpx;">
									<checkbox :value="item.id" :checked="item.is_check" />{{item.goods_name}}
								</view>
							</view>
							
						</checkbox-group>
					</view>
				</view>
				
				<view style="margin-top: 30rpx;color:darkseagreen;">已检查：</view>
				<view class="" style="display: flex;flex-wrap: wrap;margin-top: 20rpx;">
					<view class="">
						<checkbox-group @change="checkNoGoods">
							<view class="" style="display: flex;flex-wrap: wrap;">
								<view v-for="item in goodsListNo" style="margin-right: 20rpx;margin-top: 20rpx;">
									<checkbox :value="item.id" :checked="item.is_check" />{{item.goods_name}}
								</view>
							</view>
							
						</checkbox-group>
					</view>
				</view>
				<view class="" style="position: absolute;bottom: 30rpx;display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;">
					<view class="" @click="allCheck" style="height: 90rpx;width: 300rpx;display: flex;align-items: center;
					justify-content: center;background-color: darkseagreen;color: #FFFFFF;border-radius: 16rpx;margin-right: 30rpx;">
						一键检查全部
					</view>
					<view class="" @click="closeCheck" style="height: 90rpx;width: 300rpx;display: flex;align-items: center;justify-content: center;background-color: red;color: #FFFFFF;border-radius: 16rpx;">
						关闭弹窗
					</view>
				</view>
			</view>
		</m-popup>

		<view class="" style="height: 140rpx;">

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	import ttLock from './lock/ttLock.vue'
	import yyLock from './lock/yyLock.vue'
	export default {
		data() {
			return {
				list: [{
					name: '已领取',
					status: 2
				}, {
					name: '打扫中',
					status: 3
				}, {
					name: '已完成',
					status: 4
				}],
				bool: true,
				params: {
					page: 1,
					limit: 10,
					status: 1
				},
				roomList: [],
				index: 0,
				pop: false,
				range: [],
				value: '',
				room: null,
				hackReset: true,
				remark: '',
				popDoor: false,
				popCheckGood: false,
				detail: null,
				goodsList:[],
				goodsListNo:[]
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager'])
		},
		components: {
			ttLock,
			yyLock
		},
		watch: {
			timeRange(newval) {
				console.log('范围选:', this.timeRange);
			}
		},
		onLoad() {
			this.getWxAuthorizeLocation()

			const systemSetting = wx.getSystemSetting()
			const systemInfo = wx.getDeviceInfo()
			const appAuth = wx.getAppAuthorizeSetting()
			console.log(systemInfo)
			console.log(systemSetting.locationEnabled)
			console.log(systemSetting.wifiEnabled)
			if (!systemSetting.bluetoothEnabled) {
				uni.showModal({
					title: '太着急啦',
					content: '请打开手机蓝牙！',
					showCancel: false,
					success: (res) => {

					}
				})

			}

			if (!systemSetting.locationEnabled) {
				uni.showModal({
					title: '太着急啦',
					content: '请打开系统定位服务！',
					showCancel: false
				})

			}

			if (appAuth.bluetoothAuthorized != 'authorized' && systemInfo.system.includes('iOS')) {
				uni.showModal({
					title: '授权失败',
					content: '请允许微信使用蓝牙权限',
					showCancel: false
				})

			}

			if (appAuth.cameraAuthorized != 'authorized') {
				uni.showModal({
					title: '授权失败',
					content: '请允许微信使用手机的摄像头权限',
					showCancel: false
				})

			}


			if (appAuth.locationAuthorized != 'authorized') {
				uni.showModal({
					title: '授权失败',
					content: '请允许微信使用手机的定位权限',
					showCancel: false
				})

			}
			this.bool = true
			this.params.status = 2
			this.params.page = 1
			this.getList()

			this.$iBox.http('getRoomCleanAdmin', {})({
				method: 'post'
			}).then(res => {
				let a = []
				res.data.forEach(item => {
					let b = {
						text: item.name + '(' + item.nickname + ')',
						value: item.id
					}
					a.push(b)
				})
				this.range = a
			}).catch(function(error) {
				console.log('网络错误', error)
			})

		},
		methods: {
			tab_click(e) {
				console.log(e);
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
				})
				this.index = e
				this.params.status = e + 2
				this.params.page = 1
				this.bool = true
				this.getList()
			},
			checkChange(e){
				console.log(e,'ko');
				this.$iBox.http('checkStoreGoods', {
					ids: e.detail.value,
					is_check:1
				})({
					method: 'post'
				}).then(res => {
					this.$iBox.http('getStoreBillList', {
						room_bill_id:this.detail.bill_id,
						is_check:0
					})({
						method: 'post'
					}).then(res => {
						this.goodsList = res.data
					}).catch(function(error) {
						console.log('网络错误', error)
					})
					
					this.$iBox.http('getStoreBillList', {
						room_bill_id:this.detail.bill_id,
						is_check:1
					})({
						method: 'post'
					}).then(res => {
						this.goodsListNo = res.data
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}).catch(function(error) {
					console.log('网络错误', error)
				})
				
			},
			checkNoGoods(e){
				console.log(e.detail.value);
				let list = []
				this.goodsListNo.forEach(item=>{
					list.push(item.id.toString())
				})
				console.log(list);
				let result = list.filter(item => !e.detail.value.includes(item));
				console.log(result,'result');
				
				this.$iBox.http('checkStoreGoods', {
					ids: result,
					is_check:0
				})({
					method: 'post'
				}).then(res => {
					this.$iBox.http('getStoreBillList', {
						room_bill_id:this.detail.bill_id,
						is_check:0
					})({
						method: 'post'
					}).then(res => {
						this.goodsList = res.data
					}).catch(function(error) {
						console.log('网络错误', error)
					})
					
					this.$iBox.http('getStoreBillList', {
						room_bill_id:this.detail.bill_id,
						is_check:1
					})({
						method: 'post'
					}).then(res => {
						this.goodsListNo = res.data
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			change(e) {
				console.log("e:", e, this.value);
			},
			allCheck(){
				this.$iBox.http('getStoreBillList', {
					room_bill_id: this.detail.bill_id,
					is_check:0
				})({
					method: 'post'
				}).then(res => {
					let list = []
					res.data.forEach(item=>{
						list.push(item.id.toString())
					})
					
					this.$iBox.http('checkStoreGoods', {
						ids: list,
						is_check:1
					})({
						method: 'post'
					}).then(res => { 
						this.$iBox.http('getStoreBillList', {
							room_bill_id:this.detail.bill_id,
							is_check:0
						})({
							method: 'post'
						}).then(res => {
							this.goodsList = res.data
						}).catch(function(error) {
							console.log('网络错误', error)
						})
						
						this.$iBox.http('getStoreBillList', {
							room_bill_id:this.detail.bill_id,
							is_check:1
						})({
							method: 'post'
						}).then(res => {
							this.goodsListNo = res.data
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					})
					
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			showCheck(e) {
				this.detail = e
				this.popCheckGood = true

				this.$iBox.http('getStoreBillList', {
					room_bill_id: e.bill_id,
					is_check:0
				})({
					method: 'post'
				}).then(res => {
					this.goodsList = res.data
				}).catch(function(error) {
					console.log('网络错误', error)
				})
				
				this.$iBox.http('getStoreBillList', {
					room_bill_id: e.bill_id,
					is_check:1
				})({
					method: 'post'
				}).then(res => {
					this.goodsListNo = res.data
				}).catch(function(error) {
					console.log('网络错误', error)
				})


			},
			closeCheck(e) {
				this.popCheckGood = false
			},
			peifu(e) {
				uni.navigateTo({
					url: '/packageB/peifu/peifu?id=' + e.bill_id
				})
			},
			ifTT() {
				let a = ''
				if (this.room) {

					a = this.room.lock_list.filter(item => {
						return item.lock_type_sign == 'tongtong'
					})
				}


				return a && a.length > 0
			},
			ifYY() {
				console.log('ddj');
				let a = ''
				if (this.room) {

					a = this.room.lock_list.filter(item => {
						return item.lock_type_sign == 'yaya'
					})
				}
				console.log(a, 'kl3');
				return a && a.length > 0
			},

			closePopDoor() {
				this.popDoor = false
			},
			closePop() {
				this.pop = false
			},
			addMsg(e) {

				this.room = e
				console.log(e, 'l');
				this.pop = true

			},
			openDoor(e) {
				if (e.lock_list.length == 0 || !e.lock_list) {
					uni.showModal({
						title: '提示',
						content: '此房间无门锁,已开始计时打扫!',
						success() {

						}
					})
				} else {
					this.popDoor = true
				}

				this.room = e
				console.log(e, 'kl');
				if (this.index == 0) {
					let params = {
						id: e.id,
						status: 3
					}
					this.$iBox.http('updateCleanRoomStatus', params)({
						method: 'post'
					}).then(res => {
						this.tab_click(1)
						this.remark = ''
						this.params.status = 3
						this.params.page = 1
						this.bool = true
						this.getList()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}

			},
			sure() {
				let params = {
					id: this.room.id,
					user_remark: this.remark
				}

				this.$iBox.http('updateUserRoomClean', params)({
					method: 'post'
				}).then(res => {
					this.pop = false
					this.remark = ''

					this.params.page = 1
					this.bool = true
					this.getList()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			overClean(e) {
				uni.showModal({
					title: '提示',
					content: '是否确认打扫完成!',
					success: res => {
						if (res.confirm) {
							let params = {
								id: e.id
							}

							this.$iBox.http('finishRoomClean', params)({
								method: 'post'
							}).then(res => {
								this.tab_click(2)
								this.pop = false
								this.remark = ''
								this.params.page = 1
								this.bool = true
								this.getList()
							}).catch(function(error) {
								console.log('网络错误', error)
							})
						} else {

						}
					}
				})

			},
			getList() {
				this.$iBox.http('getMyRoomCleanList', this.params)({
					method: 'post'
				}).then(res => {
					this.roomList = res.data.list

					console.log(this.index, this.roomList);
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			showAdmin(e) {
				this.pop = true
				this.room = e
			},
			getWxAuthorizeLocation() {
				uni.getSetting({
					success: (res) => {

						// 如果从未申请定位权限，则申请定位权限
						if (res.authSetting['scope.userLocation'] == null) {
							uni.authorize({
								scope: 'scope.userLocation',
								success() {
									// 用户同意
									// 相关操作

								},
								fail() {
									uni.showToast({
										title: '无法申请定位权限,请确认是否已经授权定位权限',
										icon: "none",
										duration: 2000
									})

								}
							})
							return
						}

						// if (res.authSetting['scope.bluetooth'] == null) {
						// 	uni.authorize({
						// 		scope: 'scope.bluetooth',
						// 		success() {
						// 			// 用户同意
						// 			// 相关操作
						// 		},
						// 		fail() {
						// 			uni.showToast({
						// 				title: '无法申请蓝牙权限,请确认是否已经授权蓝牙权限',
						// 				icon: "none",
						// 				duration: 2000
						// 			})
						// 		}
						// 	})
						// 	return
						// }


					}
				});
			},

		},
		// // 上拉加载
		onReachBottom() {

			if (this.bool) {
				++this.params.page
				this.params.status = this.index + 1
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getRoomCleanList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.roomList.concat(res.data.list)
					this.roomList = new_list
					if (this.roomList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #f5f5f5;
	}
</style>
<style scoped lang="scss">

</style>