<template>
	<view>
		<view class="main_card">
			<view class="cd1" @click="pointCenter">
				<text class="tx1">我的积分</text>
				<p class="tx2">{{userInfo.point?userInfo.point:'0'}}<text style="font-size: 22rpx;">分</text>
					<u-icon name="arrow-right" style="font-size: 34rpx;font-weight: 400;padding-left: 80rpx;"></u-icon>
				</p>
			</view>
			<view class="cd2">
				<view class="in_cd2" @click="pointBill">
					兑换记录<u-icon name="arrow-right" style="font-size: 28rpx;font-weight: 100;padding-left: 8rpx;">
					</u-icon>
				</view>
			</view>
		</view>
		<view class="good_box">
			<p style="font-size: 46rpx;font-weight: 700;">积分兑好物</p>
			<scroll-view scroll-x="true" style="padding: 30rpx;width: 100%;" scroll-with-animation="true">
				<view class="" style="display: flex;align-items: center;flex-wrap: wrap;">
					<view v-for="(item, index) in list" :key="index"
						:class="chooseId === item.id ? 'getActiveStyle' : 'getDefaultStyle'"
						@click="onClickScrollItem(item)">{{item.type_name}}</view>
				</view>

			</scroll-view>

			<view class="good_card">
				<view class="cardinner" v-for="item in goodsList" @click="goDetail(item)">
					<image :src="item.cover_pic" style="height: 300rpx;width: 100%;"></image>
					<view class="" style="padding:0 20rpx;">
						<p style="font-size: 36rpx;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;">
							{{item.goods_name}}
						</p>
						<view class="" style="display: flex;align-items: center;">
							<p style="font-size: 28rpx;font-weight: 500;color: #A5673F;">{{item.point_amount}}<text
									style="font-size: 24rpx;">积分</text></p>
							<text v-if="item.price">+</text>
							<p style="font-size: 28rpx;font-weight: 500;color: #A5673F;" v-if="item.price">
								{{item.price}}<text style="font-size: 24rpx;">元</text></p>
						</view>

						<text style="font-size: 22rpx;color:#888888">已兑{{item.sale_count}}件</text>
					</view>
				</view>
			</view>
		</view>
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list: [],
				goodsList: [],
				current: 0,
				params: {
					page: 1,
					limit: 20,
					type_id: '',
					hot: ''
				},
				bool: true,
				chooseId: '',
				if_login: false,
				hackReset: true,
			}
		},

		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'city','setting'])
		},

		watch: {},
		async onLoad() {
			await this.$onLaunched;

		},
		async onShow() {
			await this.$onLaunched;

			

			// uni.showLoading({
			// 	title: '加载中...'
			// })
			this.$iBox.http('getPointMallGoodsTypeList', {})({
				method: 'post'
			}).then(res => {
				this.list = res.data
				this.chooseId = res.data[0].id
				this.params.type_id = res.data[0].id
				this.params.page = 1
				this.$iBox.http('getPointMallGoods', this.params)({
					method: 'post'
				}).then(res => {
					this.goodsList = res.data.list
				})
			})
		},
		methods: {
			...mapActions('pointShop', ['getPointShopDetail']),
			loginSucess() {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true

					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					if (set[0].property) {
						let a = set[0].property.value
						if (a == 2) {
							if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
								.upgrade_growth_value > -1) {
								this.if_login = false

							} else {
								this.if_login = true
							}

						} else if (a == 1) {
							// this.pop = true
							if (this.userInfo.phone) {
								this.if_login = false

							} else {
								this.if_login = true
							}
						}
					}


				})
			},
			onClickScrollItem(e) {
				console.log(e);
				this.chooseId = e.id
				this.params.type_id = e.id
				this.$iBox.http('getPointMallGoods', this.params)({
					method: 'post'
				}).then(res => {
					this.goodsList = res.data.list
				})

			},
			pointCenter() {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
						.upgrade_growth_value > -1) {
						this.if_login = false
						uni.navigateTo({
							url: '/pages/pointList/pointList'
						})

					} else {
						this.if_login = true
					}
				})

			},
			goDetail(e) {
				// this.hackReset = false
				// this.$nextTick(() => {
				// 	this.hackReset = true
				// 	if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
				// 		.upgrade_growth_value > -1) {
				// 		this.if_login = false
						

				// 	} else {
				// 		this.if_login = true
				// 	}
				// })
				
				let set = this.setting.filter(item => {
					return item.sign == 'auto_register_member'
				})
				if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo
					.grade_info
					.upgrade_growth_value > -
					1) {
						this.getPointShopDetail(e)
						uni.navigateTo({
							url: '/packageA/pointShop/pointDetail/pointDetail'
						})
					}else{
						let set = this.setting.filter(item => {
							return item.sign == 'auto_register_member'
						})
						let a = set[0].property.value
						if (a == 2) {
							this.if_login = false
						
						} else if (a == 1) {
							// this.pop = true
							uni.navigateTo({
								url: '/packageA/memberInfo/memberInfo'
							})
						}
					}
				
			},
			pointBill() {
				uni.navigateTo({
					url: '/packageA/pointShop/pointBill/pointBill'
				})
			}
		},
		onReachBottom() {
			if (this.bool) {
				++this.params.page
				this.params.type_id = this.list[this.current].id
				this.status = 'loadmore'
				this.$iBox.http('getPointMallGoods', this.params)({
					method: 'get'
				}).then(res => {
					// console.log('我是返回', res.data)
					let new_list = this.goodsList.concat(res.data.list)
					this.goodsList = new_list
					if (this.goodsList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.main_card {
		height: 240rpx;
		width: 700rpx;
		margin: 30rpx auto;
		border-radius: 24rpx;
		background-image: linear-gradient(to right, #fef5e6, #ffe8c6);
		display: flex;
		align-items: center;

		.cd1 {
			height: 100%;
			width: 50%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			padding: 50rpx;

			.tx1 {
				font-size: 34rpx;
				color: #65420a;
				font-weight: 600rpx;
			}

			.tx2 {
				font-weight: 700rpx;
				font-size: 46rpx;
				margin-top: 30rpx;
				color: #65420a;
			}
		}

		.cd2 {
			height: 100%;
			width: 50%;
			display: flex;
			align-items: center;
			padding: 30rpx;
			justify-content: center;
			margin-top: 60rpx;

			.in_cd2 {
				width: 200rpx;
				height: 66rpx;
				background: #f0d190;
				border-radius: 46rpx;
				color: #5a3400;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: 700;
			}
		}
	}

	.good_box {
		padding: 30rpx;
		margin-top: 60rpx;

		.getActiveStyle {
			background: #f0d190;
			width: fit-content;
			padding: 10rpx 20rpx;
			border-radius: 30rpx;
			margin: 14rpx;

		}

		.getDefaultStyle {
			border: 1px solid #f0d190;
			width: fit-content;
			padding: 10rpx 20rpx;
			border-radius: 30rpx;
			margin: 14rpx;
		}

		.good_card {
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;
			margin-bottom: 20rpx;

			.cardinner {
				width: 330rpx;
				height: 460rpx;
				box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
				margin-top: 20rpx;
			}
		}
	}
</style>