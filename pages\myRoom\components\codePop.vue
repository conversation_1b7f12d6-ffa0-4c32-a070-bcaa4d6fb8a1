<template>
	<view>
		<!-- 样式一 -->
		<view class="bgBox" v-if="!styleModel.bg_image">
			<view class="boxCount" :style="{ background: themeColor.main_color, color: themeColor.bg_color }"
				@click="showFloor">
				<text class="icon-erweima" style="padding-right: 10rpx;"></text> 识别乘梯
			</view>

			<view class="boxCount" :style="{ background: themeColor.main_color, color: themeColor.bg_color }"
				@click="showFood">
				<text class="icon-erweima" style="padding-right: 10rpx;"></text>用餐服务
			</view>
		</view>

		<view class="bgBox" v-if="styleModel.bg_image">
			<view class="boxCount" :style="{ background: themeColor.main_color, color: themeColor.bg_color }"
				@click="showFloor">
				<image :src="styleModel.bg_image"
					style="border-radius: 40rpx;position: absolute;top: 0;right: 0;width: 100%;height: 100%;z-index: 0;">
				</image>
				<view class="boxCount" style="z-index: 2;font-size: 40rpx;">
					<text class="icon-erweima" style="padding-right: 10rpx;"></text>识别乘梯
				</view>
			</view>

			<view class="boxCount" :style="{ background: themeColor.main_color, color: themeColor.bg_color }"
				@click="showFood">
				<image :src="styleModel.bg_image"
					style="border-radius: 40rpx;position: absolute;top: 0;right: 0;width: 100%;height: 100%;z-index: 0">
				</image>
				<view class="boxCount" style="z-index: 2;font-size: 40rpx;">
					<text class="icon-erweima" style="padding-right: 10rpx;"></text>用餐服务
				</view>

			</view>
		</view>



		<!-- 梯控 -->
		<m-popup :show="show3" mode="center" @closePop="closePop3">
			<view
				style="display: flex;flex-direction: column;align-items: center;position: relative;width: 700rpx;padding: 20rpx;">
				<view class="icon-close" style="position: absolute;right: 20rpx;top:20rpx;font-size: 46rpx;"
					@click="closePop3">

				</view>
				<p style="font-size: 44rpx;font-weight: 600;color: darkslateblue;" v-if="billDetail.floor_name">
					您的入住楼层:{{ billDetail.floor_name }}</p>
				<p style="margin: 20rpx auto;font-size:34rpx;font-weight:600">扫码后点击楼层</p>
				<image :src="url" style="height: 300rpx;width: 300rpx;margin-bottom: 30rpx;" mode=""></image>
				<view class="" style="margin: 30rpx auto;display: flex;flex-direction: column;">
					<text style="font-size: 28rpx;color: #CD1225;">*若二维码没生效请点击下方刷新按钮</text>
					<view class="" style="margin: 30rpx auto;" @click="reload">
						<uni-icons type="refreshempty" size="23"></uni-icons>
						<text style="font-size: 34rpx;color: #2979ff;">刷新</text>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;justify-content: center;"
					v-if="floor_list[0].mac_config">
					<view class="" @click="bluthOpen()"
						style="width: 600rpx;height: 80rpx;display: flex;align-items: center;justify-content: center;border-radius: 30rpx;color: #FFFFFF;background-color: #00aa00">
						<text>{{ isConnecting ? '正在连接电梯...' : '点击启动电梯' }}</text>
					</view>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	import qrcode1 from '@/packageA/plugins/qrcode.js';
	import QR from '@/packageA/plugins/wxqrcode.js';
	const bgAudioManager = uni.getBackgroundAudioManager();
	export default {
		props: {
			billDetail: Object,
			styleModel: Object
		},
		data() {
			return {
				floor_list: [],
				show3: false,
				url: '',
				server: null,
				ble_adv_sta: false,
				timer_id_adv: 5,
				timer_id_adv1: 5,
				breakfastSetting: null,
				bleDeviceId: null,
				serviceId: null,
				characteristicId: null,
				isConnecting: false,
				connectionTimeout: null
			};
		},
		components: {},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		watch: {

		},
		mounted() {
			this.stopScan()
			

		},
		methods: {

			closePop3() {
				console.log('关闭弹窗');
				this.show3 = false;

				// 清理旧的广播方式
				if (this.server) {
					this.stopAdvertising();
				}

				// 清理新的连接方式
				this.closeConnection();
				this.isConnecting = false;

				if (this.connectionTimeout) {
					clearTimeout(this.connectionTimeout);
					this.connectionTimeout = null;
				}
			},
			reload() {
				this.showPop()
			},
			showPop() {
				this.$iBox
					.http('getLiftConfigList', {
						shop_id: this.hotel.id
					})({
						method: 'post'
					})
					.then(res => {
						this.bluthOpen()
						this.floor_list = res.data
						// 如果存在序列号则开启蓝牙广播
						if (this.floor_list.length == 0) {
							uni.showToast({
								icon: 'none',
								title: '本酒店暂无梯控'
							})
							return
						}
						console.log(this.floor_list, 'md');


						let that = this
						let a = []
						let floorList = []
						this.billDetail.floor_number ? floorList.push(this.billDetail.floor_number.toString(16)) : ''
						this.floor_list.forEach((item, index) => {
							if (item.public_floor) {
								item.public_floor.forEach(item1 => {
									floorList.push(item1)
								})
							}

						})
						floorList = [...new Set(floorList)]

						floorList.forEach(item => {
							let floor = {
								floor: item
							}
							a.push(floor)

						})

						let floors = []

						this.floor_list.forEach(item => {
							let floor = {}
							floor.sn = item.sn
							floor.floors = a
							floors.push(floor)
						})


						let lifts = {
							floors: floors,
							all_lift: 1,
							direct_arrival: 0 //是否直达
						}
						let url = ''
						console.log(floors, 'dd', this.hotel.shop_name == '豪瑞特酒店');
						url = qrcode1.generateAccessCode('a', new Date(), 30, Number(this.userInfo.id), lifts, [])

						this.url = QR.createQrCodeImg(url.encrypt)
					})
					.catch(function(error) {
						console.log('33434', error);
					});



			},
			bluthOpen() {
				// 使用自动连接代替蓝牙广播
				wx.openBluetoothAdapter({
					mode: 'central',
					success: e => {
						console.log('初始化蓝牙成功:' + e.errMsg);
						if (this.floor_list[0].mac_config) {
							console.log('开始自动连接电梯...');
						
							let floor = this.billDetail.floor_number.toString(16)
						
							// 显示加载状态
							uni.showLoading({
								title: '正在连接电梯...'
							});
						
							this.connectAndSendData(floor);
						
							// 隐藏加载状态将在连接成功后自动处理
						}
					},
					fail: e => {
						console.log('初始化蓝牙失败，错误码：' + (e.errCode || e.errMsg));
						uni.showToast({
							icon: 'error',
							title: '检查蓝牙是否打开'
						})
					}
				});
				console.log(this.floor_list);
				
			},
			showFloor() {
				this.showPop()
				this.show3 = true
				// 开锁语音
				bgAudioManager.title = '提醒'
				bgAudioManager.epname = '提醒'
				bgAudioManager.singer = '提醒'
				bgAudioManager.src =
					'https://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_mp3/%E6%A2%AF%E6%8E%A71.mp3'


			},
			showFood() {

				// 获取早餐设置
				this.breakfastSetting = this.shopSetting.filter(item => {
					return item.sign == 'breakfast_setting'
				})[0].property

				if (this.breakfastSetting.floor_number) {
					let floor = this.breakfastSetting.floor_number.toString(16)
					console.log(floor, 'm');

					// 使用新的自动连接方式
					this.connectAndSendData(floor);

					setTimeout(() => {
						uni.navigateTo({
							url: '/packageA/breakfastCard/breakfastCard'
						})
					}, 1000);
				}

			},
			// startAdvertising(floor) {
			// 	// 梯控协议格式：
			// 	// 起始码(1) + 数据长度(1) + 功能码(1) + 地址码1(1) + 地址码2(1) + 数据(N) + 校验码(2)
			// 	// 示例：59 07 00 ff 16 03 66 02 (3楼)

			// 	const frame = new Uint8Array(8);

			// 	// 1. 起始码
			// 	frame[0] = 0x59;

			// 	// 2. 数据长度 (根据示例应该是0x07)
			// 	frame[1] = 0x07;

			// 	// 3. 功能码 (开启楼层功能)
			// 	frame[2] = 0x00;

			// 	// 4. 地址码1 (梯控固定为0x16，但示例中是0xff，使用示例值)
			// 	frame[3] = 0xff;

			// 	// 5. 地址码2 (梯控地址码)
			// 	frame[4] = 0x16;

			// 	// 6. 数据 (楼层号)
			// 	frame[5] = parseInt(floor, 16);

			// 	// 7. 校验码计算 
			// 	// 数据校验位方式：59+07+00+FF+16+0B+EE=26E，校验位 6E 02 (低字节在前)
			// 	let checksum = 0xEE; // 固定salt值0xEE
			// 	for (let i = 0; i < 6; i++) {
			// 		checksum += frame[i];
			// 	}

			// 	// 校验码取最后两个字节 (低字节在前，高字节在后)
			// 	frame[6] = checksum & 0xFF;        // 低字节
			// 	frame[7] = (checksum >> 8) & 0xFF; // 高字节

			// 	// 转换为设备名称字符串
			// 	const deviceName = Array.from(frame)
			// 		.map(b => b.toString(16).padStart(2, '0').toUpperCase())
			// 		.join('');

			// 	console.log(`楼层${parseInt(floor, 16)}的广播编码:`, deviceName);
			// 	console.log(`校验和: 0x${checksum.toString(16)}, 校验码: ${frame[6].toString(16).padStart(2, '0')} ${frame[7].toString(16).padStart(2, '0')}`);

			// 	// 使用自动连接方式代替广播
			// 	this.connectAndSendData(deviceName);
			// },

			// 新的自动连接方法
			connectAndSendData(floor) {
				this.isConnecting = true;
				// 开始扫描附近的蓝牙设备
				wx.startBluetoothDevicesDiscovery({
					allowDuplicatesKey: false,
					success: (res) => {
						console.log('开始扫描蓝牙设备:', res);

						// 设置扫描超时
						// this.connectionTimeout = setTimeout(() => {
						// 	this.stopScan();
						// 	this.isConnecting = false;
						// 	uni.showToast({
						// 		title: '未找到电梯蓝牙设备',
						// 		icon: 'none'
						// 	});
						// }, 50000);

						// 监听发现的设备
						this.onBluetoothDeviceFound(floor);
					},
					fail: (err) => {
						console.error('扫描蓝牙设备失败:', err);
						this.isConnecting = false;
						uni.showToast({
							title: '蓝牙扫描失败',
							icon: 'none'
						});
					}
				});
			},

			// 发现蓝牙设备时的处理
			onBluetoothDeviceFound(floor) {
				wx.onBluetoothDeviceFound((res) => {
					const devices = res.devices;
					console.log(res, '发现设备')
					// 查找电梯设备（根据设备名称或MAC地址前缀）
					const targetDevice = devices.find(device => {
						return device.name && (
							device.localName && (device.localName.includes('SMARTBOX_E'))
						);
					});

					if (targetDevice) {
						console.log('发现电梯设备:', targetDevice);
						this.stopScan();
						this.connectToDevice(targetDevice.deviceId, floor);
					} else {

						uni.showToast({
							icon: 'none',
							title: '暂未扫描到梯控'
						})
					}
				});
			},

			// 停止扫描
			stopScan() {
				wx.stopBluetoothDevicesDiscovery({
					success: (res) => {
						console.log('停止扫描蓝牙设备:', res);
					}
				});

				if (this.connectionTimeout) {
					clearTimeout(this.connectionTimeout);
					this.connectionTimeout = null;
				}
			},

			// 连接到设备
			connectToDevice(deviceId, floor) {
				wx.createBLEConnection({
					deviceId: deviceId,
					success: (res) => {
						console.log('连接蓝牙设备成功:', res);
						this.bleDeviceId = deviceId;

						// 获取服务
						this.getServices(deviceId, floor);
					},
					fail: (err) => {
						console.error('连接蓝牙设备失败:', err);
						this.isConnecting = false;
						uni.showToast({
							title: '连接电梯失败',
							icon: 'none'
						});
					}
				});
			},

			// 获取蓝牙服务
			getServices(deviceId, floor) {
				wx.getBLEDeviceServices({
					deviceId: deviceId,
					success: (res) => {
						console.log('获取服务成功:', res.services);
						
						for (let item of res.services) {
							wx.getBLEDeviceCharacteristics({
								deviceId: deviceId,
								serviceId: item.uuid,
								success: (res) => {
									console.log('获取特征值成功:', res.characteristics);
									res.characteristics.forEach(char=>{
										if(char.properties.write&&!char.properties.read){
											const writeCharacteristic = char
											console.log(writeCharacteristic,'writeCharacteristic');
											if (writeCharacteristic) {
												this.characteristicId = writeCharacteristic.uuid;
												this.sendElevatorCommand(deviceId, item.uuid, writeCharacteristic.uuid, floor);
											} else {
												console.error('未找到可写特征值');
												this.isConnecting = false;
												this.closeConnection();
											}
										}
									})
									
								},
								fail: (err) => {
									console.error('获取特征值失败:', err);
									this.isConnecting = false;
									this.closeConnection();
								}
							});
						}
						
						// 查找目标服务（通常电梯控制服务）
						// const targetService = res.services[0]

						// if (targetService) {
						// 	this.serviceId = targetService.uuid;
						// 	this.getCharacteristics(deviceId, targetService.uuid, floor);
						// } else {
						// 	// 使用第一个可用服务
						// 	this.serviceId = res.services[0].uuid;
						// 	this.getCharacteristics(deviceId, res.services[0].uuid, floor);
						// }
					},
					fail: (err) => {
						console.error('获取服务失败:', err);
						this.isConnecting = false;
						this.closeConnection();
					}
				});
			},

			// 获取特征值
			getCharacteristics(deviceId, serviceId, floor) {
				wx.getBLEDeviceCharacteristics({
					deviceId: deviceId,
					serviceId: serviceId,
					success: (res) => {
						console.log('获取特征值成功:', res.characteristics);

						// 查找可写的特征值
						const writeCharacteristic = res.characteristics.find(char => {
							return char.properties.write&&!char.properties.read;
						});
						console.log(writeCharacteristic,'writeCharacteristic');
						if (writeCharacteristic) {
							this.characteristicId = writeCharacteristic.uuid;
							this.sendElevatorCommand(deviceId, serviceId, writeCharacteristic.uuid, floor);
						} else {
							console.error('未找到可写特征值');
							this.isConnecting = false;
							this.closeConnection();
						}
					},
					fail: (err) => {
						console.error('获取特征值失败:', err);
						this.isConnecting = false;
						this.closeConnection();
					}
				});
			},

			// 发送电梯控制命令
			sendElevatorCommand(deviceId, serviceId, characteristicId, floor) {
				// 生成电梯控制数据
				const command = this.generateElevatorData(floor);
				console.log(command,deviceId,serviceId,characteristicId,this.hexStringToArrayBuffer(command));
				wx.writeBLECharacteristicValue({
					deviceId: deviceId,
					serviceId: serviceId,
					characteristicId: characteristicId,
					value: this.hexStringToArrayBuffer(command),
					success: (res) => {
						console.log('发送电梯命令成功:', res);
						this.isConnecting = false;
						uni.showToast({
							title: '电梯已启动',
							icon: 'success'
						});

						// 延迟关闭连接
						setTimeout(() => {
							this.closeConnection();
						}, 2000);
					},
					fail: (err) => {
						console.error('发送电梯命令失败:', err);
						this.isConnecting = false;
						this.closeConnection();
						uni.showToast({
							title: '电梯启动失败',
							icon: 'none'
						});
					}
				});
			},
             hexStringToArrayBuffer(hexString) {
              const cleanHex = hexString.replace(/[^0-9a-fA-F]/g, '');
              if (cleanHex.length % 2 !== 0) {
                throw new Error("Hex string length must be even.");
              }
              const buffer = new Uint8Array(cleanHex.length / 2);
              for (let i = 0; i < cleanHex.length; i += 2) {
                buffer[i / 2] = parseInt(cleanHex.substr(i, 2), 16);
              }
              return buffer.buffer;
            },
		// 关闭蓝牙连接
		closeConnection() {
			if (this.bleDeviceId) {
				wx.closeBLEConnection({
					deviceId: this.bleDeviceId,
					success: (res) => {
						console.log('关闭蓝牙连接成功:', res);
					}
				});
				this.bleDeviceId = null;
			}

			wx.closeBluetoothAdapter({
				success: (res) => {
					console.log('关闭蓝牙模块成功:', res);
				}
			});
		},

		// 生成电梯控制数据
		generateElevatorData(floor) {
			// 梯控协议格式：
			// 起始码(1) + 数据长度(1) + 功能码(1) + 地址码1(1) + 地址码2(1) + 数据(N) + 校验码(2)
			// 示例：59 07 00 ff 16 03 66 02 (3楼)

			const frame = new Uint8Array(8);

			// 1. 起始码
			frame[0] = 0x59;

			// 2. 数据长度
			frame[1] = 0x07;

			// 3. 功能码
			frame[2] = 0x00;

			// 4. 地址码1
			frame[3] = 0xff;

			// 5. 地址码2
			frame[4] = 0x16;

			// 6. 数据 (楼层号)
			frame[5] = parseInt(floor, 16);

			// 7. 校验码计算 
			let checksum = 0xEE;
			for (let i = 0; i < 6; i++) {
				checksum += frame[i];
			}

			// 校验码取最后两个字节 (低字节在前，高字节在后)
			frame[6] = checksum & 0xFF;
			frame[7] = (checksum >> 8) & 0xFF;
			const deviceName = Array.from(frame)
				.map(b => b.toString(16).padStart(2, '0').toUpperCase())
				.join('');
			return deviceName;
		},

		// 原有的广播方法已废弃，改为自动连接
		startAdvertising(floor) {
			console.warn('startAdvertising方法已废弃，使用connectAndSendData代替');
			this.connectAndSendData(floor);
			// startAdvertising(floor) {
			// 	// 屏幕常亮下,小程序的广播持续时间：3分钟
			// 	// [18:09:20.076 ~ 18:12:19.483]
			// 	let mac = this.floor_list[0].mac_config
			// 	let device_id = mac
			// 	let sign_num = ''
			// 	for (var i = 0; i < 12 - mac.length; i++) {
			// 		console.log(i);
			// 		device_id = '0' + device_id
			// 	}

			// 	let floor_num = floor.toString(16).length < 2 ? '0' + floor : floor
			// 	console.log(floor_num,'floor_num');
			// 	device_id = 'BE' + device_id + '02' + floor_num + 'EE'
			// 	console.log(device_id, 'tty');
			// 	let itotal = 0,
			// 		len = device_id.length,
			// 		num = 0;
			// 	while (num < len) {
			// 		let s = device_id.substring(num, num + 2);
			// 		itotal += parseInt(s, 16)
			// 		num = num + 2;
			// 	}
			// 	let mode = itotal % 256;
			// 	let shex = mode.toString(16)
			// 	let iLen = shex.length;
			// 	if (iLen < 2) {
			// 		shex = '0' + shex
			// 	}

			// 	device_id = ('590700FF160B6E02'.slice(0, -2) + shex).toUpperCase()
			// 	console.log(device_id, 'arrNum');//BE00000E2100100204F1 
			// 	if (this.server) {
			// 		this.server.startAdvertising({
			// 			advertiseRequest: {
			// 				connectable: false,
			// 				deviceName: device_id
			// 			},
			// 			powerLevel: "high",
			// 			success: res => {
			// 				console.log(res, '返回广播')

			// 			}
			// 		})
			// 	}

		},

		stopAdvertising() {
			if (JSON.stringify(this.server) == "{}") {
				return
			}
			this.ble_adv_sta = false
			this.server.stopAdvertising({
				success: (res) => {

				},
				fail: (res) => {

				},
				complete: (res) => {

				}
			})
		},
	}
	}
</script>

<style lang="scss" scoped>
	.bgBox {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 15rpx 30rpx;
		margin-top: 30rpx;

		.boxCount {
			width: 300rpx;
			height: 80rpx;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: relative;
		}
	}
</style>