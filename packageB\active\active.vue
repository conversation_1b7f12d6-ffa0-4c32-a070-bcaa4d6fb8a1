<template>
	<view>
		<view class="" @click="toDetail(item)"
		 style="margin: 30rpx auto;min-height: 320rpx;border-radius: 32rpx;border: 1px solid #E1EBE8;width: 686rpx;background-color: #FFFFFF;flex-direction: column;display: flex;justify-content:space-between;" v-for="item in list">
			<view class="" style="padding: 20rpx 30rpx;display: flex;align-items: center;border-bottom: 1px solid #E1EBE8;">
				<image src="http://doc.hanwuxi.cn/wp-content/uploads/2025/02/active.png" style="height: 40rpx;width: 40rpx;border-radius: 32rpx;" mode="aspectFill"></image>
				<text style="color: #222222;margin-left: 14rpx;">{{item.active_name}}</text>
			</view>
			<image :src="item.pic" style="height: 240rpx;width: 100%;" mode=""></image>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list:[]
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'setting']),
		},
		async onShow() {
			await this.$onLaunched;
			this.$iBox.http('getActive', {})({
				method: 'post'
			}).then(res => { 
				this.list = res.data
			})
		},
		methods: {
			...mapActions('hotel',['getActive']),
			toDetail(e){
				this.getActive(e)
				uni.navigateTo({
					url:'/packageB/active/activeDetail'
				})
			}
		},
		onShareAppMessage() {
			// 1.返回节点对象
			let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
			let currentPage = pages[pages.length - 1]; //获取当前页面的对象
			let url = currentPage.route //当前页面url
			return {
				path: url +'?share_id='+this.userInfo.id
			};
		}
	}
</script>

<style scoped lang="scss">
	view {
		box-sizing: border-box;
	}

	.sharebtn {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 40%;
		opacity: 0;
		z-index: 99999;
	}
</style>