<template>
	<view class="box">
		<view class="title">
			<text>距离设备越近，信号值越高排序越靠前</text>
			<text style="color: #0081FF;" @click="refresh">刷新</text>
		</view>
		<view class="" style="height: 80rpx;">
		</view>

		<view class="box1" v-for="item in searchList" @click="conn(item)" hover-class="bind_lock" v-if="!item.isShow">
			<view class="box2">
				<text class="icon-mimasuo" style="font-size: 80rpx;"></text>
				<view class="content">
					<view class="" style="font-size: 34rpx;font-weight: 600;">
						<text>锁名:{{item.LOCKNAME}}</text>
					</view>
					<view class="" style="font-size: 34rpx;font-weight: 600;">
						<text>锁ID:{{item.LOCKID}}</text>
					</view>

					<view class="" style="font-size: 22rpx;margin-top: 10rpx;display: flex;align-items: center;">
						<text style="padding-left: 6rpx;">信号强度:{{100+item.RSSI}}</text>
					</view>
			

				</view>
			</view>
			<view class="icon-tianjia" style="font-size: 40rpx;">

			</view>

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	const plugin = requirePlugin("yayaLock");
	export default {
		data() {
			return {
				searchList:[],
				lockList: [],
				roomNumber: '',
				params: {
					page: 1,
					limit: 1000
				},
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		onLoad(option) {
			console.log(option, 'dd')
			this.roomNumber = option.roomNumber
			this.getYYList();
			this.refreshDeviceList()
		},
		methods: {
			...mapActions('room', ['getLockData']),
			getYYList() {
				uni.showLoading({
					title: '正在搜索设备'
				})
				this.params.page = 1
				this.$iBox.http('getYayaLockList', this.params)({
						method: 'post'
					})
					.then(res => {
						this.lockList = res.data.list
						//首先通过蓝牙搜索
						this.refreshDeviceList()
						uni.hideLoading()
					})
			},
			refresh() {
				this.refreshDeviceList()
			},
			//-----------------------------------------------------丫丫锁--------------------
			refreshDeviceList() {
				var that = this;
				console.log("开锁扫描");
				plugin.scanSmartLock(that.scanDeviceCallBack);
				//scanRefreshLock with params scanTime in millisecond and scanCallBack
				//plugin.scanRefreshLock(15000,this.scanDeviceCallBack)
			},
			/**
			 * 扫描设备回调
			 */
			scanDeviceCallBack(res) {
				var that = this;
				var dev = res.data.msg
				var deviceName = dev.localName
				let macname = dev.localName.slice(2, 14)
				let mac = ""
				for (let i = 0, len = macname.length; i < len; i++) {
					mac += macname[i];
					if (i % 2 == 1 && i <= len - 2) mac += ":";
				}
			
				console.log("scanDeviceCallBack: " + deviceName);
				var rssi = dev.RSSI
				var lockId = plugin.parseDeviceId(deviceName);
				var deviceType = plugin.parseDeviceType(deviceName);
				var devName = plugin.parseDeviceName(deviceName);
				var devMid = dev.deviceId;
				var lock = {
					"LOCKID": lockId,
					"RSSI": rssi,
					"LOCKNAME": mac,
					"DATA": {
						KEYLOCKID: lockId,
						KEYUSERNAME: devName,
						DEVICETYPE: deviceType,
						DEVMID: devMid
					}
				};
				console.log(lock,'lock');
				 
				var lockList = this.searchList;
				//获取当前设备列表的id列表
				var lockIdList = lockList.map(function(item) {
					return item.LOCKID;
				})
				if (lockIdList.indexOf(lockId) < 0 && (deviceType == 11 || deviceType == 12)) {
					//验证当前lock是否在列表里,如果不在,则添加
					lockList.push(lock);
				} else if (lockIdList.indexOf(lockId) != -1 && (deviceType == 11 || deviceType == 12)) {
					//如果在,则更新
					lockList.splice(lockIdList.indexOf(lockId), 1, lock);
				}
				
				if (lockList == null || lockList == undefined || lockList.length < 1) {
					uni.showToast({
						title: '未匹配到设备！'
					})
				} else {
					// 匹配到添加
					for (let item of lockList) {
						item.isShow = false
						this.lockList.forEach(item1 => {
							if (item.LOCKNAME == item1.device_id) {
								item.isShow = true
							}
						})
					}
					 
					this.searchList = lockList.sort(this.compareValues('RSSI','desc'));
				}
			
			},
			compareValues(key, order = 'asc') {
			  return function innerSort(a, b) {
			    if (!a.hasOwnProperty(key) || !b.hasOwnProperty(key)) {
			      // 该属性在任何一个对象上都不存在
			      return 0;
			    }
			
			    const varA = (typeof a[key] === 'string')
			      ? a[key].toUpperCase() : a[key];
			    const varB = (typeof b[key] === 'string')
			      ? b[key].toUpperCase() : b[key];
			
			    let comparison = 0;
			    if (varA > varB) {
			      comparison = 1;
			    } else if (varA < varB) {
			      comparison = -1;
			    }
			    return (
			      (order === 'desc') ? (comparison * -1) : comparison
			    );
			  };
			},
			conn(e) {
				let params = {
					device_id: e.LOCKNAME,
					lock_name: e.LOCKID,
					room_number: this.roomNumber
				}
				this.$iBox.http('addYayaLock', params)({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title: '提示',
						content: '添加成功！',
						showCancel: false,
						success: res => {
							if (res.confirm) {
								uni.navigateBack({
									delta: 2
								})
							}
						}
					})
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.box {
		position: relative;

		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			position: fixed;
			top: 0;
			width: 100vw;
			background-color: #FFFFFF;
			height: 80rpx;
			border-bottom: 1px solid #eee;
		}

	}

	.box1 {
		height: 160rpx;
		width: 100%;
		background-color: #FFFFFF;
		border: 1px solid #eee;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;

	}

	.box2 {
		height: 160rpx;
		width: 100%;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		.content {
			display: flex;
			flex-direction: column;

			justify-content: center;
			padding-left: 20rpx;
		}
	}
</style>
