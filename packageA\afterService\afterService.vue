<template>
	<view class="rateBox">

		<!-- 订单卡片 -->
		<view class="order-info">
			<p style="padding: 0 40rpx;">商品信息</p>
			<list-cell padding="0 40rpx" line-right bgcolor="#ffffff">
				<view class="" style="width: 100%;display: flex;flex-direction: column;background-color: #FFFFFF;">
					<view class="" style="display: flex;align-items: center;margin-top: 40rpx;overflow: hidden;"
						v-for="(item, index) in billDetail.goods_list">
						<view class="" style="display: flex;flex-direction: column;width: 70%;justify-content: center;">
							<image :src="item.cover_pic" mode="" style="width: 60rpx;height: 60rpx;"></image>
							<view class="" style="">{{ item.goods_name }}</view>
						</view>
						<view style="display: flex;flex-shrink: 0;font-weight: 600;margin-left: 40rpx;" class="">
							x{{ item.goods_count }}</view>
						<view style="display: flex;flex-shrink: 0;font-weight: 600;margin-left: 40rpx;">
							{{item.goods_point}}积分+{{ item.goods_price }}元
						</view>
					</view>

					<view
						style="display: flex;justify-content: space-between;align-items: center;font-size: 40rpx;font-weight: 600;">
						<view>合计</view>
						<view style="font-size: 30rpx;">{{ billDetail.point_amount }}积分+{{ billDetail.bill_amount }}元</view>
					</view>
				</view>
			</list-cell>
		</view>
		
		<view class=""
			style="min-height: 280rpx;width: 90vw;background: #fff;border-radius: 20rpx;margin-top: 20rpx;position: relative;display: flex;flex-direction: column;">
			<view style="padding: 30rpx;">
				<uni-data-checkbox v-model="radio1" :localdata="typeList"></uni-data-checkbox> 
			</view>
			<view class=""
				style="min-height: 240rpx;width: 90vw;background: #fff;border-radius: 20rpx;margin-top: 30rpx;">
				<textarea v-model="text_num" maxlength="300" placeholder="请填写售后原因!"
					disable-default-padding="true"
					style="height:220rpx;width: 96%;margin-left: 10rpx;margin-right: 10rpx;border: 1px solid #EEEEEE;padding: 20rpx;" />
			</view>
			<view class="" style="position: absolute;bottom: 20rpx;right: 20rpx;">
				<text style="color:#909399">{{ text_num.length }}/300</text>
			</view>
		</view>
		<view class="" style="width: 100vw;padding: 40rpx;">
			<p>上传图片</p>
		</view>

		<view class="" style="min-height: 220rpx;width: 90vw;display: flex;flex-wrap: wrap;">

			<view class=""
				style="position: relative;height: 220rpx;width: 220rpx;display: flex;align-items: center;justify-content: center;border-radius: 6rpx;"
				v-if="filesArr.length>0" v-for="(item, index) in filesArr">
				<image :src="item" mode="aspectFill" style="height: 200rpx;width: 200rpx;border-radius: 6rpx;"></image>
				<view class="icon-close" style="color: red;position: absolute;right: -5rpx;top: -5rpx;font-size: 36rpx;"
					@click="del(index)">

				</view>
			</view>
			<view class=""
				style="height: 220rpx;width: 220rpx;display: flex;align-items: center;justify-content: center;border-radius: 6rpx;"
				@click="upload">
				<view class="icon-tianjia1" style="font-size: 120rpx;">
					
				</view>
				
			</view>
		</view>
		<view class="" style="height: 300rpx;">

		</view>
		<view class="btn_pay">
			<view class="btn" :style="'background:'+themeColor.main_color+';color:'+themeColor.bg_color"
				@click="submit">
				<text>申请售后</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	import listCell from '@/components/m-list-cell/m-list-cell.vue'
	export default {
		data() {
			return {
				bill_id: '',
				text_num: '',
				billDetail: '',
				id: '',
				if_dis: true,
				if_load: false,
				filesArr: [], 
				img_length: 0,
				action: '',
				radio1:0,
				typeList:[{
					text: '退货（不退款）',
					value: 0
				}, {
					text: '退货退款',
					value: 1
				}, {
					text: '换货',
					value: 2
				}]
			};
		},
		components: {
			listCell
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'hotelList']),
			...mapState('pointShop', ['pointShopDetail']),
		},
		watch: {
			text_num() {
				this.if_dis = false;
			},
			filesArr() {
				this.img_length = this.filesArr.length;
			}
		},
		onLoad(e) {
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					console.log(res.data);
					this.action = res.data + '/wx/Resource/uploadFile'
				}
			});

			this.billDetail = this.pointShopDetail;

		},
		methods: {
			onUploaded(lists) {
				this.filesArr = lists;
			},
			upload(res) {
				let i = 0
				let files = this.filesArr
				uni.chooseMedia({
					count: 9,
					mediaType: ['image', 'video'],
					sourceType: ['album', 'camera'],
					maxDuration: 30,
					camera: 'back',
					success: (res) => {
						console.log(res.tempFiles)

						res.tempFiles.forEach(item => {
							uni.uploadFile({
								url: this.action, //仅为示例，非真实的接口地址
								filePath: item.tempFilePath,
								header: {
									'AUTHTOKEN': this.userInfo.user_token
								},
								formData: {
									'shop_id': this.hotel.id
								},
								name: 'file',
								success: (uploadFileRes) => {
									i++;
									files.push(JSON.parse(uploadFileRes.data).data)
								}
							});
						})

						this.img_length = i
						this.filesArr = files
						console.log(this.filesArr, 'file');
					}
				});

			},
			getRate(e) {
				console.log(e, 'ererer');
				this.score = e
			},
			del(e) {
				this.filesArr.splice(e, 1);
			},
			submit() {
				if (!this.text_num) {
					uni.showToast({
						icon: 'error',
						title: '请填写售后原因！'
					})
					return
				}
				
				let params = {
					bill_id: this.bill_id,
					type: this.radio1,
					reason: this.text_num,
					pic: this.filesArr
				}
				this.$iBox
					.http('applyAfterSale', params)({
						method: 'post'
					})
					.then(res => {
						uni.showModal({
							title: '提示',
							content: '提交成功',
							showCancel: false,
							success() {
								uni.navigateBack()
							}
						})
					});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: #fff;
	}

	.rateBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		.order-info {
			border-radius: 0 0 30rpx 30rpx;
			box-shadow: 0 0 10rpx 0 rgba($color: #333, $alpha: 0.1);
			margin: 30rpx 0;
			background-color: $bg-color-white;
			width: 100%;
			padding: 30rpx;
		}
	}

	.card {
		/* height:200rpx; */
		min-height: 260rpx;
		width: 90vw;
		background: #ffffff;
		border-radius: 20rpx;
		// border-bottom: 1px solid #eee;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
		margin-top: 40rpx;
		margin-bottom: 32rpx;
		padding-left: 10rpx;
		// border: 1px solid #e4e7ed;
		display: flex;
		align-items: ceter;
		justify-content: center;
		padding: 20rpx;
	}

	.l1 {
		width: 40%;
		height: 80%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.l1 image {
		width: 90%;
		height: 200rpx;
		border-radius: 20rpx;
	}

	.l2 {
		width: 60%;
		height: 100%;
		padding-left: 10rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
	}

	.tag {
		width: 80rpx;
		height: 30rpx;
		background: #dd514c;
		font-size: 16rpx;
		color: #ffffff;
		border-radius: 4rpx;
	}

	.l3 {
		width: 24%;
		height: 100%;
	}

	.btn_pay {
		width: 100%;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: 30rpx;

		.btn {
			width: 80%;
			height: 100%;
			border-radius: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>
