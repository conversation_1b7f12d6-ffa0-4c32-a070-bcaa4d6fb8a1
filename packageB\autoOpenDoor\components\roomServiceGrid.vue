<template>
	<view class="serviceBox">
		<!-- 样式一 -->
		<view class="" v-if="styleModel==1">
			<view class="title" :style="{color:themeColor.text_main_color}">
				<text class="icon-fengefu"></text>
				<text>{{name}}</text>
			</view>
			<view class="gridBox" :style="{color:themeColor.text_main_color}">
				<view class="item1" v-for="item in list" @click="toDetail(item)" v-if="item.status ==1">
					<image :src="item.icon" style="width: 80rpx;height: 80rpx;"></image>
					<text>{{item.title}}</text>
				</view>
			</view>
		</view>
		<view class="" v-if="styleModel==2">
			<view class="title" :style="{color:themeColor.text_main_color}" v-if="list.length > 0">
				<text class="icon-fengefu"></text>
				<text>{{name}}</text>
			</view>
			<view class="gridBox1" :style="{color:themeColor.text_main_color}">
				<view class="item1" v-for="item in list" @click="toDetail(item)" v-if="item.status ==1">
					<image :src="item.icon" style="width: 80rpx;height: 80rpx;"></image>
					<text style="font-size: 24rpx;">{{item.title}}</text>
				</view>
			</view>
		</view>
		
		<view class="" v-if="styleModel==3" style="width: 718rpx;min-height: 214rpx;border-radius: 32rpx;padding: 24rpx;margin: 30rpx auto;" 
		:style="{background:themeColor.com_color1+'20'}">
			<view class="gridBox" :style="{color:themeColor.text_main_color}">
				<view class="item1" v-for="item in list" @click="toDetail(item)" v-if="item.status ==1">
					<image :src="item.icon" style="width: 80rpx;height: 80rpx;"></image>
					<text :style="{color:themeColor.text_second_color}">{{item.title}}</text>
				</view>
			</view>
		</view>

		<!-- 分享钥匙按钮 -->
		<m-popup mode="bottom" :show="shareShow" @closePop="closeShare" :customStyles="cusStyle">
			<view class="shareBox">
				<p>分享给他人</p>
				<view class="" style="display: flex;flex-direction: column;align-items: center;">
					<text class="icon-weixin" style="font-size: 80rpx;color: #55aa00;"></text>
					<text>微信</text>
				</view>
				<view class="" style="width: 400rpx;">
					<button open-type="share" type="primary">分享</button>
				</view>
			</view>
		</m-popup>



		<!-- 删除钥匙按钮 -->
		<m-popup mode="bottom" :show="delShow" @closePop="closeDel" :customStyles="cusStyle">
			<view class="delBox">
				<p>删除入住人钥匙</p>
				<view class="user" v-for="item in billDetail.users" v-if="item.is_main==0" @click="delKey(item)">
					<text>姓名:{{item.name}}</text>
					<text style="color: chocolate;font-size: 30rpx;font-weight: 600;">回收钥匙</text>
				</view>
			</view>
		</m-popup>

		<!-- 分享钥匙按钮 -->
		<m-popup mode="bottom" :show="visitorShow" @closePop="closeVisitor" :customStyles="cusStyle">
			<view class="shareBox">
				<p>分享给访客</p>
				<view class="" style="display: flex;flex-direction: column;align-items: center;">
					<text class="icon-weixin" style="font-size: 80rpx;color: #55aa00;"></text>
					<text>微信</text>
				</view>
				<view class="" style="width: 400rpx;">
					<button open-type="share" type="primary">分享</button>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				shareShow: false,
				delShow: false,
				visitorShow: false,
				cusStyle: null
			};
		},
		props: {
			list: {
				type: Array
			},
			styleModel: {
				type: Number
			},
			name: {
				type: String
			},
			billDetail: {
				type: Object
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting'])
		},
		mounted() {
			this.cusStyle = {
				zindex: 1001
			}
		},
		methods: {
			closeShare() {
				this.shareShow = false
			},
			closeDel() {
				this.delShow = false
			},
			closeVisitor() {
				this.visitorShow = false
			},
			delKey(e) {

				this.$iBox.http('delRoomUser', {
					room_user_id: e.id
				})({
					method: 'post'
				}).then(res => {
					uni.showToast({
						icon: 'none',
						title: '回收钥匙成功！'
					})
					this.delShow = false
				})
			},
			toDetail(e) {

				let ifMain = this.shopSetting.filter(item => {
					return item.sign == 'main_user_check_out'
				})[0].property.status

				let user = this.billDetail.users.filter(item => {
					return item.common_code == this.userInfo.common_code
				})[0]

				if (e.path) {
					if(e.sign == 'self_change_room'){
						uni.navigateTo({
							url: '/' + e.path + '?bill_id=' + this.billDetail.id
						})
					}else {
						if (user.is_main || !ifMain) {
							uni.navigateTo({
								url: '/' + e.path + '?bill_id=' + this.billDetail.id
							})
						} else {
							uni.showToast({
								icon: 'none',
								title: '只有主入住人可操作！'
							})
						}
					}
					

				} else if (e.sign == 'share_key') {
					if (((user.is_main || !ifMain) && !this.billDetail.team_id)) {
						
						if (this.billDetail.bill_status == 4 ) {
							this.$iBox.http('createRoomBillQrCode', {
								bill_id: this.billDetail.id,
								type: e.sign == 'share_key' ? 2 : 3
							})({
								method: 'post'
							}).then(res => {
								let itemCode = {
									share_code:res.data.code,
									shareSign:e.sign,
									team_id:res.data.team_id
								}
								this.shareShow = true
								this.$emit('shareTo', itemCode)
							})
						} else {
							uni.showModal({
								title: '提示',
								content: '分享失败，请重新分享!',
								showCancel: false,
								success: res => {
									uni.reLaunch({
										url: '/pages/myRoom/myRoom'
									})
								}
							})
						}

						
					} else if (!user.is_main && ifMain && !this.billDetail.team_id) {
						uni.showToast({
							icon: 'none',
							title: '只有主入住人可操作！'
						})
					} else if (this.billDetail.team_id) {
						uni.showToast({
							icon: 'none',
							title: '团队订单不可操作'
						})
					}

				} else if (e.sign == 'del_key') {
					if ((user.is_main || !ifMain) && !this.billDetail.team_id) {
						this.delShow = true
						this.$emit('shareTo', e.sign)
					} else if (!user.is_main && ifMain && !this.billDetail.team_id) {
						uni.showToast({
							icon: 'none',
							title: '只有主入住人可操作！'
						})
					} else if (this.billDetail.team_id) {
						uni.showToast({
							icon: 'none',
							title: '团队订单不可操作'
						})
					}

				} else if (e.sign == 'visitor') {
					if ((user.is_main || !ifMain) && !this.billDetail.team_id) {
						
						if (this.billDetail.bill_status == 4 ) {
							this.$iBox.http('createRoomBillQrCode', {
								bill_id: this.billDetail.id,
								type: e.sign == 'share_key' ? 2 : 3
							})({
								method: 'post'
							}).then(res => {
								let itemCode = {
									share_code:res.data.code,
									shareSign:e.sign,
									team_id:res.data.team_id
								}
								this.visitorShow = true
								this.$emit('shareTo', itemCode)
							})
						} else {
							uni.showModal({
								title: '提示',
								content: '分享失败，请重新分享!',
								showCancel: false,
								success: res => {
									uni.reLaunch({
										url: '/pages/myRoom/myRoom'
									})
								}
							})
						}
					} else if (!user.is_main && ifMain && !this.billDetail.team_id) {
						uni.showToast({
							icon: 'none',
							title: '只有主入住人可操作！'
						})
					} else if (this.billDetail.team_id) {
						uni.showToast({
							icon: 'none',
							title: '团队订单不可操作'
						})
					}

				} else {
					uni.showToast({
						icon: 'none',
						title: '暂未上线！'
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.serviceBox {
		// background-color: #ffffff;

		.title {
			padding: 30rpx 30rpx 0rpx 30rpx;
		}

		.gridBox {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			padding: 0 30rpx;

			.item1 {
				display: flex;
				flex-direction: column;
				width: 33%;
				height: 150rpx;
				align-items: center;
				justify-content: space-around;
				margin-bottom: 30rpx;
				margin-top: 20rpx;
			}
		}

		.gridBox1 {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			padding: 0 30rpx;

			.item1 {
				display: flex;
				flex-direction: column;
				width: 20%;
				height: 150rpx;
				align-items: center;
				justify-content: space-around;
				margin-bottom: 10rpx;
				margin-top: 10rpx;
				font-size: 24rpx;
			}
		}
	}

	.shareBox {
		width: 100%;
		height: 400rpx;
		border-radius: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		justify-content: space-around;
	}

	.delBox {
		width: 100%;
		height: 600rpx;
		border-radius: 40rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		// justify-content: space-around;

		.user {
			height: 80rpx;
			width: 700rpx;
			border: 1px solid #e4e7ed;
			border-radius: 10rpx;
			margin: 20rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 10rpx;
			background-color: #f3f4f6;
		}
	}
</style>