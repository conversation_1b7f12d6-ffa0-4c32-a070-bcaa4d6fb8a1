<template>
	<view class="deviceBox">
		<!-- 样式一 -->
		<view class="box1" v-if="styleModel==1">
			<view class="title" :style="{color:themeColor.text_main_color}">
				<text class="icon-fengefu"></text>
				<text>{{name}}</text>
			</view>
			<view class="deviceItemBox" :style="{color:themeColor.text_main_color}">
				<block class="" :key="index" v-for="(item, index) in list">
					<lock v-if="item.sign=='blue_tooth_lock'" :styleMode='1' :bill_detail="billDetail"></lock>
				</block>
			</view>
		</view>

		<view class="box2" v-if="styleModel==2">
			<!-- <text class="icon-jiantou" style="transform: rotateY(180deg);font-size: 44rpx;" @click="pre()"></text> -->
			<view class="" style="display: flex;align-items: center;justify-content: center;">
				<block class="" :key="index" v-for="(item, index) in list" style="display: flex;align-items: center;">
					<lock v-if="item.sign=='blue_tooth_lock'" :styleMode='styleModel' :bill_detail="billDetail"></lock>
				</block>
			</view>
			<!-- <text class="icon-jiantou" style="font-size: 44rpx;" @click="next()"></text> -->
		</view>
		
		<view class="box3" v-if="styleModel==3">
			<view class="" :style="{background: 'radial-gradient(100% 114.19% at 0% 0%, '+themeColor.bg_main_color+' 0%, '+themeColor.bg_main1_color+' 100%)'
}" style="height: 240rpx;width: 718rpx;border-top-right-radius: 32rpx;border-top-left-radius: 32rpx;padding: 48rpx 32rpx;display: flex;align-items: center;justify-content: space-between;">
				<view class="" style="">
					<p style="font-size: 96rpx;" :style="{color:themeColor.bg_color}">{{billDetail.room_number}}</p>
					<p style="font-size: 40rpx;" :style="{color:themeColor.bg_color}">
						{{billDetail.building_name}}{{billDetail.floor_name}}</p>
				</view>
				<view class="">
					<view class="" style="display: flex;color: #FFFFFF;width: 300rpx;justify-content: space-between;">
						<view class=""
							style="display: flex;align-items: center;justify-content: ;flex-direction: column;"
							@click="showFloor">
							<view class="icon-erweima" style="font-size: 90rpx;color: #FFFFFF;"></view>
							<text style="font-size: 28rpx;margin-top: 30rpx;">识别乘梯</text>
						</view>
						<view class="" style="height: 80rpx;width:1px;background: #FFFFFF;margin-top: 20rpx;">

						</view>
						<view class=""
							style="display: flex;align-items: center;justify-content: center;flex-direction: column;"
							@click="showFood">
							<view class="icon-erweima" style="font-size: 90rpx;color: #FFFFFF;"></view>
							<text style="font-size: 28rpx;margin-top: 30rpx;">用餐服务</text>
						</view>
					</view>
				</view>
			</view>
			<view class="">
				<block class="" :key="index" v-for="(item, index) in list" style="display: flex;align-items: center;">
					<lock v-if="item.sign=='blue_tooth_lock'" :styleMode='styleModel' :bill_detail="billDetail"></lock>
				</block>
			</view>
		</view>
		
		<m-popup :show="show3" mode="center" @closePop="closePop3">
			<view
				style="display: flex;flex-direction: column;align-items: center;position: relative;width: 700rpx;padding: 20rpx;">
				<view class="icon-close" style="position: absolute;right: 20rpx;top:20rpx;font-size: 46rpx;"
					@click="closePop3">
		
				</view>
				<p style="font-size: 44rpx;font-weight: 600;color: darkslateblue;" v-if="billDetail.floor_name">
					您的入住楼层:{{billDetail.floor_name}}</p>
				<p style="margin: 20rpx auto;font-size:34rpx;font-weight:600">扫码后点击楼层</p>
				<image :src="url" style="height: 300rpx;width: 300rpx;margin-bottom: 30rpx;" mode=""></image>
				<view class="" style="margin: 30rpx auto;display: flex;flex-direction: column;">
					<text style="font-size: 28rpx;color: #CD1225;">*若二维码没生效请点击下方刷新按钮</text>
					<view class="" style="margin: 30rpx auto;" @click="reload">
						<uni-icons type="refreshempty" size="23"></uni-icons>
						<text style="font-size: 34rpx;color: #2979ff;">刷新</text>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;justify-content: center;"
					v-if="floor_list[0].mac_config">
					<view class="" @click="bluthOpen()"
						style="width: 600rpx;height: 80rpx;display: flex;align-items: center;justify-content: center;border-radius: 30rpx;color: #FFFFFF;background-color: #00aa00">
						<text>{{ble_adv_sta?'正在启动电梯,请等待...':'点击蓝牙启动电梯'}}</text>
						<text v-if="timer_id_adv>0&&timer_id_adv<4">(倒计时{{timer_id_adv}}秒)</text>
					</view>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	import lock from './lock/lock.vue';
	import codePop from '../codePop.vue';
	import qrcode1 from '@/packageA/plugins/qrcode.js';
	import QR from '@/packageA/plugins/wxqrcode.js';
	const bgAudioManager = uni.getBackgroundAudioManager();
	export default {
		data() {
			return {
				itemNum: 1,
				show3:false,
				show3: false,
				url: '',
				server: null,
				ble_adv_sta: false,
				timer_id_adv: 5,
				breakfastSetting:null
			};
		},

		props: {
			list: {
				type: Array
			},
			styleModel: {
				type: Number
			},
			name: {
				type: String
			},
			billDetail: {
				type: Object
			},
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('room', ['hardWareList']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting']),
		},
		components: {
			lock,
			codePop
		},
		mounted() {
			wx.openBluetoothAdapter({
				mode: 'peripheral',
				success: e => {
					console.log('初始化蓝牙成功:' + e.errMsg);
				},
				fail: e => {
					console.log('初始化蓝牙失败，错误码：' + (e.errCode || e.errMsg));
				}
			});
			
			this.$iBox
				.http('getLiftConfigList', {
					shop_id: this.hotel.id
				})({
					method: 'post'
				})
				.then(res => {
					this.floor_list = res.data
					// 如果存在序列号则开启蓝牙广播
				
				})
				.catch(function(error) {
					console.log('33434', error);
				});
		},
		methods: {

			next() {

				if (this.itemNum == 2) {
					--this.itemNum
				} else if (this.itemNum == 3) {
					this.itemNum == 1
				} else {
					++this.itemNum
				}
				console.log(this.itemNum);
			},
			pre() {
				if (this.itemNum == 2) {
					--this.itemNum
				} else if (this.itemNum == 3) {
					this.itemNum == 1
				} else {
					++this.itemNum
				}
			},
			closePop3() {
				console.log(this.server, 'server');
				this.show3 = false
				if (this.server) {
					this.stopAdvertising()
				}
			
			},
			reload() {
				this.showPop()
			},
			showPop() {
				if (this.floor_list.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '本酒店暂无梯控'
					})
					return
				}
				console.log(this.floor_list, 'md');
			
			
				let that = this
				let a = []
				let floorList = []
				this.billDetail.floor_number ? floorList.push(this.billDetail.floor_number.toString(16)) : ''
				this.floor_list.forEach((item, index) => {
					if (item.public_floor) {
						item.public_floor.forEach(item1 => {
							floorList.push(item1)
						})
					}
			
				})
				floorList = [...new Set(floorList)]
			
				floorList.forEach(item => {
					let floor = {
						floor: item
					}
					a.push(floor)
			
				})
			
				let floors = []
			
				this.floor_list.forEach(item => {
					let floor = {}
					floor.sn = item.sn
					floor.floors = a
					floors.push(floor)
				})
			
			
				let lifts = {
					floors: floors,
					all_lift: 1,
					direct_arrival: 0 //是否直达
				}
				let url = ''
				console.log(floors, 'dd', this.hotel.shop_name == '豪瑞特酒店');
				url = qrcode1.generateAccessCode('a', new Date(), 30, Number(this.userInfo.id), lifts, [])
			
				this.url = QR.createQrCodeImg(url.encrypt)
			
			},
			bluthOpen() {
				// 如果存在序列号则开启蓝牙广播
				if (this.floor_list[0].mac_config && this.timer_id_adv == 0) {
					console.log('klsdsd');
					uni.showLoading({
						title: '正在启动电梯，请稍等'
					})
					let floor = this.billDetail.floor_number.toString(16)
					this.startPerphiAdv(floor)
					setTimeout(res => {
						console.log(this.server, ' this.server');
						if (this.server) {
							this.stopAdvertising()
						}else{
							this.ble_adv_sta = false
						}
			
					}, 15000)
			
					let a = setTimeout(res => {
						if (this.timer_id_adv > 0) {
							--this.timer_id_adv
						} else {
							this.timer_id_adv = 0
							clearTimeout(a)
							uni.hideLoading()
						}
			
			
					}, 15000)
			
				}
			},
			showFloor() {
				this.showPop()
				this.show3 = true
				// 开锁语音
				bgAudioManager.title = '提醒'
				bgAudioManager.epname = '提醒'
				bgAudioManager.singer = '提醒'
				bgAudioManager.src =
					'https://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_mp3/%E6%A2%AF%E6%8E%A71.mp3'
			
			
			},
			showFood() {
			
				// 获取早餐设置
				this.breakfastSetting = this.shopSetting.filter(item => {
					return item.sign == 'breakfast_setting'
				})[0].property
			
				this.startPerphiAdv(this.breakfastSetting.floor_number)
				setTimeout(res => {
					if (this.server) {
						this.stopAdvertising()
					}
			
				}, 5000)
				uni.navigateTo({
					url: '/packageA/breakfastCard/breakfastCard'
				})
			},
			startAdvertising(floor) {
				// 屏幕常亮下,小程序的广播持续时间：3分钟
				// [18:09:20.076 ~ 18:12:19.483]
				let mac = this.floor_list[0].mac_config
				let device_id = mac
				let sign_num = ''
				for (var i = 0; i < 12 - mac.length; i++) {
					console.log(i);
					device_id = '0' + device_id
				}
			
				let floor_num = floor.toString(16).length < 2 ? '0' + floor : floor
				device_id = 'BE' + device_id + '02' + floor_num + 'EE'
				console.log(device_id, 'tty');
				let itotal = 0,
					len = device_id.length,
					num = 0;
				while (num < len) {
					let s = device_id.substring(num, num + 2);
					itotal += parseInt(s, 16)
					num = num + 2;
				}
				let mode = itotal % 256;
				let shex = mode.toString(16)
				let iLen = shex.length;
				if (iLen < 2) {
					shex = '0' + shex
				}
			
				device_id = (device_id.slice(0, -2) + shex).toUpperCase()
				console.log(device_id, 'arrNum');
				if (this.server) {
					this.server.startAdvertising({
						advertiseRequest: {
							connectable: false,
							deviceName: device_id
						},
						powerLevel: "high",
						success: res => {
							console.log(res, '返回广播')
			
						}
					})
				}
			
			},
			createBLEPeripheralServer(floor) {
				console.log('kaishi')
				wx.createBLEPeripheralServer({
					success: (res) => {
						console.log('kaishi1')
						console.log("创建BLE外设【成功】", res.server)
						this.server = res.server
					},
					fail: (res) => {
						console.log('kaishi2')
						console.log("创建BLE外设【失败】", res)
					},
					complete: (res) => {
						console.info("创建BLE外设【完成】", res)
						console.log("开始广播")
			
						this.startAdvertising(floor);
					}
				})
			},
			
			startPerphiAdv(floor) {
				console.log(floor, 'guangbo');
				this.ble_adv_sta = true
				if (!this.server) {
					this.createBLEPeripheralServer(floor);
			
				} else {
					console.log("准备广播，已有服务无需再建，serverId:", this.server.serverId)
					console.log("开始广播")
			
					this.startAdvertising(floor);
				}
			
			},
			stopAdvertising() {
				if (JSON.stringify(this.server) == "{}") {
					console.log("停止广播，暂未创建服务")
					return
				}
				this.ble_adv_sta = false
				this.server.stopAdvertising({
					success: (res) => {
						console.log("关闭BLE广播【成功】")
					},
					fail: (res) => {
						console.log("关闭BLE广播【失败】", res)
					},
					complete: (res) => {
						console.info("关闭BLE广播【完成】", res)
					}
				})
			},
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.deviceBox {
		margin-top: 40rpx;

		.box1 {
			display: flex;
			// align-items: center;
			flex-direction: column;
			justify-content: space-between;

			.title {
				padding: 30rpx 30rpx 0rpx 30rpx;
			}

			.deviceItemBox {
				display: flex;
				align-items: center;
				justify-content: space-between;
				flex-wrap: wrap;
				padding: 0 30rpx;

				.item1 {
					height: 180rpx;
					width: 330rpx;
					margin-top: 30rpx;
					border-radius: 20rpx;
					box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

					&_title {
						padding: 20rpx;
						font-size: 28rpx;
					}

					&_content {
						padding: 0rpx 30rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.img_lock {
							height: 80rpx;
							width: 80rpx;
						}

						&_text {
							display: flex;
							flex-direction: column;
						}
					}
				}
			}
		}


		.box2 {
			min-height: 300rpx;
			width: 100%;
			display: flex;
			align-items: center;
			// justify-content: center;
			padding: 30rpx;

			&_title {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
				justify-content: center;
				margin: 40rpx 0;
				width: 100%;

				.title {
					width: fit-content;
					padding: 0 20rpx;
					height: 60rpx;
					border-radius: 30rpx;
					background: #55aa00;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx;
				}
			}


		}

		.box3 {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
		}
	}
</style>