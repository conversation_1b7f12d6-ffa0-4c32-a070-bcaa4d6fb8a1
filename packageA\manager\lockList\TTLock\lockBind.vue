<template>
	<view class="box">
		<text style="margin-top: 60rpx;">激活设备</text>
		
		<text class="icon-mimasuo" style="font-size: 90rpx;"></text>
		<text style="margin-top: 60rpx;">激活方式：触摸/按下数字键、或刷卡</text>
		
		<view style="width: 400rpx;margin: 100rpx auto;">
			<button type="primary"  @click="bindLock">下一步</button>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				roomNumber:''
			}
		},
		onLoad(option) {
			this.roomNumber = option.roomNumber
		},
		methods: {
			bindLock(){
				uni.navigateTo({
					url:'./findLock?roomNumber=' + this.roomNumber
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		display: flex;
		flex-direction: column;
		align-items: center;
	}
</style>
