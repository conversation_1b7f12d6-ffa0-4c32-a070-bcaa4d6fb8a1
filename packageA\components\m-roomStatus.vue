<template>
	<view class="box">
		<view class="roomBox">
			<view class="roomBoxContent" v-for="(item, index) in roomList" :key="index" >
				<p class="title" :style="{color:themeColor.main_color}">{{item.building}}({{item.room_count}})</p>
				<view class="roomBoxContentBox" v-for="(item1, index1) in item.floor_list" :key="index1" >
					<m-divider>{{item1.floor}}({{item1.room_count}})</m-divider>
					<view class="statusBox">
						<view class="roomBox" v-for="(item2, index2) in item1.room_list" :key="index2"  @click="roomStatusGet(item2)">
							<view class="room" :style="{background:item2.room_status_color}">
								<view class=""
									style="display: flex;align-items: center;justify-content: space-between;">
									<text>{{item2.room_number}}</text>
									<text v-if="item2.bill_info.connect_code"
										style="font-size: 32rpx;font-weight: 600;color: red">联</text>
								</view>
								<p
									style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;font-size: 24rpx;">
									{{item2.room_type_name}}
								</p>
								<view class="" style="display: flex;align-items: center;margin-top: 8rpx;text-overflow: ellipsis;overflow: hidden;white-space: nowrap;">
									<p style="font-size: 24rpx;" v-if="item2.bill_info&&item2.bill_info.bill_status==3&&item2.bill_info.room_user.length==0">{{item2.bill_info.link_man}}</p>
									<p style="font-size: 24rpx;" v-else><text v-for="(name,index6) in item2.bill_info.room_user">{{name.name}}{{index6>0?',':''}}</text></p>
									<text v-if="item2.bill_info.bill_source_name"
										style="font-size: 24rpx;">({{item2.bill_info.bill_source_name}})</text>
								</view>
								<p style="font-size: 24rpx;">{{item2.bill_info.tempGrade_name}}</p>
								<view class="" style="display: flex;align-items: center;width: 100%;">
									<view class="circle" v-if="item2.bill_info&&item2.bill_info.bill_status == 3">
										<p class="timeOver"
											:style="leaveAndTime(item2.bill_info.enter_time_plan).includes('-')||leaveAndTime(item2.bill_info.enter_time_plan).includes('m')?'background:#ff0105':'background:#00a61b'">
											{{leaveAndTime(item2.bill_info.enter_time_plan)}}
										</p>
										<view class="big-circle"
											v-if="leaveAndTime(item2.bill_info.enter_time_plan).includes('-')||leaveAndTime(item2.bill_info.enter_time_plan).includes('m')">
										</view>
									</view>
									<view class="circle" v-if="item2.bill_info&&item2.bill_info.bill_status == 4">
										<p class="timeOver"
											:style="leaveAndTime(item2.bill_info.leave_time_plan).includes('-')||leaveAndTime(item2.bill_info.leave_time_plan).includes('m')?'background:#ff0105':'background:#00a61b'">
											{{leaveAndTime(item2.bill_info.leave_time_plan)}}
										</p>
										<view class="big-circle"
											v-if="leaveAndTime(item2.bill_info.leave_time_plan).includes('-')||leaveAndTime(item2.bill_info.leave_time_plan).includes('m')">
										</view>
									</view>
									<view class="timeOver1"
										v-if="item2.bill_info&&item2.bill_info.total_bill_balance < 0">
										欠
									</view>

									<view class="futureStatusBox" v-if="item2.room_status_record_future.length > 0"
										:style="{background:item2.room_status_record_future[0].color}">
										{{futureTime(item2.room_status_record_future[0].start_time_plan)}}
									</view>
								</view>


								<view class="status" style="" :style="'color:#ffffff;background:'+item2.clear_color">
									<text>{{item2.clear_status_name.substring(0, 1)}}</text>
								</view>
								<!-- 如果是脏状态强化显示 -->
								
							</view>
						</view>

					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		name: "m-roomStatus",
		props: {
			roomList: {
				type: Array
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		data() {
			return {

			};
		},
		mounted() {

		},
		methods: {
			roomStatusGet(e) {
				this.$emit('roomGet', e)
			},

			leaveAndTime(e) {
				let lastTime = this.$moment(e * 1000).locale('zh-cn').fromNow()
				console.log(lastTime);
				if(lastTime=='1 小时'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "hours")+'小时';
				}else if(lastTime=='1 天'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}else if(lastTime.includes('月')){
					
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}
				if (lastTime.includes('前')) {
					if (lastTime.includes('分')) {
						lastTime = '-' + lastTime.split('分')[0].trim() + 'm'
					} else if (lastTime.includes('时')) {
						lastTime = '-' + lastTime.split('小时')[0].trim() + 'h'
					} else if (lastTime.includes('天')) {
						lastTime = '-' + lastTime.split('天')[0].trim() + 'd'
					} else if (lastTime.includes('月')) {
						lastTime = '-' + lastTime.split('个')[0].trim() + '月'
					}
				} else {
					if (lastTime.includes('分')) {
						lastTime = lastTime.split('分')[0].trim() + 'm'
					} else if (lastTime.includes('时')) {
						lastTime = lastTime.split('小时')[0].trim() + 'h'
					} else if (lastTime.includes('天')) {
						lastTime = lastTime.split('天')[0].trim() + 'd'
					} else if (lastTime.includes('月')) {
						lastTime = lastTime.split('个')[0].trim() + '月'
					}
				}
				return lastTime;
			},
			futureTime(e) {
				let startTime = this.$moment(this.$moment().format('YYYY-MM-DD'), 'YYYY-MM-DD').unix()
				let endTime = this.$moment(e * 1000).format('YYYY-MM-DD')
				console.log(endTime,'endTime');
				if (this.$moment(startTime * 1000).format('YYYY-MM-DD') == endTime) {
					return '今'
				}else if (this.$moment(startTime * 1000).add(1, 'day').format('YYYY-MM-DD') == endTime) {
					return '明'
				} else if (this.$moment(startTime * 1000).add(2, 'day').format('YYYY-MM-DD') == endTime) {
					return '后'
				} else {
					return '未'
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {

		display: flex;
		flex-wrap: wrap;
		padding: 30rpx 0;

		.roomBox {
			width: 100%;

			.roomBoxContent {
				width: 100%;

				.title {
					// padding: 30rpx 0;
					font-size: 36rpx;
					font-weight: 600;
				}

				display: flex;
				flex-direction: column;

				.roomBoxContentBox {
					width: 100%;

					.statusBox {
						width: 100%;
						display: flex;
						flex-wrap: wrap;
						align-items: center;
						// justify-content: center;

						.roomBox {
							width: 33%;
							height: 280rpx;
							display: flex;
							align-items: center;
							justify-content: center;

							.room {
								width: 204rpx;
								min-height: 230rpx;
								border-radius: 8rpx;
								padding: 8rpx;
								color: #fff;
								position: relative;

								.circle {
									width: 48rpx;
									height: 48rpx;
									position: relative;

									.big-circle {
										position: absolute;
										top: 10rpx;
										left: 0px;
										width: 100%;
										height: 100%;
										border-radius: 50%;
										background: #FF0033;
										animation: twinkling 1s infinite ease-in-out;
										animation-fill-mode: both;
									}

									.timeOver {

										border-radius: 50%;
										margin-top: 12rpx;

										width: 48rpx;
										height: 48rpx;
										border-radius: 50%;
										color: #fff;
										font-weight: 600;
										display: flex;
										align-items: center;
										justify-content: center;
										font-size: 18rpx;
									}

									@keyframes twinkling {
										0% {
											opacity: 0.2;
											transform: scale(1);
										}

										50% {
											opacity: 0.5;
											transform: scale(1.12);
										}

										60% {
											opacity: 0.4;
											transform: scale(1.32);
										}

										70% {
											opacity: 0.3;
											transform: scale(1.52);
										}

										100% {
											opacity: 0.2;
											transform: scale(1);
										}
									}
								}

								.timeOver1 {

									border-radius: 50%;
									margin-top: 20rpx;
									margin-left: 4rpx;
									background: #FF0033;
									width: 48rpx;
									height: 48rpx;
									border-radius: 50%;
									color: #fff;
									font-weight: 600;
									display: flex;
									align-items: center;
									justify-content: center;
									font-size: 18rpx;
								}

								.status {
									position: absolute;
									bottom: 10rpx;
									right: 10rpx;
									// width: fit-content;
									padding: 2rpx 6rpx;
									border-radius: 8rpx;
									font-size: 24rpx;
									font-weight: 600;
								}

								.futureStatusBox {
									width: 42rpx;
									height: 42rpx;
									border-radius: 50%;
									color: #fff;
									font-weight: 600;
									display: flex;
									align-items: center;
									justify-content: center;
									font-size: 18rpx;
									position: absolute;
									bottom: 8rpx;
									right: 51rpx;
								}
							}
						}
					}
				}
			}


		}
	}
</style>
