<template>
	<view class="all_box" v-if="show">
		<!-- 模式一 -->
		<view v-if="mode==1" class="m-hotelCard_box"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="tabs">
				<template v-for="item in list">
					<view @click="changeTab(item)" class="tab"
						:style="activeIndex != item.id?'background:'+themeColor.main_color+'80':''"
						:class="{ 'active': activeIndex == item.id }">
						<text style="font-size: 32rpx;"
							:style="activeIndex != item.id?'color:#FFFFFF':'#000000'">{{item.name}}</text>
						<view class="" v-if="activeIndex == item.id"
							style="position: absolute;margin: 0 auto;bottom: 10rpx;left: 0;right: 0;width: 100%;display: flex;align-items: center;justify-content: center;">
							<view class="icon-banyuan2-copy" style="font-size: 40rpx;"
								:style="{color:themeColor.main_color}" v-if="activeIndex == item.id">

							</view>
						</view>

					</view>
				</template>
			</view>
			<template class="" v-if="activeIndex==1">
				<view class="m-hotelCard_box_title" @click="changeHotel" :style="{color:themeColor.text_main_color}">
					<view class="m-hotelCard_box_title_hotel">
						<view class="icon-jiudian" style="font-size: 44rpx;"
							:style="{color:themeColor.text_main_color}">

						</view>
						<text class="m-hotelCard_box_title_hotel_text">{{hotel_zong_name}}</text>
					</view>
					<view class=""
						style="display: flex;justify-content: space-between;align-items: center;padding:30rpx;"
						:style="{'border-bottom':'1px solid ' + themeColor.border_color}">
						<view class="" style="display: flex;flex-direction: column;">
							<p style="font-size: 38rpx;">{{hotel.shop_name}}</p>
							<view style="font-size: 26rpx;display: flex;align-items: center;margin-top: 8rpx;">
								<text v-if="hotel.distance>1000">离我{{(hotel.distance/1000).toFixed(2)}}km</text>
								<text v-if="hotel.distance<=1000">离我{{(hotel.distance).toFixed(2)}}m</text>
								<view
									style="margin-left: 30rpx;max-width: 380rpx; overflow: hidden;text-overflow: ellipsis;white-space: nowrap; ">
									{{hotel.address}}</view>
							</view>
						</view>
						<view class="icon-jiantou" style="font-size: 30rpx;"
							:style="{color:themeColor.text_title_color}">

						</view>
					</view>

				</view>
				<m-dateCard :show="true" :mode="3"></m-dateCard>
				<view class="m-hotelCard_box_button" @click="hotelDetail"
					:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
					<text style="color: #FFFFFF;">搜索</text>
				</view>
				<view class="m-hotelCard_box_else">
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-xiaoshi" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						24小时客服
					</view>
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-jinggao" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						会员折扣
					</view>
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-youhuiquan" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						订房优惠
					</view>
				</view>
			</template>
			<template v-if="activeIndex==2">
				<m-searchAuto></m-searchAuto>
			</template>

		</view>

		<!-- 模式二 -->
		<view v-if="mode==2" class="m-hotelCard_box"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="tabs">
				<template v-for="item in list">
					<view @click="changeTab(item)" class="tab"
						:style="activeIndex != item.id?'background:'+themeColor.main_color+'80':''"
						:class="{ 'active': activeIndex == item.id }">
						<text style="font-size: 32rpx;"
							:style="activeIndex != item.id?'color:#FFFFFF':'#000000'">{{item.name}}</text>
						<view class="" v-if="activeIndex == item.id"
							style="position: absolute;margin: 0 auto;bottom: 10rpx;left: 0;right: 0;width: 100%;display: flex;align-items: center;justify-content: center;">
							<view class="icon-banyuan2-copy" style="font-size: 40rpx;"
								:style="{color:themeColor.main_color}" v-if="activeIndex == item.id">

							</view>
						</view>

					</view>
				</template>
			</view>
			<template class="" v-if="activeIndex==1">
				<view class="m-hotelCard_box_title" @click="changeHotel" :style="{color:themeColor.text_main_color}">

					<view class=""
						style="display: flex;justify-content: space-between;align-items: center;padding:30rpx;"
						:style="{'border-bottom':'1px solid ' + themeColor.border_color}">
						<view class="" style="display: flex;flex-direction: column;">
							<p style="font-size: 38rpx;">{{hotel.shop_name}}</p>
							<view style="font-size: 26rpx;display: flex;align-items: center;margin-top: 8rpx;">
								<text v-if="hotel.distance>1000">离我{{(hotel.distance/1000).toFixed(2)}}km</text>
								<text v-if="hotel.distance<=1000">离我{{(hotel.distance).toFixed(2)}}m</text>
								<view
									style="margin-left: 30rpx;max-width: 380rpx; overflow: hidden;text-overflow: ellipsis;white-space: nowrap; ">
									{{hotel.address}}</view>
							</view>
						</view>
						<view class="icon-jiantou" style="font-size: 30rpx;"
							:style="{color:themeColor.text_title_color}">

						</view>
					</view>

				</view>
				<m-dateCard :show="true" :mode="3"></m-dateCard>
				<view class="m-hotelCard_box_button" @click="hotelDetail"
					:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
					<text style="color: #FFFFFF;">搜索</text>
				</view>
				<view class="m-hotelCard_box_else">
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-xiaoshi" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						24小时客服
					</view>
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-jinggao" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						会员折扣
					</view>
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-youhuiquan" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						订房优惠
					</view>
				</view>
			</template>
			<template v-if="activeIndex==2">
				<m-searchAuto></m-searchAuto>
			</template>

		</view>


		<!-- 模式三 -->
		<view v-if="mode==3" class="m-hotelCard_box"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">

			<view class="m-hotelCard_box_title" @click="changeHotel" :style="{color:themeColor.text_main_color}">
				<view class="m-hotelCard_box_title_hotel">
					<view class="icon-jiudian" style="font-size: 44rpx;" :style="{color:themeColor.text_main_color}">

					</view>
					<text class="m-hotelCard_box_title_hotel_text">{{hotel_zong_name}}</text>
				</view>
				<view class="" style="display: flex;justify-content: space-between;align-items: center;padding:30rpx;"
					:style="{'border-bottom':'1px solid ' + themeColor.border_color}">
					<view class="" style="display: flex;flex-direction: column;">
						<p style="font-size: 38rpx;">{{hotel.shop_name}}</p>
						<view style="font-size: 26rpx;display: flex;align-items: center;margin-top: 8rpx;">
							<text v-if="hotel.distance>1000">离我{{(hotel.distance/1000).toFixed(2)}}km</text>
							<text v-if="hotel.distance<=1000">离我{{(hotel.distance).toFixed(2)}}m</text>
							<view
								style="margin-left: 30rpx;max-width: 380rpx; overflow: hidden;text-overflow: ellipsis;white-space: nowrap; ">
								{{hotel.address}}</view>
						</view>
					</view>
					<view class="icon-jiantou" style="font-size: 30rpx;" :style="{color:themeColor.text_title_color}">

					</view>
				</view>

			</view>
			<m-dateCard :show="true" :mode="3"></m-dateCard>
			<view class="m-hotelCard_box_button" @click="hotelDetail"
				:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
				<text style="color: #FFFFFF;">搜索</text>
			</view>
			<view class="m-hotelCard_box_else">
				<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
					<view class="icon-xiaoshi" style="font-size: 28rpx;padding-right: 8rpx;">
					</view>
					24小时客服
				</view>
				<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
					<view class="icon-jinggao" style="font-size: 28rpx;padding-right: 8rpx;">
					</view>
					会员折扣
				</view>
				<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
					<view class="icon-youhuiquan" style="font-size: 28rpx;padding-right: 8rpx;">
					</view>
					订房优惠
				</view>
			</view>

		</view>


		<!-- 模式四 -->
		<view v-if="mode==4" class="m-hotelCard_box"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">

			<view class="m-hotelCard_box_title" @click="selectCity" :style="{color:themeColor.text_main_color}">

				<view class="" style="display: flex;justify-content: space-between;align-items: center;padding:30rpx;"
					:style="{'border-bottom':'1px solid ' + themeColor.border_color}">
					<view class="" style="display: flex;align-items: center)">
						<p style="font-size: 38rpx;">{{city.name}}</p>
						<view style="font-size: 32rpx;display: flex;align-items: center;">
							<view
								style="margin-left: 30rpx;max-width: 440rpx; overflow: hidden;text-overflow: ellipsis;white-space: nowrap; ">
								{{hotel.shop_name}}</view>
						</view>
					</view>
					<view class="icon-jiantou" style="font-size: 30rpx;" :style="{color:themeColor.text_title_color}">

					</view>
				</view>

			</view>
			<m-dateCard :show="true" :mode="3"></m-dateCard>
			<view class="m-hotelCard_box_button" @click="searchHotel"
				:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
				<text style="color: #FFFFFF;">搜索</text>
			</view>
			<view class="m-hotelCard_box_else">
				<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
					<view class="icon-xiaoshi" style="font-size: 28rpx;padding-right: 8rpx;">
					</view>
					24小时客服
				</view>
				<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
					<view class="icon-jinggao" style="font-size: 28rpx;padding-right: 8rpx;">
					</view>
					会员折扣
				</view>
				<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
					<view class="icon-youhuiquan" style="font-size: 28rpx;padding-right: 8rpx;">
					</view>
					订房优惠
				</view>
			</view>
		</view>

		<!-- 模式四五-->
		<view v-if="mode==5" class="m-hotelCard_box"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="tabs">
				<template v-for="item in list">
					<view @click="changeTab(item)" class="tab"
						:style="activeIndex != item.id?'background:'+themeColor.main_color+'80':''"
						:class="{ 'active': activeIndex == item.id }">
						<text style="font-size: 32rpx;"
							:style="activeIndex != item.id?'color:#FFFFFF':'#000000'">{{item.name}}</text>
						<view class="" v-if="activeIndex == item.id"
							style="position: absolute;margin: 0 auto;bottom: 10rpx;left: 0;right: 0;width: 100%;display: flex;align-items: center;justify-content: center;">
							<view class="icon-banyuan2-copy" style="font-size: 40rpx;"
								:style="{color:themeColor.main_color}" v-if="activeIndex == item.id">

							</view>
						</view>

					</view>
				</template>
			</view>
			<template class="" v-if="activeIndex==1">
				<view class="m-hotelCard_box_title" @click="changeHotel" :style="{color:themeColor.text_main_color}">

					<view class=""
						style="display: flex;justify-content: space-between;align-items: center;padding:30rpx;"
						:style="{'border-bottom':'1px solid ' + themeColor.border_color}">
						<view class="" style="display: flex;flex-direction: column;">
							<p style="font-size: 38rpx;">{{hotel.shop_name}}</p>

						</view>
						<view class="" :style="'border:1px solid '+ themeColor.main_color"
							style="width: 152rpx;height: 60rpx;border-radius: 48rpx;display: flex;align-items: center;justify-content: center;">
							<view class="icon-dianhua" style="font-size: 30rpx;" :style="{color:themeColor.main_color}">

							</view>
							<text @click="call" style="font-size: 30rpx;"
								:style="{color:themeColor.main_color}">电话</text>
						</view>

					</view>

				</view>
				<m-dateCard :show="true" :mode="3"></m-dateCard>

				<view class="" style="display: flex;align-items: center;padding: 0 30rpx;" @click="chooseLocation">
					<view class=""
						style="max-width: 550rpx;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;">
						<text style="font-size: 32rpx;">{{hotel.address}}</text>
					</view>
					<text style="margin: 0 16rpx;">|</text>
					<view class="" style="display: flex;align-items: center;">
						<view class="icon-daohang" style="font-size: 30rpx;">

						</view>
						<text style="font-size: 32rpx;">导航</text>
					</view>
				</view>

				<view class="m-hotelCard_box_else">
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-xiaoshi" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						24小时客服
					</view>
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-jinggao" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						会员折扣
					</view>
					<view class="m-hotelCard_box_else_t1" :style="{color:themeColor.text_second_color}">
						<view class="icon-youhuiquan" style="font-size: 28rpx;padding-right: 8rpx;">
						</view>
						订房优惠
					</view>
				</view>
			</template>
			<template v-if="activeIndex==2">
				<m-searchAuto></m-searchAuto>
			</template>

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-hotelCard",
		data() {
			return {
				list: [{
					id: 1,
					name: '预订酒店'
				}, {
					id: 2,
					name: '查预定'
				}],
				activeIndex: 1,
				hotel_zong_name: ''
			};
		},
		props: {
			show: {
				type: Boolean,
				default: true
			},
			mode: {
				type: [Number, String],
				default: 1
			},
			showBtn: {
				type: Boolean,
				default: true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'hotelList']),
		},
		mounted() {
			if(this.hotelList.length > 0){
				
				let hotel_zong = this.hotelList.filter(item => {
					return this.hotel.id == item.shop_pid
				})
				console.log(hotel_zong,'hotel_zong');
				this.hotel_zong_name  = hotel_zong[0]?hotel_zong[0].shop_name:''
				
			}
			

		},
		methods: {
			changeTab(e) {
				this.activeIndex = e.id
				this.$emit('changeType', e)
			},
			changeHotel() {
				console.log(this.hotelList);
				uni.navigateTo({
					url: '/pages/hotelList/hotelList'
				})


			},

			hotelDetail() {
				uni.navigateTo({
					url: '/pages/hotelDetail/hotelDetail'
				})
			},
			searchHotel() {
				uni.navigateTo({
					url: '/pages/hotelList/hotelList?type=city'
				})
			},

			selectCity() {
				uni.navigateTo({
					url: '/pages/cityList/cityList'
				})
			},
			call() {
				uni.makePhoneCall({
					phoneNumber: this.hotel.link_phone
				})
			},
			chooseLocation() {
				wx.openLocation({
					latitude: Number(this.hotel.latitude), //维度
					longitude: Number(this.hotel.longitude), //经度
					name: this.hotel.shop_name, //目的地定位名称
					scale: 15, //缩放比例
					address: this.hotel.address, //导航详细地址
					fail: err => {
						console.log(err);
					}
				})
			}


		}
	}
</script>

<style lang="scss" scoped>
	.all_box {
		z-index: 2;
		position: relative;
	}


	.tabs {
		display: flex;
		width: 100%;
		overflow: hidden;
		border-radius: 32rpx 32rpx 0 0;
		background: #D6EED6;
		justify-content: space-between;
		height: 50px;
	}

	.tab {
		// flex: 0 0 33.34%;
		// width: fit-content;
		// height: 50px;
		// cursor: pointer;
		width: 50%;
		background-color: #D6EED6;
		position: relative;
		text-align: center;
		// line-height: 50px;
		color: #0C0F1485;
		font-size: 34rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.tab.active {
		background-color: #fff;
		color: #0C0F14EB;
		;
	}

	.tab.active:before {
		content: '';
		position: absolute;
		top: 0;
		left: -50px;
		height: 100%;
		width: 50px;
		z-index: 2;
		background-color: #fff;
		clip-path: path('M 0,50 C 25,50 25,0 50,0 L 50, 50 Z');
	}

	.tab.active:after {
		content: '';
		position: absolute;
		top: 0;
		right: -50px;
		height: 100%;
		width: 50px;
		z-index: 2;
		background-color: #fff;
		clip-path: path('M 0,0 C 25,0 25,50 50,50 L 0, 50 Z');
	}

	.content-wrap {
		min-height: 200px;
		background-color: #fff;
	}

	.m-hotelCard_box {
		width: 720rpx;
		margin: 48rpx auto;
		margin-bottom: 32rpx;
		min-height: 300rpx;
		border-radius: 32rpx;
		padding-bottom: 20rpx;
		// box-shadow: rgba(0, 0, 0, 0.09) 0px 3px 12px;

		&_title {
			width: 100%;
			min-height: 160rpx;
			display: flex;
			flex-direction: column;
			justify-content: center;
			font-size: 40rpx;
			padding: 20rpx 30rpx;

			&_img {
				font-size: 40rpx;
				padding-right: 2rpx;
			}

			&_city_box {
				padding: 30rpx;
				display: flex;
				flex-direction: column;

				&_title {
					// height: 50rpx;
					width: 100%;
					display: inline-flex;
					align-items: center;
					z-index: 3;
				}

				&_border {
					width: 110rpx;
					height: 16rpx;
					margin-left: 16rpx;
					margin-top: -10rpx;
					z-index: 2;
					// background-color: l;
				}
			}



			&_hotel {
				width: 100%;
				padding: 0 30rpx;
				// padding-left: 40rpx;
				display: flex;
				align-items: center;
				// justify-content: space-between;
				font-size: 40rpx;

				&_text {
					word-wrap: break-word;
					word-break: break-all;
					width: 90%;
					// font-weight: 500;
				}
			}
		}

		&_bottom {
			// border-bottom: 1px solid #303133;
			margin: 20rpx auto;
			width: 90%;
		}


		&_button {
			margin: 30rpx auto;
			width: 90%;
			height: 100rpx;
			border-radius: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			// filter: blur(5rpx);
		}

		&_else {
			margin: 10rpx auto;
			width: 80%;
			height: 30rpx;
			border-radius: 50rpx;
			display: flex;
			align-items: center;
			font-weight: 500;

			&_t1 {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 22rpx;
				flex: 1
			}
		}
	}
</style>