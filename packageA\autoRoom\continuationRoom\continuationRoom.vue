<template>
	<view style="padding: 20rpx 0;">
		<view class="roomInfo">
			<view class="nameBox" style="display: flex;justify-content: space-between;align-items: center;">
				<text style="font-size: 28rpx;font-weight: 600;">{{billDetail.room_type_name}}</text>
				<text v-if="billDetail.bill_status==8" style="font-size: 22rpx;color: brown;">正在申请退房,请等待酒店确认！</text>
			</view>
			<!-- 全日房 -->
			<view class="elseBox" v-if="billDetail.room_sale_type_sign=='standard'">
				<text style="">{{billDetail.enter_time_plan | moment}} 至 {{billDetail.leave_time_plan | moment}}</text>
				<text style="padding-left: 10rpx;">{{billDetail.stay_time+ '晚'}}</text>
				<text></text>
			</view>
			<!-- 时租房会议室 -->
			<view class="elseBox"
				v-if="billDetail.room_sale_type_sign=='hour'||billDetail.room_sale_type_sign=='conference_room'">
				<text style="">{{billDetail.enter_time_plan | moment}} </text>
				<text style="padding-left: 10rpx;">{{billDetail.stay_time+ '小时'}}</text>
				<text></text>
			</view>
			<!-- 月租 -->
			<view class="elseBox" v-if="billDetail.room_sale_type_sign=='long_standard'">
				<text style="">{{billDetail.enter_time_plan | moment}} 至 {{billDetail.leave_time_plan | moment}}</text>
				<text style="padding-left: 10rpx;">{{billDetail.stay_time+ '月'}}</text>
				<text></text>
			</view>
			<p><text style="font-size: 24rpx;">{{billDetail.room_service.service_name}}</text></p>
			<view class="manInfo">
				<view class="manInfo_title">
					<text class="manInfo_title1" style="padding-right: 60rpx;">入住人</text>
					<text>{{billDetail.link_man}}</text>
				</view>
				<view class="manInfo_title">
					<text class="manInfo_title1">联系手机</text>
					<text>{{billDetail.link_phone}}</text>
				</view>
				<view class="manInfo_title">
					<text class="manInfo_title1">入住说明</text>
					<text>{{billDetail.enter_time_plan | moment2}}</text>之后入住，
					<text>{{billDetail.leave_time_plan | moment2}}</text>之前退房
				</view>
			</view>
		</view>

		<view class="orderInfo">
			<p style="font-size: 40rpx;font-weight: 600;line-height: 60rpx;">订单信息</p>
			<view class="manInfo_title">
				<text class="manInfo_title1">订单号:</text>
				<text>{{billDetail.bill_code}}</text>
			</view>
			<view class="manInfo_title">
				<text class="manInfo_title1">订单来源:</text>
				<text>{{billDetail.bill_source_name}}</text>
			</view>
			<view class="manInfo_title">
				<text class="manInfo_title1">下单时间:</text>
				<text>{{billDetail.create_time | moment}}</text>
			</view>
		</view>

		<!-- 选择天数 -->
		<view class="dayNum">
			<p style="margin-right: 8rpx;width: 160rpx;"><text style="color: red;">*</text><text
					style="font-size: 34rpx;">续房时长</text></p>
			<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
				<uni-number-box :min="1" :step="1" v-model="days" :max="100000000000"></uni-number-box>天
			</view>
		</view>

		<view class="orderInfo" style="min-height: 140rpx;">
			<p style="font-size: 36rpx;font-weight: 600;line-height: 60rpx;">续房价格</p>
			<scroll-view scroll-y="true" style="max-height: 400rpx;min-height: 120rpx;">
				<view class="" v-for="(item, index) in xfPrice" :key="index"
					style="display: flex;justify-content: space-between;width: 100%;">
					<text>{{item.date}}</text>
					<text>￥{{item.room_price}}</text>
				</view>
			</scroll-view>
			<view class="" style="display: flex;justify-content: flex-end;">
				<p style="">房费:￥{{totalPrice}}</p>

			</view>
			<view class="" style="display: flex;justify-content: flex-end;">

				<p style="font-weight: 600;color: crimson">总计:￥{{showPrice}}</p>
			</view>
		</view>

		<m-pointService v-if="ifCoupon" @chooseCoup="chooseCoupon" :limitTime="coupon_limit_time" :coupType="1"
			:choosePrice="coupon_price" :limitNum="totalPrice"></m-pointService>

		<view class="" style="margin: 80rpx auto;width: 700rpx;">
			<button type="primary" @click="continueRoom">续房</button>
		</view>

		<!-- 支付弹窗 -->
		<m-popup :show="pop" @closePop="closePop">
			<m-payCard @toPay="payFor" :payType="payList" v-if="hackReset"></m-payCard>
		</m-popup>

		<!-- 选择优惠券弹窗 -->
		<m-popup :show="popCoupon" @closePop="closePopCoupon">
			<m-chooseCoupon :coupType="1" :limit="{limitNum:totalPrice}" @getCouponIfo="getInfo"></m-chooseCoupon>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				payList: ['weixin', 'tongyong', 'duli'], //支付方式
				pop: false,
				days: 1,
				hackReset: true,
				coupon_limit_time: 0,
				coupon_price: 0,
				coupon_id: 0,
				popCoupon: false,
				xfPrice: [],
				totalPrice: 0,
				showPrice: 0,
				ifCoupon: false
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting']),
			...mapState('room', ['billDetail', 'roomBillUser']),
		},
		watch: {
			days: {
				handler(newVal, oldVal) {
					this.$iBox.http('getExtendRoomPrice', {
						bill_id: this.billDetail.id,
						days: this.days
					})({
						method: 'post'
					}).then(res => {
						this.xfPrice = res.data

						let price = 0
						res.data.forEach(item => {
							price += item.room_price
						})

						this.totalPrice = price.toFixed(2)
						this.showPrice = this.totalPrice
					})
				},
				deep: true,
				immediate: true
			},
			coupon_price: {
				handler(newVal, oldVal) {
					this.showPrice = this.totalPrice - this.coupon_price
					if (this.showPrice < 0) {
						this.showPrice = 0
					}

					this.showPrice = this.showPrice.toFixed(2)
				},
				deep: true,
				immediate: true
			}
		},
		mounted() {
			// 刷新订单
			this.ifCoupon = this.shopSetting.filter(item => {
				return item.sign == 'extend_room_coupon'
			})[0].property.status
		},
		methods: {
			continueRoom() {
				this.pop = true
			},
			closePop() {
				this.pop = false
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
				})
			},
			closePopCoupon() {
				this.popCoupon = false
			},
			chooseCoupon(e) {

				this.popCoupon = true
			},

			getInfo(e) {

				this.popCoupon = false
				if (e) {
					this.coupon_price = e.coupon_info.discounts
					this.coupon_id = e.id
					this.coupon_limit_time = e.limit_time
				} else {
					this.coupon_price = 0
					this.coupon_id = ''
				}
			},

			fnPay(e) {
				this.payType = e
				let param = {
					bill_id: this.billDetail.id,
					booking_type: '',
					pay_type: '',
					days: this.days,
					user_coupon_id: this.coupon_id,
				}
				// 首先判断是否用微信还是余额,0:微信  1:通用余额  2.独立余额
				if (e == 'weixin') {
					param.pay_type = 1
					param.booking_type = 1
					// 判断是全日房下单
					this.$iBox
						.http('standardExtendRoomBill', param)({
							method: 'post'
						})
						.then(res => {
							if (res.data && res.code === 0) {
								if (res.data.bizCode == '0000') {
									// 随行付
									uni.requestPayment({
										provider: 'wxpay',
										AppId: res.data.payAppId,
										timeStamp: res.data.payTimeStamp,
										nonceStr: res.data.paynonceStr,
										package: res.data.payPackage,
										signType: res.data.paySignType,
										paySign: res.data.paySign,
										success: (res) => {
											uni.showModal({
												title: '提示',
												content: '续房成功',
												showCancel: false,
												success: res => {
													if (res.confirm) {
														uni.navigateBack({})
													}
												}
											})
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});


								} else {
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.paySign,
										success: (res) => {
											uni.showModal({
												title: '提示',
												content: '续房成功',
												showCancel: false,
												success: res => {
													if (res.confirm) {
														uni.navigateBack({})
													}
												}
											})

										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
								}

							} else if (res.code === 0 && !res.data) {
								uni.showModal({
									title: '提示',
									content: '续房成功',
									showCancel: false,
									success: res => {
										if (res.confirm) {
											uni.navigateBack({})
										}
									}
								})
							}
						})

				} else {
					param.booking_type = e != 'daodian' ? 1 : 2
					param.pay_type = e == 'tongyong' ? 2 : (e == 'duli' ? 3 : '')

					uni.showModal({
						title: '正在续房',
						content: `是否使用${e==1?'通用余额':'独立余额'}支付`,
						success: res => {
							if (res.confirm) {
								this.$iBox
									.http('standardExtendRoomBill', param)({
										method: 'post'
									})
									.then(res => {
										uni.showModal({
											title: '提示',
											content: '续房成功',
											showCancel: false,
											success: res => {
												if (res.confirm) {
													uni.navigateBack({})
												}
											}
										})

									})
							}
						}
					})
				}

			},

			payFor(e) {
				this.$iBox.throttle(() => {
					if (this.billDetail.room_sale_type_sign == 'standard') {
						this.fnPay(e)
					} else {
						uni.showToast({
							icon: 'none',
							title: '非全日房请到酒店前台办理续房！',
							duration: 1500
						})
					}

				}, 2000);
			},
		}
	}
</script>

<style lang="scss" scoped>
	// 房间详情
	.roomInfo {
		margin: 20rpx auto;
		border-radius: 30rpx;
		background: #ffffff;
		min-height: 200rpx;
		width: 94%;
		border: 1px solid transparent;
		padding: 20rpx;

		.nameBox {
			font-size: 40rpx;
			font-weight: 500;
		}

		.elseBox {
			font-size: 24rpx;
			color: #333;
		}

		.manInfo {
			margin-top: 20rpx;
			line-height: 60rpx;

			.manInfo_title {
				.manInfo_title1 {
					font-size: 28rpx;
					font-weight: 600;
					width: 190rpx;
					padding-right: 30rpx;
				}
			}
		}
	}

	// 订单信息
	.orderInfo {
		margin: 20rpx auto;
		border-radius: 30rpx;
		background: #ffffff;
		min-height: 200rpx;
		width: 94%;
		border: 1px solid transparent;
		padding: 20rpx;

		.manInfo_title {
			margin-top: 20rpx;

			.manInfo_title1 {
				font-size: 28rpx;
				font-weight: 600;
				width: 190rpx;
				padding-right: 30rpx;
			}
		}
	}

	.dayNum {
		margin: 20rpx auto;
		border-radius: 30rpx;
		background: #ffffff;
		min-height: 100rpx;
		width: 94%;
		border: 1px solid transparent;
		padding: 20rpx;
		display: flex;
		align-items: center;
	}
</style>