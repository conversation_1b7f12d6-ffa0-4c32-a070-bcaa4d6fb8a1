<template>
	<view>
		<view class="" style="width: 100%;height: 480rpx;background: #FFFFFF;">
			<image :src="goodDetail.cover_pic" style="width: 100%;height: 480rpx;" mode="aspectFit"></image>
		</view>

		<view class="card_iu">
			<p>{{goodDetail.goods_name}}</p>
			<view class="" style="display: flex;align-items: center;">
				<p style="font-size: 28rpx;font-weight: 500;color: #A5673F;">{{goodDetail.point_amount}}<text
						style="font-size: 24rpx;">积分</text></p>
				<text v-if="goodDetail.price">+</text>
				<p style="font-size: 28rpx;font-weight: 500;color: #A5673F;" v-if="goodDetail.price">
					{{goodDetail.price}}<text style="font-size: 24rpx;">元</text>
				</p>
			</view>
			<text style="font-size: 22rpx;color:#888888">已兑{{goodDetail.sale_count}}件</text>
		</view>
		<view class="boxb" style="">
			<p>兑换数量:</p>
			<uni-number-box v-model="goodNumber" step="1" max="100000" min="1" />
		</view>

		<view class="typeBox">
			<p>领取方式:</p>
			<view class="boxOut">
				<view class="boxInner" v-for="item in typeList" @click="checkType(item)">
					<text>{{item.delivery_name}}</text>
					<view :class="typeCurrent == item.delivery_type?'radio_box':'radio_box_no'">
					</view>

				</view>

			</view>
		</view>

		<view class="boxAddress" style="" @click="chooseAdd" v-if="typeCurrent==1">
			<view class="addressBox" v-if="address">
				<view class="item">
					<text>{{address.detail}}</text>
				</view>
				<view class="item1">
					<text>{{address.address}}</text>
				</view>
				<view class="item2">
					<text>{{address.user_name}}</text>
					<text style="margin-left: 40rpx;">{{address.user_phone}}</text>
					<view class="" style="width: fit-content;padding: 8rpx;color: red;border-radius: 4rpx;height: 24rpx;
					font-size: 18rpx;color: #ffffff;display: flex;align-items: center;background-color: red;
					justify-content: center;margin-left: 40rpx;" v-if="address.is_default">
						<text>默认</text>
					</view>
				</view>
				<view class="edit">
					<view class="icon-jiantou">

					</view>
				</view>
			</view>
			<view class="addressBox" style="display: flex;align-items: center;" v-else>
				<p>请选择收获地址</p>
				<view class="edit">
					<view class="icon-jiantou">

					</view>
				</view>
			</view>
		</view>

		<view class="roomBox" v-if="typeCurrent==3">
			<text v-if="roomInfo.room_number">房间号:{{roomInfo.room_number}}</text>
			<text v-else>您暂无入住房间，请选择其他方式</text>
		</view>

		<view class="" style="width: 100%;margin-top: 20rpx;padding: 30rpx;background-color: #FFFFFF;">
			<p>产品详情</p>
			<view class="bg-white padding">
				<rich-text :nodes="rich"></rich-text>
			</view>
		</view>

		<!-- 支付弹窗 -->
		<m-popup :show="pop" @closePop="closePop">
			<m-payCard :payType='payTypeArr' @toPay="payFor"></m-payCard>
		</m-popup>

		<m-popup :show="pop1" @closePop="closePop1" mode="center">
			<view class="customPay">

				<view style="position: absolute;top: 20rpx;right: 30rpx;" @click="closePop1">
					<view class="icon-close" style="font-size: 40rpx;"></view>
				</view>
				<view class=""
					style="width: 90%;height: 200rpx;border-bottom: 1px solid #e4e7ed;display: flex;flex-direction: column;align-items: center;justify-content: space-around;">
					<text style="font-size:30rpx;font-weight: 600;">{{hotel.shop_name}}</text>
					<view class="" style="display: flex;align-items: center;">
						<p>单价:</p>
						<p style="font-size: 28rpx;font-weight: 500;color: #A5673F;">{{goodDetail.point_amount}}<text
								style="font-size: 24rpx;">积分</text></p>
						<text v-if="goodDetail.price">+</text>
						<p style="font-size: 28rpx;font-weight: 500;color: #A5673F;" v-if="goodDetail.price">
							{{goodDetail.price}}<text style="font-size: 24rpx;">元</text>
						</p>
					</view>
					<p>数量:x{{goodNumber}}</p>
				</view>

				<view class="" style="display: flex;align-items: center;margin-top: 30rpx;font-size: 44rpx;">
					<p>总计:</p>
					<p style="font-size: 44rpx;font-weight: 500;color: #A5673F;">
						{{goodDetail.point_amount*goodNumber}}<text style="font-size: 44rpx;">积分</text>
					</p>
					<text v-if="goodDetail.price">+</text>
					<p style="font-size: 44rpx;font-weight: 500;color: #A5673F;" v-if="goodDetail.price">
						{{goodDetail.price*goodNumber}}<text style="font-size: 44rpx;">元</text>
					</p>
				</view>
				<view class="btn_pay">
					<view class="btn" :style="'background:'+themeColor.main_color+';color:'+themeColor.bg_color"
						@click="surePay">
						<text>确认支付</text>
					</view>
				</view>
			</view>
		</m-popup>

		<view class="btn_jf" :style="{'background-color':themeColor.main_color}" @click="toPayBox">
			立即购买
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				goodDetail: null,
				rich: '',
				address: null,
				goodNumber: 1,
				pop: false,
				pop1: false,
				payType: '',
				payTypeArr: [],
				typeList: [],
				typeCurrent: 0,
				roomInfo: null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'city', 'addressDetail']),
			...mapState('pointShop', ['pointShopDetail']),

		},
		onLoad() {

			uni.setNavigationBarTitle({
				title: this.pointShopDetail.goods_name
			})

			
			this.$iBox.http('getPointMallGoodsInfoById', {
				id: this.pointShopDetail.id
			})({
				method: 'post'
			}).then(res => {
				this.goodDetail = res.data

				let payTypeArr = []
				res.data.pay_type.forEach(item1 => {
					if (item1 == 1) {
						payTypeArr.push('weixin')
					} else if (item1 == 2) {
						payTypeArr.push('tongyong')
					} else if (item1 == 3) {
						payTypeArr.push('duli')
					}

				})
				this.payTypeArr = payTypeArr
				if (this.goodDetail.detail) {
					this.rich = this.$iBox.formatRichText(this.goodDetail.detail)
				}
			})

			//查询配送方式 
			this.$iBox
				.http('getDeliveryType', {})({
					method: 'post'
				})
				.then(res => {
					this.typeList = res.data
					this.typeCurrent = res.data[0].delivery_type
				})
			// 查询默认地址
			this.$iBox
				.http('getUserAddress', {
					page: 1,
					limit: 100
				})({
					method: 'post'
				})
				.then(res => {
					if (res.data.list.length > 0) {
						let address = res.data.list.filter(item => {
							return item.is_default == 1
						})
						if (address.length > 0) {
							this.address = address[0]
						} else {
							this.address = res.data.list[0]
						}
					}


				})
				.catch(function(error) {
					console.log('网络错误', error);
				});
			// 查询房间
			this.$iBox
				.http('getStayingRoom', {})({
					method: 'post'
				})
				.then(res => {
					this.roomInfo = res.data
				})


		},
		onShow() {
			this.address = this.addressDetail
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),

			chooseAdd() {
				let id = this.address ? this.address.id : '-1'
				uni.navigateTo({
					url: '/packageA/address/chooseAddress/chooseAddress?id=' + id
				})
			},
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			toPayBox() {
				console.log(this.goodDetail, 'goodDetail');
				if (this.goodDetail.price == 0) {
					// 代表金额为0纯积分支付
					this.surePayPoint()
				} else {
					this.pop = true
				}

			},
			checkType(e) {
				this.typeCurrent = e.delivery_type
			},
			payFor(e) {
				this.$iBox.throttle(() => {
					this.payType = e
					this.fnPay(e)
				}, 2000);
			},
			fnPay(e) {
				if (this.typeCurrent == 1 && !this.address) {
					uni.showToast({
						icon: 'error',
						title: '请选择地址'
					})
					return
				}

				if (this.typeCurrent == 3 && !this.roomInfo) {
					uni.showToast({
						icon: 'error',
						title: '您无入住中房间，请选择其他方式'
					})
					return
				}
				let goods_list = []

				let item1 = {
					goods_id: this.goodDetail.id,
					goods_count: this.goodNumber
				}
				goods_list.push(item1)

				// 首先判断是否用微信还是余额,0:微信  1:通用余额  2.独立余额
				if (this.payType == 'weixin') {
					uni.showLoading({
						title: '等待支付...'
					})
					let params = {
						goods_info: goods_list,
						pay_type: 1,
						user_name: this.typeCurrent == 1 ? this.address.user_name : '',
						user_phone: this.typeCurrent == 1 ? this.address.user_phone : '',
						address: this.typeCurrent == 1 ? this.address.address + this.address.detail : '',
						delivery_type: this.typeCurrent,
						room_id: this.typeCurrent == 3 ? this.roomInfo.id : ''
					}

					this.$iBox.http('addPointMallAddOrder', params)({
						method: 'post'
					}).then(res => {
						if (res.data.bizCode == '0000') {
							// 随行付
							uni.requestPayment({
								provider: 'wxpay',
								AppId: res.data.payAppId,
								timeStamp: res.data.payTimeStamp,
								nonceStr: res.data.paynonceStr,
								package: res.data.payPackage,
								signType: res.data.paySignType,
								paySign: res.data.paySign,
								success: (res) => {
									uni.hideLoading()
									uni.navigateTo({
										url: '/pages/resultsPage/resultsPointShopPage'
									})

								},
								fail: function(err) {
									uni.hideLoading()
								}
							});


						} else {
							// 微信支付
							uni.requestPayment({
								provider: 'wxpay',
								timeStamp: res.data.timeStamp,
								nonceStr: res.data.nonceStr,
								package: res.data.package,
								signType: 'MD5',
								paySign: res.data.paySign,
								success: (res) => {
									uni.hideLoading()
									uni.navigateTo({
										url: '/pages/resultsPage/resultsPointShopPage'
									})

								},
								fail: function(err) {
									uni.hideLoading()
								}
							});
						}

					})

				} else {
					this.pop = false
					this.pop1 = true

				}

			},

			surePay() {
				this.$iBox.throttle1(() => {
					this.submit()
				}, 2000);
			},
			submit() {
				if (this.typeCurrent == 1 && !this.address) {
					uni.showToast({
						icon: 'error',
						title: '请选择地址'
					})
					return
				}

				if (this.typeCurrent == 3 && !this.roomInfo) {
					uni.showToast({
						icon: 'error',
						title: '您无入住中房间，请选择其他方式'
					})
					return
				}
				uni.showModal({
					title: '提示',
					content: `是否确定兑换${this.goodDetail.goods_name},数量${this.goodNumber}件`,
					success: (res) => {
						if (res.confirm) {
							let params = {
								goods_info: [{
									goods_id: this.goodDetail.id,
									goods_count: this.goodNumber
								}],
								pay_type: this.payType == 'tongyong' ? 2 : (this.payType ==
									'duli' ? 3 :
									''),
								user_name: this.typeCurrent == 1 ? this.address.user_name : '',
								user_phone: this.typeCurrent == 1 ? this.address.user_phone : '',
								address: this.typeCurrent == 1 ? this.address.address + this.address
									.detail : '',
								delivery_type: this.typeCurrent,
								room_id: this.typeCurrent == 3 ? this.roomInfo.id : ''
							}

							this.$iBox.http('addPointMallAddOrder', params)({
								method: 'post'
							}).then(res => {

								uni.showToast({
									icon: 'none',
									title: '兑换成功'
								})
								uni.navigateTo({
									url: '/pages/resultsPage/resultsPointShopPage'
								})


							})

						} else if (res.cancel) {
							console.log('用户点击取消')
						}
					}
				})


			},
			surePayPoint() {
				this.$iBox.throttle1(() => {
					this.submitPoint()
				}, 2000);
			},
			submitPoint() {
				if (this.typeCurrent == 1 && !this.address) {
					uni.showToast({
						icon: 'error',
						title: '请选择地址'
					})
					return
				}

				if (this.typeCurrent == 3 && !this.roomInfo) {
					uni.showToast({
						icon: 'error',
						title: '您无入住中房间，请选择其他方式'
					})
					return
				}

				uni.showModal({
					title: '提示',
					content: `是否确定兑换${this.goodDetail.goods_name},数量${this.goodNumber}件`,
					success: (res) => {
						if (res.confirm) {
							let params = {
								goods_info: [{
									goods_id: this.goodDetail.id,
									goods_count: this.goodNumber
								}],
								pay_type: this.goodDetail.pay_type[0],
								user_name: this.typeCurrent == 1 ? this.address.user_name : '',
								user_phone: this.typeCurrent == 1 ? this.address.user_phone : '',
								address: this.typeCurrent == 1 ? this.address.address + this.address
									.detail : '',
								delivery_type: this.typeCurrent,
								room_id: this.typeCurrent == 3 ? this.roomInfo.id : ''
							}

							this.$iBox.http('addPointMallAddOrder', params)({
								method: 'post'
							}).then(res => {

								uni.showToast({
									icon: 'none',
									title: '兑换成功'
								})
								uni.navigateTo({
									url: '/pages/resultsPage/resultsPointShopPage'
								})


							})

						} else if (res.cancel) {
							console.log('用户点击取消')
						}
					}
				})
			}
		}
	}
</script>
<style>
	page {
		background: #f3f4f6;
	}
</style>
<style scoped lang="scss">
	.card_iu {
		width: 100%;
		height: 150rpx;
		background: #FFFFFF;
		z-index: 999999999;
		padding: 30rpx;
	}

	.boxb {
		width: 100%;
		background: #FFFFFF;
		margin-top: 20rpx;
		display: flex;
		justify-content: space-between;
		padding: 20rpx;

		.boxt {
			height: 120rpx;
			width: 300rpx;
			background-color: #FFFFFF;
			border-radius: 24rpx;
			box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.boxt_ac {
			height: 120;
			width: 300rpx;
			background-image: linear-gradient(to right, #fe9f3f, #ff8142);
			color: #FFFFFF;
			border-radius: 24rpx;
			box-shadow: rgba(0, 0, 0, 0.05) 0px 6px 24px 0px, rgba(0, 0, 0, 0.08) 0px 0px 0px 1px;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	.typeBox {
		width: 100%;
		background: #FFFFFF;
		margin-top: 20rpx;
		padding: 20rpx;

		.boxOut {
			display: flex;
			justify-content: space-between;
			margin-top: 30rpx;

			.boxInner {
				width: 200rpx;
				border: 1px solid #bdbdbd;
				padding: 30rpx 6rpx;
				border-radius: 10rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 10rpx;
				font-size: 26rpx;
				font-weight: 600;
			}

			.radio_box {
				display: inline-block;
				vertical-align: bottom;
				width: 20px;
				height: 20px;
				background: radial-gradient(circle, #fff 20%, #409eff 20%);
				border-radius: 50%;
			}

			.radio_box_no {
				display: inline-block;
				vertical-align: bottom;
				width: 20px;
				height: 20px;
				background: #fff;
				border-radius: 50%;
				border: 1px solid #b3b3b3;
			}
		}
	}

	.boxAddress {
		width: 100%;
		background: #FFFFFF;
		margin-top: 20rpx;
		padding: 20rpx;

		.addressBox {
			width: 100%;
			height: 170rpx;
			padding: 20rpx;
			position: relative;
			line-height: 44rpx;
			border-bottom: 1px solid #eee;

			.item {
				font-size: 24rpx;
				color: #7f7f7f;
				overflow: hidden;
			}

			.item1 {
				font-size: 30rpx;
				color: #000000;
				font-weight: 600;
				overflow: hidden;
			}

			.item2 {
				display: flex;
				align-items: center;
				font-size: 26rpx;
			}

			.edit {
				position: absolute;
				right: 10rpx;
				height: 100%;
				top: 0;
				display: flex;
				align-items: center;
			}
		}
	}

	.roomBox {
		width: 100%;
		background: #FFFFFF;
		margin-top: 20rpx;
		padding: 20rpx;
	}

	.customPay {
		height: 600rpx;
		width: 600rpx;
		background: #FFFFFF;
		display: flex;
		flex-direction: column;
		align-items: center;
		border-radius: 20rpx;
		position: relative;

		.btn_pay {
			width: 100%;
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			position: fixed;
			bottom: 30rpx;

			.btn {
				width: 80%;
				height: 100%;
				border-radius: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}

	.btn_jf {
		position: fixed;
		bottom: 30rpx;
		height: 80rpx;
		width: 640rpx;
		border-radius: 24rpx;
		margin: 0 auto;
		left: 0;
		right: 0;

		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>