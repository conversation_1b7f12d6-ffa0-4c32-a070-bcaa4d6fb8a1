<template>
	<view>
		<view class=""
			style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
			v-if="recordList.length==0">
			<view class="icon-quesheng<PERSON>_z<PERSON><PERSON>" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无订单</p>
		</view>
		<view class="listBox" v-for="item in recordList" v-else>
			<view class="">
				<image :src="item.avatar_url" style="width: 60rpx;height: 60rpx;border-radius: 50%;" mode=""></image>
			</view>
			<view class="title">
				<text>{{item.nickname}}</text>
			</view>
			<view class="title1">
				<text>酒店名称:{{item.shop_name}}</text>
			</view>
			<view class="title">
				<text>来源:{{item.source=='wxxcx'?'微信小程序':'其他'}}</text>
			</view>
			<view class="title" style="width: 100%;">
				<text>时间:{{item.create_time | moment1}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				params: {
					page: 1,
					limit: 10
				},
				recordList: [],
				bool: true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		async onShow() {
			this.params.page = 1
			this.getRecord()
		},
		methods: {

			getRecord() {
				// 查询提现记录
				this.$iBox.http('getDistributionUserRecord', this.params)({
					method: 'post'
				}).then(res => {
					this.recordList = res.data.list
				})
			}
		},
		onReachBottom() {

			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getDistributionUserRecord', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.recordList.concat(res.data.list)
					this.recordList = new_list
					if (this.recordList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style scoped lang="scss">
	.listBox {
		padding: 30rpx;
		border-bottom: 1px solid #e4e7ed;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		background: #FFFFFF;

		.title {
			padding: 10rpx;
			width: 33%;
			display: flex;
			align-items: center;
			// justify-content: center;
			font-size: 28rpx;
		}

		.title1 {
			padding: 10rpx;
			width: 50%;
			display: flex;
			align-items: center;
			// justify-content: center;
			font-size: 28rpx;
		}
	}
</style>