<template>
	<view style="margin:  0 auto;display: flex;align-items: center;justify-content: center;">
		<image :src="img_url" v-if="show" @click="toCoupon" class="imgStyle"></image>
	</view>
</template>

<script>
	export default {
		name:"m-banner",
		props:{
			show :{
				type:Boolean,
				default:true
			},
			img_url:{
				type:String,
				default:''
			}
		},
		data() {
			return {
				
			};
		},
		mounted() {
			console.log(this.img_url,'imgurl');
		},
		methods:{
			toCoupon(){
				uni.navigateTo({
					url:'/packageA/couponCenter/couponCenter'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.imgStyle{
		width: 700rpx;
		height: 260rpx;
		margin: 20rpx auto;
	}
</style>
