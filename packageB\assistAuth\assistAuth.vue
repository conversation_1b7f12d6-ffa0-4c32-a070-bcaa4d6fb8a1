<template>
	<view>
		<view class="" style="background-color: #FFFFFF;padding: 64rpx 48rpx;height: 100vh;">
			<p style="color: #000000E0;font-size: 32rpx;">请拍摄您的身份证人像面</p>
			<p style="color: #00000066;font-size: 30rpx;margin-top: 16rpx;">请确保身份证完整清晰(证件底纹、文字、照片清晰)</p>
			<view class="" v-if="!userMsg"
				style="width: 100%;height: 400rpx;display: flex;align-items: center;justify-content: center;margin-top: 64rpx;position: relative;"
				@click="toTakePhone">
				<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/shzbg.png"
					style="height: 400rpx;width: 654rpx;position: absolute;top: 0;bottom: 0;right: 0;left: 0;margin: auto;"
					mode=""></image>
				<view class=""
					style="display: flex;flex-direction: column;;align-items: center;justify-content: center;z-index: 2;">
					<view class=""
						style="background: linear-gradient(0deg, #65E3D6 0%, #51DEDE 100%);width: 128rpx;height: 128rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;">
						<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/zxj.png"
							style="width: 80rpx;height: 80rpx;z-index: 10;" mode=""></image>
					</view>

					<p style="color: #51DEDE;font-size: 32rpx;margin-top: 16rpx;">拍摄身份证人像面</p>
				</view>
			</view>
			<view class="" v-if="userMsg" style="width: 100%;height: 400rpx;display: flex;align-items: center;justify-content: center;margin-top: 64rpx;position: relative;">
				<image :src="userMsg.id_image" mode="" style="height: 400rpx;width: 654rpx;position: absolute;top: 0;bottom: 0;right: 0;left: 0;margin: auto;"></image>
				<view class=""
					style="display: flex;flex-direction: column;;align-items: center;justify-content: center;z-index: 2;">
					<view class=""
						style="background: #FFFFFF;width: 128rpx;height: 128rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;">
						<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/Frame.png"
							style="width: 80rpx;height: 80rpx;z-index: 10;" mode=""></image>
					</view>
				
					<p style="color: #ffffff;font-size: 32rpx;margin-top: 16rpx;">重新拍摄</p>
				</view>
			</view>
			<view class="" style="margin: 32rpx auto;width: 654rpx;height: 192rpx;padding: 32rpx;background-color: #F5F5F5;" v-if="userMsg">
				<p>核对您的身份信息</p>
				<p style="color: #000000E0;margin-top: 10rpx;"><text>{{userMsg.name}}</text><text
						style="margin-left: 32rpx;">{{userMsg.identification_number}}</text></p>
				<p style="color: #000000E0;margin-top: 10rpx;">手机号码:{{userMsg.phone}}</p>
			</view>
			<view class="" v-if="userMsg" @click="goMain" style="width: 622rpx;height: 96rpx;
			background: linear-gradient(90deg, #637DFD 0%, #5050FA 100%);border-radius: 48rpx;display: flex;align-items: center;justify-content: center;margin: 112rpx auto;
">
				<text style="font-size: 32rpx;color: #FFFFFF;">已提交</text>
			</view>
			<view class="" v-else style="width: 622rpx;height: 96rpx;
						background: linear-gradient(90deg, #d1d9fb 0%, #cbccfb 100%);border-radius: 48rpx;display: flex;align-items: center;justify-content: center;margin: 112rpx auto;
			">
				<text style="font-size: 32rpx;color: #FFFFFF;">请上传</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				userMsg: null,
				action_idCard: '',
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting', 'setting']),
		},
		onLoad() {
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					this.action_idCard = res.data + '/wx/User/uploadUserInfo'
				}
			});
		},
		methods: {
			goMain(){
				uni.reLaunch({
					url:'/pages/index/index'
				})
			},
			toTakePhone() {
				
				uni.chooseMedia({
					count: 9,
					mediaType: ['image'],
					sourceType: ['album', 'camera'],
					sizeType: ['compressed'],
					camera: 'back',
					success: (res) => {
						console.log(res, 'id');
						uni.showLoading({
							title:'加载中...'
						})
						uni.uploadFile({
							url: this.action_idCard, //
							header: {
								'AUTHTOKEN': this.userInfo.user_token,
								'Content-Type': 'application/x-www-form-urlencoded',
								'chartset': 'utf-8'
							},
							filePath: res.tempFiles[0].tempFilePath,
							name: 'file',
							formData: {
								'shop_id': this.hotel.id
							},
							success: (uploadFileRes) => {
								uni.hideLoading()
								if (JSON.parse(uploadFileRes.data).data) {
									let dataInfo = JSON.parse(uploadFileRes.data).data
									console.log(dataInfo,'uploadFileRes');
									this.userMsg = dataInfo
									this.idCard_img = dataInfo.id_image
									this.name = dataInfo.name
									this.id_number = dataInfo.identification_number
									this.popLoading = false
								} else {
									uni.showModal({
										title: '提示',
										content: '身份证照片识别失败，请重新上传身份证！',
										showCancel: false,
										success: res1 => {
											this.popLoading = false
											this.idCard_img = ''
											this.name = ''
											this.id_number = ''
										}
									})
								}

							},
							fail: () => {
								this.popLoading = false
							}
						});

					},
					fail: err => {
						console.log(err, 'err');
						this.popLoading = false
					}
				})
			},
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">

</style>