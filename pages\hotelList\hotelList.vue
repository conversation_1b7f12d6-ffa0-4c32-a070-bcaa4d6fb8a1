<template>
	<view >
		<view class="mainPage" style="position: fixed;top:0;z-index: -1;width: 750rpx;height: 100vh;" :style="{background: 'linear-gradient(180deg, '+ themeColor.bg_main_color+'40'+' 0%, '+ '#F5F5F5 40%)'}">
		
		</view>
		<view class="" style="position: fixed;top: 0;width: 750rpx;z-index:9;">
			<view class="" :style="{marginTop:searchBarTop + 'px',height:searchBarHeight + 'px',opacity:opacity}"
				style="display: flex;align-items: center;z-index: 9;position: relative;justify-content: center;">
				<view class="" @click="goMain"
					style="position: absolute;left: 30rpx;top: 0;bottom:0;margin: 0 auto;height: 100%;display: flex;align-items: center;">
					<uni-icons type="left" size="22"></uni-icons>
				</view>
				<text @click="goMain" style="color: #000000;">酒店列表</text>
			</view>
		
		</view>
		<view class="" style="" :style="{height:navBarHeight+'px'}"></view>
		<m-searchBox v-model="keyword" :mode="2" :if_city="ifCity"></m-searchBox>
		<m-hotelList :hotel_List="hotelList"></m-hotelList>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				hotelList: [],
				keyword: '',
				params: {
					page: 1,
					limit: 10
				},
				bool: true,
				ifCity: false,
				city_code: '',
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				opacity:1,
				imgHeight:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'city','startDate','endDate'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options,this.startDate,this.endDate);
			if (options && options.type == 'city') {
				// 查询酒店列表
				this.ifCity = true
			} else {
				this.ifCity = false
			}
		},
		async onShow() {
			await this.$onLaunched;
			const systemInfo = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo.statusBarHeight + 44;
			// #ifdef MP-WEIXIN || APP-PLUS
			// 获取状态栏和胶囊位置
			const {
				top,
				height
			} = uni.getMenuButtonBoundingClientRect()
			this.imgHeight = (top + height + 10) * 0.6;
			// #endif
			
			// #ifdef H5
			this.imgHeight = 100;
			// #endif
			this.params.page = 1
			this.params.date = this.$moment(this.startDate*1000).format('YYYY-MM-DD')
			// 获取定位权限
			uni.authorize({
				scope: 'scope.userLocation',
				success: () => {

					uni.getLocation({
						type: 'wgs84',
						success: (res) => {
							this.params.latitude = res.latitude
							this.params.longitude = res.longitude
							if (this.ifCity) {
								// 查询酒店列表
								this.params.city_code = this.city.city_code
								this.getList()
							} else {
								this.getList()
							}
						},
					})
				},
				fail: () => {
					// this.$iBox.auth('getLocation',
					// 	'小程序需要获取您的位置，为您推荐合适的入住酒店');
					if (this.ifCity) {
						// 查询酒店列表
						this.params.city_code = this.city.city_code
						this.getList()
					} else {
						this.getList()
					}
				}
			})
		},
		methods: {
			...mapActions('hotel', ['getHotelList', 'getHotel']),
			goMain(){
				uni.navigateBack()
			},
			getList() {
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getShopList', this.params)({
					method: 'post'
				}).then(res => {
					
					if (this.ifCity) {
						// 查询酒店列表
						this.hotelList = res.data.list
						
					} else {
						this.hotelList = res.data.list
						this.getHotelList(res.data.list)
					}
					
					

					uni.hideLoading()
				})
			}
		},
		onPageScroll(e) {
			let calc = 1 - (e.scrollTop) / this.imgHeight;
			this.opacity = calc
		},
		// // 上拉加载
		onReachBottom() {

			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getShopList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.hotelList.concat(res.data.list)
					this.hotelList = new_list
					console.log(this.hotelList.length, res.data.count);
					if (this.hotelList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>
<style scoped lang="scss">

</style>
