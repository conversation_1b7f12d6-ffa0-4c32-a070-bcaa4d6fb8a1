<template>
	<view>
		<view class="titleList">
			<m-tabs :list="titleList" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="changeInvoce" v-if="hackReset"
				:activeIndex="current" :config="{color:themeColor.text_main_color,
							  fontSize:30,
							  activeColor:'#0055ff',
							  underLineColor:themeColor.com_color1,
							  underLineWidth:80,
							  underLineHeight:0}">
			</m-tabs>
			<m-tabs :list="statusList" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="changeStatus" v-if="hackReset"
				:activeIndex="current1" :config="{color:themeColor.text_main_color,
							  fontSize:30,
							  activeColor:themeColor.com_color1,
							  underLineColor:themeColor.com_color1,
							  underLineWidth:80,
							  underLineHeight:5}">
			</m-tabs>
		
			
			<view class=""
				style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
				v-if="recordList.length==0">
				<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;"
					:style="{color:themeColor.com_color1}">
				</view>
				<p :style="{color:themeColor.com_color1}">暂无订单</p>
			</view>
			<view class="billContent" v-for="item in recordList" :key="item.id" v-else>
				<view class="billContent" v-if="statusIndex==0">
					<view class="" style="width: 70%;display: flex;flex-direction: column;justify-content: center;" @click="goDetail(item)">
						<p>订单ID:{{item.id}}</p>
						<p>订单类型:订房订单</p>
						<p>订单金额:￥{{item.bill_amount}}</p>
						<p style="font-size: 24rpx;color: #333;padding-top: 16rpx;">查看订单详情</p>
					</view>
					<view class="" v-if="statusIndex==0" style="width: 30%;display: flex;align-items: center;justify-content: center;" :style="{color:themeColor.main_color}" @click="invoiceCenter(item)">
						<view class="" style="width: fit-content;padding: 20rpx;border-radius: 6rpx;" :style="'border: 1px solid'+themeColor.main_color">
							申请开票
						</view>
						
					</view>
				</view>
				<view class="billContent" v-else>
					<view class="" style="width: 70%;display: flex;flex-direction: column;justify-content: center;" @click="goDetail(item)">
						<p>订单ID:{{item.bill_id}}</p>
						<p>订单类型:订房订单</p>
						<p>订单金额:￥{{item.bill_amount}}</p>
						<p style="font-size: 24rpx;color: #333;padding-top: 16rpx;">查看订单详情</p>
					</view>
					<view class="" v-if="statusIndex==1" style="width: 30%;display: flex;;align-items: center;justify-content: center;flex-direction: column;" :style="{color:themeColor.main_color}">
						<p>{{item.status==0?'待处理':(item.status==1?'处理中':(item.status==2?'完成':'拒绝'))}}</p>
						<p style="color: #909399;font-size: 24rpx;" v-if="item.status==2">酒店已处理请检查电子邮箱或快递,若有问题请及时联系酒店!</p>
					</view>
				</view>
				
				
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				statusList:[{
					id: 1,
					name: '可申请'
				},{
					id: 2,
					name: '已申请'
				}],
				titleList: [{
					id: 1,
					name: '入住订单'
				},
				{
					id: 2,
					name: '超市订单'
				}],
				current:0,
				current1:0,
				invoceIndex:0,
				statusIndex:0,
				recordList:[],
				bill_type_id:'',
				params:{
					page:1,
					limit:10,
					status:''
				},
				bool:true,
				hackReset: true,
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		onShow() {
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			this.statusIndex = 0
			this.invoceIndex = 0
			this.current1 = 0
			this.current = 0
			this.$iBox.http('getBillType', {
				
			})({
				method: 'post'
			}).then(res => {
					
					if(this.invoceIndex == 0){ 
						this.bill_type_id = res.data.filter(item => {
							return item.sign == 'BOOK_ROOM'
						})[0].id
					}else if(this.invoceIndex==1){
						this.bill_type_id = res.data.filter(item => {
							return item.sign == 'STORE'
						})[0].id
					}
			
				
				this.$iBox.http('getAllBill', {
					bill_type_id:this.bill_type_id
				})({
					method: 'post'
				}).then(res => {
					this.recordList = res.data
				})
			
			})
		},
		methods:{
			...mapActions('hotel',['getHotelBillDetail']),
			changeStatus(e){
				console.log(e,'dsd');
				this.statusIndex = e
				this.$iBox.http('getBillType', {
				})({
					method: 'post'
				}).then(res => {
						if(this.invoceIndex == 0){ 
							this.bill_type_id = res.data.filter(item => {
								return item.sign == 'BOOK_ROOM'
							})[0].id
						}else if(this.invoceIndex==1){
							this.bill_type_id = res.data.filter(item => {
								return item.sign == 'STORE'
							})[0].id
						}
				
					if(e==0){
						this.$iBox.http('getAllBill', {
							bill_type_id:this.bill_type_id
						})({
							method: 'post'
						}).then(res => {
							this.recordList = res.data
						})
					}else{
						this.params.page=1
						this.$iBox.http('getInvoiceApply', this.params)({
							method: 'post'
						}).then(res => {
							this.recordList = res.data.list
						})
					}
					
					
				
				})
			},
			changeInvoce(e) {
				this.invoceIndex = e
				this.$iBox.http('getBillType', {
				})({
					method: 'post'
				}).then(res => {
						if(this.invoceIndex == 0){ 
							this.bill_type_id = res.data.filter(item => {
								return item.sign == 'BOOK_ROOM'
							})[0].id
						}else if(this.invoceIndex==1){
							this.bill_type_id = res.data.filter(item => {
								return item.sign == 'STORE'
							})[0].id
						}
				
					
					this.$iBox.http('getAllBill', {
						bill_type_id:this.bill_type_id
					})({
						method: 'post'
					}).then(res => {
						this.recordList = res.data
					})
				
				})
				
			},
			invoiceCenter(e){
				uni.navigateTo({
					url:'/packageA/invoiceCenter/invoiceCenter?bill_id=' + e.id +'&bill_type_id=' + this.bill_type_id
				})
			},
			goDetail(e){
				let billId = {
					id:''
				}
				if(this.statusIndex==0){
					billId.id = e.id
				}else{
					billId.id = e.bill_id
				}
				this.getHotelBillDetail(billId)
				uni.navigateTo({
					url:'/packageA/hotelBill/hotelBillDetail'
				})
			}
		},
		// // 上拉加载
		onReachBottom() {
			if(this.statusIndex==1){
				if (this.bool) {
					++this.params.page
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getInvoiceApply', this.params)({
						method: 'post'
					}).then(res => {
						let new_list = this.recordList.concat(res.data.list)
						this.recordList = new_list
						if (this.recordList.length == res.data.count) {
							this.bool = false
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}
			}
			
		
		}
	}
</script>

<style lang="scss" scoped>
	.titleList {
		display: flex;
		flex-direction: column;
		align-items: center;
		
		.titleItem {
			display: flex;
			align-items: center;
			justify-content: center;
			// width: 50%;
			height: 60rpx;
			border: 1px solid #ccc
		}
		
		.billContent {
			padding: 20rpx;
			display: flex;
			align-items: center;
			margin-top: 30rpx;
			width: 700rpx;
			min-height: 200rpx;
			border-radius: 20rpx;
			background: #FFFFFF;
		}
	}
</style>
