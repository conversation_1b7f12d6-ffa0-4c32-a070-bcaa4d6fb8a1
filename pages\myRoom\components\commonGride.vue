<template>
	<view class="serviceBox">
		<!-- 样式一 -->
		<view class="" v-if="styleModel==1">
			<view class="title" :style="{color:themeColor.text_main_color}" v-if="list.length>0&&ifShow">
				<text class="icon-fengefu"></text>
				<text>{{name}}</text>
			</view>
			<view class="gridBox" :style="{color:themeColor.text_main_color}">
				<view class="item1" v-for="item in list" @click="toDetail(item)" v-if="item.status==1">
					<image :src="item.icon" style="width: 80rpx;height: 80rpx;"></image>
					<text>{{item.title}}</text>
				</view>
			</view>
		</view>
		
		<view class=""  v-if="styleModel==2">
			<view class="title" :style="{color:themeColor.text_main_color}" v-if="list.length>0&&ifShow">
				<text>{{name}}</text>
			</view>
			<view class="" style="width: 718rpx;min-height: 214rpx;border-radius: 32rpx;padding: 24rpx;margin: 30rpx auto;"
			:style="{background:themeColor.com_color1+'20'}">
			
				<view class="gridBox" :style="{color:themeColor.text_main_color}">
					<view class="item1" v-for="item in list" @click="toDetail(item)" v-if="item.status ==1">
						<image :src="item.icon" style="width: 80rpx;height: 80rpx;"></image>
						<text :style="{color:themeColor.text_second_color}">{{item.title}}</text>
					</view>
				</view>
			</view>
		</view>
		

		<!-- wifi端弹窗 -->
		<m-popup mode="center" :show="is_wifi" @closePop="closeWifi">
			<view class="" style="width: 600rpx;height: 600rpx;border-radius: 30rpx;background-color: #ffffff;
			display: flex;flex-direction: column;align-items: center;justify-content: space-around;position: relative;">
				<view class="icon-close" @click="closeWifi"
					style="position: absolute;right: 30rpx;top: 30rpx;font-size: 44rpx;"></view>
				<text class="icon-Wi-Fi" style="font-size: 80rpx;"></text>
				<view class="" style="font-size: 40rpx;">
					<p>wifi名称:{{wifiInfo.wifi_name}}</p>
					<p style="margin-top: 30rpx;" @click="copyPs">wifi密码:{{wifiInfo.password}}
						<text style="color: #55aa7f;padding-left: 20rpx;font-size: 30rpx;">复制</text>
					</p>
				</view>

				<button type="primary" @click="goWifi">一键连接WIFI</button>
			</view>
		</m-popup>
		
		<!-- 车牌弹窗 -->
		<m-popup mode="center" :show="carNumberShow" @closePop="closeCarNumber">
			<view class="" style="width: 600rpx;height: 400rpx;border-radius: 30rpx;background-color: #ffffff;
			display: flex;flex-direction: column;align-items: center;position: relative;padding: 30rpx;justify-content: space-between;">
				<p>入住期间每次出入录入一次车牌（具体政策请询问酒店）</p>
				<p style="font-size: 44rpx;display: flex;align-items: center;">
					车牌号码：<text>{{carPlate?carPlate:'暂无车牌'}}</text>
				</p>
				<view class="" style="width: 400rpx;">
					<button type="primary" @click="addCar">{{carPlate?'更新车牌':'添加车牌'}}</button>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				ifShow: true,
				wifiInfo: null,
				is_wifi: false,
				carPlate:'',
				carNumberShow:false,
				
			};
		},
		props: {
			list: {
				type: Array
			},
			styleModel: {
				type: Number
			},
			name: {
				type: String
			},
			billDetail: {
				type: Object
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		mounted() {
			let a = 0
			this.list.forEach(item => {
				if (item.status == 1) {
					a++
				}
			})
			console.log(a, 'a', this.billDetail);
			if (a > 0) {
				this.ifShow = true
			} else {
				this.ifShow = false
			}
		},
		methods: {
			closeCarNumber(){
				this.carNumberShow = false
			},
			toDetail(e) {
					if (e.path == 'packageA/wifiList/wifiList') {
						if (this.billDetail&&this.billDetail.hardware_list.wifi_list.length > 0) {
							this.wifiInfo = this.billDetail.hardware_list.wifi_list[0]
							this.is_wifi = true
						} else {
							uni.navigateTo({
								url: '/' + e.path
							})
						}
					} else if(e.sign=='car_number'){
						this.carNumberShow = true
						
						this.$iBox
							.http('getRoomBillCarNumber', {
								bill_id: this.billDetail.id
							})({
								method: 'post'
							})
							.then(res => {
								this.carPlate = res.data.car_number
							});
					}else{
						uni.navigateTo({
							url: '/' + e.path
						})
					}
			},
			addCar(){
				wx.chooseLicensePlate({
					success: res => {
						console.log(res, 'car');
						this.carPlate = res.plateNumber
						uni.showLoading({
							title: '添加成功...'
						})
						this.$iBox
							.http('updateRoomBillCarNumber', {
								bill_id: this.billDetail.id,
								car_number: this.carPlate
							})({
								method: 'post'
							})
							.then(res => {
				
								uni.hideLoading()
							});
					}
				})
			},
			closeWifi() {
				this.is_wifi = false
			},
			goWifi() {
				this.startWifi()
			},
			copyPs() {
				uni.setClipboardData({
					data: this.wifiInfo.password, //要被复制的内容
					success: () => { //复制成功的回调函数
						uni.showToast({ //提示
				 		title: '复制成功'
						})
					},
				})
			},
			//初始化 Wi-Fi 模块
			startWifi() {
				var that = this;

				uni.startWifi({
					success: (res) => {
						//请求成功连接Wifi

						this.Connected();
					},
					fail: function(res) {
						console.log(res);
						uni.showToast({
							title: 'wifi连接失败',
							icon: 'none'
						});
					}
				});
			},
			//连接已知Wifi
			Connected() {
				var that = this;
				console.log(this.wifiInfo);
				uni.connectWifi({
					SSID: this.wifiInfo.wifi_name,
					password: this.wifiInfo.password,
					success: function(res) {
						uni.hideLoading()
						uni.showToast({
							title: 'wifi连接成功！'
						});
					},

					fail: function(res) {
						uni.hideLoading()
						uni.showModal({
							title: '提示',
							content: 'wifi连接失败!',
							showCancel: false,
							success: res => {

							}
						})
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.serviceBox {
		

		.title {
			padding: 30rpx 30rpx 0rpx 30rpx;
		}

		.gridBox {
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			padding: 0 30rpx;

			.item1 {
				display: flex;
				flex-direction: column;
				width: 33%;
				height: 150rpx;
				align-items: center;
				justify-content: space-around;
				margin-bottom: 30rpx;
				margin-top: 20rpx;
			}
		}
	}
</style>
