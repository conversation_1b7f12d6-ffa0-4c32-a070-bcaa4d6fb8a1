<template>
	<view>
		<view class="" style="width: 700rpx;margin: 0 auto;">
			<uni-easyinput prefixIcon="search" v-model="lock_alias" placeholder="请输入房间号" @input="inputLock">
				</uni-easyinput>
		</view>

		<!-- 排序选择器 -->
		<view class="sort-section" style="width: 700rpx;margin: 20rpx auto 0;">
			<view class="sort-tabs">
				<view class="sort-tab" :class="{'active': sortField === 'lock_alias'}" @click="setSortField('lock_alias')">
					<text class="tab-text">🏠 按房间号排序</text>
				</view>
				<view class="sort-tab" :class="{'active': sortField === 'electric_quantity'}" @click="setSortField('electric_quantity')">
					<text class="tab-text">🔋 按电量排序</text>
				</view>
			</view>
		</view>
		
		<view class=""
			style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
			v-if="lockListSearch&&lockListSearch.length==0">
			<view class="icon-suoding" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无门锁</p>
		</view>
		
		<view class="box_outer" style="background:#fff" v-else>
			<view class="box" v-for="item in lockListSearch" @click="goLock(item)" hover-class="bind_lock">
				<!-- <text class="icon-mimasuo" style="font-size: 80rpx;"></text> -->
				<view class="content" style="width: 100%;">
					<view class="" style="display: flex;align-items: center;width: 100%;justify-content: space-between;">
						<view class="">
							<text v-if="item.lock_alias">房间:{{item.lock_alias}}</text>
						</view>
					</view>
					<view class="">
						<text>锁名:{{item.lock_name}}</text>
					</view>
					<!-- <view class="">
						<text>锁ID:{{item.device_id}}</text>
					</view> -->
					<view style="display: flex;align-items: center;font-size: 22rpx;" v-if="item.electric_quantity">
						电量
						<view class="icon-iconset0252">
						</view>
						<text>:{{item.electric_quantity}}%</text>
					</view>
				</view>
				<!-- <view class=""
					style="width: 30%;height: 100%;display: flex;flex-direction: column;justify-content: center;align-items: center;">
					<view
						style="padding: 10rpx 20rpx;;width: fit-content;border-radius: 10rpx;display: flex;align-items: cenrer;justify-content: center;text-align: center;font-size: 23rpx;"
						:style="{background:themeColor.main_color,color:themeColor.button_text_color}"
						@click.stop="goLock(item)">查看详情</view>
				</view> -->
			</view>
			
		</view>
		<view class="" style="height: 100rpx;"></view>
		<!-- 添加按钮 -->
		<view class="addLock" @click="info" v-if="roleType('lock_del_add')"
			:style="{background:themeColor.com_color1,color:themeColor.bg_color}">
			<view class="icon-tianjia"></view>
			<text style="font-size: 24rpx;">添加门锁</text>
		</view>
	</view>
</template>

<script>
	const plugin = requirePlugin("yayaLock");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				params: {
					page: 1,
					limit: 50,
					lock_alias: '',
					sort: 'lock_alias' // 默认按房间号排序
				},
				bool: true,
				lockListSearch: [],
				lock_alias: '',
				sortField: 'lock_alias' // 默认选中房间号排序
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
		},
		watch: {
			lockListSearch: {
				handler(newDate, oldDate) {
					this.lockListSearch = this.lockListSearch
				},
				immediate: true,
				deep: true
			}
		},
		onLoad(options) {},
		onShow() {
			this.bool = true;
			this.params.page = 1;
			this.getYYList();
		},
		methods: {
			...mapActions('room', ['getLockData']),
			inputLock(){
				this.params.lock_alias = this.lock_alias
				this.params.page = 1
				this.bool = true;
				this.getYYList();
			},

			// 设置排序字段
			setSortField(field) {
				// 如果点击的是当前排序字段，不做任何操作
				if (this.sortField === field) {
					return;
				}

				// 设置新的排序字段
				this.sortField = field;
				this.params.sort = field;

				console.log('排序参数:', {
					sortField: this.sortField,
					sort: this.params.sort
				});

				// 显示排序加载状态
				uni.showLoading({
					title: '排序中...'
				});

				// 重新请求数据
				this.params.page = 1;
				this.bool = true;
				this.getYYList();
			},

			// 重置所有筛选和排序
			resetFilters() {
				this.lock_alias = '';
				this.params.lock_alias = '';
				this.sortField = 'lock_alias'; // 重置为默认的房间号排序
				this.params.sort = 'lock_alias';
				this.params.page = 1;
				this.bool = true;

				uni.showLoading({
					title: '重置中...'
				});

				this.getYYList();
			},
			getYYList() {
				uni.showLoading({
					title: '正在搜索设备'
				})
				this.params.page = 1
				this.$iBox.http('getYayaLockList', this.params)({
						method: 'post'
					})
					.then(res => {
						this.lockListSearch = res.data.list
						//首先通过蓝牙搜索
						this.refreshDeviceList()
						uni.hideLoading()
					})
			},

			goLock(e) {
				this.getLockData(e)
				if (this.roleType('lock_edit') || this.roleType('view_password') || this.roleType('open_lock')) {
					uni.navigateTo({
						url: '/packageA/manager/lockList/YYLock/lockSet'
					})

				} else {
					uni.showModal({
						title: '提示',
						content: '您没有编辑权限',
						showCancel: false,
						success() {

						}
					})
				}
			},
			roleType(e) {
				let role = this.roles_list.filter(item => {
					return item.permission == e
				})

				if (role.length > 0) {
					return true
				} else {
					return false
				}
			},
			info() {
				let add = this.roles_list.filter(item => {
					return item.permission == "lock_del_add"
				})

				if (add.length > 0) {
					console.log(this.roles_list, add, 'dad');
					uni.navigateTo({
						url: '/packageA/manager/lockList/YYLock/roomInfo'
					})
				} else {
					uni.showModal({
						title: '提示',
						content: '您没有添加/删除权限',
						showCancel: false,
						success() {

						}
					})
				}

			},
			//-----------------------------------------------------丫丫锁--------------------
			refreshDeviceList() {
				var that = this;
				console.log("开锁扫描");
				plugin.scanSmartLock(that.scanDeviceCallBack);
				//scanRefreshLock with params scanTime in millisecond and scanCallBack
				//plugin.scanRefreshLock(15000,this.scanDeviceCallBack)
			},
			/**
			 * 扫描设备回调
			 */
			scanDeviceCallBack(res) {
				var that = this;
				var dev = res.data.msg
				var deviceName = dev.localName
				let macname = dev.localName.slice(2, 14)
				let mac = ""
				for (let i = 0, len = macname.length; i < len; i++) {
					mac += macname[i];
					if (i % 2 == 1 && i <= len - 2) mac += ":";
				}
				console.log("丫丫锁: " + deviceName);
				console.log("scanDeviceCallBack: " + deviceName);
				let rssi = dev.RSSI
				let lockId = plugin.parseDeviceId(deviceName);
				let deviceType = plugin.parseDeviceType(deviceName);
				let devName = plugin.parseDeviceName(deviceName);
				let devMid = dev.deviceId;
				let lock = {
					"LOCKID": lockId,
					"RSSI": rssi,
					"LOCKNAME": mac,
					"DATA": {
						KEYLOCKID: lockId,
						KEYUSERNAME: devName,
						DEVICETYPE: deviceType,
						DEVMID: devMid
					}
				};

				let lockList = this.lockListSearch;

				if (lockList == null || lockList == undefined || lockList.length < 1) {
					uni.showToast({
						title: '未匹配到设备！'
					})
				} else {
					// 匹配到添加

					for (let item of lockList) {
						item.isShow = false
						// 判断是否初始化，如果lockList.isSettingMode为true则不可添加，代表已经初始化
						if (mac == item.device_id) {
							item.isShow = true
						}
					}
					console.log(lockList, 'lockList');
					this.lockListSearch = lockList
				}
			},

		},
		// // 上拉加载
		onReachBottom() {
			if (this.bool) {
				++this.params.page
				this.status = 'loadmore'
				// 确保分页加载时也使用当前的排序参数
				this.$iBox.http('getYayaLockList', this.params)({
					method: 'post'
				}).then(res => {
					console.log('我是返回', res.data)
					let new_list = this.lockListSearch.concat(res.data.list)
					this.lockListSearch = new_list
					if (this.lockListSearch.length == res.data.count) {
						this.bool = false
					}

					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	/* 排序选择器样式 */
	.sort-section {
		.sort-tabs {
			display: flex;
			background: #f8f9fa;
			border-radius: 12rpx;
			padding: 8rpx;
			border: 1rpx solid #e9ecef;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		}

		.sort-tab {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 16rpx 12rpx;
			border-radius: 8rpx;
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			cursor: pointer;
			position: relative;
			min-height: 60rpx;

			&.active {
				background: linear-gradient(135deg, #007aff 0%, #0056d3 100%);
				box-shadow: 0 4rpx 12rpx rgba(0, 122, 255, 0.4);
				transform: translateY(-2rpx);

				.tab-text {
					color: #fff;
					font-weight: 600;
				}
			}

			&:active {
				transform: scale(0.96);
			}

			&:not(.active):hover {
				background: rgba(0, 122, 255, 0.1);
			}
		}

		.tab-text {
			font-size: 26rpx;
			color: #495057;
			transition: all 0.3s ease;
			font-weight: 500;
			text-align: center;
		}
	}

	.box_outer {
		display: flex;
		flex-wrap: wrap;
		width: 750rpx;
		.box {
			height: 160rpx;
			width: 220rpx;
			border: 1px solid #eee;
			display: flex;
			align-items: center;
			padding: 0 6rpx;
			margin: 20rpx auto;
			border-radius: 20rpx;
			box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
			font-size: 24rpx;
			.content {
				display: flex;
				flex-direction: column;
		
				justify-content: center;
				padding-left: 20rpx;
			}
		}
	}
	

	.addLock {
		position: fixed;
		bottom: 10rpx;
		right: 0;
		left: 0;
		height: 80rpx;
		width: 600rpx;
		margin: 0 auto;
		border-radius: 20rpx;
		background-color: #CCCCCC;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.bind_lock {
		opacity: 0.9;
		background: #f7f7f7;
	}
</style>