<template>
	<view class="u-content">

		<view
			style="color: #FFFFFF;background-color: #007AFF;width: 100%;height: 80rpx;display: flex;align-items: center;justify-content: center;">
			{{statusText}}
		</view>
		<view style="margin-top: 60rpx;" v-if="content"><u-parse :html="content" :tag-style="style"
				:show-with-animation="true" :selectable="true"></u-parse></view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				content: ``,
				style: {
					// 字符串的形式
					p: 'color: red;font-size:32rpx',
					span: 'font-size: 30rpx'
				},
				wifi_id: '',
				status: 'loading',
				statusText:'正在连接wifi！',
				wifi: {},
				shop_id: '',
				title: ''
			};
		},
		computed: {
			...mapState('hotel', ['hotel'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			
			let scene = wx.getEnterOptionsSync()
			if (scene.query.scene && (scene.scene == 1047 || scene.scene == 1048 || scene.scene == 1049 || scene.scene == 1007 || scene.scene == 1008)) {
				let query = decodeURIComponent(scene.query.scene)
				//解析参数
				if (query.includes("wifi_id")) {
					this.wifi_id = this.$iBox.linkFormat(query, "wifi_id")
					this.shop_id = this.$iBox.linkFormat(query, "shop_id")
				}
			
			}

			console.log();
			let that = this;
			// this.$iBox
			// 	.http('getAdvertisement', { shop_id: this.shop_id })({
			// 		method: 'post'
			// 	})
			// 	.then(res => {
			// 		this.content = res.data.content;
			// 		this.title = res.data.title
			// 	});

			this.$iBox
				.http('getWifiById', {
					wifi_id: this.wifi_id
				})({
					method: 'post'
				})
				.then(res => {
					that.wifi = res.data;
					console.log(res, 'd000d');
					uni.getSystemInfo({
						success: function(res) {
							var system = '';
							that.platform = res.platform;
							if (res.platform == 'android') system = parseInt(res.system.substr(8));
							if (res.platform == 'ios') system = parseInt(res.system.substr(4));

							if (res.platform == 'android' && system < 6) {
								uni.showToast({
									title: '手机版本不支持',
									icon: 'none'
								});
								return;
							}
							if (res.platform == 'ios' && system < 11.2) {
								uni.showToast({
									title: '手机版本不支持',
									icon: 'none'
								});
								return;
							}

							if (res.platform == 'ios' && system > 11.2) {
								// 如果判断是ios系统并且符合条件，则显示提示页

								that.startWifi();
							} else {
								that.startWifi();
							}

							//2.初始化 Wi-Fi 模块
						}
					});
				});

			uni.hideLoading();

		},
		methods: {
			main() {
				uni.reLaunch({
					url: '/pages/index/index'
				})
			},
			//初始化 Wi-Fi 模块
			startWifi: function() {
				var that = this;
				uni.startWifi({
					success: function(res) {
						//请求成功连接Wifi

						that.Connected();
					},
					fail: function(res) {
						that.statusText = 'wifi连接失败'
						uni.showToast({
							title: 'wifi连接失败',
							icon: 'none'
						});
					}
				});
			},
			//连接已知Wifi
			Connected() {
				var that = this;

				uni.connectWifi({
					SSID: that.wifi.wifi_name,
					password: that.wifi.password,
					success:(res)=> {
						that.statusText = 'wifi连接成功'
						uni.hideLoading()
						uni.showToast({
							title: 'wifi连接成功！'
						});
					},

					fail: (res)=> {
						that.statusText = 'wifi连接失败'
						uni.hideLoading()
						uni.showModal({
							title: '提示',
							content: 'wifi连接失败!',
							showCancel: false,
							success: res => {

							}
						})
					}
				});
			}
		}
	};
</script>

<style></style>