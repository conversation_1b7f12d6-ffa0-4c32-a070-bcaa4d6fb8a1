<template>
	<view class="box">
		<view class="resBox">
			<view class="resTitle">
				<view class="icon-chenggong" style="font-size: 80rpx;" :style="{color:themeColor.main_color}"></view>
				<text style="font-size: 48rpx;font-weight: 600;padding-left: 20rpx;">充值成功</text>
			</view>
			
		</view>
		<view class="btnClass">
		
			
			<view class="goDetail" @click="goBack" :style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}">
				完成
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'unit', 'startDate', 'endDate', 'roomInfo', 'linkMan', 'shopSetting']),
			},
			onLoad() {
				this.$iBox.http('getUserInfo', {
					simple:false
				})({
					method: 'post'
				}).then(res => {
					let userInfo = res.data
					userInfo.session_key =  this.userInfo.session_key
					this.updateUserInfo(userInfo)
				})
			},
			methods:{
				...mapActions('login', ['updateUserInfo']),
				goBack(){
					uni.navigateBack()
				}
			}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #fff;
	}
	.box {
		height: 100vh;
		background: #fff;
	}
	
	.resBox{
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
		.resTitle{
			margin-top: 140rpx;
			display: flex;
			align-items: center;
		}
	}
	
	.btnClass{
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 80rpx 50rpx;
		
		.goDetail {
			height: 100rpx;
			width: 300rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>
