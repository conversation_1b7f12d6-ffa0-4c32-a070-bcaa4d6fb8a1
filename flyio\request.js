import Flyio from './interceptors'
import API from './apiUrl'
import Config from './config'
import Vue from 'vue'
import store from '../store'
let Fly = require("flyio/dist/npm/wx")

// 缓存管理器
class CacheManager {
	constructor() {
		this.cachePrefix = 'hotel_cache_'
		this.defaultExpireTime = 30 * 60 * 1000 // 30分钟
		this.cacheStrategies = {
			'hotel': 30 * 60 * 1000,      // 酒店信息30分钟
			'user': 24 * 60 * 60 * 1000,  // 用户信息24小时
			'ui': 60 * 60 * 1000,         // UI配置1小时
			'city': 7 * 24 * 60 * 60 * 1000, // 城市列表7天
			'setting': 60 * 60 * 1000     // 设置信息1小时
		}
	}

	// 生成缓存键
	generateKey(url, params = {}) {
		const paramStr = JSON.stringify(params)
		return `${this.cachePrefix}${url}_${this.hashCode(paramStr)}`
	}

	// 简单哈希函数
	hashCode(str) {
		let hash = 0
		for (let i = 0; i < str.length; i++) {
			const char = str.charCodeAt(i)
			hash = ((hash << 5) - hash) + char
			hash = hash & hash // 转换为32位整数
		}
		return Math.abs(hash).toString(36)
	}

	// 设置缓存
	set(url, params, data, customExpireTime) {
		try {
			const key = this.generateKey(url, params)
			const expireTime = customExpireTime || this.getCacheExpireTime(url)
			const cacheData = {
				data: data,
				timestamp: Date.now(),
				expireTime: expireTime,
				version: '1.0'
			}
			uni.setStorageSync(key, cacheData)
			console.log(`缓存已设置: ${key}`)
		} catch (error) {
			console.error('设置缓存失败:', error)
		}
	}

	// 获取缓存
	get(url, params) {
		try {
			const key = this.generateKey(url, params)
			const cacheData = uni.getStorageSync(key)

			if (!cacheData) {
				return null
			}

			// 检查是否过期
			if (this.isExpired(cacheData)) {
				this.remove(key)
				return null
			}

			console.log(`缓存命中: ${key}`)
			return cacheData.data
		} catch (error) {
			console.error('获取缓存失败:', error)
			return null
		}
	}

	// 检查缓存是否过期
	isExpired(cacheData) {
		return Date.now() - cacheData.timestamp > cacheData.expireTime
	}

	// 获取缓存过期时间
	getCacheExpireTime(url) {
		for (const [key, time] of Object.entries(this.cacheStrategies)) {
			if (url.toLowerCase().includes(key)) {
				return time
			}
		}
		return this.defaultExpireTime
	}

	// 删除缓存
	remove(key) {
		try {
			uni.removeStorageSync(key)
		} catch (error) {
			console.error('删除缓存失败:', error)
		}
	}

	// 清理过期缓存
	cleanup() {
		try {
			const info = uni.getStorageInfoSync()
			info.keys.forEach(key => {
				if (key.startsWith(this.cachePrefix)) {
					const cacheData = uni.getStorageSync(key)
					if (cacheData && this.isExpired(cacheData)) {
						this.remove(key)
					}
				}
			})
		} catch (error) {
			console.error('清理缓存失败:', error)
		}
	}
}

// 请求优化器
class RequestOptimizer {
	constructor() {
		this.pendingRequests = new Map() // 正在进行的请求
		this.requestQueue = [] // 请求队列
		this.maxRetries = 3 // 最大重试次数
		this.retryDelay = 1000 // 重试延迟
		this.cacheManager = new CacheManager()
	}

	// 生成请求唯一标识
	generateRequestId(url, params) {
		return `${url}_${JSON.stringify(params)}`
	}

	// 检查是否需要跳过缓存
	shouldSkipCache(url, config) {
		// 需要实时数据的接口列表
		const realTimeApis = ['getShopList', 'getUI']

		// 如果配置中明确要求跳过缓存
		if (config.skipCache) {
			return true
		}

		// 如果是实时接口，跳过缓存
		if (realTimeApis.includes(url)) {
			console.log(`实时接口跳过缓存: ${url}`)
			return true
		}

		return false
	}

	// 请求去重
	deduplicateRequest(url, params, config) {
		const requestId = this.generateRequestId(url, params)

		// 如果相同请求正在进行，返回现有的Promise
		if (this.pendingRequests.has(requestId)) {
			console.log(`请求去重: ${requestId}`)
			return this.pendingRequests.get(requestId)
		}

		// 检查是否需要跳过缓存
		const skipCache = this.shouldSkipCache(url, config)

		// 检查缓存（如果不跳过缓存）
		if (!skipCache) {
			const cachedData = this.cacheManager.get(url, params)
			if (cachedData) {
				console.log(`使用缓存数据: ${requestId}`)
				return Promise.resolve(cachedData)
			}
		}

		// 创建新请求
		const request = this.createRequest(url, params, config, skipCache)
		this.pendingRequests.set(requestId, request)

		// 请求完成后清理
		request.finally(() => {
			this.pendingRequests.delete(requestId)
		})

		return request
	}

	// 创建请求
	createRequest(url, params, config, skipCache = false) {
		return this.retryRequest(() => {
			return Flyio.request(API[url] || url, params, {
				...Config.flyConfig,
				...config
			}).then(res => {
				// 成功时缓存数据（如果不跳过缓存）
				if (Config.resSuccess.value.includes(res[Config.resSuccess.key])) {
					if (!skipCache) {
						this.cacheManager.set(url, params, res)
						console.log(`数据已缓存: ${url}`)
					} else {
						console.log(`跳过缓存: ${url}`)
					}
					return res
				} else {
					throw new Error(res.msg || '请求失败')
				}
			})
		}, this.maxRetries)
	}

	// 自动重试
	retryRequest(requestFn, maxRetries, currentRetry = 0) {
		return requestFn().catch(error => {
			if (currentRetry < maxRetries) {
				console.log(`请求重试 ${currentRetry + 1}/${maxRetries}:`, error.message)
				return new Promise(resolve => {
					setTimeout(() => {
						resolve(this.retryRequest(requestFn, maxRetries, currentRetry + 1))
					}, this.retryDelay * Math.pow(2, currentRetry)) // 指数退避
				})
			}
			throw error
		})
	}

	// 并行请求
	parallelRequest(requests) {
		const promises = requests.map(req => {
			return this.deduplicateRequest(req.url, req.params, req.config || {})
				.catch(error => {
					console.error(`并行请求失败 ${req.url}:`, error)
					return { error: error.message, url: req.url }
				})
		})
		return Promise.all(promises)
	}

	// 清理缓存
	clearCache() {
		this.cacheManager.cleanup()
	}
}

// 创建全局实例
const requestOptimizer = new RequestOptimizer()

// 异常情况的错误处理
const errorFunction = (reqConfig, err) => {
	// 如果有异常需要提示
	if (!reqConfig.errorAction && reqConfig.isErrorDefaultTip) {
		setTimeout(() => {
			Config.resError.tipShow(err)
		}, 0)
	}
	throw (err)
}
let promises = [] // 接收接口请求的promise数组
let loadingTimer = [] // loading的定时器


// 优化后的接口请求封装函数
const handleRequest = (url = '', data = {}) => {
	return (flyConfig = {}, defaultTipConfig = {}) => {
		let tipConfig = {
			...Config.reqConfig,
			...defaultTipConfig
		}

		// 使用请求优化器处理请求
		const optimizedRequest = requestOptimizer.deduplicateRequest(url, data, {
			...flyConfig,
			skipCache: flyConfig.skipCache || false
		})

		// 处理loading状态
		if (tipConfig.isLoading && !flyConfig.skipLoading) {
			// 延迟显示loading，避免闪烁
			const loadingTimer = setTimeout(() => {
				uni.showLoading({
					title: '加载中...',
					mask: true
				})
			}, 300)

			optimizedRequest.finally(() => {
				clearTimeout(loadingTimer)
				uni.hideLoading()
			})
		}

		// 添加到promise数组用于批量管理
		promises.push(optimizedRequest.catch(e => {}))
		Promise.all(promises).then(data => {
			if (data.length !== promises.length) return
			promises = [] // 所有请求完后清除promise数组
		}).catch(() => {
			promises = [] // 请求异常完后清除promise数组
		})

		return optimizedRequest.catch(err => {
			errorFunction(tipConfig, err.message || err)
		})
	}
}

// 批量请求函数
const batchRequest = (requests) => {
	return requestOptimizer.parallelRequest(requests)
}

// 清理缓存函数
const clearRequestCache = () => {
	requestOptimizer.clearCache()
}

// 手动设置缓存
const setRequestCache = (url, params, data, expireTime) => {
	requestOptimizer.cacheManager.set(url, params, data, expireTime)
}

// 获取缓存
const getRequestCache = (url, params) => {
	return requestOptimizer.cacheManager.get(url, params)
}

// 导出优化后的请求函数和工具
export default handleRequest
export {
	batchRequest,
	clearRequestCache,
	setRequestCache,
	getRequestCache,
	requestOptimizer
}
