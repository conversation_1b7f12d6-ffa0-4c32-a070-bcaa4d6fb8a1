<template>
	<view>
		<m-popup mode="bottom"  :show="manShow">
			<view class="manBox">
				<view class="" style="height: 180rpx;width: 500rpx;display: flex;flex-direction: column;
				align-items: center;justify-content: space-between;margin: 0 auto;">
					<p style="color: #000000E0;font-size: 40rpx;">公安提醒您</p>
					<p style="color: #00000066;font-size: 30rpx;">请如实选择入住人数、否则您将承担相应的法律责任!</p>
				</view>
				<view class="itemBox">
					<view class="item_choose" style="background:#b9b9b966;color:black" @click="chooseMan(1)">
						
						<text class="icon-renyuan" style="font-size: 100rpx;margin-top: 30rpx;"
						 :style="{color:themeColor.main_color}"></text>
						<text style="font-size: 30rpx;margin-top: 10rpx;">一人入住</text>
					</view>
					<view class="item_choose"  style="background:#b9b9b966;color:black" @click="chooseMan(max_user_count)" v-if="billDetail.max_user_count>1">
						
						<text class="icon-duoren" style="font-size: 100rpx;margin-top: 30rpx;"
						 :style="{color:themeColor.main_color}"></text>
						<text style="font-size: 30rpx;margin-top: 10rpx;">两人(及以上)入住</text>
					</view>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				manShow:false
			}
		},
		props:{
			if_show:{
				type:Boolean
			}
		},
		watch:{
			if_show:{
				handler(n,o){
					console.log(this.if_show,n,o);
					this.manShow = this.if_show
				},
				immediate:true,
				deep:true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
			...mapState('room', ['billDetail', 'roomBillUser']),
		},
		mounted() {
			
			console.log(this.billDetail,'chooseman');
		},
		methods: {
			closePop(){
				this.manShow = false
			},
			chooseMan(e){
				this.$emit('selectMan',e)
				this.manShow = false
			}
		}
	}
</script>

<style scoped lang="scss">
	.manBox {
		height: 50vh;
		width: 700rpx;
		padding: 30rpx;
		border-radius: 48rpx;
		.itemBox {
			display: flex;
			justify-content: space-around;
			margin-top: 80rpx;
			.item_choose {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				border-radius: 30rpx;
				height: 300rpx;
				width: 280rpx;
				
				position: relative;
			}
		}
	}

</style>
