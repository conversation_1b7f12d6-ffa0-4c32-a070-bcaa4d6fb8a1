<template>
	<view>
		<view class="avator_box">
			<button class=""
				style="width: 280rpx;height: 220rpx;border-radius: 50%;background-color: rgba(0, 0, 0, 0);color:#FFFFFF;position: relative;"
				id="shareBtn" open-type="chooseAvatar" type="primary" @chooseavatar="onChooseAvatar">
				<view class="iconfont"
					style="position: absolute;left: 42rpx;height: 220rpx;top: 0rpx;width: 220rpx;display: flex;align-items: center;justify-content: center;">
					<image :src="avatar_url" style="width: 140rpx;height: 140rpx;" mode=""></image>

				</view>
				<image src="../../static/images/edit.png" class="edit" mode=""></image>
			</button>

		</view>

		<view class="InfoBox">
			<view class="nameBox">
				<text style="color: red;">*</text>
				<text style="padding-right: 80rpx;">手机号:</text>
				<view class="" style="width: 300rpx;">
					<input type="number" v-model="phone" disabled="true" />
				</view>
				<text :style="{color: themeColor.main_color}" @click="editPhone">修改</text>
			</view>
			<view class="nameBox">
				<text style="color: red;">*</text>
				<text style="padding-right: 120rpx;">姓名:</text>
				<view class="" style="width: 400rpx;">
					<input type="text" placeholder="真实姓名(必填)" v-model="name" />
				</view>
			</view>
			<view class="nameBox">

				<text style="padding-right: 120rpx;">昵称:</text>
				<view class="" style="width: 400rpx;">
					<input type="nickname" @change="getNickname" placeholder="您得昵称(非必填)" v-model="nickname" />
				</view>
			</view>

			<view class="nameBox">

				<text style="padding-right: 90rpx;">身份证:</text>
				<view class="" style="width: 400rpx;">
					<input type="idcard" placeholder="用于自助入住(非必填)" v-model="idcard" />
				</view>
			</view>
			<view class="nameBox">
				<text style="padding-right: 120rpx;">生日:</text>
				<view class="" style="width: 400rpx;">
					<picker mode="date" :value="date" @change="bindDateChange" :end="date">
						<view class="uni-input">{{date}}</view>
					</picker>
				</view>
			</view>
			<view class="nameBox">
				<text style="padding-right: 120rpx;">性别:</text>
				<view class="" style="width: 400rpx;">
					<picker mode="selector" :value="sex" :range="list" @change="sexChange">
						<view class="uni-input">{{sex}}</view>
					</picker>
				</view>
			</view>
			<!-- 	<view class="check_contant">
				<checkbox-group>
					<label class="radio" style="font-size: 22rpx;">
						<checkbox :value="cb" checked="true" color="#0031d0" style="transform:scale(0.7)" />
						<text>购买即视为同意</text><text style="text-decoration:underline;"
							@click="agreement">{{hotel.shop_name}}会员用户协议</text>
					</label>
				</checkbox-group>
			</view> -->

			<view class="" style="width: 500rpx;margin: 80rpx auto;">
				<view class="btn_register"
					:style="{background:themeColor.com_color1,color:themeColor.bg_color}" @click="register">
					确认编辑</view>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				phone: '',
				name: '',
				nickname: '',
				avatar_url: '',
				idcard: '',
				date: '',
				sex: '保密',
				list: ['保密', '男', '女'],
				action: ''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		async onLoad() {
			await this.$onLaunched;
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					console.log(res.data);
					this.action = res.data + '/wx/Resource/uploadFile'
				}
			});
			this.phone = this.userInfo.phone ? this.userInfo.phone : ''
			this.nickname = this.userInfo.nickname ? this.userInfo.nickname : ''
			this.name = this.userInfo.name ? this.userInfo.name : ''
			this.idcard = this.userInfo.identification_number ? this.userInfo.identification_number : ''
			this.date = !this.userInfo.birthday && this.userInfo.birthday === null ? '填写您的生日' : this.userInfo.birthday
			this.sex = this.userInfo.gender == 0 ? '保密' : (this.userInfo.gender == 1 ? '男' : '女')
			this.avatar_url = this.userInfo.avatar_url ? this.userInfo.avatar_url : ''
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			bindDateChange(e) {
				this.date = e.detail.value
			},
			sexChange(e) {
				this.sex = e.detail.value == 0 ? '保密' : (e.detail.value == 1 ? '男' : '女')
			},
			editPhone() {
				uni.navigateTo({
					url: '/packageA/editPhone/editPhone'
				})
			},
			getNickname(e) {
				console.log(e);
				this.nickname = e.detail.value
			},
			onChooseAvatar(e) {
				
				const {
					avatarUrl
				} = e.detail
				console.log(e, 'ff',avatarUrl);
				uni.uploadFile({
					url: this.action, //仅为示例，非真实的接口地址
					filePath: avatarUrl,
					header: {
						'AUTHTOKEN': this.userInfo.user_token
					},
					formData: {
						'shop_id': this.hotel.id
					},
					name: 'file',
					success: (uploadFileRes) => {
						
						this.avatar_url = JSON.parse(uploadFileRes.data).data
					}
				});

			},
			register() {
				if (!this.phone) {
					uni.showToast({
						icon:'none',
						title: '为了保障您得会员服务，请填写手机号！'
					})
					return;
				}

				if (!this.name) {
					uni.showToast({
						icon:'none',
						title: '为了保障您得会员服务，请填写您的姓名！'
					})
					return;
				}
				console.log(this.$moment(this.date, 'YYYY-MM-DD').format('YYYY/MM/DD'));
				let params = {
					name: this.name,
					nickname: this.nickname,
					avatar_url: this.avatar_url,
					identification_number: this.idcard,
					identification_type: 1,
					birthday: this.$moment(this.date, 'YYYY-MM-DD').format('YYYY/MM/DD'),
					gender: this.sex == '保密' ? 0 : (this.sex == '男' ? 1 : 2)
				}

				// 修改会员信息
				this.$iBox.http('updateUserInfo', params)({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title: '提示',
						content: '修改信息成功',
						showCancel: false,
						success: (res) => {
							if (res.confirm) {
								// 更新用户信息
								this.$iBox.http('getUserInfo', {
									simple: false
								})({
									method: 'post'
								}).then(res => {
									let userInfo = res.data
									userInfo.session_key = this.userInfo.session_key
									this.updateUserInfo(userInfo)
								})
							}
						}
					})
				})

			}
		}
	}
</script>

<style lang="scss" scoped>
	.avator_box {
		display: flex;
		align-items: center;
		height: 320rpx;
		width: 100%;
		justify-content: center;
		flex-direction: column;
		position: relative;

		.avator {
			height: 140rpx;
			width: 140rpx;
			border-radius: 50%;
			z-index: 1;

		}

		.edit {
			position: absolute;
			width: 40rpx;
			height: 40rpx;
			bottom: 51rpx;
			right: 65rpx;
			z-index: 2;
		}
	}

	.InfoBox {
		padding: 40rpx;

		.codeBtn {
			padding: 10rpx;
			border-radius: 6rpx;
			font-size: 22rpx;
		}

		.nameBox {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			border-bottom: 1px solid #e4e7ed;
			// justify-content: space-between;
		}

		.check_contant {
			padding: 30rpx 0;
		}

		.btn_register {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 500rpx;
			height: 90rpx;
			border-radius: 20rpx;
		}
	}
</style>
