<template>
	<view>
		<m-popup mode="bottom" :show="showLogin" @closePop="closeLogin" :customStyles="customStyle">
			<view class="loginBox">
				<p style="font-size: 36rpx;font-weight: 600;">登录解锁更多精彩</p>
				<!-- <p style="font-size: 30rpx;color: #909399;">授权后可享受会员权益</p> -->
				<view class="" style="display: flex;flex-direction: column;align-items: center;">
					<image :src="hotel.cover_pic" style="width: 220rpx;height: 220rpx;border-radius: 50%;" mode=""></image>
					<p style="margin-top: 30rpx;font-size: 34rpx;font-weight: 700;">{{hotel?hotel.shop_name:'酒店预定'}}</p>
				</view>
				
				<view class="item2">
					<button type="primary" open-type="getPhoneNumber|agreePrivacyAuthorization" @getphonenumber="getPhoneNumber"
						style="background: #4e4eff;border-radius: 32rpx;width: 640rpx;font-size: 32rpx;height: 98rpx;display: flex;align-items: center;justify-content: center;"><text
							class="cuIcon-weixin text-white padding-right"></text>授权手机号</button>
					<p @click="goSuperMan" style="font-size: 28rpx;display: flex;align-items: center;margin-top: 40rpx;color: #909399;">点击手动获取</p>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	var log = require('../../plugins/log.js')
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-login",
		data() {
			return {
				showLogin: true,
				phone: ''
			};
		},
		props:{
			customStyle:{
				type:Object,
				default: null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'setting']),
		},
		mounted() {
			
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			closeLogin(){
				this.showLogin = false
				this.$emit('closeToLogin','success')
			},
			goSuperMan(){
				uni.navigateTo({
					url:'/packageA/memberInfo/memberInfo'
				})
			},
			// 获取手机号
			getPhoneNumber(e) {
				log.info('hello test hahaha') // 日志会和当前打开的页面关联，建议在页面的onHide、onShow等生命周期里面打
				log.warn('warn')
				log.error('error')
				log.setFilterMsg('filterkeyword')
				log.addFilterMsg('addfilterkeyword')
				let that = this;
				if (e.detail.errMsg === "getPhoneNumber:ok") {
					this.$iBox.http('getUserPhone', {
						iv: e.detail.iv,
						encrypted_data: e.detail.encryptedData,
						session_key: this.userInfo.session_key

					})({
						method: 'post'
					}).then(res2 => {
						let set =this.setting.filter(item => {
							return item.sign == 'auto_register_member'
						})
						if(set[0].property){
							let a = set[0].property.value
							if (a == 2) {
								// 注册会员
								this.$iBox.http('memberRegister', {
									phone: res2.data,
									shop_id: this.hotel.id ? this.hotel.id : ''
								})({
									method: 'post'
								}).then(resReg => {
								
									this.$iBox.http('getUserInfo', {
										simple: false
									})({
										method: 'post'
									}).then(resUser => {
										let userInfo = resUser.data
										userInfo.session_key = this.userInfo.session_key
										// 再判断是否是线下会员
										if (userInfo.grade_info && userInfo.grade_info
											.upgrade_growth_value > -1) {
											this.updateUserInfo(userInfo)
											uni.showToast({
												title: '授权手机成功！'
											})
											this.$emit('loginTo','success')
											this.showLogin = false
										} else {
											// 如果不是线下会员在组测会员
										
												let params = {
													phone: res2.data
												}
												this.$iBox.http('updateUserInfo', params)({
													method: 'post'
												}).then(resUp => {
													// 更新用户信息
													this.$iBox.http('getUserInfo', {
														simple: false
													})({
														method: 'post'
													}).then(res1 => {
														let userInfo = res1.data
														userInfo.session_key = this
															.userInfo.session_key
														this.updateUserInfo(
															userInfo)
														uni.showToast({
															title: '注册会员成功！'
														})
														this.$emit('loginTo','success')
														this.showLogin = false
													})
												}).catch(function(error) {
													console.log('网络错误', error)
												})
								
										}
								
									})
								})
								
							}else {
								// 注册会员
								this.$iBox.http('registerUserPhone', {
									phone: res2.data,
									shop_id: this.hotel.id ? this.hotel.id : ''
								})({
									method: 'post'
								}).then(resReg => {
								
									this.$iBox.http('getUserInfo', {
										simple: false
									})({
										method: 'post'
									}).then(resUser => {
										let userInfo = resUser.data
										userInfo.session_key = this.userInfo.session_key
										// 再判断是否是线下会员
										if (userInfo.grade_info && userInfo.grade_info
											.upgrade_growth_value > -1) {
											this.updateUserInfo(userInfo)
											uni.showToast({
												title: '授权手机成功！'
											})
											this.$emit('loginTo','success')
											this.showLogin = false
										} else {
											// 如果不是线下会员在组测会员
											// 注册会员
											this.$iBox.http('memberRegister', {
												phone: res2.data,
												shop_id: this.hotel.id ? this.hotel.id : ''
											})({
												method: 'post'
											}).then(res => {
												let params = {
													phone: res2.data
												}
												this.$iBox.http('updateUserInfo', params)({
													method: 'post'
												}).then(resUp => {
													// 更新用户信息
													this.$iBox.http('getUserInfo', {
														simple: false
													})({
														method: 'post'
													}).then(res1 => {
														let userInfo = res1.data
														userInfo.session_key = this
															.userInfo.session_key
														this.updateUserInfo(
															userInfo)
														uni.showToast({
															title: '注册会员成功！'
														})
														this.$emit('loginTo','success')
														this.showLogin = false
													})
												}).catch(function(error) {
													console.log('网络错误', error)
												})
											})
								
										}
								
									})
								})
							}
						}
						
						
						
					})
				}else{
					uni.showModal({
						title:'提示',
						content:e.detail.errMsg+';请重新点击授权！'
					})
				}
			},
		},
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">
	.loginBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		height: 60vh;
		padding: 30rpx;
		position: relative;
		.item2 {
			height: 360rpx;
			// width: 750rpx;
			margin: 0 auto;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}
	}
</style>