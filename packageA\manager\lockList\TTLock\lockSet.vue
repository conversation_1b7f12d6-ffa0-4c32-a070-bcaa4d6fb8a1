<template>
	<view>
		<p style="height: 60rpx;margin: 40rpx auto;width: 100%;display: flex;justify-content: center;font-size: 44rpx;">
			{{lockDetail.lock_alias?lockDetail.lock_alias:''}}
		</p>
		<p style="height: 60rpx;margin: 40rpx auto;width: 100%;display: flex;justify-content: center;font-size: 34rpx;">
			{{roomInfo.building_name}}({{roomInfo.floor_name}})
		</p>
		<view class="open" style="position: relative;">
			<!-- 蓝牙 -->
			<view class="blue">
				<view :class="Cstyle!='click'?'blueTeeth':'blueTeeth_active'" @click="open('click')"
					hover-class="bind_lock">
					<text class="icon-mimasuo" style="font-size: 80rpx;"></text>
					<text>蓝牙开锁</text>
				</view>
				<p style="height: 60rpx;margin: 20rpx auto;width: 100%;display: flex;justify-content: center;font-size: 34rpx;align-items: center;"
					@click="refreshTime">
					<uni-icons type="loop" size="24"></uni-icons>
					<text style="font-size: 28rpx;">点击校准锁时间</text>
				</p>
			</view>

		</view>


		<!--  -->
		<view class="boxContent" style="width: 100%;padding: 30rpx;">
			<view class="itemBox" @click="lockInfo" v-if="roleType('lock_edit')">
				<view class="itemContent">
					<view class="icon-bianji" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">修改锁名称</text>
						<text style="font-size: 24rpx;color: #ccc;">修改门锁的名称</text>
					</view>
				</view>
			</view>
			<view class="itemBox" @click="toPassWord"
				v-if="roleType('view_temp_password')||roleType('view_time_password')||roleType('view_forever_password')">
				<view class="itemContent">
					<view class="icon-yanzhengma" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">密码管理</text>
						<text style="font-size: 24rpx;color: #ccc;">查看管理随机密码</text>
					</view>
				</view>
			</view>
			<view class="itemBox" @click="delPass" v-if="roleType('delete_password')">
				<view class="itemContent">
					<view class="icon-yanzhengma" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">删除密码</text>
						<text style="font-size: 24rpx;color: #ccc;">管理删除密码</text>
					</view>
				</view>
			</view>
			<view class="itemBox" v-if="roleType('lock_edit')" @click="toRecord">
				<view class="itemContent">
					<text class="icon-lanya" style="font-size: 60rpx;"></text>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">操作记录</text>
						<text style="font-size: 24rpx;color: #ccc;">需要蓝牙连接</text>
					</view>
				</view>
			</view>
			<view class="itemBox" v-if="roleType('lock_edit')" @click="toRecordLock">
				<view class="itemContent">
					<view class="icon-dingdanhao" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">开锁记录</text>
						<text style="font-size: 24rpx;color: #ccc;">不需要连蓝牙</text>
					</view>
				</view>
			</view>
			<view class="itemBox" v-if="roleType('lock_edit')" @click="goSetting">
				<view class="itemContent">
					<view class="icon-shebeisheshi" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">锁设置</text>
						<text style="font-size: 24rpx;color: #ccc;">更改锁设置</text>
					</view>
				</view>
			</view>

			<view class="itemBox" @click="upShan">
				<view class="itemContent">
					<view class="icon-shebeisheshi" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">激活门锁刷卡</text>
						<text style="font-size: 24rpx;color: #ccc;">激活门锁刷卡功能</text>
					</view>
				</view>
			</view>
		</view>

		<view class="" style="height: 240rpx;">

		</view>

		<view hover-class="bind_lock" :style="{background:themeColor.main_color}" v-if="roleType('lock_del_add')"
			@click="delLock(lockDetail)"
			style="position: fixed;bottom: 0;width: 100%;height: 100rpx;border-top: 1px solid #ccc;display: flex;align-items: center;justify-content: center;">
			<text :style="{color: themeColor.bg_color }">重置门锁</text>
		</view>

		<!-- 自定义密码面板 -->
		<m-popup :show="show" @closePop="closePop" :mode="'bottom'">
			<view style="width: 700rpx;height: 600rpx;" class="flex flex-direction align-center justify-around">
				<view class="flex flex-direction justify-around align-center"
					style="width: 100%;height: 100%;padding: 20rpx;">
					<u-field v-model="passcode" label="设置" :required="true" placeholder="请填写需要设置的密码">
					</u-field>
					<view class="" style="display: flex;align-items: center;">
						<text>开始时间:</text>
						<input type="text" disabled="true" v-model="datetimesingle" style="height: 80rpx;border: 1px solid #eee;
						width: 380rpx;display: flex;padding: 0 17px;align-items: center;" @click="showDate">
						<u-picker mode="time" v-model="show2" @confirm="sureDate" :params="params"></u-picker>
					</view>
					<view class="" style="display: flex;align-items: center;">
						<text>失效时间:</text>
						<input type="text" disabled="true" v-model="datetimesingle1" style="height: 80rpx;border: 1px solid #eee;
						width: 380rpx;display: flex;padding: 0 17px;align-items: center;" @click="showDate1">
						<u-picker mode="time" v-model="show3" @confirm="sureDate1" :params="params"></u-picker>

					</view>
					<u-button type="success" size="mini" @click="sureSet">确定</u-button>
				</view>
			</view>
		</m-popup>

		<!-- 管理员密码面板 -->
		<m-popup v-model="show1" @closePop="closePop1">
			<view style="width: 600rpx;height: 300rpx;" class="flex flex-direction align-center justify-around">
				<view class="flex flex-direction justify-around align-center"
					style="width: 100%;height: 100%;padding: 20rpx;font-size: 40rpx;">
					<text>{{password?password:'门锁未设置管理员密码'}}</text>
				</view>

			</view>
		</m-popup>
	</view>
</template>

<script>
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				passcode: '',
				password: '',
				show: false,
				show1: false,
				show2: false,
				show3: false,
				datetimesingle: "",
				datetimesingle1: '',
				startTime: '',
				endTime: '',
				params: {
					year: true,
					month: true,
					day: true,
					hour: true,
				},
				roomInfo: null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		async onShow() {
			await this.$onLaunched;
			// 停止蓝牙扫描设备
			this.handleStopAllOperations()
			
			this.$iBox.http('getRoomByLockId', {
				lock_id: this.lockDetail.id,
				lock_type: "tongtong"
			})({
				method: 'post'
			}).then(res => {
				this.roomInfo = res.data
			})

			
				plugin.setShowLog(true, this.handleShowLog);
			
		},
		methods: {
			...mapActions('room', ['getType']),
			// 停止所有蓝牙操作，并退出操作中状态
			handleStopAllOperations() {
				plugin.stopAllOperations().then(res => {
					console.log(res);
				})
			},
			/* TODO 处理用户错误日志, 用户可自行操作日志上传 */
			handleShowLog(...args) {
				console.log("操作日志:", ...args);
			},
			closePop() {
				this.show = false
			},
			closePop1() {
				this.show1 = false
			},
			showDate() {
				this.show2 = true
			},
			showDate1() {
				this.show3 = true
			},
			sureDate(e) {
				console.log(e, 'confirm');
				this.datetimesingle = e.year + '/' + e.month + '/' + e.day + ' ' + e.hour + ':00:00'
			},
			sureDate1(e) {
				console.log(e, 'confirm');
				this.datetimesingle1 = e.year + '/' + e.month + '/' + e.day + ' ' + e.hour + ':00:00'
			},
			roleType(e) {
				let role = this.roles_list.filter(item => {
					return item.permission == e
				})

				if (role.length > 0) {
					return true
				} else {
					return false
				}
			},
			toRecord() {
				uni.navigateTo({
					url: '/packageA/manager/lockList/TTLock/lockRecord'
				})
			},
			toRecordLock() {
				uni.navigateTo({
					url: '/packageA/manager/lockList/TTLock/openRecord'
				})
			},
			goSetting() {
				uni.navigateTo({
					url: '/packageA/manager/lockList/TTLock/setting'
				})
			},
			upShan() {
				uni.navigateTo({
					url: '/packageA/manager/lockList/TTLock/cardSetting'
				})

			},
			delPass() {
				this.$iBox.http('getPasswordType', {})({
					method: 'post'
				}).then(res => {
					let a = res.data.filter(item => {
						return item.keyboardPwdType == 4
					})[0]
					this.getType(a)
					uni.navigateTo({
						url: '/packageA/manager/lockList/TTLock/lockPassword'
					})
				})
			},
			// 校准锁时间
			refreshTime() {
				uni.showLoading({
					title: '正在校准，请稍后！'
				})
				const start = Date.now();
				// 建议使用服务器时间
				plugin.setLockTime({
					lockData: this.lockDetail.lock_data,
					serverTime: Date.now(),
				}).then(res => {
					if (res.errorCode === 0) {
						wx.showToast({
							icon: "success",
							title: "锁时间已校准"
						});
						wx.hideLoading();
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'none',
							title: `校准锁时间失败:${res.errorMsg}`
						})
					}
				});
				// 调用设置锁时间接口，（！！为安全考虑，开锁时间请传入服务器时间）
				// 2.7.0版本开始，开锁接口成功后自动校准本地锁时间
				// plugin.setLockTime(Date.now(), this.lockDetail.lock_data, res => {
				// 	if (res.errorCode === 10003) {
				// 		console.log("获取版本信息时设备连接已断开", res)
				// 	}
				// }, deviceId).then(res => {
				// 	uni.hideLoading({});
				// 	if (!!res.deviceId) deviceId = res.deviceId;
				// 	console.log(res)
				// 	if (res.errorCode === 0) {
				// 		// uni.showToast({
				// 		// 	icon: 'none',
				// 		// 	title: `锁时间已校准--操作时间:${Date.now() - start}ms`
				// 		// })

				// 	} else {
				// 		// uni.showToast({
				// 		// 	icon: 'none',
				// 		// 	title: `校准锁时间失败:${res.errorMsg}`
				// 		// })

				// 	}
				// }, deviceId)
			},

			// 点击开锁
			toOpenDoor() {
				uni.showLoading({
					title: '正在开启智能锁,请等待！'
				})

				plugin.controlLock({
					/* 控制智能锁方式 3 -开锁, 6 -闭锁 */
					controlAction: 3,
					lockData: this.lockDetail.lock_data,
					serverTime: Date.now(),
				}).then(res => {
					if (res.errorCode == 0) {
						wx.showToast({
							icon: "success",
							title: "已开锁"
						});
						wx.hideLoading();
						this.toReadRecord();
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'none',
							title: `开锁失败: ${res.errorMsg}`
						})
					}
				})

				// 调用开锁接口
				// plugin.controlLock(plugin.ControlAction.OPEN, this.lockDetail.lock_data, res => {
				// 	if (res.errorCode === 10003) {
				// 		console.log("获取版本信息时设备连接已断开", res)
				// 	}
				// }, null, deviceId).then(res => {
				// 	console.log(res)
				// 	if (!!res.deviceId) deviceId = res.deviceId;
				// 	if (res.errorCode === 0) {
				// 		uni.showToast({
				// 			icon: 'none',
				// 			title: `已开锁--操作时间:${Date.now() - start}ms.`
				// 		})
				// 		this.toReadRecord()
				// 	} else {
				// 		uni.showToast({
				// 			icon: 'none',
				// 			title: `开锁失败: ${res.errorMsg}`
				// 		})
				// 	}
				// })
			},

			setPass() {
				this.show = true
				// 设置开始结束时间
				this.datetimesingle = this.$moment().format('YYYY/MM/DD HH:00:00')
				this.startTime = this.$moment().format('YYYY-MM-DD HH:00:00')
				this.datetimesingle1 = this.$moment().add(3, 'hour').format('YYYY/MM/DD HH:00:00')
				this.endTime = this.$moment().add(3, 'hour').format('YYYY/MM/DD HH::00:00')
			},
			getPass() {
				uni.showLoading({
					title: 'loading...'
				})

				this.$iBox.http('createPassword', {
					id: this.lockDetail.id,
					keyboard_pwd_type: 2
				})({
					method: 'post'
				}).then(res => {
					this.show1 = true
					this.password = res.data.password
					//首先通过蓝牙搜索
					uni.hideLoading()
				})
			},
			sureSet() {
				if (this.passcode) {
					this.toGetDIYPasscode()
				} else {
					uni.showToast({
						icon: 'none',
						title: '请输入密码'
					})
				}

			},
			// 添加自定义密码
			toGetDIYPasscode() {
				uni.showLoading({
					title: '正在设置中...'
				})
				const startDate = this.datetimesingle;
				const endDate = this.datetimesingle1;
				const passcode = this.passcode;
				const start = new Date(startDate).getTime();
				const end = new Date(endDate).getTime();

				const startTime = Date.now();
				// 添加自定义密码
				plugin.createCustomPasscode(passcode, start, end, this.lockDetail.lock_data, res => {
					console.log(res)
					if (res.errorCode === 0) {
						this.$iBox.http('addKeyboardPwd', {
								id: this.lockDetail.id,
								password: passcode,
								start_date: start / 1000,
								end_date: end / 1000
							})({
								method: 'post'
							})
							.then(res => {
								uni.hideLoading()
								this.show = false
							})

					} else if (res.errorCode === 20) {
						uni.showToast({
							icon: 'none',
							title: '密码已存在，请重新设置！'
						})
					} else {
						uni.hideLoading()
						uni.showToast({
							icon: 'none',
							title: '密码设置失败'
						})
					}
				})
			},

			open(e) {
				console.log(e)
				this.Cstyle = e

				let a = setTimeout(() => {
					this.Cstyle = ''
					clearTimeout(a)
				}, 500)
				// 开锁前校准锁时间

				this.toOpenDoor()
			},
			lockInfo() {
				uni.navigateTo({
					url: './lockInfo'
				})
			},
			toPassWord() {
				uni.navigateTo({
					url: './lockPassType'
				})
			},
			lockRecord() {
				uni.navigateTo({
					url: './lockRecord'
				})
			},
			// 读取操作记录
			toReadRecord() {
				uni.showLoading({
					title: `正在读取锁内操作记录`,
				})
				// this.lockDetail.lock_data
				const start = Date.now();
				// 获取操作记录
				plugin.getOperationLog({
					/* 读取操作记录方式 1 -全部, 2 -最信 */
					logType: 2,
					lockData: this.lockDetail.lock_data
				}).then(res => {
					uni.hideLoading({});
					console.log(res, '读取最新操作记录')
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'success',
							title: `操作记录已获取--操作时间::${Date.now() - start}`
						})
						this.$iBox.http('uploadOpenRecord', {
								id: this.lockDetail.id,
								records: res.log
							})({
								method: 'post'
							})
							.then(res => {

							})
					} else {
						uni.showToast({
							icon: 'error',
							title: "读取操作记录失败:" + res.errorMsg
						})

					}
				})
			},
			delLock(e) {
				let that = this
				uni.showLoading({
					icon: 'none',
					title: '正在重置智能锁'
				})
				/**
				 * 调用重置接口 
				 * 请传入钥匙lockData, 初始化返回的lockData不做任何限制，直接使用调用接口仅适用于本地测试
				 */
				plugin.resetLock({
					lockData: this.lockDetail.lock_data
				}).then(res => {
					if (res.errorCode == 0) {
						// 同步到服务器
						this.$iBox.http('delTtLock', {
							id: this.lockDetail.id,
						})({
							method: 'post'
						}).then(res => {
							wx.hideLoading();
							if (!!res) {
								uni.showToast({
									icon: "none",
									title: '智能锁已删除',
									complete: (res) => {
										this.toReadRecord()
										setTimeout(() => {
											uni.navigateBack();
										}, 1000);
									}
								})
							} else {
								uni.showToast({
									icon: 'error',
									title: "同步服务器失败，锁已重置"
								})
							}
						})
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'error',
							title: `重置失败: ${res.errorMsg}`
						})
					}
				})


				// plugin.resetLock(this.lockDetail.lock_data, res => {
				// 	if (res.errorCode === 10003) {
				// 		console.log("监控到设备连接已断开", res)
				// 	}
				// }, deviceId).then(res => {
				// 	wx.hideLoading({});
				// 	if (!!res.deviceId) deviceId = res.deviceId;
				// 	console.log(res)
				// 	if (res.errorCode === 0) {
				// 		// 同步到服务器
				// 		this.$iBox.http('delTtLock', {
				// 			id: this.lockDetail.id,
				// 		})({
				// 			method: 'post'
				// 		}).then(res => {
				// 			if (!!res) {
				// 				uni.showToast({
				// 					icon: "none",
				// 					title: '智能锁已删除',
				// 					complete: (res) => {
				// 						this.toReadRecord()
				// 						setTimeout(() => {
				// 							uni.navigateBack();
				// 						}, 1000);
				// 					}
				// 				})
				// 			} else {
				// 				uni.showToast({
				// 					icon: 'success',
				// 					title: "同步服务器失败，锁已重置"
				// 				})
				// 			}
				// 		})
				// 	} else {
				// 		uni.showToast({
				// 			icon: 'error',
				// 			title: `重置失败: ${res.errorMsg}`
				// 		})
				// 	}
				// })
			}
		},
		onHide() {
			this.handleStopAllOperations();
		},

		/**
		 * 生命周期函数--监听页面卸载
		 */
		onUnload() {
			this.handleStopAllOperations();
		},
	}
</script>

<style lang="scss" scoped>
	.blue {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
	}

	.blueTeeth {
		height: 200rpx;
		width: 200rpx;
		margin: 0rpx auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #55555533;
		border-radius: 50%;
		color: #00557f;
		position: relative;

		.img_b {
			width: 80rpx;
			height: 80rpx;

		}


	}

	.blueTeeth_active {
		height: 240rpx;
		width: 240rpx;
		margin: 100rpx auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #55555533;
		border-radius: 50%;
		color: #00557f;
		position: relative;
		animation: mymove 0.5s infinite;
		/*轮流反向播放动画。*/
		animation-iteration-count: 1;


		/*动画的速度曲线*/
		.img_b {
			width: 200rpx;
			height: 200rpx;

		}
	}


	@keyframes mymove {
		0% {
			transform: scale(1);
			/*开始为原始大小*/
		}

		50% {
			transform: scale(1.1);
		}

		100% {
			transform: scale(1);
		}

	}

	.bind_lock {
		opacity: 0.9;
		background: #f7f7f7;
	}

	.boxContent {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 300rpx;

		.itemBox {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50%;
			height: 200rpx;

			margin-top: 20rpx;

			.itemContent {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 14rpx;
				width: 92%;
				height: 90%;
				border-radius: 20rpx;
				background-color: #fff;

				.itemContentText {
					display: flex;
					flex-direction: column;
					justify-content: end;
				}
			}
		}
	}
</style>