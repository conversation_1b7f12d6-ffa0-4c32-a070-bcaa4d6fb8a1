<template>
	<view style="width: 100%;" v-if="show">
		<view class="m-tabs_box" :style="{background:themeColor.bg_color}" v-if="mode==1">
			<scroll-view scroll-x="true" class="scroll-view-h">

				<view class="m-tabs_box_nav">
					<view v-for="item in list" class="m-tabs_box_nav_item" :key="item.id" @click="chooseType(item)">
						<text
							:style="item.id==itemCurrent?'font-weight:500;font-size:42rpx;'+'color:'+themeColor.text_main_color:'font-weight:300;font-size:38rpx;'+'color:'+themeColor.text_main_color">{{item.name}}</text>
						<view class="m-tabs_box_nav_border" v-if="item.id==itemCurrent"
							:style="{ 'background-image':'linear-gradient(90deg,'+ themeColor.main_color + ' 20%, #FFFFFF 100%)'}">
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-roomType",
		props: {
			show: {
				type: Boolean,
				default: true
			},
			mode: {
				type: [Number, String],
				default: 1
			},
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['unit', 'startDate', 'endDate', 'saleTypes','hotel', 'shopSetting']),
		},
		data() {
			return {
				itemCurrent: 0,
				list: [],
			};
		},
		watch: {

		},
		async mounted() {
			await this.$onLaunched;
			this.$iBox.http('getSaleType', {shop_id:this.hotel.id})({
				method: 'post'
			}).then(res => { 
				this.list = res.data
				let type = this.list.filter(item => {
					return item.sign == this.unit
				})[0]
				this.itemCurrent = type?type.id:res.data[0].id
			})
			console.log(this.unit, 'saleTypes');

			
		},
		updated() {},
		beforeDestroy() {
			uni.$off('onShow')
		},
		methods: {
			...mapActions('hotel', ['getUnit', 'getChooseDate']),
			chooseType(e) {
				this.itemCurrent = e.id
				console.log(e.sign,'dddd');
				this.getUnit(e.sign)
				
				// 获取每天的实时时间，如果时间小于当天这代表过期弹框提示重新刷新页面
				
				let enter_time = this.shopSetting.filter(item => {
					
					return item.sign == 'enter_time'
				})[0].property.value
				
				enter_time = Number(enter_time.split(':')[0]) 
				
				let date = {}
				// 判断是否为凌晨
				if (0 <= this.$moment().get('hours') && this.$moment().get('hours') < enter_time) {
					if (e.sign == 'standard') {
						date.startDate = this.$moment().subtract(1, 'day').startOf('day').unix()
						date.endDate = this.$moment().startOf('day').unix()
					} else if(e.sign == 'long_standard'){
						date.startDate = this.$moment().subtract(1, 'day').startOf('day').unix()
						date.endDate = this.$moment().startOf('day').add(7,'day').unix()
					} else {
						date.startDate = this.$moment().startOf('day').unix()
					}

				} else {
					if (e.sign == 'standard') {
						date.startDate = this.$moment().startOf('day').unix()
						date.endDate = this.$moment().add(1, 'day').startOf('day').unix()
					} else if(e.sign == 'long_standard'){
						date.startDate = this.$moment().startOf('day').unix()
						date.endDate = this.$moment().add(7, 'day').startOf('day').unix()
					} else {
						date.startDate = this.$moment().startOf('day').unix()
					}

				}

				this.getChooseDate(date)
				this.$emit('chooseType',e.id)



			}
		},


	}
</script>

<style lang="scss" scoped>
	.m-tabs_box {
		// height: 140rpx;
		// padding: 10rpx;
		// width: 100%;
		display: flex;
		flex: 1;
		white-space: nowrap;
		width: 750rpx;

		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		// margin: 20rpx auto;
		// border-radius: 20rpx;
		// width: 100%;
		.scroll-view-h {
			white-space: nowrap;
			width: 100%;
			height: 100%;
			box-sizing: border-box;

			.m-tabs_box_nav {
				width: 100%;
				height: 100%;
				display: flex;
				align-items: center;
				position: relative;

				&_border {
					width: 100%;
					height: 16rpx;
					border-radius: 10rpx;
					margin-top: -10rpx;
					z-index: 2;
					// background-color: l;
					animation-name: bordermove;
					animation-duration: 200ms;
					animation-iteration-count: 1;
					/*无限循环*/
					animation-timing-function: ease;

					@keyframes bordermove {
						0% {
							width: 0;
						}

						50% {
							width: 35rpx
						}

						100% {
							width: 70rpx
						}
					}
				}



				&_item {
					padding: 16rpx 40rpx;
					// display: flex;
					// flex-direction: column;
					// justify-content: center;
					// align-items: center;
					font-weight: 300;
					font-size: 36rpx;
				}
			}

		}
	}

	.m-tabs_box1 {
		padding: 30rpx;
		// width: 100%;
		display: flex;
		flex: 1;
		white-space: nowrap;

		&_nav {
			display: flex;
			width: 100%;
			padding: 10rpx 20rpx;
			justify-content: center;



			&_item {
				// margin: 0 30rpx;
				width: 200rpx;
				padding: 20rpx 40rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-weight: 300;
				font-size: 36rpx;
				border-radius: 30rpx;
			}
		}
	}
</style>
