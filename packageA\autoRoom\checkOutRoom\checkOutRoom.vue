<template>
	<view>
		<view class="roomInfo">
			<view class="nameBox" style="display: flex;justify-content: space-between;align-items: center;">
				<text style="font-size: 28rpx;font-weight: 600;">{{billDetail.room_type_name}}</text>
				<text v-if="billDetail.bill_status==8" style="font-size: 22rpx;color: brown;">正在申请退房,请等待酒店确认！</text>
			</view>
			<!-- 全日房 -->
			<view class="elseBox" v-if="billDetail.room_sale_type_sign=='standard'">
				<text style="">{{billDetail.enter_time_plan | moment}} 至 {{billDetail.leave_time_plan | moment}}</text>
				<text style="padding-left: 10rpx;">{{billDetail.stay_time+ '晚'}}</text>
				<text></text>
			</view>
			<!-- 时租房会议室 -->
			<view class="elseBox"
				v-if="billDetail.room_sale_type_sign=='hour'||billDetail.room_sale_type_sign=='conference_room'">
				<text style="">{{billDetail.enter_time_plan | moment}} </text>
				<text style="padding-left: 10rpx;">{{billDetail.stay_time+ '小时'}}</text>
				<text></text>
			</view>
			<!-- 月租 -->
			<view class="elseBox" v-if="billDetail.room_sale_type_sign=='long_standard'">
				<text style="">{{billDetail.enter_time_plan | moment}} 至 {{billDetail.leave_time_plan | moment}}</text>
				<text style="padding-left: 10rpx;">{{billDetail.stay_time+ '月'}}</text>
				<text></text>
			</view>
			<p><text style="font-size: 24rpx;">{{billDetail.room_service.service_name}}</text></p>
			<view class="manInfo">
				<view class="manInfo_title">
					<text class="manInfo_title1" style="padding-right: 60rpx;">入住人</text>
					<text>{{billDetail.link_man}}</text>
				</view>
				<view class="manInfo_title">
					<text class="manInfo_title1">联系手机</text>
					<text>{{billDetail.link_phone}}</text>
				</view>
			<!-- 	<view class="manInfo_title">
					<text class="manInfo_title1">入住说明</text>
					<text>{{billDetail.enter_time_plan | moment2}}</text>之后入住，
					<text>{{billDetail.leave_time_plan | moment2}}</text>之前退房
				</view> -->
			</view>
		</view>

		<view class="orderInfo">
			<p style="font-size: 40rpx;font-weight: 600;line-height: 60rpx;">订单信息</p>
			<view class="manInfo_title">
				<text class="manInfo_title1">订单号:</text>
				<text>{{billDetail.bill_code}}</text>
			</view>
			<view class="manInfo_title">
				<text class="manInfo_title1">订单来源:</text>
				<text>{{billDetail.bill_source_name}}</text>
			</view>
			<view class="manInfo_title">
				<text class="manInfo_title1">下单时间:</text>
				<text>{{billDetail.create_time | moment}}</text>
			</view>
		</view>

		<view class="" style="margin: 50rpx auto;width: 700rpx;">
			<button type="primary" @click="checkOutTh">申请退房</button>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {

			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
			...mapState('room', ['billDetail', 'roomBillUser']),
		},
		methods: {
			// 退房 - 防重复触发版本
			checkOutTh() {
				this.$iBox.throttle1(() => {
					this.checkOut()
				}, 2000);
			},

			checkOut() {
				if(this.billDetail.bill_status==8){
					uni.showToast({
						icon:'error',
						title:'正在申请退房中，请等待酒店前台操作!',
						duration:1000
					})
					return
				}
				let params = {
					bill_id: this.billDetail.id
				}
				uni.showModal({
					title: '提示',
					content: '是否确定退房?',
					success: res => {
						if (res.confirm) {
							// 显示加载状态
							uni.showLoading({
								title: '退房中...'
							});

							if (this.billDetail.room_sale_type_sign == 'standard') {
								this.$iBox.http('standardApplyCheckOut', params)({
										method: 'post'
									})
									.then(res => {
										uni.hideLoading();
										uni.showToast({
											icon:'success',
											title:'退房成功!',
											duration:1000
										})
										uni.navigateBack({})
									})
									.catch(err => {
										uni.hideLoading();
										console.error('退房失败:', err);
									})
							} else if (this.billDetail.room_sale_type_sign == 'long_standard') {
									this.$iBox.http('longStandardApplyCheckOut', params)({
											method: 'post'
										})
										.then(res => {
											uni.hideLoading();
											uni.showToast({
												icon:'success',
												title:'退房成功!',
												duration:1000
											})
											uni.navigateBack({})
										})
										.catch(err => {
											uni.hideLoading();
											console.error('退房失败:', err);
										})
							} else if (this.billDetail.room_sale_type_sign == 'hour') {
									this.$iBox.http('hourApplyCheckOut', params)({
											method: 'post'
										})
										.then(res => {
											uni.hideLoading();
											uni.showToast({
												icon:'success',
												title:'退房成功!',
												duration:1000
											})
											uni.navigateBack({})
										})
										.catch(err => {
											uni.hideLoading();
											console.error('退房失败:', err);
										})
							} else {
									this.$iBox.http('conferenceApplyCheckOut', params)({
											method: 'post'
										})
										.then(res => {
											uni.hideLoading();
											uni.showToast({
												icon:'success',
												title:'退房成功!',
												duration:1000
											})
											uni.navigateBack({})
										})
										.catch(err => {
											uni.hideLoading();
											console.error('退房失败:', err);
										})
							}
						} else {

						}
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	// 房间详情
	.roomInfo {
		margin: 20rpx auto;
		border-radius: 30rpx;
		background: #ffffff;
		min-height: 200rpx;
		width: 94%;
		border: 1px solid transparent;
		padding: 20rpx;

		.nameBox {
			font-size: 40rpx;
			font-weight: 500;
		}

		.elseBox {
			font-size: 24rpx;
			color: #333;
		}

		.manInfo {
			margin-top: 20rpx;
			line-height: 60rpx;

			.manInfo_title {
				.manInfo_title1 {
					font-size: 28rpx;
					font-weight: 600;
					width: 190rpx;
					padding-right: 30rpx;
				}
			}
		}
	}

	// 订单信息
	.orderInfo {
		margin: 20rpx auto;
		border-radius: 30rpx;
		background: #ffffff;
		min-height: 200rpx;
		width: 94%;
		border: 1px solid transparent;
		padding: 20rpx;

		.manInfo_title {
			margin-top: 20rpx;

			.manInfo_title1 {
				font-size: 28rpx;
				font-weight: 600;
				width: 190rpx;
				padding-right: 30rpx;
			}
		}
	}
</style>