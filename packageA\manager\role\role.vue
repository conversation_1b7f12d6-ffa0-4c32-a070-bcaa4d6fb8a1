<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<view class="status-bar"></view>

		<!-- 头部区域 -->
		<view class="header-section">
			<view class="header-bg"></view>
			<view class="header-content">
				<view class="back-btn" @click="goBack">
					<u-icon name="arrow-left" size="20" color="#fff"></u-icon>
				</view>
				<view class="header-info">
					<text class="header-title">选择酒店</text>
					<text class="header-subtitle">请选择要管理的酒店</text>
				</view>
				<view class="header-decoration">
					<view class="decoration-dot"></view>
					<view class="decoration-dot"></view>
					<view class="decoration-dot"></view>
				</view>
			</view>
		</view>

		<!-- 酒店列表区域 -->
		<view class="hotel-list-container">
			<view class="list-header">
				<view class="list-title">
					<view class="title-icon">🏨</view>
					<text class="title-text">可管理酒店</text>
				</view>
				<view class="hotel-count">
					<text class="count-number">{{managerList.length}}</text>
					<text class="count-unit">家</text>
				</view>
			</view>

			<!-- 当前选中提示 -->
			<view class="current-selection-tip" v-if="itemIndex">
				<view class="tip-icon">✅</view>
				<text class="tip-text">当前选中：{{getCurrentHotelName()}}</text>
			</view>

			<view class="hotel-list">
				<view
					class="hotel-item"
					:class="{'hotel-item-selected': itemIndex == item.id}"
					v-for="(item, index) in managerList"
					:key="index"
					@click="changeHotelTh(item)"
				>
					<view class="hotel-avatar-container">
						<image
							class="hotel-avatar"
							src="../../../static/images/hotelImg.png"
							mode="aspectFill"
						></image>
						<view class="avatar-border" :class="{'avatar-border-active': itemIndex == item.id}"></view>
						<view class="status-indicator" v-if="itemIndex == item.id">
							<u-icon name="checkmark" size="12" color="#fff"></u-icon>
						</view>
					</view>

					<view class="hotel-info">
						<view class="hotel-name">{{item.shop_name}}</view>
						<view class="hotel-account">
							<view class="account-label">管理账号</view>
							<view class="account-value">{{item.name}}</view>
						</view>
					</view>

					<view class="hotel-actions">
						<view class="current-badge" v-if="itemIndex == item.id">
							<text class="badge-text">当前选中</text>
						</view>
						<view class="select-btn" v-else>
							<text class="select-text">选择</text>
						</view>
						<view class="arrow-icon" :class="{'arrow-icon-active': itemIndex == item.id}">
							<u-icon name="arrow-right" size="16" :color="itemIndex == item.id ? '#667eea' : '#ccc'"></u-icon>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部提示 -->
		<view class="bottom-tip">
			<view class="tip-icon">💡</view>
			<text class="tip-text">点击酒店卡片即可切换管理权限</text>
		</view>

		<!-- 选择说明 -->
		<view class="selection-guide" v-if="managerList.length > 0">
			<view class="guide-content">
				<view class="guide-item">
					<view class="guide-icon current">✅</view>
					<text class="guide-text">蓝色边框表示当前选中</text>
				</view>
				<view class="guide-item">
					<view class="guide-icon cache">🔄</view>
					<text class="guide-text">系统会记住您的选择</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				isHover: false,
				managerList: [],
				itemIndex: '',
				cacheKey: 'selected_hotel_id' // 缓存键名
			}
		},
		computed:{
			...mapState('login', ['userInfo', 'theme', 'menu']),
			...mapState('hotel', ['roles_list','manager']),
		},
		async onLoad() {
			await this.$onLaunched;

			console.log(this.manager);

			// 获取酒店列表
			this.$iBox
				.http('getBossList', {})({
					method: 'post'
				})
				.then(res => {
					this.managerList = res.data

					// 设置默认选中的酒店
					this.setDefaultSelectedHotel()
				})
		},
		methods: {
			...mapActions('hotel', ['getManager']),

			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				})
			},

			// 设置默认选中的酒店
			setDefaultSelectedHotel() {
				if (!this.managerList || this.managerList.length === 0) {
					return
				}

				// 1. 优先使用当前manager中的酒店ID
				if (this.manager && this.manager.id) {
					this.itemIndex = this.manager.id
					this.saveSelectedHotelToCache(this.manager.id)
					return
				}

				// 2. 其次从缓存中获取上次选择的酒店
				const cachedHotelId = this.getSelectedHotelFromCache()
				if (cachedHotelId) {
					// 验证缓存的酒店ID是否在当前列表中
					const cachedHotel = this.managerList.find(hotel => hotel.id === cachedHotelId)
					if (cachedHotel) {
						this.itemIndex = cachedHotelId
						return
					}
				}

				// 3. 最后默认选择第一个酒店
				if (this.managerList.length > 0) {
					this.itemIndex = this.managerList[0].id
					this.saveSelectedHotelToCache(this.managerList[0].id)
				}
			},

			// 从缓存中获取选中的酒店ID
			getSelectedHotelFromCache() {
				try {
					return uni.getStorageSync(this.cacheKey)
				} catch (e) {
					console.log('获取酒店缓存失败:', e)
					return null
				}
			},

			// 保存选中的酒店ID到缓存
			saveSelectedHotelToCache(hotelId) {
				try {
					uni.setStorageSync(this.cacheKey, hotelId)
					console.log('酒店选择已缓存:', hotelId)
				} catch (e) {
					console.log('保存酒店缓存失败:', e)
				}
			},

			// 获取当前选中酒店的名称
			getCurrentHotelName() {
				if (!this.itemIndex || !this.managerList) {
					return '未选择'
				}
				const currentHotel = this.managerList.find(hotel => hotel.id === this.itemIndex)
				return currentHotel ? currentHotel.shop_name : '未知酒店'
			},

			// 切换酒店 - 防重复触发版本
			changeHotelTh(item) {
				this.$iBox.throttle1(() => {
					this.changeHotel(item)
				}, 2000);
			},

			// 切换酒店
			changeHotel(item) {
				// 如果点击的是当前酒店，不执行切换
				if (this.itemIndex === item.id) {
					uni.showToast({
						title: '当前已是该酒店',
						icon: 'none',
						duration: 2000
					})
					return
				}

				// 显示加载提示
				uni.showLoading({
					title: '切换中...'
				})

				this.$iBox.http('chooseAdmin', {admin_id: item.id})({
					method: 'post'
				}).then(() => {
					// 更新Vuex状态
					this.getManager(item)

					// 更新本地选中状态
					this.itemIndex = item.id

					// 保存到缓存
					this.saveSelectedHotelToCache(item.id)

					uni.hideLoading()
					uni.showToast({
						title: `已切换到 ${item.shop_name}`,
						icon: 'success',
						duration: 2000,
						success: () => {
							setTimeout(() => {
								uni.navigateBack({
									delta: 1
								})
							}, 2000)
						}
					})
				}).catch((error) => {
					console.log('切换酒店失败:', error)
					uni.hideLoading()
					uni.showToast({
						title: '切换失败，请重试',
						icon: 'error',
						duration: 2000
					})
				})
			}
		}
	}
</script>

<style lang="scss">
	page {
		background: linear-gradient(180deg, #f8faff 0%, #f1f3f8 100%);
		min-height: 100vh;
	}

	/* 页面容器 */
	.page-container {
		min-height: 100vh;
		background: linear-gradient(180deg, #f8faff 0%, #f1f3f8 100%);
	}

	/* 状态栏占位 */
	.status-bar {
		height: var(--status-bar-height, 44rpx);
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	/* 头部区域 */
	.header-section {
		position: relative;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 30rpx 50rpx;
		overflow: hidden;
	}

	.header-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
		backdrop-filter: blur(10rpx);
	}

	.header-content {
		position: relative;
		z-index: 2;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.back-btn {
		width: 80rpx;
		height: 80rpx;
		background: rgba(255, 255, 255, 0.15);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		transition: all 0.3s ease;
	}

	.back-btn:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
	}

	.header-info {
		flex: 1;
		text-align: center;
		margin: 0 30rpx;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: 700;
		color: #fff;
		margin-bottom: 8rpx;
		letter-spacing: 1rpx;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}

	.header-subtitle {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		font-weight: 400;
	}

	.header-decoration {
		display: flex;
		flex-direction: column;
		gap: 8rpx;
	}

	.decoration-dot {
		width: 8rpx;
		height: 8rpx;
		background: rgba(255, 255, 255, 0.4);
		border-radius: 50%;
		animation: pulse 2s infinite;
	}

	.decoration-dot:nth-child(2) {
		animation-delay: 0.5s;
	}

	.decoration-dot:nth-child(3) {
		animation-delay: 1s;
	}

	@keyframes pulse {
		0%, 100% { opacity: 0.4; transform: scale(1); }
		50% { opacity: 1; transform: scale(1.2); }
	}

	/* 酒店列表容器 */
	.hotel-list-container {
		margin: -30rpx 30rpx 30rpx;
		background: #fff;
		border-radius: 28rpx 28rpx 0 0;
		box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
		overflow: hidden;
		position: relative;
		z-index: 3;
	}

	.list-header {
		padding: 40rpx 30rpx 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		border-bottom: 1rpx solid #f1f3f4;
		background: linear-gradient(135deg, #fafbff 0%, #f6f8fc 100%);
	}

	.list-title {
		display: flex;
		align-items: center;
	}

	.title-icon {
		font-size: 32rpx;
		margin-right: 12rpx;
		filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
	}

	.title-text {
		font-size: 32rpx;
		font-weight: 600;
		color: #1f2937;
		letter-spacing: 0.5rpx;
	}

	.hotel-count {
		display: flex;
		align-items: center;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		border: 1rpx solid rgba(102, 126, 234, 0.2);
	}

	.count-number {
		font-size: 28rpx;
		color: #667eea;
		font-weight: 600;
		margin-right: 4rpx;
	}

	.count-unit {
		font-size: 22rpx;
		color: #667eea;
		font-weight: 500;
	}

	/* 当前选中提示 */
	.current-selection-tip {
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 20rpx 30rpx;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
		border-radius: 20rpx;
		border: 1rpx solid rgba(14, 165, 233, 0.2);
		box-shadow: 0 4rpx 12rpx rgba(14, 165, 233, 0.1);
	}

	.current-selection-tip .tip-icon {
		font-size: 24rpx;
		margin-right: 12rpx;
		filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.1));
	}

	.current-selection-tip .tip-text {
		font-size: 26rpx;
		color: #0369a1;
		font-weight: 500;
		letter-spacing: 0.5rpx;
	}

	/* 酒店列表 */
	.hotel-list {
		padding: 20rpx 0 40rpx;
	}

	.hotel-item {
		display: flex;
		align-items: center;
		padding: 30rpx;
		margin: 0 20rpx 20rpx;
		background: #fff;
		border-radius: 24rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid transparent;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		overflow: hidden;
	}

	.hotel-item::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.02) 0%, rgba(118, 75, 162, 0.02) 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	.hotel-item:active {
		transform: scale(0.98);
	}

	.hotel-item:active::before {
		opacity: 1;
	}

	.hotel-item-selected {
		border-color: rgba(102, 126, 234, 0.3);
		box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
		background: linear-gradient(135deg, #fafbff 0%, #f8f9fe 100%);
	}

	.hotel-item-selected::before {
		opacity: 1;
	}

	/* 酒店头像容器 */
	.hotel-avatar-container {
		position: relative;
		margin-right: 24rpx;
	}

	.hotel-avatar {
		width: 120rpx;
		height: 120rpx;
		border-radius: 50%;
		border: 4rpx solid #f1f3f4;
		transition: all 0.3s ease;
	}

	.avatar-border {
		position: absolute;
		top: -4rpx;
		left: -4rpx;
		right: -4rpx;
		bottom: -4rpx;
		border: 3rpx solid transparent;
		border-radius: 50%;
		transition: all 0.3s ease;
	}

	.avatar-border-active {
		border-color: #667eea;
		box-shadow: 0 0 20rpx rgba(102, 126, 234, 0.3);
	}

	.status-indicator {
		position: absolute;
		top: -8rpx;
		right: -8rpx;
		width: 32rpx;
		height: 32rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		border: 3rpx solid #fff;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		animation: statusPulse 2s infinite;
	}

	@keyframes statusPulse {
		0%, 100% { transform: scale(1); }
		50% { transform: scale(1.1); }
	}

	/* 酒店信息 */
	.hotel-info {
		flex: 1;
		padding-right: 20rpx;
	}

	.hotel-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #1f2937;
		margin-bottom: 12rpx;
		letter-spacing: 0.5rpx;
		line-height: 1.3;
	}

	.hotel-account {
		display: flex;
		align-items: center;
		gap: 12rpx;
	}

	.account-label {
		font-size: 22rpx;
		color: #9ca3af;
		background: #f3f4f6;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-weight: 500;
	}

	.account-value {
		font-size: 26rpx;
		color: #4b5563;
		font-weight: 500;
	}

	/* 酒店操作区域 */
	.hotel-actions {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 12rpx;
	}

	.current-badge {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 8rpx 18rpx;
		border-radius: 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		animation: selectedPulse 2s infinite;
	}

	.badge-text {
		font-size: 20rpx;
		color: #fff;
		font-weight: 600;
		letter-spacing: 0.5rpx;
	}

	.select-btn {
		background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
		padding: 8rpx 18rpx;
		border-radius: 20rpx;
		border: 1rpx solid #d1d5db;
		transition: all 0.3s ease;
	}

	.select-text {
		font-size: 20rpx;
		color: #6b7280;
		font-weight: 500;
		letter-spacing: 0.5rpx;
	}

	@keyframes selectedPulse {
		0%, 100% {
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
			transform: scale(1);
		}
		50% {
			box-shadow: 0 6rpx 20rpx rgba(102, 126, 234, 0.4);
			transform: scale(1.02);
		}
	}

	.arrow-icon {
		width: 48rpx;
		height: 48rpx;
		background: #f9fafb;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		border: 1rpx solid #e5e7eb;
	}

	.arrow-icon-active {
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
		border-color: rgba(102, 126, 234, 0.3);
		transform: translateX(4rpx);
	}

	/* 底部提示 */
	.bottom-tip {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 12rpx;
		padding: 30rpx;
		margin: 0 30rpx 40rpx;
		background: linear-gradient(135deg, #fff9f0 0%, #fef3e2 100%);
		border-radius: 20rpx;
		border: 1rpx solid rgba(245, 158, 11, 0.2);
	}

	.tip-icon {
		font-size: 28rpx;
		filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
	}

	.tip-text {
		font-size: 24rpx;
		color: #92400e;
		font-weight: 500;
		letter-spacing: 0.5rpx;
	}

	/* 选择说明 */
	.selection-guide {
		margin: 20rpx 30rpx 40rpx;
		background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
		border-radius: 20rpx;
		border: 1rpx solid #e2e8f0;
		overflow: hidden;
	}

	.guide-content {
		padding: 24rpx 30rpx;
	}

	.guide-item {
		display: flex;
		align-items: center;
		margin-bottom: 16rpx;
	}

	.guide-item:last-child {
		margin-bottom: 0;
	}

	.guide-icon {
		width: 32rpx;
		height: 32rpx;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 16rpx;
		font-size: 16rpx;
	}

	.guide-icon.current {
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
		border: 1rpx solid rgba(102, 126, 234, 0.2);
	}

	.guide-icon.cache {
		background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(5, 150, 105, 0.1) 100%);
		border: 1rpx solid rgba(16, 185, 129, 0.2);
	}

	.guide-text {
		font-size: 24rpx;
		color: #475569;
		font-weight: 500;
		letter-spacing: 0.5rpx;
	}

	/* 响应式优化 */
	@media (max-width: 375px) {
		.hotel-item {
			padding: 24rpx;
			margin: 0 15rpx 15rpx;
		}

		.hotel-avatar {
			width: 100rpx;
			height: 100rpx;
		}

		.hotel-name {
			font-size: 28rpx;
		}

		.account-value {
			font-size: 24rpx;
		}
	}</style>
