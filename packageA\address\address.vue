<template>
	<view>
		<view class="box" >
			<view class=""
				style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 160rpx;background-color: #f3f4f6;"
				v-if="addressList.length==0">
				<view class="icon-quesheng<PERSON>_zanwujilu" style="font-size: 140rpx;"
					:style="{color:themeColor.com_color1}">
				</view>
				<p :style="{color:themeColor.com_color1}">暂无地址</p>
			</view>
			<view class="addressBox" v-for="item in addressList" v-if="addressList.length > 0" @click="editAddress(item)">
				<view class="item">
					<text>{{item.detail}}</text>
				</view>
				<view class="item1">
					<text>{{item.address}}</text>
				</view>
				<view class="item2">
					<text>{{item.user_name}}</text>
					<text style="margin-left: 40rpx;">{{item.user_phone}}</text>
					<view class="" 
					style="width: fit-content;padding: 8rpx;color: red;border-radius: 4rpx;height: 24rpx;
					font-size: 18rpx;color: #ffffff;display: flex;align-items: center;background-color: red;
					justify-content: center;margin-left: 40rpx;" v-if="item.is_default">
						<text>默认</text>
					</view>
				</view>
				<view class="edit">
					<view class="icon-bianji">
						
					</view>
				</view>
			</view>
			
			
			<view class="addBox">
				<view class="btnBox" @click="addAddress" :style="{'background-color': themeColor.main_color,'color':themeColor.bg_color}">
					添加新地址
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				addressList:''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'city']),
			...mapState('pointShop', ['pointShopDetail']),
		
		},
		onShow() {
			this.$iBox
				.http('getUserAddress', {
					page:1,
					limit:100
				})({
					method: 'post'
				})
				.then(res => {
					this.addressList = res.data.list
				})
				.catch(function(error) {
					console.log('网络错误', error);
				});
		},
		methods: {
			editAddress(e){
				uni.navigateTo({
					url:'./editAddress/editAddress?address='+JSON.stringify(e)
				})
			},
			addAddress(){
				uni.navigateTo({
					url:'./addAddress/addAddress'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		width: 710rpx;
		margin: 20rpx auto;
		border-radius: 20rpx;
		background: #ffffff;
		.addressBox {
			width: 100%;
			height: 170rpx;
			padding: 20rpx;
			position: relative;
			line-height: 44rpx;
			border-bottom: 1px solid #eee;
			.item {
				font-size: 24rpx;
				color: #7f7f7f;
				overflow: hidden;
			}
			
			.item1 {
				font-size: 30rpx;
				color: #000000;
				font-weight: 600;
				overflow: hidden;
			}
			
			.item2 {
				display: flex;
				align-items: center;
				font-size: 26rpx;
			}
			
			.edit {
				position: absolute;
				right: 10rpx;
				height: 100%;
				top: 0;
				display: flex;
				align-items: center;
			}
		}
		
		.addBox{
			position: fixed;
			bottom: 0;
			height: 160rpx;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			.btnBox{
				width: 90%;
				border-radius: 30rpx;
				height: 70rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
	}
</style>
