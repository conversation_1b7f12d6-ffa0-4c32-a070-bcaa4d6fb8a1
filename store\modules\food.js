import Vue from 'vue'

const state = {
	CateringOrderDetail:null,
	cartList:[],//购物车,
	tableNum:'',
	userCount:'',
	tableId:''
}

const mutations = {
	// 自定义tabbar栏
	PUSHDETAIL: (state, CateringOrderDetail) => {
		state.CateringOrderDetail = CateringOrderDetail
		console.log(CateringOrderDetail,'CateringOrderDetail');
	},
	PUSHCARD: (state, cartList) => {
		state.cartList = cartList
	},
	PUSHTABLE: (state, tableNum) => {
		state.tableNum = tableNum
	},
	PUSNUSERCOUNT: (state, userCount) => {
		state.userCount = userCount
	},
	PUSHTABLEID: (state, tableId) => {
		state.tableId = tableId
	},
}

const actions = {
	getCateringOrderDetail({
		commit
	}, params) {
		commit('PUSHDETAIL', params)
	},
	getCart({
		commit
	}, params) {
		commit('PUSHCARD', params)
	},
	getTableNum({
		commit
	}, params) {
		commit('PUSHTABLE', params)
	},
	
	getUserCount({
		commit
	}, params) {
		commit('PUSNUSERCOUNT', params)
	},
	
	getTableId({
		commit
	}, params) {
		commit('PUSHTABLEID', params)
	},
}

export default {
	namespaced: true,
	state,
	mutations,
	actions
}
