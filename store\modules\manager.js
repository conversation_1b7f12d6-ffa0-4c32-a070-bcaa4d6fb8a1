import Vue from 'vue'

const state = {
	billDetail:null,
	roomInfo:null
}

const getters = {
	
}

const mutations = {
	// 自定义tabbar栏
	PUSHBILLDETAIL: (state, billDetail) => {
		state.lockDetail = billDetail
		
	},
	PUSHROOMINFO: (state, roomInfo) => {
		state.roomInfo = roomInfo
		
	},
	
}

const actions = {
	getBillDetail({
		commit
	}, params) {
		commit('PUSHBILLDETAIL', params)
	},
	getRoomInfo({
		commit
	}, params) {
		commit('PUSHROOMINFO', params)
	},
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}
