<template>
	<view class="box">
		<view class="detailBox">
			<p style="font-size: 26rpx;color: #9c9c9c;">快递单号:{{orderNum?orderNum:'暂无物流信息'}}</p>
			<p style="font-size: 36rpx;color: #1e1e1e;margin-top: 10rpx;">快递公司:{{name}}</p>
		</view>
		<uni-steps :options="list" active-color="#007AFF" :active="active" direction="column" />
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				id:'',
				list:[],
				active:'',
				orderNum:'',
				name:''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'city', 'addressDetail']),
		},
		onLoad(options) {
			this.id = options.id
			uni.showLoading({
				title: '加载中...'
			})
			this.$iBox.http('queryExpress',{id:this.id})({
				method: 'post'
			}).then(res => {
				let list = []
				this.orderNum = res.data.number
				this.name = res.data.type
				res.data.list.forEach(item => {
					let detail = {
						title:'',
						desc:''
					}
					detail.title = item.status
					detail.desc = item.time
					list.push(detail)
				})
				
				this.list = list
				this.active = list.length-1
				uni.hideLoading()
			})
			
		}
	}
</script>

<style lang="scss" scoped>
	.box{
		min-height: 100vh;
		background: #ffffff;
		padding: 30rpx;
		
		.detailBox{
			height: 160rpx;
			background: #ffffff;
			box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
			width: 700rpx;
			margin: 20rpx auto;
			padding: 20rpx;
		}
	}
</style>
