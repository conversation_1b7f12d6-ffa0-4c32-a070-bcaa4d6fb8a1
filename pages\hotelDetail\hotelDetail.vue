<template>
	<view :style="{background:themeColor.bg_color}">
		<!-- 返回按钮 -->
		<view class="backBtn" @click="goBack">
			<view class="icon-jiantou"
				style="transform: rotate(180deg);color: #FFFFFF;font-size: 40rpx;font-weight: 600;">
			</view>
		</view>
		<view class="" :key="index" v-for="(item, index) in diyModel">
			<view class="" v-if="item.sign=='slideshow'" style="position: relative;">
				<swiper class="m-swiper" @change="change"
					circular autoplay="true”">
					<swiper-item class="m-swiper_item" v-for="(item, index) in swiperList" :key="item.id"
						@click="()=>toPage(item)">
						<view class="m-swiper_item_content">
							<image :src="item.image?item.image:item"
								:style="'border-top-left-radius:'+round+'rpx;border-top-right-radius:'+round +'rpx'"
								class="m-swiper_item_content_img" mode="aspectFill"
								v-if="$iBox.image(item.image?item.image:item)"></image>
							<video class="m-swiper_item_content_img" v-if="$iBox.video(item.image?item.image:item)"
								:id="`video-${index}`" :enable-progress-gesture="false"
								:src="item.image?item.image:item" :poster="getPoster(item)" controls></video>
							<text v-if="item.title && $iBox.image(item.image?item.image:item)"
								class="m-swiper_item_content_title">{{ item.title }}</text>
						</view>

					</swiper-item>

				</swiper>
				
				<view class="m-swiper_dot1" @click="toPhoto">
					<text class="icon-tupian"></text>
					<text>查看图片</text>
				</view>
			</view>
			<m-hotelDetailCard v-if="item.sign=='book_room_list_info'" :mode="item.property"></m-hotelDetailCard>
			<m-roomList v-if="hackReset&&item.sign=='room_type'" :mode="item.property.style"></m-roomList>
			<m-hotelAmenities v-if="item.sign=='hotel_facility'" :mode="item.property.style"
				:detailShow="true"></m-hotelAmenities>
			<m-rateCard v-if="item.sign=='hotel_evaluate'" :mode="item.property.style"></m-rateCard>
		</view>
		<view class="" style="height: 30rpx;">

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				swiperList: [],
				roomList: [],
				diyModel: [],
				hackReset: true,
				currentIndex: 0
			}
		},
		current(val, preVal) {
			if (val === preVal) return;
			this.currentIndex = val; // 和上游数据关联上
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		async onLoad() {
			await this.$onLaunched;
			this.swiperList = this.hotel.pic_list
		},
		async onShow() {
			await this.$onLaunched;
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			this.pageUI()
			// 进入此页初始化各个时间级房间类型
			this.$iBox.http('getSaleType', {
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {
				this.getSaleTypes(res.data)
			})
		},
		methods: {
			...mapActions('hotel', ['getUnit', 'getChooseDate', 'getSaleTypes']),
			pageUI(e) {
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getHomePageUi', {
					path: 'pages/hotelDetail/hotelDetail',
					shop_id: this.hotel.id
				})({
					method: 'post'
				}).then(res => {
					this.diyModel = res.data
					uni.hideLoading()
					console.log(this.diyModel);
				})
			},
			goBack() {
				let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
				if (pages.length > 1) {
					uni.navigateBack({
						delta: 1
					})
				} else {
					uni.reLaunch({
						url: '/pages/index/index'
					})
				}

			},
			getPoster(item) {
				return typeof item === 'object' && item.poster ? item.poster : ''
			},
			toPage(e){
				console.log(e,'dd');
				if(e.type){
					if (e.type == 'xcx_page') {
						if(e.url == 'pages/myCenter/myCenter'||e.url == 'pages/myRoom/myRoom'||e.url == 'pages/moreTabs/moreTabs'){
							uni.switchTab({
								url:'/' + e.url
							})
						}else {
							uni.navigateTo({
								url: '/' + e.url
							})
						}
						
					} else if (e.type == 'h5') {
						uni.navigateTo({
							url: '/pages/webView/webView?url=' + e.url
						})
					} else {
						let appid = '';
						let path = '';
						let data = {}
						appid = e.url.split('#')[0]
						if (e.url.split('#').length > 1) {
							let a = e.url.indexOf('#')
							path = e.url.slice(a + 1)
						}
						// #ifdef MP-WEIXIN
						wx.navigateToMiniProgram({
							appId: appid,
							path: path,
							extraData:data,
							success(res) {
								// 打开成功
								console.log(res);
							}
						});
						// #endif
				}
				
				}
			},
			change(e) {
				const {
					current
				} = e.detail
				this.pauseVideo(this.currentIndex)
				this.currentIndex = current
				this.$emit('change', e.detail)
			},
			// 切换轮播时，暂停视频播放
			pauseVideo(index) {
				const lastItem = this.swiperList[index].image
				console.log(lastItem, 'vido');
				if (this.$iBox.video(lastItem)) {
					// 当视频隐藏时，暂停播放
					const video = uni.createVideoContext(`video-${index}`, this)
					video.pause()
				}
			},
			toPhoto(){
				uni.navigateTo({
					url:'/packageA/hotelPhotos/hotelPhotos'
				})
			}
		},
		onShareAppMessage: function(res) {
			// 1.返回节点对象
			let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
			let currentPage = pages[pages.length - 1]; //获取当前页面的对象
			let url = currentPage.route //当前页面url
			console.log(url, 'url');
			return {
				path: '/' + url
			}
		},
		onUnload() {
			// 如果是在酒店详情页，撤销页面时恢复到正常模式
			console.log('onunload');

		}
	}
</script>
<style lang="scss" scoped>
	.backBtn {
		position: fixed;
		top: 100rpx;
		left: 30rpx;
		width: 70rpx;
		height: 70rpx;
		background-color: rgba($color: #000000, $alpha: 0.5);
		border-radius: 50%;
		z-index: 99999;
		display: flex;
		align-items: center;
		justify-content: center;

	}
	
	.m-swiper {
		width: 100%;
		height: 450rpx;
		
		&_item {
			height: 100%;
			width: 100%;
			
			&_content {
				height: 100%;
				width: 100%;
				
				&_img {
					height: 100%;
					width: 100%;
					
				}
	
				&_title {
					position: absolute;
					background-color: rgba(0, 0, 0, 0.3);
					bottom: 20rpx;
					left: 0;
					right: 0;
					font-size: 28rpx;
					padding: 12rpx 24rpx;
					color: #FFFFFF;
					flex: 1;
				}
			}
			
		
		}
	
	}
	
	.m-swiper_dot{
		position: absolute;
		background-color: rgba(0, 0, 0, 0.3);
		bottom: 40rpx;
		border-radius: 30rpx;
		right: 20rpx;
		font-size: 24rpx;
		padding: 12rpx 24rpx;
		color: #FFFFFF;
		flex: 1;
	}
	
	.m-swiper_dot1{
		position: absolute;
		background-color: rgba(0, 0, 0, 0.3);
		bottom: 40rpx;
		border-radius: 30rpx;
		right: 0rpx;
		left: 0;
		margin:0 auto;
		font-size: 24rpx;
		padding: 12rpx 20rpx;
		color: #FFFFFF;
		width: 220rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 99;
	}
</style>