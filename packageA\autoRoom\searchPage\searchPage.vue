<template>
	<view style="">
		<!-- 背景图片 -->
		<image src="https://gd-hbimg.huaban.com/96b264566716bd6078a433151a47a03ffa7cbfcb49ff3-xbpG7X_fw240webp"
			style="position: absolute;height: 100%;width: 100%;z-index: 0;"></image>
		<view class="nav-bar" :style="'height:'+navBarHeight+'px'">
			<view class="search" @click="backTab"
				:style="'height:'+menuHeight+'px;min-height:' +menuHeight+'px; line-height:'+menuHeight+'px; left:'+menuRight+'px; bottom:'+menuBotton+'px;'">
					 <uni-icons type="back" size="20" color="#FFFFFF"></uni-icons>返回首页
			</view>
		</view>
		<m-searchBill :mode="1"></m-searchBill>


	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				hackReset: true,
				ifChoose: false,
				navBarHeight: 0, // 导航栏高度
				menuRight: 0, // 胶囊距右方间距（方保持左、右间距一致）
				menuBotton: 0, // 胶囊距底部间距（保持底部间距一致）
				menuHeight: 0, // 胶囊高度（自定义内容可与胶囊高度保证一致）
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		async onLoad() {
			const that = this;
			this.ifChoose = false
			// 获取系统信息
			const systemInfo = wx.getSystemInfoSync();
			// 胶囊按钮位置信息
			const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
			// 导航栏高度 = 状态栏到胶囊的间距（胶囊距上距离-状态栏高度） * 2 + 胶囊高度 + 状态栏高度
			that.navBarHeight = (menuButtonInfo.top - systemInfo.statusBarHeight) * 2 + menuButtonInfo.height + systemInfo.statusBarHeight;
			that.menuRight = systemInfo.screenWidth - menuButtonInfo.right;
			that.menuBotton = menuButtonInfo.top - systemInfo.statusBarHeight;
			that.menuHeight = menuButtonInfo.height;
		},
		async onShow() {
			await this.$onLaunched;

		},
		components: {

		},
		methods: {
			backTab() {
				uni.reLaunch({
					url: '/pages/index/index'
				})
			}
		}

	}
</script>

<style lang="scss" scoped>
	.nav-bar {
		position: fixed;
		width: 100%;
		top: 0;
		color: #fff;
		// background: #000;
	}

	.nav-bar .search {
		width: 60%;
		color: #ffffff;
		font-size: 30rpx;
		position: absolute;
		display: flex;
		align-items: center;
		// justify-content: center;
	}
</style>