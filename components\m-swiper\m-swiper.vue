<template>
	<view style="position: relative;display: flex;align-items: center;justify-content: center;">
		<swiper class="m-swiper" @change="change" :style="'height:'+height+'rpx;'+'width:'+width+'rpx;'" circular autoplay="true”">
			<swiper-item class="m-swiper_item" v-for="(item, index) in swiper_list" :key="item.id" @click="()=>toPage(item)">
				<view class="m-swiper_item_content">
					<image :src="item.image?item.image:item" :style="'border-top-left-radius:'+round+'rpx;border-top-right-radius:'+round +'rpx'" class="m-swiper_item_content_img" mode="aspectFill"
						v-if="$iBox.image(item.image?item.image:item)"></image>
					<video class="m-swiper_item_content_img" v-if="$iBox.video(item.image?item.image:item)" :id="`video-${index}`"
						:enable-progress-gesture="false" :src="item.image?item.image:item" :poster="getPoster(item)" controls></video>
					<text v-if="item.title && $iBox.image(item.image?item.image:item)"
						class="m-swiper_item_content_title">{{ item.title }}</text>
				</view>
				
			</swiper-item>
			
		</swiper>
		<!-- <view class="m-swiper_dot">
			<text>{{currentIndex+1}}/{{swiper_list?swiper_list.length:0}}</text>
		</view>
		<view class="m-swiper_dot1" @click="toPhoto">
			<text class="icon-tupian"></text>
			<text>查看图片</text>
		</view> -->
	</view>
</template>

<script>
	export default {
		props: {
			// 显示与否
			show: {
				type: Boolean,
				default: true
			},
			height: {
				type: [String, Number],
				default: 360
			},
			width: {
				type: [String, Number],
				default: 686
			},
			swiper_list: {
				type: [Array,String],
				default: []
			},
			round: {
				type: [String, Number],
				default: 0
			}
		},
		mounted() {
			// console.log(this.$iBox.image('dsd.png'), 'swiper');
			console.log(this.round,'dd');
		},
		data() {
			return {
				currentIndex: 0
			}
		},
		watch: {
			current(val, preVal) {
				if (val === preVal) return;
				this.currentIndex = val; // 和上游数据关联上
			}
		},
		methods: {
			// 当一个轮播item为视频时，获取它的视频海报
			getPoster(item) {
				return typeof item === 'object' && item.poster ? item.poster : ''
			},
			toPage(e){
				console.log(e,'dd');
				if(e.type){
					if (e.type == 'xcx_page') {
						if(e.url == 'pages/myCenter/myCenter'||e.url == 'pages/myRoom/myRoom'||e.url == 'pages/moreTabs/moreTabs'){
							uni.switchTab({
								url:'/' + e.url
							})
						}else {
							uni.navigateTo({
								url: '/' + e.url
							})
						}
						
					} else if (e.type == 'h5') {
						uni.navigateTo({
							url: '/pages/webView/webView?url=' + e.url
						})
					} else {
						let appid = '';
						let path = '';
						let data = {}
						appid = e.url.split('#')[0]
						if (e.url.split('#').length > 1) {
							let a = e.url.indexOf('#')
							path = e.url.slice(a + 1)
						}
						// #ifdef MP-WEIXIN
						wx.navigateToMiniProgram({
							appId: appid,
							path: path,
							extraData:data,
							success(res) {
								// 打开成功
								console.log(res);
							}
						});
						// #endif
				}
				
				}
			},
			change(e) {
				const {
					current
				} = e.detail
				this.pauseVideo(this.currentIndex)
				this.currentIndex = current
				this.$emit('change', e.detail)
			},
			// 切换轮播时，暂停视频播放
			pauseVideo(index) {
				const lastItem = this.swiper_list[index].image
				console.log(lastItem, 'vido');
				if (this.$iBox.video(lastItem)) {
					// 当视频隐藏时，暂停播放
					const video = uni.createVideoContext(`video-${index}`, this)
					video.pause()
				}
			},
			toPhoto(){
				uni.navigateTo({
					url:'/packageA/hotelPhotos/hotelPhotos'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.m-swiper {
		width: 686rpx;
		height: 340rpx;
		border-radius: 16rpx;
		&_item {
			height: 100%;
			width: 100%;
			border-radius: 16rpx;
			&_content {
				height: 100%;
				width: 100%;
				
				&_img {
					height: 100%;
					width: 100%;
					border-radius: 16rpx;
				}

				&_title {
					position: absolute;
					background-color: rgba(0, 0, 0, 0.3);
					bottom: 20rpx;
					left: 0;
					right: 0;
					font-size: 28rpx;
					padding: 12rpx 24rpx;
					color: #FFFFFF;
					flex: 1;
				}
			}
			
		
		}
	
	}
	.m-swiper_dot{
		position: absolute;
		background-color: rgba(0, 0, 0, 0.3);
		bottom: 40rpx;
		border-radius: 30rpx;
		right: 20rpx;
		font-size: 24rpx;
		padding: 12rpx 24rpx;
		color: #FFFFFF;
		flex: 1;
	}
	
	.m-swiper_dot1{
		position: absolute;
		background-color: rgba(0, 0, 0, 0.3);
		bottom: 40rpx;
		border-radius: 30rpx;
		right: 0rpx;
		left: 0;
		margin:0 auto;
		font-size: 24rpx;
		padding: 12rpx 20rpx;
		color: #FFFFFF;
		width: 220rpx;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>
