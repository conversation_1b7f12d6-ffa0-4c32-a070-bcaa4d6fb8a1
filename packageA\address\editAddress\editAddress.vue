<template>
	<view>
		<view class="box">
			<view class="item">
				<p class="title">姓名</p>
				<input type="text" placeholder="请填写联系人姓名" v-model="name">
			</view>
			<view class="item">
				<p class="title">手机号码</p>
				<input type="number" placeholder="请填写联系人电话" v-model="phone">
			</view>
			<view class="item">
				<p class="title">所在地址</p>
				<picker @change="bindPickerChange" mode="region" :value="address">
					<view class="uni-input">{{address}}</view>
				</picker>
			</view>
			<view class="item" style="align-items: flex-start;">
				<p class="title">详细地址</p>
				<textarea placeholder="请填写详细地址" style="height: 100rpx;border: 1px solid #eee;border-radius: 10rpx;padding: 10rpx;" v-model="detail"></textarea>
			</view>
			<view class="item" style="justify-content: space-between;">
				<p class="title">设置默认地址</p>
				<switch @change="switchChange" :checked="addressObj.is_default"/>
			</view>
			
		</view>
		
		<view class="addBox">
			<view class="btnBox" @click="editAddress"
				:style="{'background-color': themeColor.main_color,'color':themeColor.bg_color}">
				保存
			</view>
			
			<view class="btnBox" @click="delAddress" style="margin-top: 14rpx;" :style="'border: 1px solid '+themeColor.main_color">
				删除
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				addressObj:null,
				name:'',
				phone:'',
				address:'',
				detail:'',
				is_default:false
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'city']),
			...mapState('pointShop', ['pointShopDetail']),
		
		},
		onLoad(options) {
			this.addressObj = JSON.parse(options.address)
			this.name = this.addressObj.user_name
			this.phone = this.addressObj.user_phone
			this.address = this.addressObj.address
			this.detail = this.addressObj.detail
			this.is_default = this.addressObj.is_default
		},
		methods:{
			switchChange(e){
				console.log(e);
				this.is_default = e.detail.value
			},
			bindPickerChange(e) {
				let add = ''
				e.detail.value.forEach(item=>{
					add +=item
				})
				this.address = add
				console.log(e);
			},
			editAddress(){
				if (!this.name) {
					uni.showToast({
						icon: 'none',
						title: '请填写姓名'
					})
				
					return
				}
				
				if (!this.phone) {
					uni.showToast({
						icon: 'none',
						title: '请填写电话'
					})
				
					return
				}
				
				if (!this.address || this.address == '点击选择地区' ||!this.detail) {
					uni.showToast({
						icon: 'none',
						title: '请填写地址'
					})
				
					return
				}
				
				let params = {
					user_name: this.name,
					user_phone: this.phone,
					address: this.address ,
					detail:this.detail,
					is_default: this.is_default,
					id:this.addressObj.id
				}
				
				this.$iBox
					.http('updateUserAddress', params)({
						method: 'post'
					})
					.then(res => {
						uni.navigateBack()
					})
					.catch(function(error) {
						console.log('网络错误', error);
					});
			},
			delAddress(){
				uni.showModal({
					title:'提示',
					content:'是否删除?',
					success:(res)=>{
						if(res.confirm){
							this.$iBox
								.http('delUserAddress', {id:this.addressObj.id})({
									method: 'post'
								})
								.then(res => {
									uni.showToast({
										icon:'none',
										title:''
									})
									uni.navigateBack()
								})
								.catch(function(error) {
									console.log('网络错误', error);
								});
						}else{
							
						}
					}
				})
			
			}
		}
	}
</script>

<style lang="scss" scoped>
.box {
		width: 710rpx;
		margin: 20rpx auto;
		border-radius: 20rpx;
		background: #ffffff;

		.item {
			display: flex;
			align-items: center;
			padding: 20rpx;

			.title {
				width: 160rpx;
				font-size: 26rpx;
				font-weight: 600;
			}
		}

	}


	.addBox {
		position: fixed;
		bottom: 0;
		height: 180rpx;
		width: 100%;
		display: flex;
		align-items: center;
		flex-direction: column;

		.btnBox {
			width: 90%;
			border-radius: 30rpx;
			height: 70rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>
