<template>
	<view>
		<!-- 日历选择 -->
		<m-calendar :initMonth="bookLimit" :start-date="startDate" :bgColor="themeColor.bg_main1_color" :imme_days="immobilization_days" :end-date="endDate" :mode="type" @callback="getDate" :themeColor="themeColor.main_color">
			<view class="box" :style="{background:theme.color}" v-if="if_sure">
				确定
			</view>
		</m-calendar>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return { //日期均为yyyy-mm-dd或者yyyy/mm/dd格式
				startDate: '',
				endDate: '',
				betweenStart: '',
				betweenEnd: '',
				type: 1,
				immobilization_days: 0,
				if_sure: false,
				bookLimit:6
			}
		},
		computed: {
			...mapState('login', ['userInfo',]),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting', 'hotelList'])

		},
		watch: {
			choose_date() {
				
			}
		},
		methods: {
			...mapActions('hotel', ['getChooseDate']),
			//获取回调的日期数据
			getDate(date) {
				console.log(date,'回调日期');
				let date1 = {}
				date1.startDate = this.$moment(date.startStr.dateStr,'YYYY/MM/DD').unix() 
				if (date.endStr) {
					date1.endDate = this.$moment(date.endStr.dateStr,'YYYY/MM/DD').unix()
				}
				console.log(date1,'date');
				this.getChooseDate(date1)
				uni.navigateBack({})
			}
		},
		onLoad(option) {
			// 获取自助入住设置，
			this.bookLimit = this.shopSetting.filter(item => {
				return item.sign == 'BOOKING_LIMIT_TIME'
			})[0].property.value
			
			this.type = option.type
			this.startDate = option.startDate
			this.immobilization_days =  option.countDays
			if(option.type==2){
				this.endDate = option.endDate
			}else if(option.type==3){
				this.endDate = this.$moment(option.startDate).add(option.countDays,'days').format('YYYY/MM/DD')
			}
			console.log(this.endDate,'end');
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		height: 70rpx;
		line-height: 70rpx;
		color: #fff;
		text-align: center;
		width: 100%;
		position: fixed;
		bottom: 0rpx;
		left: 0;
	}
</style>
