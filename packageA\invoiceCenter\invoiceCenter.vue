<template>
	<view>
		<view class="invoceBox">
			<view class="titleList">
				<view class="titleItem" @click="changeInvoce(item)" v-for="item in titleList"
					:style="invoceIndex==item.id?'color:'+themeColor.main_color+';border:1px solid '+themeColor.main_color+';width:'+(100/titleList.length)+'%;':'width:'+(100/titleList.length)+'%;'">
					{{item.name}}
				</view>
			</view>
			<view class="invoceContent">
				<view class="content">
					
					<view class="" style="width: 680rpx;background: #FFFFFF;">
						<uni-combox label="发票抬头" :candidates="candidates" placeholder="请输入发票抬头" v-model="name"
							@select="selectItem" @input="inputName"></uni-combox>
					</view>
					<text style="color: red;" >*</text>
				</view>
				<view class="content">
					
					<view class="" style="width: 680rpx;">
						<uni-easyinput v-model="code" placeholder="请输入纳税人识别号"></uni-easyinput>

					</view>
					<text style="color: red;" v-if="invoceIndex==2">*</text>
				</view>
				<view class="content">
					
					<view class="" style="width: 680rpx;">
						<uni-easyinput v-model="phone" placeholder="联系方式"></uni-easyinput>
				
					</view>
				
				</view>
				<view class="content">
					
					<view class="" style="width: 680rpx;">
						<uni-easyinput v-model="address" placeholder="地址"></uni-easyinput>
				
					</view>
				
				</view>
				<view class="content">
					
					<view class="" style="width: 680rpx;">
						<uni-easyinput v-model="Email" placeholder="邮箱"></uni-easyinput>
				
					</view>
				
				</view>
				<view class="content" style="margin-top: 30rpx;">
					<view class="" style="width: 680rpx;    margin-top: 40rpx;">
						<uni-easyinput v-model="remark" type="textarea" placeholder="备注"></uni-easyinput>
				
					</view>
				
				</view>
			</view>
			<!-- 抬头记录 -->
			<view class="recordBox">
				<p style="font-size: 26rpx;color: red;">抬头记录:(系统只会显示最近10条抬头记录)</p>
				<view class="recodeItem" v-for="(item, index) in recordList" :key="index" @click="chooseInvoce(item)">
					<p>发票抬头:{{item.buyer_title}}</p>
					<p>纳税人识别号:{{item.buyer_taxpayer_num}}</p>
					<p style="font-size: 24rpx;color: blue;position: absolute;right: 10rpx;top: 0rpx;bottom: 0;margin: auto 0;">
						选择</p>
				</view>
			</view>
		</view>



		<view class="btnSure">
			<view class="btnItem" :style="{background:themeColor.main_color}" @click="sure">
				<text>确认申请</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				titleList: [{
					id: 1,
					name: '个人普票'
				}, {
					id: 2,
					name: '公司普票'
				}],
				invoceIndex: 1,
				name: '',
				code: '',
				candidates: [],
				list: [],
				recordList: [],
				remark:'',
				bill_id:'',
				bill_type_id:'',
				phone:'',
				address:'',
				Email:''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		watch: {
			name() {

			}
		},
		onLoad(options) {
			console.log(options);
			if(options){
				this.bill_id = options.bill_id
				this.bill_type_id = options.bill_type_id
			}
			this.$iBox.http('getUserInvoiceTitle', {
				page: 1,
				limit: 10
			})({
				method: 'post'
			}).then(res => {
				this.recordList = res.data.list
			})
		},
		methods: {
			changeInvoce(e) {
				this.invoceIndex = e.id
			},
			inputName() {

				this.$iBox.http('searchBuyerTaxNum', {
					buyer_title: this.name
				})({
					method: 'post'
				}).then(res => {
					if(res.data){
						this.list = res.data
						let a = []
						res.data.forEach(item => {
							a.push(item.buyer_title)
						})
						this.candidates = a
					}
					
				})
			},
			chooseInvoce(e) {
				this.name = e.buyer_title
				this.code = e.buyer_taxpayer_num
				this.phone = e.taker_phone
				this.Email = e.buyer_email
				this.address = e.buyer_address
			},
			selectItem(e) {
				console.log(e, 'select');
				this.list.filter(item => {
					return item.buyer_title == this.name
				})
				if (this.list.length > 0) {
					this.code = this.list[0].buyer_taxcode
					this.name = this.list[0].buyer_title
				}
			},
			sure(){
				if(this.name == ''){
					uni.showToast({
						icon:'none',
						title:'请输入抬头名称'
					})
					return
				}
				
				if(this.invoceIndex==2&&this.code == ''){
					uni.showToast({
						icon:'none',
						title:'请输入纳税人号'
					})
					return
				}
				
				uni.showModal({
					title:'申请发票',
					content:'是否申请发票，等待酒店开具?',
					confirmText:'申请',
					success:res=>{
						if(res.confirm){
							let params = {
								bill_id:this.bill_id,
								bill_type_id:this.bill_type_id,
								title_type:this.invoceIndex,
								buyer_title:this.name,
								buyer_taxpayer_num:this.code,
								taker_phone:this.phone,
								buyer_email:this.Email,
								buyer_address:this.address,
								remark:this.remark
							}
							this.$iBox.http('addInvoiceApply', params)({
								method: 'post'
							}).then(res => {
								uni.navigateBack()
							})
						}
					}
				})

			}

		}
	}
</script>

<style lang="scss" scoped>
	page {
		background-color: #FFFFFF;
	}

	.invoceBox {

		// background: #FFFFFF;
		.titleList {
			display: flex;
			align-items: center;

			.titleItem {
				display: flex;
				align-items: center;
				justify-content: center;
				
				height: 60rpx;
				border: 1px solid #ccc
			}
		}

		.invoceContent {

			margin-top: 20rpx;

			.content {
				padding: 10rpx 30rpx;
				display: flex;
				height: 100rpx;
				align-items: center;
			}
		}

		.recordBox {
			margin-top: 60rpx;
			padding: 30rpx;

			.recodeItem {
				padding: 30rpx;
				background: #FFFFFF;
				width: 680rpx;
				margin: 20rpx 0;
				position: relative;
			}
		}
	}

	.btnSure {
		width: 100%;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: 20rpx;

		.btnItem {
			width: 300rpx;
			height: 80rpx;
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>
