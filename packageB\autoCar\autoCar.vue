<template>
	<view>
		<view class="" style="padding: 30rpx;background-color: #FFFFFF;border-radius: 20rpx;height: 300rpx;">
			<view class="" style="height: 100rpx;display: flex;align-items: center;padding: 20rpx 0;">
				<text style="margin-right: 30rpx;">起点站:</text>
				<picker @change="bindPickerChangeStart" :value="index" :range="array">
					<view class="uni-input" style="border-bottom: 1px solid #999999;min-width: 300rpx;display: flex;justify-content: center;color: #999999;" v-if="index==-1">点击选择站点</view>
					<view class="uni-input" style="border-bottom: 1px solid #999999;min-width: 300rpx;display: flex;justify-content: center;color: #393939;" v-else>{{array[index]}}</view>
				</picker>

			</view>
			<view class="" style="height: 100rpx;display: flex;align-items: center;">
				<text style="margin-right: 30rpx;">终点站:</text>
				<picker @change="bindPickerChangeEnd" :value="index1" :range="array1">
					<view class="uni-input" style="border-bottom: 1px solid #999999;min-width: 300rpx;display: flex;justify-content: center;color: #999999;" v-if="index1==-1">点击选择站点</view>
					<view class="uni-input" style="border-bottom: 1px solid #999999;min-width: 300rpx;display: flex;justify-content: center;color: #393939;" v-else>{{array1[index1]}}</view>
				</picker>

			</view>
		</view>
		<view class="" v-if="!ifCall" @click="callCar" style="height: 20vh;width: 100%;display: flex;
		align-items: center;justify-content: center;flex-direction: column;">
			<view class="icon-hujiaoguanli" style="font-size: 80rpx;"></view>
			<text style="margin-top: 20rpx;">呼叫车辆</text>
		</view>

		<view class="" v-if="ifCall" style="height: 400rpx;width: 600rpx;display: flex;align-items: center;justify-content: center;">
			<text>正在呼叫车辆中...</text>
		</view>
		
		<!-- <view class="" style="display: flex;align-items: center;padding: 30rpx;" >
			<view :style="current==index?'color:#55aa7f':''" style="margin-right: 30rpx;display: flex;align-items: center;" @click="chooseType(item)" v-for="(item,index) in tabList">
			{{item.name}}
			<view class="" style="width: 40rpx;height: 40rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;color: #FFFFFF;background: #ff0000;">
				{{index==0?tips.tip_0:(index==1?tips.tip_1:(index==2?tips.tip_2:tips.tip_3))}}
			</view>
			</view>
		</view> -->
		<view class="" style="padding: 30rpx;width: 690rpx;margin: 20rpx auto;background-color: #FFFFFF;border-radius: 30rpx;min-height: 250rpx;" v-for="item in billList">
			<view style="color: #999999;font-size: 40rpx;width: 100%;margin-bottom: 20rpx;display: flex;align-items: center;justify-content: space-between;">{{current==0?'等待酒店接单，请耐心等待':(current==1?'车辆正在途中,请耐心等待!':'')}}<button type="warn" @click="cancleCall(item)" size="mini" v-if="current==0">取消</button></view>
			<p><text style="color: #666666;">起点站:</text>{{item.start_station_name}}</p>
			<p><text style="color: #666666;">终点站:</text>{{item.end_station_name}}</p>
			<p><text style="color: #666666;">呼叫时间:</text>{{item.create_time | moment1}}</p>
		</view>
		<view class="" style="height: 80rpx;">
			
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				start: '',
				end: '',
				index: 0,
				array: [],
				index1: 0,
				array1: [],
				params: {
					page: 1,
					limit: 100,
					shop_id: '',
					longitude: null,
					latitude: null
				},
				params1: {
					page: 1,
					limit: 100,
					shop_id: '',
					status:0
				},
				stationList:[],
				ifCall:false,
				tabList:[{id:1,name:'待接单'},{id:2,name:'已接单'},{id:3,name:'已完成'},{id:4,name:'已取消'}],
				current:0,
				billList:[],
				tips:null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['city', 'hotel']),
		},
		async onLoad() {
			await this.$onLaunched;
			
			let that = this;
			// wx.showModal({
			// 	content: '检测到您没打开定位权限，是否去设置打开？',
			// 	confirmText: "确认",
			// 	cancelText: "取消",
			// 	success: function(res) {
			// 		console.log(res);
			// 		//点击“确认”时打开设置页面
			// 		if (res.confirm) {
			// 			console.log('用户点击确认')
			// 			wx.openSetting({
			// 				success: (res) => {
			// 					that.getLocation()
			// 				}
			// 			})
			// 		} else {
			// 			console.log('用户点击取消')
			// 		}
			// 	}
			// });
			
			let scene = wx.getEnterOptionsSync()
			if (scene.query.scene) {
				// 扫码场景
				let query = decodeURIComponent(scene.query.scene)
				console.log(query, 'query', this.$iBox.linkFormat(query, "start_id"), this.$iBox.linkFormat(query, "shop_id"));
				//解析参数
				if (query.includes("start_id")) {
					this.params.shop_id = this.$iBox.linkFormat(query, "shop_id")
					this.params1.shop_id = this.$iBox.linkFormat(query, "shop_id")
					this.params.page=1
					let station_id = this.$iBox.linkFormat(query, "start_id")
					this.$iBox.http('getShuttleBusStationList', this.params)({
						method: 'post'
					}).then(res => {
						this.stationList = res.data.list
						let arr = ['选择站点']
						res.data.list.forEach((item,index)=>{
							arr.push(item.station_name)
							if(item.id==station_id){
								this.index = index+1
							}
						})
						this.array = arr
						this.array1 = arr
					})
					
					this.params1.status = [0,1]
					this.getBillList()
								
				} else {
					this.params.page=1
					this.params.shop_id=this.hotel.id
					
					this.$iBox.http('getShuttleBusStationList', this.params)({
						method: 'post'
					}).then(res => {
						this.stationList = res.data.list
						let arr = ['选择站点']
						res.data.list.forEach(item=>{
							arr.push(item.station_name)
						})
						this.array = arr
						this.array1 = arr
					})
					
					this.params1.status = [0,1]
					this.params1.shop_id = this.hotel.id
					this.getBillList()
					
				}
								
			}else{
				this.params.page=1
				this.params.shop_id=this.hotel.id
				
				this.$iBox.http('getShuttleBusStationList', this.params)({
					method: 'post'
				}).then(res => {
					this.stationList = res.data.list
					let arr = ['选择站点']
					res.data.list.forEach(item=>{
						arr.push(item.station_name)
					})
					this.array = arr
					this.array1 = arr
				})
				
				this.params1.status = [0,1]
				this.params1.shop_id = this.hotel.id
				this.getBillList()
			}
			
			
		},

		methods: {
			chooseType(e){
				this.current = e.id-1
				this.params1.status = e.id-1
				this.params1.shop_id = this.hotel.id
				this.getBillList()
			},
			bindPickerChangeStart(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index = e.detail.value
			},
			bindPickerChangeEnd(e) {
				console.log('picker发送选择改变，携带值为', e.detail.value)
				this.index1 = e.detail.value
			},
			getBillList(){
				this.$iBox.http('getShuttleBusRecordList', this.params1)({
					method: 'post'
				}).then(res => {
					this.billList = res.data.list
					this.tips = res.data.tips
				})
			},
			cancleCall(e){
				this.$iBox.http('cancelShuttleBusRecord', {id:e.id})({
					method: 'post'
				}).then(res => {
					uni.showToast({
						icon:'none',
						title:'取消呼叫成功!'
					})
					this.getBillList()
				})
			},
			callCar(){
				uni.showLoading({
					title:'呼叫中...',
					
				})
				if(this.index==0||this.index1==0){
					uni.showToast({
						icon:'none',
						title:'请选择站点'
					})
					return
				}
				
				if(this.index==this.index1){
					uni.showToast({
						icon:'none',
						title:'起点站与终点站相同'
					})
					return
				}
				
				this.$iBox.http('addShuttleBusRecord', {start_station_id:this.stationList[this.index-1].id,end_station_id:this.stationList[this.index1-1].id,shop_id:this.hotel.id})({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.ifCall = true
					uni.showToast({
						icon:'none',
						title:'呼叫车辆成功！请耐心等待!',
						
					})
				})
				
				
			},
			getLocation() {
				let that = this;
				uni.getLocation({
					type: 'wgs84',
					geocode: true, //设置该参数为true可直接获取经纬度及城市信息
					success: (location) => {
						this.params.latitude = location.latitude
						this.params.longitude = location.longitude
			
					},
					fail: function() {
						uni.showToast({
							title: '获取地址失败，将导致部分功能不可用',
							icon: 'none'
						});
					}
				});
			},
		}
	}
</script>

<style>

</style>