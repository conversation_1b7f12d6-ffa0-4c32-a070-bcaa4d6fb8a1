<template>
	<view class="pageBox" >
		<view class="mainPage" style="position: fixed;top:0;z-index: -1;width: 750rpx;height: 100vh;" :style="{background: 'linear-gradient(180deg, '+ themeColor.bg_main_color+'40'+' 0%, '+ '#F5F5F5 40%)'}">
		
		</view>
		<view class="" style="position: fixed;top: 0;width: 750rpx;z-index:9;">
			<view class="" :style="{marginTop:searchBarTop + 'px',height:searchBarHeight + 'px',opacity:opacity}"
				style="display: flex;align-items: center;width: 100%;z-index: 9;position: relative;">
				<view class="" @click="goMain"
					style="position: absolute;left: 30rpx;top: 0;bottom:0;margin: 0 auto;height: 100%;display: flex;align-items: center;">
					<uni-icons type="left" size="22"></uni-icons>
				</view>
				<text @click="goMain" style="color: #000000;margin-left: 90rpx;">{{hotel.shop_name}}</text>
			</view>
		
		</view>
		<view class="" style="" :style="{height:navBarHeight+'px'}"></view>
		<view class="InfoBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="InfoBox_title" style="display: flex;align-items: center;justify-content: space-between;">
				<view>
					<text>{{startDate_show}}</text>
					<text v-if="unit=='long_standard'">起</text>
					<text class="" v-if="unit=='standard'">
						- {{endDate_show}}
					</text>
					<text  style="padding-left: 30rpx;font-size: 28rpx;" v-if="unit=='standard'">共{{countDay_show}}晚</text>

				</view>
				<view @click="toDetail" :style="{color:themeColor.text_title_color}" style="font-size: 24rpx;display: flex;align-items: center;">房型详情<uni-icons :color="themeColor.text_title_color" type="right" size="16"></uni-icons></view>
			</view>
			<view class="InfoBox_title" style="width: 640rpx;">
				<text >{{roomInfo.item.name}}</text>
				<text v-if="unit=='hour'||unit=='conference_room'">({{roomInfo.item.room_sale_type_name}})</text>
				<text style="font-size: 22rpx;color: #999;padding: 10rpx;">|</text>
				<view class="" style="max-width: 260rpx;white-space:nowrap;overflow:hidden;text-overflow:ellipsis">
					<text v-for="(item,index) in roomInfo.item.specialty" style="padding-left: 10rpx;font-size: 22rpx;">{{item}}</text>
				</view>
				
				<text style="font-size: 22rpx;color: #999;padding: 10rpx;"
					v-if="unit=='hour'||unit=='conference_room'||unit=='long_standard'">|</text>
				<!-- 时租房 -->
				<text style="" v-if="unit=='hour'||unit=='conference_room'">
					{{roomInfo.item.start_time_limit + '-'+ roomInfo.item.end_time_limit}},{{ roomInfo.item.room_sale_type_name}}
				</text>
				<!-- 月租房 -->
				<text style="" v-if="unit=='long_standard'">
					*入住天数按照实际月数计算</text>
			</view>
			<view class="InfoBox_content">
				<text>套餐名称:{{roomInfo.service.service_name}}</text>
				<text
					style="padding-left: 14rpx;">套餐说明:{{roomInfo.service.service_content?roomInfo.service.service_content:'暂无详情'}}</text>
			</view>
			<view class="InfoBox_content">
				<view class="" style="padding: 4rpx 6rpx;font-size: 16rpx;width: fit-content;"
					:style="'border:1px solid '+themeColor.main_color+';color:'+themeColor.main_color">
					<text class="icon-dengji"></text>
					{{roomInfo.service.cancelable==1?`入住当天${roomInfo.service.before_end_time}前可免费取消`:'不可取消'}}
				</view>
				<view v-if="tqRoom" class="" style="padding: 4rpx 6rpx;font-size: 16rpx;margin-left: 20rpx;"
					:style="'border-radius:'+themeColor.main_color+';color:'+themeColor.main_color">
					{{tqRoom}}
				</view>

			</view>
		</view>
		<view class="manBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="title" :style="{'border-bottom':'1px solid '+ themeColor.border_color}">
				<text style="font-size: 28rpx;" :style="{color:themeColor.text_second_color}">房间数量</text>
				<m-number-box :min="roomInfo.item.usable_count>0?1:0"
					:max="roomInfo.item.usable_count>0?roomInfo.item.usable_count:0"
					@change="showRoomNumberBox"></m-number-box>
			</view>

			<view class="room" :style="{'border-bottom':'1px solid '+ themeColor.border_color}"  style="padding: 0;" v-if="unit=='long_standard'">
				<text style="width:160rpx;font-size: 28rpx;" :style="{color:themeColor.text_second_color}">入住月数</text>
				<picker @change="bindPickerChange1" :value="index" :range="monthNumArr">
					<view class=""
						style="display: flex;align-items: center;justify-content: space-between;height: 100rpx;">
						<view class="" style="width: 440rpx;">
							<view class="uni-input">{{monthNum}}个月</view>
						</view>
						<view class="icon-jiantou" :style="{color:themeColor.text_title_color}">
						</view>
					</view>
				</picker>
			</view>
			<view class="nameBox" :style="{'border-bottom':'1px solid '+ themeColor.border_color}" v-for="(item,index) in roomNames">
				<text style="width:160rpx;font-size: 28rpx;" :style="{color:themeColor.text_second_color}">住客姓名{{index+1}}</text>
				<view class="" style="width: 400rpx;">
					<input type="text" placeholder="请输入入住人姓名" :style="{color:themeColor.text_main_color}" v-model="item.name" />
				</view>
				<!-- <image @click="goManList" src="../../static/images/linkman.png" v-if="index==0"
					style="height: 40rpx;width: 40rpx;padding:0 0rpx 0 20rpx;" mode=""></image> -->
			</view>
			<view class="roomNameTips" v-if="roomNames.length > 1">
				<text>请输入住客姓名，每间仅需填一人，姓名不可重复</text>
			</view>
			<view class="nameBox" :style="{'border-bottom':'1px solid '+ themeColor.border_color}">
				<text style="width:160rpx;font-size: 28rpx;" :style="{color:themeColor.text_second_color}">手机号</text>
				<view class="" style="width: 400rpx;">
					<input type="number" :style="{color:themeColor.text_main_color}" placeholder="用于接收预定信息" v-model="phone" />
				</view>
			</view>
			<view class="room" @click="readyTime" :style="{'border-bottom':'1px solid '+ themeColor.border_color}">
				<view class="" style="display: flex;align-items: center;">
					<text style="width:160rpx;font-size: 28rpx;" :style="{color:themeColor.text_second_color}">{{unit=='standard'||unit=='long_standard'?'预计到店':'选择入住时间'}}</text>
					<view class="" style="width: 400rpx;">
						<text style="">{{planTime}}</text>
					</view>
				</view>
				
				<view class="icon-jiantou" :style="{color:themeColor.text_title_color}">
				</view>
			</view>
		</view>
		<view class="cashBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}" style="padding: 30rpx;display: flex;flex-direction: column;" @click="goShop">
			<view style="font-size: 34rpx;width: 100%;display: flex;align-items:center;justify-content: space-between;">
				<text>精选服务</text>
				<view style="position: relative;align-items: center;display: flex;align-items: center;text-align: center;height: 100%;">
					<text  v-if="cartList.length==0" style="font-size: 24rpx;color:#0C0F1485;">去定制个性化服务</text>
					<text  v-if="cartList.length>0" style="font-size: 28rpx;color:#FD6265;">您已经选择{{memCount}}项服务</text>
				
					<view class="icon-jiantou" style="font-size: 22rpx;color: #0C0F1485;">
					</view>
					
				</view>
				
			</view>
			<view class="" style="margin-top: 30rpx;font-size: 24rpx;color: #555555;width: 100%;height: 120rpx;display: flex;align-items: center;position: relative;justify-content: space-between;">
				<view class="" v-for="item in listImages" style="width: 120rpx;height: 120rpx;border-radius: 16rpx;">
					<image :src="item" style="width: 120rpx;height: 120rpx;" mode=""></image>
				</view>
				
			</view>
			<!-- <view class="">
				<p style="color: #161b2585;font-size: 26;">选择服务可使用积分抵扣或直接支付费用</p>
			</view> -->
		</view>
		<m-pointService @chooseCoup="chooseCoupon" :limitTime="coupon_limit_time" :coupType="1"
			:choosePrice="coupon_price" :limitNum="roomPrice * roomNum" :roomType="roomInfo.item.id"></m-pointService>

		<!-- 押金 -->
		<view class="cashBox" v-if="!ifOnline&&cash_pledge>0"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="room">
				<text style="padding-right: 80rpx;">押金</text>
				<view class="" style="padding: 0;display: flex;align-items: center;">
					<text style="color: red;font-size: 28rpx;">￥{{cash_pledge}}/每间房</text>
				</view>
			</view>
		</view>

		<!-- 发票 -->
		<view class="cashBox" style="padding: 30rpx;display: flex;flex-direction: column;"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<p style="font-size: 32rpx;">发票·服务</p>
			<view class="" style="margin-top: 30rpx;font-size: 28rpx;" :style="{color:themeColor.text_second_color}">
				<p>酒店开票 | 订单中提交开票信息 | 前台领取</p>
			</view>
		</view>
		<!-- 占位 -->
		<view class="" style="height: 160rpx;">
		</view>
		<view class="submitBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="price">
				<view class="" style="padding-left: 30rpx;">
					<p style="font-size: 48rpx;font-weight: 500;" :style="{color:themeColor.main_color}">
						<text style="font-size: 22rpx;" :style="{color:themeColor.main_color}">￥</text>
						<text>{{totalPrice}}</text>
						<text v-if="coupon_price&&coupon_price>0" style="font-size: 22rpx;text-decoration: line-through;margin-left: 10rpx;" :style="{color:themeColor.main_color}"><text>￥{{roomPrice * roomNum}}</text></text>
					</p>
					<view class="" style="display: flex;align-items: center;">
						
						<p v-if="coupon_price&&coupon_price>0" style="font-size: 22rpx;" :style="{color:themeColor.main_color}">已优惠:￥{{coupon_price}}</p>
						
					</view>
				
				</view>

				<view class="t2" @click="priceDetail">
					<text class="" style="font-size: 24rpx;" :style="{color: themeColor.main_color}">费用明细</text>
				</view>
			</view>
			<view class="dateBox">
				<view class="t1" :style="{background: 'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, '+themeColor.bg_main1_color +' 100%)'}" @click="lastBill">
					<text style="font-size: 36rpx;color:#FFFFFF">去支付</text>
					<view class="" style="font-size: 14rpx;color:#FFFFFF">
						<text>{{startDate_show1}}</text>
						<text class="" v-if="unit=='standard'||unit=='long_standard'">
							- {{endDate_show1}}
						</text>
						<text style="padding-left: 30rpx;" v-if="unit=='standard'">共{{countDay_show}}晚</text>
						<text style="padding-left: 30rpx;" v-if="unit=='long_standard'">共{{monthNum}}月</text>
						<text v-if="unit=='hour'||unit=='conference_room'"> -共{{roomInfo.item.stay_time}}小时</text>
					</view>
				</view>
			</view>
		</view>
		<!-- 酒店详情说明 -->
		<m-popup :show="popDetail" @closePop="closePopDetail">
			<scroll-view scroll-y="true" style="height: 80vh;">
				<view class="roomDetail"
					:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
					<m-swiper :swiper_list="roomInfo.item.image_list" :round="20"></m-swiper>
					<text class="roomDetail_title">{{roomInfo.item.name}}</text>
					<view class="roomDetail_basic">
						<view class="roomDetail_basic_item" v-for="item in roomInfo.item.basic_msg" :key="item.id">
							<text :style="{color:themeColor.text_title_color}"
								style="padding-right: 40rpx;">{{item.label}}</text>
							<text>{{item.des}}</text>
						</view>
					</view>
					<view class="roomInfo.item">
						<m-amenities :mode="item.property.style" :list="roomInfo.item.facility"></m-amenities>
					</view>
					<view @click="closePopDetail" class="icon-close"
						style="position: absolute;top: 30rpx;left:30rpx;font-size: 40rpx;color: #ffffff;">
					</view>
				</view>
			</scroll-view>
		</m-popup>

		<!-- 支付弹窗 -->
		<m-popup :show="pop" @closePop="closePop">
			<m-payCard @toPay="payFor" :payType="payList" v-if="hackReset"></m-payCard>
		</m-popup>

		<!-- 选择优惠券弹窗 -->
		<m-popup :show="popCoupon" @closePop="closePopCoupon">
			<m-chooseCoupon  v-if="hackReset1" :coupType="1" :shopId="hotel.id" :limit="{limitNum:roomPrice * roomNum}" :roomType="roomInfo.item.id"
				@getCouponIfo="getInfo"></m-chooseCoupon>
		</m-popup>

		<!-- 预计到店时间选择,假如是全日房 -->
		<m-popup :show="pop1" @closePop="closePop1">
			<view class="ready_time">
				<p style="font-size: 38rpx;font-weight: 500;">
					{{unit=='standard'||unit=='long_standard'?'预计到店':'选择入住时间'}}
				</p>
				<scroll-view scroll-y="true" style="height: 750rpx;" :scroll-into-view="scrollId"
					scroll-with-animation="true">
					<view class="ready_time_item" :id="'q'+index" v-for="(item,index) in planTimeList" :key="index"
						@click="chooseReady({'item':item,'index':index})" :style="planTime==item?'color:red':''">
						{{item}}
					</view>
				</scroll-view>
			</view>
		</m-popup>
		<!-- 会员支付方式弹出窗 -->
		<m-popup :show="pop4" @closePop="closePop4" mode="center">
			<view style="" class="customPay" v-if="!ifOnline" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
				<view style="position: absolute;top: 20rpx;right: 30rpx;" @click="closePop4">
					<view class="icon-close" style="font-size: 40rpx;"></view>
				</view>
				<view class=""
					style="width: 90%;height: 200rpx;border-bottom: 1px solid #e4e7ed;display: flex;flex-direction: column;align-items: center;justify-content: space-around;">
					<text style="font-size:30rpx;">{{hotel.shop_name}}</text>
					<text style="font-size:40rpx;">总计:￥{{totalPrice}}</text>
				</view>
				<view class=""
					style="width: 94%;height: 50rpx;display: flex;align-items: center;justify-content: space-between;">
					<text
						style="color:909399;">￥{{roomPrice * roomNum +  servicePrice * roomNum * (unit=='standard'?priceList.length:(unit=='long_standard'?monthNum:1))}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="integral-fill" color="#0055ff"></u-icon>
						<text style="">{{payType=='tongyong'?'跨店会员自动扣除':'单店会员自动扣除'}}（房费）</text>
					</view>
				</view>
				<view class=""
					style="width: 94%;height: 50rpx;display: flex;align-items: center;justify-content: space-between;">
					<text
						style="color:909399;">￥{{totalGoodsPrices}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="integral-fill" color="#0055ff"></u-icon>
						<text
							style="">精选服务费用</text>
					</view>
				
				</view>
				<view class="" v-if="coupon_price > 0"
					style="width: 94%;height: 50rpx;display: flex;align-items: center;justify-content: space-between;">
					<text style="color:909399;">-￥{{coupon_price}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="weixin-circle-fill" color="#00ca17"></u-icon>
						<text style="">优惠券</text>
					</view>

				</view>
				<view class=""
					style="width: 94%;height: 50rpx;display: flex;align-items: center;justify-content: space-between;">
					<text style="color:909399;">￥{{cash_pledge * roomNum}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="weixin-circle-fill" color="#00ca17"></u-icon>
						<text style="">微信支付（押金）</text>
					</view>

				</view>
				<view class="btn_pay">
					<view class="btn"
						:style="'background:'+themeColor.main_color+';color:'+themeColor.bg_color"
						@click="surePay">
						<text>确认支付</text>
					</view>
				</view>
			</view>

			<!-- 无押金 -->
			<view class="customPay" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}" v-else>

				<view style="position: absolute;top: 20rpx;right: 30rpx;" @click="closePop4">
					<view class="icon-close" style="font-size: 40rpx;"></view>
				</view>
				<view class=""
					style="width: 90%;height: 200rpx;border-bottom: 1px solid #e4e7ed;display: flex;flex-direction: column;align-items: center;justify-content: space-around;">
					<text style="font-size:30rpx;">{{hotel.shop_name}}</text>
					<text style="font-size:40rpx;">总计:￥{{totalPrice}}</text>
				</view>
				<view class=""
					style="width: 94%;height: 50rpx;display: flex;align-items: center;justify-content: space-between;">
					<text
						style="color:909399;">￥{{roomPrice * roomNum +  servicePrice * roomNum * (unit=='standard'?priceList.length:(unit=='long_standard'?monthNum:1))}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="integral-fill" color="#0055ff"></u-icon>
						<text
							style="">{{payType=='tongyong'?'跨店会员自动扣除':(payType=='duli'?'单店会员自动扣除':'到店付款')}}（房费）</text>
					</view>

				</view>
				<view class=""
					style="width: 94%;height: 50rpx;display: flex;align-items: center;justify-content: space-between;">
					<text
						style="color:909399;">￥{{totalGoodsPrices}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="integral-fill" color="#0055ff"></u-icon>
						<text
							style="">精选服务费用</text>
					</view>
				
				</view>
				<view class="" v-if="coupon_price > 0"
					style="width: 94%;height: 50rpx;display: flex;align-items: center;justify-content: space-between;">
					<text style="color:909399;font-weight: 600;">-￥{{coupon_price}}</text>
					<view class="" style="display: flex;align-items: center;">
						<u-icon name="weixin-circle-fill" color="#00ca17"></u-icon>
						<text style="font-weight: 600;">优惠券</text>
					</view>

				</view>
				<view class="btn_pay">
					<view class="btn"
						:style="'background:'+themeColor.main_color+';color:'+themeColor.bg_color"
						@click="surePay">
						<text>{{payType!='daodian'?'确认支付':'确认预定'}}</text>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 明细弹窗 -->
		<m-popup :show="pop3" @closePop="closePop3">
			<scroll-view scroll-y="true" style="height: 70vh;">
				<view class="detail_pop">
					<view style="position: absolute;top: 20rpx;left: 30rpx;" @click="closePop3">
						<view class="icon-close" style="font-size: 40rpx;"></view>
					</view>
					<p class="pop3_title">费用明细</p>
					<view class="pop3_title1">
						<text :style="{color:themeColor.text_main_color}">{{startDate_show}}</text>
						<text class="" v-if="unit=='standard'||unit=='long_standard'">
							- {{endDate_show}}
						</text>
						<text style="padding-left: 30rpx;" v-if="unit=='standard'">共{{countDay_show}}晚</text>
						<!-- 时租房 -->
						<p style="padding: 30rpx 0 0 30rpx;color: darkgray;"
							v-if="unit=='hour'||unit=='conference_room'">
							可住时间段：{{roomInfo.item.start_time_limit + '-'+ roomInfo.item.end_time_limit}},{{ roomInfo.item.room_sale_type_name}}
						</p>
						<!-- 月租房 -->
						<text style="padding-left: 30rpx;" v-if="unit=='long_standard'">共{{monthNum}}月</text>
						<!-- <p style="padding: 30rpx 0 0 30rpx;color: darkgray;" v-if="unit=='long_standard'">至少入住14天</p> -->
						<text style="padding-left: 30rpx;">{{roomNum}}间</text>
					</view>

					<view class="room">
						<text style="padding-right: 80rpx;font-size: 36rpx;">房费合计</text>
						<view class="" style="font-weight: 600;font-size: 36rpx;"
							:style="{color:themeColor.main_color}">
							<text>￥{{roomPrice * roomNum}}</text>
						</view>
					</view>
					<view class="room" v-for="(item, index) in priceList" :key="item.id">
						<view class="">
							<text :style="{color:themeColor.text_main_color}">{{item.date}}</text>
							<text
								style="padding-left: 30rpx;">{{unit=="standard"||unit=='long_standard'?'1晚':`${roomInfo.item.stay_time}小时`}}</text>
							<text>{{roomInfo.service.service_name}}/每间</text>
							<text v-if="index==(priceList.length-1)">(离店日)</text>
						</view>
						<view class="" style="font-size: 22rpx;">
							<text>{{roomNum}}间 x ￥{{item.room_price}}</text>
						</view>
					</view>
					<view class="room" v-if="!ifOnline">
						<text style="padding-right: 80rpx;color: #555555;">押金</text>
						<view class="" style="font-weight: 300;color: #555555;">
							<P>￥{{cash_pledge}}x {{roomNum}}间</P>
						</view>
					</view>
					<view class="room">
						<text style="padding-right: 80rpx;color: #555555;">套餐 {{roomInfo.service.service_name}}</text>
						<view class="" style="font-weight: 300;color: #555555;">
							<p>￥{{servicePrice * roomNum * (unit=='standard'?priceList.length:(unit=='long_standard'?monthNum:1))}}
							</p>
						</view>
					</view>
					<view class="room" v-for="(item, index) in cartList" :key="item.id" v-if="cartList.length>0">
						<view class="">
							<text :style="{color:themeColor.text_main_color}">{{item.name}}</text>
							
						</view>
						<view class="" style="font-size: 22rpx;" v-if="!item.ifMem">
							<text>{{item.number}} x ￥{{item.price}}</text>
						</view>
						<view class="" :style="{color:themeColor.main_color}" style="font-size: 22rpx;" v-if="item.ifMem">
							会员免费专享
						</view>
					</view>
					<view class="room" style="border-top:1px solid #dedede;margin-top: 50rpx;" v-if="coupon_info.discount_type==1">
						<text style="padding-right: 80rpx;color: #555555;" >满减券</text>
						<view class="" style="font-weight: 300;color: #555555;">
							<text>-￥{{coupon_price}}</text>
						</view>
					</view>
					<view class="room" style="border-top:1px solid #dedede;margin-top: 50rpx;" v-if="coupon_info.discount_type==2">
						<text style="padding-right: 80rpx;color: #555555;" >折扣券</text>
						<view class="" style="font-weight: 300;color: #555555;">
							<text>-￥{{coupon_price}}</text>
							<text style="margin-left: 20rpx;">享{{coupon_info.discount_rate*10}}折优惠</text>
						</view>
					</view>
					<view class="room">
						<text
							style="padding-right: 80rpx;color: #555555;">{{userInfo.grade_name?userInfo.grade_name+'优惠':'散客优惠'}}</text>
						<view class="" style="font-weight: 300;color: #555555;">
							<text>已减￥{{Math.abs(roomInfo.item.discounts_price).toFixed(2)}}/间</text>
						</view>
					</view>

					

					<view class="end" style="font-size: 40rpx;" :style="{color:themeColor.text_main_color}">
						<p>实际花费:￥{{totalPrice}}</p>
					</view>
				</view>
			</scroll-view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';

	export default {
		data() {
			return {
				name: [],
				phone: '',
				planTimeList: [], //预计到店时间数组
				planTime: '',
				limitTime: 14,
				pop: false,
				pop1: false,
				pop2: false,
				pop3: false,
				pop4: false,
				popDetail: false,
				popCoupon: false,
				scrollId: '',
				idIndex: 0,
				ifArrow: false,
				priceList: [], //每日房价集合（不包含套餐）
				servicePrice: 0, //套餐价格
				cash_pledge: 0,
				coupon_price: 0,
				ifOnline: false, //是否线上收取押金
				jlFlag: true,
				roomNum: 1, //房间数
				roomNames: [], //入住人
				monthNum: 1,
				payType: '', //付款方式
				monthNumArr: ['1个月', '2个月', '3个月', '4个月', '5个月', '6个月', '7个月', '8个月', '9个月', '10个月', '11个月', '12个月'],
				coupon_id: '',
				coupon_limit_time: 0,
				payList: ['weixin', 'tongyong', 'duli'], //支付方式,
				hackReset: true,
				hackReset1: true,
				totalGoodsPrices:0,
				listImages:[
					'http://doc.hanwuxi.cn/wp-content/uploads/2024/11/3e0d640a0eca87326b0f57a27f861a4.png',
					'http://doc.hanwuxi.cn/wp-content/uploads/2024/11/a7d27433a354f0d2756afc411daeca3.png',
					'http://doc.hanwuxi.cn/wp-content/uploads/2024/11/40505327665f0ea2bbab0921ffd2539.png',
					'http://doc.hanwuxi.cn/wp-content/uploads/2024/11/a260394dcd0e28431a44c6b55ced279.png',
					'http://doc.hanwuxi.cn/wp-content/uploads/2024/11/4ffbfdc9183fdfa8f779d5eaed72b36.png',
				],
				memCount:'',
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				opacity:1,
				imgHeight:'',
				coupon_info:null
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'unit', 'startDate', 'endDate', 'roomInfo', 'linkMan', 'shopSetting',
				'setting','shareId'
			]),
			...mapState('market', ['cartList']),
			startDate_show() {
				//正常双日期情况：如果是凌晨显示时间按照昨天-今天的，其他月租房和钟点房则正常
				//有两种情况：1，默认进入如果是凌晨会赋予开始时间为昨天结束时间为今天，2，日历选择凌晨开始时间选择昨天，结束时间为今天
				//首先判断当前时间是否大于开始时间就代表的是选择的凌晨
				if (this.$moment().startOf('day').unix() > this.$moment(this.startDate).format('x') * 1) {
					return '凌晨入住'
				} else {
					return this.$moment.unix(this.startDate).format('MM月DD日')
				}
			},
			endDate_show() {

				if (this.unit == 'standard') {
					return this.$moment.unix(this.endDate).format('MM月DD日')
				} else if (this.unit == 'long_standard') {
					return this.$moment.unix(this.startDate).add(this.monthNum, 'months').format('MM月DD日')
				}
			},
			startDate_show1() {
				//正常双日期情况：如果是凌晨显示时间按照昨天-今天的，其他月租房和钟点房则正常
				//有两种情况：1，默认进入如果是凌晨会赋予开始时间为昨天结束时间为今天，2，日历选择凌晨开始时间选择昨天，结束时间为今天
				//首先判断当前时间是否大于开始时间就代表的是选择的凌晨
				if (this.$moment().startOf('day').unix() > this.$moment(this.startDate).format('x') * 1) {
					return '凌晨入住'
				} else {
					return this.$moment.unix(this.startDate).format('MM月DD日')
				}
			},
			endDate_show1() {

				if (this.unit == 'standard') {
					return this.$moment.unix(this.endDate).format('MM月DD日')
				} else if (this.unit == 'long_standard') {
					return this.$moment.unix(this.startDate).add(this.monthNum, 'months').format('MM月DD日')
				}

			},

			countDay_show() {
				if (this.unit == 'standard') {
					let s = this.$moment.unix(this.startDate)
					let e = this.$moment.unix(this.endDate)

					let c = 0
					if (this.startDate == this.endDate) {
						c = 1
					} else {
						c = e.diff(s, 'days')
					}
					return c
				}

			},
			tqRoom() {
				let a = ''
				if (this.userInfo.grade_info) {
					this.userInfo.grade_info.right_itererest.forEach(item => {
						if (item.sign == 'tqrz') {
							if (item.value && item.status == 1) {
								a = item.value
							} else {
								a = ' '
							}
						} else {
							a = ' '
						}
					})
				}


				return a
			},
			roomPrice() {
				let price = 0

				this.priceList.forEach(item => {
					price += ((Math.round(item.room_price * 100) / 100))
				})
				return (Math.round(price * 100) / 100)
			},
			totalPrice() {

				let price = 0
				if (this.unit == 'standard') {
					if (!this.ifOnline) {

						price = this.roomPrice * this.roomNum + this.cash_pledge * this.roomNum - this.coupon_price + this
							.servicePrice * this.roomNum * this.priceList.length
					} else {
						price = this.roomPrice * this.roomNum - this.coupon_price + this.servicePrice * this.roomNum * this
							.priceList.length
					}
				} else if (this.unit == 'long_standard') {
					if (!this.ifOnline) {
						price = this.roomPrice * this.roomNum + this.cash_pledge * this.roomNum - this.coupon_price + this
							.servicePrice * this.roomNum * this.monthNum
					} else {
						price = this.roomPrice * this.roomNum - this.coupon_price + this.servicePrice * this.roomNum * this
							.monthNum
					}
				} else {
					if (!this.ifOnline) {
						price = this.roomPrice * this.roomNum + this.cash_pledge * this.roomNum - this.coupon_price + this
							.servicePrice * this.roomNum
					} else {
						price = this.roomPrice * this.roomNum - this.coupon_price + this.servicePrice * this.roomNum
					}
				}

				if (this.cartList.length > 0) {
					let goodPrices = 0
					this.cartList.forEach(item => {
						if(!item.ifMem){
							goodPrices += (item.number * item.price)
						}
					})
					this.totalGoodsPrices = goodPrices
					price += goodPrices
				}

				return (Math.round(price * 100) / 100)
			}

		},
		watch: {
			planTimeList: {
				handler(newData, oldData) {
					this.planTime = this.planTimeList[0]
				},
				immediate: true
			},
			monthNum() {
				let param = {
					room_sale_type_id: this.roomInfo.item.room_sale_type_id,
					room_type_id: this.roomInfo.item.id,
					intermediary_id: 0,
					start_time: this.startDate,
					room_service_id: this.roomInfo.service.id,
					months: this.monthNum
				}
				this.$iBox
					.http('getLongStandardPriceArrByDate', param)({
						method: 'post'
					})
					.then(res => {
						this.priceList = res.data.room_price
						this.servicePrice = res.data.room_service.price
						this.cash_pledge = res.data.cash_pledge
					})
			},
			roomNum: {
				handler(newData, oldData) {
					this.coupon_price = 0

					let roomNames = []
					for (var i = 0; i < this.roomNum; i++) {
						let names = {
							name: ''
						}
						roomNames.push(names)
					}
					roomNames[0].name = this.userInfo.name ? this.userInfo.name : ''
					this.roomNames = roomNames
					this.hackReset1 = false
					this.$nextTick(() => {
						this.hackReset1 = true
					})
				},
				immediate: true,
				deep: true
			},
			roomNames: {
				handler(newData, oldData) {
					let name = []
					this.roomNames.forEach(item => {
						name.push(item.name)
					})
					this.name = name
				},
				immediate: true,
				deep: true
			}
		},
		async onLoad() {
			await this.$onLaunched;
			const systemInfo = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo.statusBarHeight + 44;
			// #ifdef MP-WEIXIN || APP-PLUS
			// 获取状态栏和胶囊位置
			const {
				top,
				height
			} = uni.getMenuButtonBoundingClientRect()
			this.imgHeight = (top + height + 10) * 0.6;
			// #endif
			
			// #ifdef H5
			this.imgHeight = 100;
			// #endif
		},
		async onShow() {
			await this.$onLaunched;
			// 查询是否有到店付
			let arrvid = this.shopSetting.filter(item => {
				return item.sign == 'arrive_hotel_pay'
			})[0].property.status
			console.log(arrvid, 'arrvid', this.roomInfo);
			if (arrvid) {
				this.payList.push('daodian')
			}
			
		
			console.log(this.cartList);
			if(this.cartList.length > 0){
				let num = 0
				this.cartList.forEach(item=>{
					console.log(num+item.number,'jj');
					num = num +item.number
				})
				console.log(num);
				this.memCount = num
			}

			// //是否是会员
			// if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -1) {
			// 	this.if_login = false
			// } else {
			// 	this.if_login = true
			// }

			// 自动填写会员信息1
			this.roomNames[0].name = this.userInfo.name ? this.userInfo.name : ''
			this.phone = this.userInfo.phone ? this.userInfo.phone : ''

			// 查询是否免押金
			if (this.shopSetting.length > 0) {
				// 先判断是否散客
				if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value >
					-
					1) {
					this.ifOnline = this.userInfo.grade_info.right_itererest.filter(item => {
						return item.sign == 'myj'
					})

					if (this.ifOnline.length > 0) {
						this.ifOnline = this.ifOnline[0].status == 1 ? true : false
					}

				} else {
					this.ifOnline = this.shopSetting.filter(item => {
						return item.sign == 'no_cash_pledge'
					})

					if (this.ifOnline > 0) {
						this.ifOnline = this.ifOnline[0].property.status == 1 ? true : false
					}
				}

			}

			console.log(this.ifOnline, 'this.ifOnline');

			// 赋值房间数量,非自选房模式

			this.TimeAsc()
			if (this.unit == 'standard') {
				let param = {
					room_sale_type_id: this.roomInfo.item.room_sale_type_id,
					room_type_id: this.roomInfo.item.id,
					intermediary_id: 0,
					start_time: this.startDate,
					end_time: this.endDate,
					room_service_id: this.roomInfo.service.id
				}
				this.$iBox
					.http('getStandardRoomPriceArrByDate', param)({
						method: 'post'
					})
					.then(res => {
						this.priceList = res.data.room_price
						this.cash_pledge = res.data.cash_pledge
						this.servicePrice = res.data.room_service.price
					})
			} else if (this.unit == 'hour') {
				let param = {
					room_sale_type_id: this.roomInfo.item.room_sale_type_id,
					room_type_id: this.roomInfo.item.id,
					intermediary_id: 0,
					start_time: this.startDate,
					room_service_id: this.roomInfo.service.id
				}
				this.$iBox
					.http('getHourRoomPriceArrByDate', param)({
						method: 'post'
					})
					.then(res => {
						this.priceList = res.data.room_price
						this.cash_pledge = res.data.cash_pledge
						this.servicePrice = res.data.room_service.price
					})
			} else if (this.unit == 'conference_room') {
				let param = {
					room_sale_type_id: this.roomInfo.item.room_sale_type_id,
					room_type_id: this.roomInfo.item.id,
					intermediary_id: 0,
					start_time: this.startDate,
					room_service_id: this.roomInfo.service.id
				}
				this.$iBox
					.http('getConferenceRoomPriceArrByDate', param)({
						method: 'post'
					})
					.then(res => {
						this.priceList = res.data.room_price
						this.cash_pledge = res.data.cash_pledge
						this.servicePrice = res.data.room_service.price
					})
			} else {
				let param = {
					room_sale_type_id: this.roomInfo.item.room_sale_type_id,
					room_type_id: this.roomInfo.item.id,
					intermediary_id: 0,
					start_time: this.startDate,
					room_service_id: this.roomInfo.service.id,
					months: this.monthNum
				}
				this.$iBox
					.http('getLongStandardPriceArrByDate', param)({
						method: 'post'
					})
					.then(res => {
						this.priceList = res.data.room_price
						this.cash_pledge = res.data.cash_pledge
						this.servicePrice = res.data.room_service.price
					})
			}

		},
		methods: {
			goMain(){
				uni.navigateBack({})
			},
			toDetail() {
				this.popDetail = true
			},

			lastBill() {
				if (this.planTime == "今日暂无可预定时间段，请选择其他日期") {
					uni.showToast({
						icon: 'none',
						title: '今日暂无可预定时间段，请选择其他日期'
					})
				} else {
					console.log(this.name, 'this.name');
					if (this.name.length == 0) {
						uni.showToast({
							icon: 'none',
							title: '请填写入住人姓名'
						})
						return;
					} else {
						for (let item of this.name) {
							if (!item) {
								uni.showToast({
									icon: 'none',
									title: '请填写入住人姓名'
								})
								return;
								break

							}

							// let pattern = /^[\u4e00-\u9fa5]{2,4}$|^[a-zA-Z]{1,30}$/gi;
							// if (!pattern.test(item.trim())) {
							// 	uni.showToast({
							// 		icon: 'none',
							// 		title: '只需输入一个预订人姓名或检查姓名是否填写正确!',
							// 		duration: 2000
							// 	})
							// 	return;
							// 	break
							// }

						}

					}

					if (!this.phone) {
						uni.showToast({
							icon: 'none',
							title: '请填写入住人电话'
						})
						return;
					}
					this.pop = true
				}


			},
			closePop() {
				this.pop = false
				this.hackReset = false

				this.$nextTick(() => {
					this.hackReset = true
				})
			},
			readyTime() {
				this.pop1 = true
				this.planTimeList = []
				// 首先判断当前时间是否小于限制到店时间14点（可以后台定义），假如是14点前，则从14点开始算
				this.scrollId = 'q' + this.idIndex
				this.TimeAsc()

			},
			goManList() {
				uni.navigateTo({
					url: '/packageA/roomForm/linkMan/manList'
				})
			},
			goShop(){
				uni.navigateTo({
					url:'/packageA/market/market'
				})
			},
			getInfo(e) {

				this.popCoupon = false
				this.coupon_info = e
				if (e) {
					if(e.discount_type == 1){
						this.coupon_price = e.coupon_info.discounts
					}else {
						this.coupon_price =(( 1- e.coupon_info.discount_rate) * this.roomPrice * this.roomNum)>e.coupon_info.discount_max?e.coupon_info.discount_max:( 1- e.coupon_info.discount_rate) * this.roomPrice * this.roomNum
					}
					this.coupon_price = Number(this.coupon_price.toFixed(2))
					this.coupon_id = e.id
					this.coupon_limit_time = e.limit_time
				} else {
					this.coupon_price = 0
					this.coupon_id = ''
				}


			},
			chooseCoupon(e) {

				this.popCoupon = true
			},
			closePopCoupon() {
				this.popCoupon = false
				
			},
			showRoomNumberBox(e) {

				this.roomNum = e
			},
			bindPickerChange1(e) {
				this.monthNum = Number(e.detail.value.split('个月')[0]) + 1
			},

			TimeAsc() {

				// 首先判断是否是全日房
				let timeList = '' //集合

				let ready_start_time = this.$moment(this.startDate * 1000).format('YYYY/MM/DD HH:mm:ss') //订单开始时间
				let ready_end_time = this.$moment(this.endDate * 1000).format('YYYY/MM/DD HH:mm:ss') //订单结束时间
				let start_time = '' //传给后台得开始时间
				let end_time = '' //传给后台得离店时间	
				let startString = '' //小程序展示得选择开始时间
				let endString = '' //小程序展示得选择结束时间

				// 时租房才有
				let limit = this.roomInfo.item.stay_time //stay_time固定入住世家n
				let start = Number(this.roomInfo.item.start_time_limit.split(':')[0])
				let end = Number(this.roomInfo.item.end_time_limit.split(':')[0])


				let enter_time = this.shopSetting.filter(item => {
					return item.sign == 'enter_time'
				})[0].property.value

				enter_time = Number(enter_time.split(':')[0])
				// 获取离店时间设置
				let leave_time = this.shopSetting.filter(item => {
					return item.sign == 'leave_time'
				})[0].property.value

				leave_time = Number(leave_time.split(':')[0])
				// let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${start}:00:00`,
				// 	'YYYY/MM/DD HH:mm:ss').unix()
				if (this.unit == 'hour' || this.unit == 'conference_room') {
					// 首先判断订单时间是否为今天，今天以外得时间可以预订全部时间段
					if (this.$moment().isSame(this.$moment(ready_start_time, 'YYYY/MM/DD HH:mm:ss'), 'day')) {
						// 再判断入离时间是否为跨天，结束时间大于开始时间为跨天
						if (end < start) {
							// 先判断是否小于开始时间比如开始时间为12点-凌晨6点。1，大于12点小于24点，2，小于6点 3,小于12点大于6点,全部可选
							if (start <= this.$moment().get('hour') && this.$moment().get('hour') < 24) {
								let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD HH') + `:00:00`,
									'YYYY/MM/DD HH:mm:ss').unix()
								// 首先判断当前时间离结束时间是否大于限制固定时间,大于则按正常流程
								if ((end + 24 - this.$moment().get('hour')) > limit) {
									for (let i = 0; i < ((end + 24 - this.$moment().get('hour') - limit) * 2 + 1) *
										30; i += 30) {
										// 因为当前这次半小时肯定不够，第一次去除,如果是大于30分则这个小时都去除
										if (i == 0 || (this.$moment().get('minute') > 30 && i == 30)) {
											continue;
										}
										start_time = this.$moment(timeFlag * 1000).add(i, 'minute').unix()
										end_time = this.$moment(start_time * 1000).add(limit * 60, 'minute').unix()

										if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time *
												1000).get('hour') < 7) {
											startString = '凌晨' + this.$moment(start_time * 1000).format('HH:mm')
										} else {
											startString = this.$moment(start_time * 1000).format('HH:mm')
										}


										if (0 <= this.$moment(end_time * 1000).get('hour') && this.$moment(end_time * 1000)
											.get('hour') < 7) {
											endString = '凌晨' + this.$moment(end_time * 1000).format('HH:mm')
										} else {
											endString = this.$moment(end_time * 1000).format('HH:mm')
										}

										timeList = `${startString} - ${endString}`

										this.planTimeList.push(
											timeList
										)
									}
								} else if ((end + 24 - this.$moment().get('hour')) > (limit / 2)) { //小于一半时间可以预订

									start_time = this.$moment(this.$moment().format('YYYY/MM/DD HH') + `:00:00`,
										'YYYY/MM/DD HH:mm:ss').unix()
									end_time = this.$moment(this.$moment().add(1, 'hour').format('YYYY/MM/DD') +
										` ${end}:00:00`, 'YYYY/MM/DD HH:mm:ss').unix()


									if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000)
										.get('hour') <= enter_time) {
										startString = '凌晨' + this.$moment(start_time * 1000).format('HH:mm')
									} else {
										startString = this.$moment(start_time * 1000).format('HH:mm')
									}


									if (0 <= this.$moment(end_time * 1000).get('hour') && this.$moment(end_time * 1000)
										.get('hour') <= enter_time) {
										endString = '凌晨' + this.$moment(end_time * 1000).format('HH:mm')
									} else {
										endString = this.$moment(end_time * 1000).format('HH:mm')
									}

									timeList = `${startString} - ${endString}`

									this.planTimeList.push(
										timeList
									)

								} else {
									timeList = `今日暂无可预定时间段，请选择其他日期`
									this.planTimeList.push(
										timeList
									)
								}
							} else if (this.$moment().get('hour') < end) {
								// 首先判断当前时间离结束时间是否大于限制固定时间,大于则按正常流程
								let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD HH') + `:00:00`,
									'YYYY/MM/DD HH:mm:ss').unix()
								if ((end - this.$moment().get('hour')) > limit) {
									for (let i = 0; i < ((end - this.$moment().get('hour') - limit) * 2 + 1) * 30; i +=
										30) {
										// 因为当前这次半小时肯定不够，第一次去除,如果是大于30分则这个小时都去除
										if (i == 0 || (this.$moment().get('minute') > 30 && i == 30)) {
											continue;
										}
										start_time = this.$moment(timeFlag * 1000).add(i, 'minute').unix()
										end_time = this.$moment(start_time * 1000).add(limit * 60, 'minute').unix()

										if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time *
												1000).get('hour') <= enter_time) {
											startString = '凌晨' + this.$moment(start_time * 1000).format('HH:mm')
										} else {
											startString = this.$moment(start_time * 1000).format('HH:mm')
										}


										if (0 <= this.$moment(end_time * 1000).get('hour') && this.$moment(end_time * 1000)
											.get('hour') <= enter_time) {
											endString = '凌晨' + this.$moment(end_time * 1000).format('HH:mm')
										} else {
											endString = this.$moment(end_time * 1000).format('HH:mm')
										}

										timeList = `${startString} - ${endString}`

										this.planTimeList.push(
											timeList
										)
									}
								} else if ((end - this.$moment().get('hour')) > (limit / 2)) { //小于一半时间可以预订

									start_time = this.$moment(this.$moment().format('YYYY/MM/DD HH') + `:00:00`,
										'YYYY/MM/DD HH:mm:ss').unix()
									end_time = this.$moment(this.$moment().add(1, 'hour').format('YYYY/MM/DD') +
										` ${end}:00:00`, 'YYYY/MM/DD HH:mm:ss').unix()


									if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000)
										.get('hour') <= enter_time) {
										startString = '凌晨' + this.$moment(start_time * 1000).format('HH:mm')
									} else {
										startString = this.$moment(start_time * 1000).format('HH:mm')
									}


									if (0 <= this.$moment(end_time * 1000).get('hour') && this.$moment(end_time * 1000)
										.get('hour') <= enter_time) {
										endString = '凌晨' + this.$moment(end_time * 1000).format('HH:mm')
									} else {
										endString = this.$moment(end_time * 1000).format('HH:mm')
									}

									timeList = `${startString} - ${endString}`

									this.planTimeList.push(
										timeList
									)

								} else {
									timeList = `今日暂无可预定时间段，请选择其他日期`
									this.planTimeList.push(
										timeList
									)
								}

							} else if (this.$moment().get('hour') > end && this.$moment().get('hour') < start) {
								//全部时间段可选 
								let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${start}:00:00`,
									'YYYY/MM/DD HH:mm:ss').unix()

								for (let i = 0; i < ((end + 24 - start - limit) * 2 + 1) * 30; i += 30) {
									// 从开始时间开始每次循环加30分钟
									// this.$moment(start_time, 'YYYY/MM/DD HH:mm:ss')
									start_time = this.$moment(timeFlag * 1000).add(i, 'minute').unix()
									end_time = this.$moment(start_time * 1000).add(limit * 60, 'minute').unix()

									if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000)
										.get('hour') <= enter_time) {
										startString = '凌晨' + this.$moment(start_time * 1000).format('HH:mm')
									} else {
										startString = this.$moment(start_time * 1000).format('HH:mm')
									}


									if (0 <= this.$moment(end_time * 1000).get('hour') && this.$moment(end_time * 1000)
										.get('hour') <= enter_time) {
										endString = '凌晨' + this.$moment(end_time * 1000).format('HH:mm')
									} else {
										endString = this.$moment(end_time * 1000).format('HH:mm')
									}
									timeList = `${startString} - ${endString}`

									this.planTimeList.push(
										timeList
									)
								}
							}
						} else {

							// 订单日期为今天且不垮天则按正常三种情况，1.当前小于开始时间，全部时间展示，2，当前时间小于结束时间前一个小时并大于开始时间，根据当前时间计算，3，大于结束时间不可预定
							if (this.$moment().get('hour') < start) {
								let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${start}:00:00`,
									'YYYY/MM/DD HH:mm:ss').unix()
								//全部时间段可选
								for (let i = 0; i < ((end - start - limit) * 2 + 1) * 30; i += 30) {
									// 从开始时间开始每次循环加30分钟

									start_time = this.$moment(timeFlag * 1000).add(i, 'minute').unix()
									end_time = this.$moment(start_time * 1000).add(limit * 60, 'minute').unix()

									startString = this.$moment(start_time * 1000).format('HH:mm')
									endString = this.$moment(end_time * 1000).format('HH:mm')

									timeList = `${startString} - ${endString}`

									this.planTimeList.push(
										timeList
									)
								}

							} else if (start <= this.$moment().get('hour') && this.$moment().get('hour') < end) {

								let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD HH') + `:00:00`,
									'YYYY/MM/DD HH:mm:ss').unix()
								// 首先判断当前时间离结束时间是否大于限制固定时间,大于则按正常流程

								if ((end - this.$moment().get('hour')) > limit) {

									for (let i = 0; i < ((end - this.$moment().get('hour') - limit) * 2 + 1) * 30; i +=
										30) {
										// 因为当前这次半小时肯定不够，第一次去除,如果是大于30分则这个小时都去除
										if (i == 0 || (this.$moment().get('minute') > 30 && i == 30)) {
											continue;
										}

										start_time = this.$moment(timeFlag * 1000).add(i, 'minute').unix()
										end_time = this.$moment(start_time * 1000).add(limit * 60, 'minute').unix()

										startString = this.$moment(start_time * 1000).format('HH:mm')
										endString = this.$moment(end_time * 1000).format('HH:mm')

										timeList = `${startString} - ${endString}`

										this.planTimeList.push(
											timeList
										)
									}
								} else if ((end - this.$moment().get('hour')) > (limit / 2)) { //小于一半时间可以预订

									start_time = this.$moment(this.$moment().format('YYYY/MM/DD HH') + `:00:00`,
										'YYYY/MM/DD HH:mm:ss').unix()
									end_time = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${end}:00:00`,
										'YYYY/MM/DD HH:mm:ss').unix()


									startString = this.$moment(start_time * 1000).format('HH:mm')
									endString = this.$moment(end_time * 1000).format('HH:mm')

									timeList = `${startString} - ${endString}`

									this.planTimeList.push(
										timeList
									)

								} else {
									timeList = `今日暂无可预定时间段，请选择其他日期`
									this.planTimeList.push(
										timeList
									)
								}

							} else {
								timeList = `今日暂无可预定时间段，请选择其他日期`
								this.planTimeList.push(
									timeList
								)
							}
						}

					} else { //订单时间不是今天则可以选择全部时间段
						let timeFlag = this.$moment(this.$moment(ready_start_time, 'YYYY/MM/DD HH:mm:ss').format(
							'YYYY/MM/DD') + ` ${start}:00:00`, 'YYYY/MM/DD HH:mm:ss').unix()

						if (end < start) {

							//全部时间段可选
							for (let i = 0; i < ((end + 24 - start - limit) * 2 + 1) * 30; i += 30) {
								// 从开始时间开始每次循环加30分钟
								// this.$moment(start_time, 'YYYY/MM/DD HH:mm:ss')
								start_time = this.$moment(timeFlag * 1000).add(i, 'minute').unix()
								end_time = this.$moment(start_time * 1000).add(limit * 60, 'minute').unix()

								if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000)
									.get('hour') <= enter_time) {
									startString = '凌晨' + this.$moment(start_time * 1000).format('HH:mm')
								} else {
									startString = this.$moment(start_time * 1000).format('HH:mm')
								}


								if (0 <= this.$moment(end_time * 1000).get('hour') && this.$moment(end_time * 1000).get(
										'hour') <= enter_time) {
									endString = '凌晨' + this.$moment(end_time * 1000).format('HH:mm')
								} else {
									endString = this.$moment(end_time * 1000).format('HH:mm')
								}

								timeList = `${startString} - ${endString}`

								this.planTimeList.push(
									timeList
								)
							}
						} else {
							//全部时间段可选
							for (let i = 0; i < ((end - start - limit) * 2 + 1) * 30; i += 30) {
								// 从开始时间开始每次循环加30分钟
								// this.$moment(start_time, 'YYYY/MM/DD HH:mm:ss')
								start_time = this.$moment(timeFlag * 1000).add(i, 'minute').unix()
								end_time = this.$moment(start_time * 1000).add(limit * 60, 'minute').unix()

								startString = this.$moment(start_time * 1000).format('HH:mm')
								endString = this.$moment(end_time * 1000).format('HH:mm')

								timeList = `${startString} - ${endString}`

								this.planTimeList.push(
									timeList
								)
							}
						}
					}

				} else {

					// 全日房
					//首先判断选择日期开始时间是昨天，则肯定是在0-6点下的单，或者为今天  或者为以后的日期，分为3种方式
					//1，凌晨入住今天的，必然选择的昨天日期
					if (this.$moment().subtract(1, 'day').isSame(this.$moment(ready_start_time, 'YYYY/MM/DD HH:mm:ss'),
							'day')) {
						let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD') + ` 00:00:00`,
							'YYYY/MM/DD HH:mm:ss').unix() //今天0点时间标识
						let nowHour = this.$moment().hour()

						for (let i = nowHour; i <= enter_time; i++) {
							// 从开始时间开始每次循环加30分钟
							// this.$moment(start_time, 'YYYY/MM/DD HH:mm:ss')
							start_time = this.$moment(timeFlag * 1000).add(i, 'hour').unix()

							if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000).get(
									'hour') <= enter_time) {
								startString = '次日凌晨' + this.$moment(start_time * 1000).format('HH:mm')
							} else {
								startString = this.$moment(start_time * 1000).format('HH:mm')
							}

							timeList = `${startString}`

							this.planTimeList.push(
								timeList
							)
						}


					} else if (this.$moment().isSame(this.$moment(ready_start_time, 'YYYY/MM/DD HH:mm:ss'), 'day')) {

						// 判断现在的时间是否在14点之前
						if (this.$moment().get('hour') < leave_time) {
							let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${leave_time}:00:00`,
								'YYYY/MM/DD HH:mm:ss').unix() //今天0点时间标识
							for (let i = 0; i <= (24 - leave_time) + enter_time; i++) {
								// 从开始时间开始每次循环加30分钟
								// this.$moment(start_time, 'YYYY/MM/DD HH:mm:ss')
								start_time = this.$moment(timeFlag * 1000).add(i, 'hour').unix()

								if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000)
									.get('hour') <= enter_time) {
									startString = '次日凌晨' + this.$moment(start_time * 1000).format('HH:mm')
								} else {
									startString = this.$moment(start_time * 1000).format('HH:mm')
								}

								timeList = `${startString}`

								this.planTimeList.push(
									timeList
								)
							}
						} else {
							let timeFlag = this.$moment().unix() //今天0点时间标识
							let hourNum = this.$moment().get('hour')
							for (let i = 1; i <= 24 + enter_time - hourNum; i++) {
								start_time = this.$moment(timeFlag * 1000).add(i, 'hour').unix()
								if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000)
									.get('hour') <= enter_time) {
									startString = '次日凌晨' + this.$moment(start_time * 1000).format('HH:') + '00'
								} else {
									startString = this.$moment(start_time * 1000).format('HH:') + '00'
								}

								timeList = `${startString}`

								this.planTimeList.push(
									timeList
								)
							}
						}
					} else {

						let timeFlag = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${leave_time}:00:00`,
							'YYYY/MM/DD HH:mm:ss').unix() //今天0点时间标识
						for (let i = 0; i <= (24 - leave_time) + enter_time; i++) {
							// 从开始时间开始每次循环加30分钟
							// this.$moment(start_time, 'YYYY/MM/DD HH:mm:ss')
							start_time = this.$moment(timeFlag * 1000).add(i, 'hour').unix()

							if (0 <= this.$moment(start_time * 1000).get('hour') && this.$moment(start_time * 1000)
								.get('hour') <= enter_time) {
								startString = '次日凌晨' + this.$moment(start_time * 1000).format('HH:mm')
							} else {
								startString = this.$moment(start_time * 1000).format('HH:mm')
							}

							timeList = `${startString}`

							this.planTimeList.push(
								timeList
							)
						}

					}

				}
			},
			chooseReady(e) {
				this.planTime = e.item
				this.idIndex = e.index

				this.closePop1()
			},
			closePop1() {
				this.pop1 = false

			},
			closePop2() {
				this.pop2 = false

			},
			closePop3() {
				this.pop3 = false
				this.ifArrow = !this.ifArrow

			},
			closePop4() {
				this.pop4 = false
			},
			closePopDetail() {
				this.popDetail = false
			},
			priceDetail() {
				this.ifArrow = !this.ifArrow
				this.pop3 = !this.pop3
			},
			fnPay(e) {
				console.log(e, '支付参数');
				uni.showLoading({
					title: '等待支付...'
				})
				this.payType = e

				// 首先判断是否用微信还是余额,0:微信  1:通用余额  2.独立余额
				if (e == 'weixin') {
					uni.hideLoading()
					// 判断是全日房下单
					if (this.unit == 'standard') {
						let room = {
							room_id: '',
							room_type_id: this.roomInfo.item.id,
							user_count: this.roomInfo.item.max_user_count,
							room_service_id: this.roomInfo.service.id
						}
						let roomList = []
						for (var i = 0; i < this.roomNum; i++) {
							roomList.push(room)
						}
						
						let goods_list = []
						
						this.cartList.forEach(item=>{
							let a = {
								goods_id:'',
								count:0
							}
							a.goods_id = item.id
							a.count = item.number
							
							goods_list.push(a)
						
						})

						let param = {
							shop_id: this.hotel.id,
							user_info: {
								link_man: this.name,
								link_phone: this.phone
							},
							goods_list:goods_list,
							enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
								` ${this.planTime}:00`,
								'YYYY/MM/DD HH:mm:ss').unix(),
							leave_time_plan: this.endDate,
							room_sale_type_id: this.roomInfo.item.room_sale_type_id,
							room_list: roomList,
							user_coupon_id: this.coupon_id,
							booking_type: 1,
							pay_type: 1,
							intermediariesId: "",
							share_user_id:this.shareId
						}

						this.$iBox
							.http('standardBookRoom', param)({
								method: 'post'
							})
							.then(res => {
								if (res.data) {
									if(res.data.bizCode=='0000'){
										// 随行付
										uni.requestPayment({
											provider: 'wxpay',
											AppId:res.data.payAppId,
											timeStamp: res.data.payTimeStamp,
											nonceStr: res.data.paynonceStr,
											package: res.data.payPackage,
											signType: res.data.paySignType,
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
										
										
									}else{
										// 微信支付
										uni.requestPayment({
											provider: 'wxpay',
											timeStamp: res.data.timeStamp,
											nonceStr: res.data.nonceStr,
											package: res.data.package,
											signType: 'MD5',
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
									}
									
								} else {
									uni.navigateTo({
										url: '/pages/resultsPage/resultsPage'
									})
								}

							})
					} else if (this.unit == 'hour') {
						let room = {
							room_id: '',
							room_type_id: this.roomInfo.item.id,
							user_count: this.roomInfo.item.max_user_count,
							room_service_id: this.roomInfo.service.id
						}
						let roomList = []
						for (var i = 0; i < this.roomNum; i++) {
							roomList.push(room)
						}
						let goods_list = []
						this.cartList.forEach(item=>{
							let a = {
								goods_id:'',
								count:0
							}
							a.goods_id = item.id
							a.count = item.number
							
							goods_list.push(a)
						
						})

						let param = {
							shop_id: this.hotel.id,
							user_info: {
								link_man: this.name,
								link_phone: this.phone
							},
							goods_list:goods_list,
							
							enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
								` ${this.planTime.split('-')[0].trim()}:00`,
								'YYYY/MM/DD HH:mm:ss').unix(),
							room_sale_type_id: this.roomInfo.item.room_sale_type_id,
							room_list: roomList,
							user_coupon_id: this.coupon_id,
							booking_type: 1,
							pay_type: 1,
							intermediariesId: "",
							share_user_id:this.shareId
						}
						this.$iBox
							.http('hourBookRoom', param)({
								method: 'post'
							})
							.then(res => {
								// 微信支付
								if (res.data) {
									if(res.data.bizCode=='0000'){
										// 随行付
										uni.requestPayment({
											provider: 'wxpay',
											AppId:res.data.payAppId,
											timeStamp: res.data.payTimeStamp,
											nonceStr: res.data.paynonceStr,
											package: res.data.payPackage,
											signType: res.data.paySignType,
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
										
										
									}else{
										// 微信支付
										uni.requestPayment({
											provider: 'wxpay',
											timeStamp: res.data.timeStamp,
											nonceStr: res.data.nonceStr,
											package: res.data.package,
											signType: 'MD5',
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
									}
								} else {
									uni.navigateTo({
										url: '/pages/resultsPage/resultsPage'
									})
								}

							})
					} else if (this.unit == 'conference_room') {
						let room = {
							room_id: '',
							room_type_id: this.roomInfo.item.id,
							user_count: this.roomInfo.item.max_user_count,
							room_service_id: this.roomInfo.service.id
						}
						let roomList = []
						for (var i = 0; i < this.roomNum; i++) {
							roomList.push(room)
						}
						let goods_list = []
						this.cartList.forEach(item=>{
							let a = {
								goods_id:'',
								count:0
							}
							a.goods_id = item.id
							a.count = item.number
							
							goods_list.push(a)
						
						})
						let param = {
							shop_id: this.hotel.id,
							user_info: {
								link_man: this.name,
								link_phone: this.phone
							},
							goods_list:goods_list,
							enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
								` ${this.planTime.split('-')[0].trim()}:00`,
								'YYYY/MM/DD HH:mm:ss').unix(),
							room_sale_type_id: this.roomInfo.item.room_sale_type_id,
							room_list: roomList,
							user_coupon_id: this.coupon_id,
							booking_type: 1,
							pay_type: 1,
							intermediariesId: "",
							share_user_id:this.shareId
						}
						this.$iBox
							.http('conferenceBookRoom', param)({
								method: 'post'
							})
							.then(res => {
								if (res.data) {
									if(res.data.bizCode=='0000'){
										// 随行付
										uni.requestPayment({
											provider: 'wxpay',
											AppId:res.data.payAppId,
											timeStamp: res.data.payTimeStamp,
											nonceStr: res.data.paynonceStr,
											package: res.data.payPackage,
											signType: res.data.paySignType,
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
										
										
									}else{
										// 微信支付
										uni.requestPayment({
											provider: 'wxpay',
											timeStamp: res.data.timeStamp,
											nonceStr: res.data.nonceStr,
											package: res.data.package,
											signType: 'MD5',
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
									}
								} else {
									uni.navigateTo({
										url: '/pages/resultsPage/resultsPage'
									})
								}

							})
					} else {

						let room = {
							room_id: '',
							room_type_id: this.roomInfo.item.id,
							user_count: this.roomInfo.item.max_user_count,
							room_service_id: this.roomInfo.service.id
						}
						let roomList = []
						for (var i = 0; i < this.roomNum; i++) {
							roomList.push(room)
						}
						let goods_list = []
						this.cartList.forEach(item=>{
							let a = {
								goods_id:'',
								count:0
							}
							a.goods_id = item.id
							a.count = item.number
							
							goods_list.push(a)
						
						})
						let param = {
							shop_id: this.hotel.id,
							user_info: {
								link_man: this.name,
								link_phone: this.phone
							},
							goods_list:goods_list,
							enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
								` ${this.planTime}:00`,
								'YYYY/MM/DD HH:mm:ss').unix(),
							room_sale_type_id: this.roomInfo.item.room_sale_type_id,
							months: this.monthNum,
							room_list: roomList,
							user_coupon_id: this.coupon_id,
							booking_type: 1,
							pay_type: 1,
							intermediariesId: "",
							share_user_id:this.shareId
						}
						this.$iBox
							.http('longStandardBookRoom', param)({
								method: 'post'
							})
							.then(res => {
								if (res.data) {
									if(res.data.bizCode=='0000'){
										// 随行付
										uni.requestPayment({
											provider: 'wxpay',
											AppId:res.data.payAppId,
											timeStamp: res.data.payTimeStamp,
											nonceStr: res.data.paynonceStr,
											package: res.data.payPackage,
											signType: res.data.paySignType,
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
										
										
									}else{
										// 微信支付
										uni.requestPayment({
											provider: 'wxpay',
											timeStamp: res.data.timeStamp,
											nonceStr: res.data.nonceStr,
											package: res.data.package,
											signType: 'MD5',
											paySign: res.data.paySign,
											success: (res) => {
												uni.navigateTo({
													url: '/pages/resultsPage/resultsPage'
												})
										
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
									}
								} else {
									uni.navigateTo({
										url: '/pages/resultsPage/resultsPage'
									})
								}

							})
					}
				} else {
					uni.hideLoading()
					this.pop = false
					this.pop4 = true
				}

			},

			payFor(e) {
				console.log(e);
				this.$iBox.throttle(() => {
					this.fnPay(e)
				}, 2000);
			},
			surePay() {
				this.$iBox.throttle1(() => {
					this.customPay()
				}, 2000);
			},
			customPay() {
				uni.showLoading({
					title: '等待支付...',
					mask: true
				})
				// 	// 判断是全日房下单
				let e = this.payType
				console.log(e, 'dd');
				if (this.unit == 'standard') {
					let room = {
						room_id: '',
						room_type_id: this.roomInfo.item.id,
						user_count: this.roomInfo.item.max_user_count,
						room_service_id: this.roomInfo.service.id
					}
					let roomList = []
					for (var i = 0; i < this.roomNum; i++) {
						roomList.push(room)
					}
					let goods_list = []
					this.cartList.forEach(item=>{
						let a = {
							goods_id:'',
							count:0
						}
						a.goods_id = item.id
						a.count = item.number
						
						goods_list.push(a)
					
					})

					let param = {
						shop_id: this.hotel.id,
						user_info: {
							link_man: this.name,
							link_phone: this.phone
						},
						goods_list:goods_list,
						enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
							` ${this.planTime}:00`,
							'YYYY/MM/DD HH:mm:ss').unix(),
						leave_time_plan: this.endDate,
						room_sale_type_id: this.roomInfo.item.room_sale_type_id,
						room_list: roomList,
						user_coupon_id: this.coupon_id,
						booking_type: e != 'daodian' ? 1 : 2,
						pay_type: e == 'tongyong' ? 2 : (e == 'duli' ? 3 : ''),
						intermediariesId: "",
						share_user_id:this.shareId
					}
					this.$iBox
						.http('standardBookRoom', param)({
							method: 'post'
						})
						.then(res => {
							// 如果有押金且押金设置显示则必定需要微信支付
							if (!this.ifOnline && res.data) {
								if(res.data.bizCode=='0000'){
									// 随行付
									uni.requestPayment({
										provider: 'wxpay',
										AppId:res.data.payAppId,
										timeStamp: res.data.payTimeStamp,
										nonceStr: res.data.paynonceStr,
										package: res.data.payPackage,
										signType: res.data.paySignType,
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
									
									
								}else{
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
								}
							} else {
								uni.hideLoading()
								uni.navigateTo({
									url: '/pages/resultsPage/resultsPage?payType=' + e
								})
							}

						})
				} else if (this.unit == 'hour') {
					let room = {
						room_id: '',
						room_type_id: this.roomInfo.item.id,
						user_count: this.roomInfo.item.max_user_count,
						room_service_id: this.roomInfo.service.id
					}
					let roomList = []
					for (var i = 0; i < this.roomNum; i++) {
						roomList.push(room)
					}
					let goods_list = []
					this.cartList.forEach(item=>{
						let a = {
							goods_id:'',
							count:0
						}
						a.goods_id = item.id
						a.count = item.number
						
						goods_list.push(a)
					
					})

					let param = {
						shop_id: this.hotel.id,
						user_info: {
							link_man: this.name,
							link_phone: this.phone
						},
						goods_list:goods_list,
						enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
							` ${this.planTime.split('-')[0].trim()}:00`,
							'YYYY/MM/DD HH:mm:ss').unix(),
						room_sale_type_id: this.roomInfo.item.room_sale_type_id,
						room_list: roomList,
						user_coupon_id: this.coupon_id,
						booking_type: e != 'daodian' ? 1 : 2,
						pay_type: e == 'tongyong' ? 2 : (e == 'duli' ? 3 : ''),
						intermediariesId: "",
						share_user_id:this.shareId
					}
					this.$iBox
						.http('hourBookRoom', param)({
							method: 'post'
						})
						.then(res => {
							// 如果有押金且押金设置显示则必定需要微信支付
							if (!this.ifOnline && this.cash_pledge > 0 && res.data) {
								if(res.data.bizCode=='0000'){
									// 随行付
									uni.requestPayment({
										provider: 'wxpay',
										AppId:res.data.payAppId,
										timeStamp: res.data.payTimeStamp,
										nonceStr: res.data.paynonceStr,
										package: res.data.payPackage,
										signType: res.data.paySignType,
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
									
									
								}else{
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
								}
							} else {
								uni.hideLoading()
								uni.navigateTo({
									url: '/pages/resultsPage/resultsPage?payType=' + e
								})
							}
						})
				} else if (this.unit == 'conference_room') {
					let room = {
						room_id: '',
						room_type_id: this.roomInfo.item.id,
						user_count: this.roomInfo.item.max_user_count,
						room_service_id: this.roomInfo.service.id
					}
					let roomList = []
					for (var i = 0; i < this.roomNum; i++) {
						roomList.push(room)
					}
					let goods_list = []
					this.cartList.forEach(item=>{
						let a = {
							goods_id:'',
							count:0
						}
						a.goods_id = item.id
						a.count = item.number
						
						goods_list.push(a)
					
					})

					let param = {
						shop_id: this.hotel.id,
						user_info: {
							link_man: this.name,
							link_phone: this.phone
						},
						goods_list:goods_list,
						enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
							` ${this.planTime.split('-')[0].trim()}:00`,
							'YYYY/MM/DD HH:mm:ss').unix(),
						room_sale_type_id: this.roomInfo.item.room_sale_type_id,
						room_list: roomList,
						user_coupon_id: this.coupon_id,
						booking_type: e != 'daodian' ? 1 : 2,
						pay_type: e == 'tongyong' ? 2 : (e == 'duli' ? 3 : ''),
						intermediariesId: "",
						share_user_id:this.shareId
					}
					this.$iBox
						.http('conferenceBookRoom', param)({
							method: 'post'
						})
						.then(res => {
							// 如果有押金且押金设置显示则必定需要微信支付
							if (!this.ifOnline && this.cash_pledge > 0 && res.data) {
								if(res.data.bizCode=='0000'){
									// 随行付
									uni.requestPayment({
										provider: 'wxpay',
										AppId:res.data.payAppId,
										timeStamp: res.data.payTimeStamp,
										nonceStr: res.data.paynonceStr,
										package: res.data.payPackage,
										signType: res.data.paySignType,
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
									
									
								}else{
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
								}
							} else {
								uni.hideLoading()
								uni.navigateTo({
									url: '/pages/resultsPage/resultsPage?payType=' + e
								})
							}
						})
				} else {

					let room = {
						room_id: '',
						room_type_id: this.roomInfo.item.id,
						user_count: this.roomInfo.item.max_user_count,
						room_service_id: this.roomInfo.service.id
					}
					let roomList = []
					for (var i = 0; i < this.roomNum; i++) {
						roomList.push(room)
					}
					let goods_list = []
					this.cartList.forEach(item=>{
						let a = {
							goods_id:'',
							count:0
						}
						a.goods_id = item.id
						a.count = item.number
						
						goods_list.push(a)
					
					})

					let param = {
						shop_id: this.hotel.id,
						user_info: {
							link_man: this.name,
							link_phone: this.phone
						},
						goods_list:goods_list,
						enter_time_plan: this.$moment(this.$moment(this.startDate * 1000).format('YYYY/MM/DD') +
							` ${this.planTime}:00`,
							'YYYY/MM/DD HH:mm:ss').unix(),
						room_sale_type_id: this.roomInfo.item.room_sale_type_id,
						months: this.monthNum,
						room_list: roomList,
						user_coupon_id: this.coupon_id,
						booking_type: e != 'daodian' ? 1 : 2,
						pay_type: e == 'tongyong' ? 2 : (e == 'duli' ? 3 : ''),
						intermediariesId: "",
						share_user_id:this.shareId
					}
					this.$iBox
						.http('longStandardBookRoom', param)({
							method: 'post'
						})
						.then(res => {
							// 如果有押金且押金设置显示则必定需要微信支付
							if (!this.ifOnline && this.cash_pledge > 0 && res.data) {
								if(res.data.bizCode=='0000'){
									// 随行付
									uni.requestPayment({
										provider: 'wxpay',
										AppId:res.data.payAppId,
										timeStamp: res.data.payTimeStamp,
										nonceStr: res.data.paynonceStr,
										package: res.data.payPackage,
										signType: res.data.paySignType,
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
									
									
								}else{
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.paySign,
										success: (res) => {
											uni.navigateTo({
												url: '/pages/resultsPage/resultsPage'
											})
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
								}
							} else {
								uni.hideLoading()
								uni.navigateTo({
									url: '/pages/resultsPage/resultsPage?payType=' + e
								})
							}
						})
				}
			}
		},
		onPageScroll(e) {
			let calc = 1 - (e.scrollTop) / this.imgHeight;
			this.opacity = calc
		},
	}
</script>

<style lang="scss" scoped>
	.mainPage {
		
	
	}
	.actions {
		margin-right: 20rpx;
		display: flex;
		// align-items: center;

		.add-btn,
		.minus-btn {
			width: 34rpx;
			height: 34rpx;
		}

		.number {
			width: 34rpx;
			height: 34rpx;
			margin: 0 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: $font-size-extra-lg;
		}

		.materials-box {
			position: relative;
			display: flex;

			.materials-btn {
				border-radius: 50rem !important;
			}

			.number-badge {
				z-index: 4;
				position: absolute;
				right: -16rpx;
				top: -14rpx;
				background-color: $bg-color-white;
				border-radius: 100%;
				width: 1.1rem;
				height: 1.1rem;
				display: flex;
				align-items: center;
				justify-content: center;

				.number {
					font-size: 20rpx;
					flex-shrink: 0;
					background-color: $color-primary;
					color: $bg-color-white;
					width: 0.9rem;
					height: 0.9rem;
					line-height: 0.9rem;
					text-align: center;
					border-radius: 100%;
				}
			}
		}
	}

	.pageBox {
		overflow: hidden;
		width: 100%;

		.InfoBox {
			padding: 10rpx 0 0 0;
			width: 686rpx;
			// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
			padding: 20rpx;
			height: 288rpx;
			padding: 32rpx;
			border-radius: 32rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			&_title {
				width: 100%;
				font-size: 32rpx;
				color: #000000E0;
				margin-top: 16rpx;
				display: flex;
				align-items: center;
			}

			&_content {
				margin-top: 14rpx;
				color: #000000E0;
				font-size: 28rpx;
			}
		}

		.manBox {
			// padding: 10rpx;
			width: 686rpx;
			// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
			margin: 20rpx auto;
			border-radius: 20rpx;
			padding: 0 30rpx;
			// background-color: #FFFFFF;
			.title {
				border-top-right-radius: 20rpx;
				border-top-left-radius: 20rpx;
				padding: 30rpx 0;
				// border-bottom: 1px solid #f1f4f9;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.room {
				padding: 30rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;
				// border-bottom: 1px solid #f1f4f9;
			}

			.nameBox {
				padding: 30rpx 0;
				display: flex;
				align-items: center;
				// border-bottom: 1px solid #f1f4f9;
				// justify-content: space-between;
			}

			.roomNameTips {
				height: 50rpx;
				width: 90%;
				padding: 14rpx;
				font-size: 24rpx;
				background: rgba(249, 159, 69, 0.2);
				color: rgba(249, 159, 69, 1);
			}
		}

		.cashBox {
			width: 688rpx;
			// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
			margin: 20rpx auto;
			border-radius: 20rpx;
			padding: 0rpx 30rpx;
			// background-color: #FFFFFF;
			.room {
				padding: 30rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;
				

			}
		}

		.priceBox {
			width: 720rpx;
			// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
			margin: 20rpx auto;
			border-radius: 20rpx;
			padding: 0 30rpx 20rpx 30rpx;
			// background-color: #FFFFFF;
			.title {
				border-top-right-radius: 20rpx;
				border-top-left-radius: 20rpx;
				padding: 30rpx 0;
				border-bottom: 1px solid #e4e7ed;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}

			.room {
				padding: 30rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 1px solid #e4e7ed;
			}

			.end {
				display: flex;
				flex-direction: column;
				align-items: flex-end;
				margin-top: 20rpx;
				margin-bottom: 30rpx;
				// color: brown;
			}
		}

		.submitBox {
			display: flex;
			height: 120rpx;
			position: fixed;
			bottom: 0px;
			width: 100%;
			// background: #FFFFFF;
			z-index: 3;
			padding-bottom: 20rpx;

			.price {
				display: flex;
				align-items: center;
				width: 50%;
				height: 100%;

				.t1 {
					display: flex;
					align-items: center;
					padding: 10rpx;
					width: 70%;
					height: 100%;
				}

				.t2 {
					display: flex;
					align-items: center;
					justify-content: center;
					padding-right: 10rpx;
					width: 50%;
					height: 100%;

					.arrow {
						animation-name: to_bottom_show;
						animation-duration: 0.1s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					.arrow_ac {
						animation-name: to_up_show;
						animation-duration: 0.1s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					/* 箭头动画 */

					@keyframes to_up_show {
						0% {
							transform: rotate(0);
						}

						50% {
							transform: rotate(90deg);
						}

						100% {
							transform: rotate(180deg);
						}
					}

					@keyframes to_bottom_show {
						0% {
							transform: rotate(180deg);
							animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
						}

						50% {
							transform: rotate(90deg);
							animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
						}

						100% {
							transform: rotate(0deg);
						}
					}

				}
			}

			.dateBox {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 50%;
				height: 100%;
				color: #FFFFFF;
				font-weight: 600;

				.t1 {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: space-around;
					width: 80%;
					height: 90%;
					border-radius: 50rpx;
					padding-top: 6rpx;
				}
			}
		}

		.ready_time {
			height: 800rpx;
			padding: 20rpx;
			margin: 0 auto;
			text-align: center;

			&_item {
				padding: 30rpx;
				border-bottom: 1px solid #e4e7ed;
			}
		}

		.detail_pop {
			display: flex;
			flex-direction: column;
			// height: 760rpx;
			padding: 30rpx;
			width: 750rpx;

			.pop3_title {
				margin: 0 auto;
				font-size: 40rpx;
			}

			.pop3_title1 {
				margin: 10rpx auto;
				font-size: 28rpx;
			}

			.room {
				box-sizing: border-box;
				width: 100%;
				padding: 10rpx 0;
				display: flex;
				align-items: center;
				justify-content: space-between;
				// border-bottom: 1px solid #e4e7ed;
				font-size: 28rpx;
			}

			.end {
				display: flex;
				flex-direction: column;
				align-items: flex-end;
				margin-top: 20rpx;
				margin-bottom: 30rpx;
			}
		}

		.customPay {
			height: 660rpx;
			width: 600rpx;
			// background: #FFFFFF;
			display: flex;
			flex-direction: column;
			align-items: center;
			border-radius: 20rpx;
			position: relative;

			.btn_pay {
				width: 100%;
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				position: fixed;
				bottom: 30rpx;

				.btn {
					width: 80%;
					height: 100%;
					border-radius: 60rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
		}

	}

	.roomDetail {
		display: flex;
		flex-direction: column;
		width: 100%;
		border-radius: 20rpx;

		&_title {
			padding: 20rpx;
			// text-align: center;
			font-size: 40rpx;
			font-weight: 600;
		}

		&_basic {
			width: 100%;
			padding: 0 30rpx;
			display: flex;
			flex-wrap: wrap;

			&_item {
				width: 50%;
				display: flex;
				justify-content: flex-start;
				line-height: 60rpx;
			}
		}

		&_facility {
			display: flex;
			flex-direction: column;
			padding: 30rpx;
			width: 100%;

			&_title {
				padding: 30rpx 0;
				font-size: 44rpx;
				font-weight: 500;
			}

			&_item {
				display: flex;
				flex-wrap: wrap;
				width: 100%;
			}
		}
	}
</style>