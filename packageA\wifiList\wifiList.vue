<template>
	<view style="position: relative;">
		<scroll-view scroll-y="true" style="height: 100vh;">
			<view class="" v-if="wifiList.length == 0" style="display: flex;flex-direction: column;;justify-content: center;align-items: center;;height: 300rpx;width: 100vw;">
				<text>暂无wifi列表，请检查手机是否打开WIFI开关!</text>
				<text style="color: #ff5500;font-size: 24rpx;width: 80vw;text-align: center;">(iphone用户因为IOS系统限制，每次查看wifi列表需要点击搜索进入系统获取)</text>
				<button type="primary"  style="margin-top: 60rpx;border-radius: 50rpx;" @click="search">重新搜索</button>
			</view>
			<view class="" v-for="item in wifiList" v-else style="display: flex;justify-content: space-between;align-items:center;height: 120rpx;" @click="conWifi(item)" hover-class="tab_hover">
				<text style="margin-left: 30rpx;">{{ item.ssid }}</text>
				<text style="color: chartreuse; margin-right:30rpx" v-if="tab == item.ssid" :style="tag == '连接成功' ? 'color:green' : 'color:red'">{{ tag }}</text>
				<text class="cuIcon-wifi" style="color: cornflowerblue;" v-else></text>
			</view>
		</scroll-view>

		<!-- 蒙版层 -->
		<view class="" v-if="if_ios" style="position: fixed;top: 0;right: 0;bottom: 0;left: 0;background: rgba(0,0,0,0.6);z-index:99"></view>
		<view
			class=""
			style="display: flex;flex-direction: column;align-items:center;justify-content: center;;position: fixed;top:0;bottom: 0;left:0;right: 0;margin:auto;height: 80vh;width: 100vw;z-index:999"
			v-if="if_ios"
		>
			<image src="https://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/wifi.jpg" mode="widthFix"></image>
			<button type="primary"  style="margin-top: 60rpx;border-radius: 50rpx;" @click="search">重新搜索</button>
		</view>
	</view>
</template>

<script>
import { mapState, mapActions } from 'vuex';
export default {
	data() {
		return {
			platform: '',
			if_ios: false,
			tab: '',
			tag: '',
			wifiList: [],
			default_wifi: [],
			shop_id:'',
			params:{
				shop_id:'',
				page:1,
				limit:100
			}
		};
	},
	computed: {
		...mapState('login', ['userInfo']),
		...mapState('ui', ['tabbar', 'themeColor']),
		...mapState('hotel', ['hotel', 'cityModel'])
	},
	async onLoad(options) {
		await this.$onLaunched;
		let that = this
			//检测手机型号
			wx.getSystemInfo({
				success: function(res) {
					var system = '';
					that.platform = res.platform;
					if (res.platform == 'android') system = parseInt(res.system.substr(8));
					if (res.platform == 'ios') system = parseInt(res.system.substr(4));
			
					if (res.platform == 'android' && system < 6) {
						uni.showModal({
							title:'提示',
							content:'安卓低于6，IOS低于11.2不支持一键WiFi功能',
							showCancel:false,
							success() {
								
							}
						})
						return;
					}
					if (res.platform == 'ios' && system < 11.2) {
						uni.showModal({
							title:'提示',
							content:'安卓低于6，IOS低于11.2不支持一键WiFi功能',
							showCancel:false,
							success() {
								
							}
						})
						return;
					}
					
					if (res.platform == 'ios' && system > 11.2) {
						// 如果判断是ios系统并且符合条件，则显示提示页
						that.if_ios = true;
					} else {
						uni.showLoading({
							title:'正在获取wifi',
							mask:true
						})
						that.if_ios = false;
						that.startWifi();
					}
					//2.初始化 Wi-Fi 模块
				}
			});
			
			uni.showLoading({
				title: '加载中...'
			})
			this.params.page = 1
			this.params.shop_id = this.hotel.id
			this.$iBox.http('getWifiList', this.params)({
				method: 'post'
			}).then(res => {
				this.default_wifi = res.data.list
				uni.hideLoading()
				console.log(this.diyModel);
			})
			
	},
	methods: {
		search() {
			let that = this;
			uni.showLoading({
				title: '搜索WIFI列表中...'
			});
			wx.getSystemInfo({
				success: function(res) {
					var system = '';
					if (res.platform == 'android') system = parseInt(res.system.substr(8));
					if (res.platform == 'ios') system = parseInt(res.system.substr(4));
					if (res.platform == 'android' && system < 6) {
						wx.showToast({
							title: '手机版本不支持',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					if (res.platform == 'ios' && system < 11.2) {
						wx.showToast({
							title: '手机版本不支持',
							icon: 'none'
						});
						uni.hideLoading();
						return;
					}
					that.platform = res.platform;
					//2.初始化 Wi-Fi 模块
					that.startWifi();
					uni.hideLoading();
				}
			});
		},
		conWifi(item) {
			let that = this;
			if (that.platform == 'ios') {
				uni.showLoading({
					title: '连接WIFI中...'
				});
			}
			wx.startWifi({
				success: function(res) {
					//请求成功连接Wifi

					that.Connected(item);
				},
				fail: function(res) {
					uni.hideLoading();
					wx.showToast({
						title: '接口调用失败',
						icon: 'none'
					});
				}
			});
		},

		//初始化 Wi-Fi 模块
		startWifi: function() {
			var that = this;
			wx.startWifi({
				success: function(res) {
					//请求成功连接Wifi
					that.getWifiList();
				},
				fail: function(res) {
					wx.showToast({
						title: '接口调用失败',
						icon: 'none'
					});
				}
			});
		},
		getWifiList: function() {
			let that = this;
			if (that.if_ios) {
				that.if_ios = false;
			}
			wx.getWifiList({
				success: function(res) {
					// 监听获取到 WiFi 列表数据事件
					wx.onGetWifiList(function(res) {
						that.wifiList = [];
						if (res && res.wifiList.length) {
							console.log(res.wifiList,'res');
							// 去重
							let wifi_list = res.wifiList
							console.log(wifi_list,'res1');
							for (let m = 0; m < wifi_list.length; m++) {
								console.log(wifi_list.length);
								for (let n = m+1; n < wifi_list.length; n++) {
									 if (wifi_list[m].SSID == wifi_list[n].SSID) {
									                wifi_list.splice(n, 1);
													console.log(wifi_list,'res2');
									                //因为数组长度减小1，所以直接 j++ 会漏掉一个元素，所以要 j--
									                n--;
									            }
								}
							}
							
							for (let i = 0; i < that.default_wifi.length; i++) {
								for (let j = 0; j < wifi_list.length; j++) {
									if (wifi_list[j].SSID == that.default_wifi[i].ssid ) {
										that.wifiList.push(that.default_wifi[i]);
									}
								}
							}
							
							uni.hideLoading()
							// console.log(that.wifiList, 'wifi');
							// 初始页面判断连接成功，获取wifi名称，调整UI显示
							wx.getConnectedWifi({
								success: function(res) {
									that.tab = res.wifi.SSID;
									that.tag = '连接成功';
								}
							});
						} else {
							uni.showToast({
								title: '暂未发现可用的WIFI!',
								icon: 'none'
							});
							if (that.platform == 'ios') {
								wx.setWifiList({
									wifiList: []
								});
							} else {
								that.wifiList = [];
							}
						}
					});
				},
				fail: function(res) {
					wx.showToast({
						title: '获取 Wi-Fi 列表数据失败,请检查WIFI开关是否打开!',
						icon: 'none',
						duration: 3000
					});
				}
			});
		},

		Connected: function(wifi1) {
			var that = this;
			let wifi = wifi1;
			// console.log(wifi1, 'll');
			if (that.platform == 'ios') {
				wx.getConnectedWifi({
					success: function(res) {
						// 判断上次连接是否与这次相同，相同则隐藏loding，不同则向下执行执行连接
						if (res.wifi.SSID == wifi.ssid) {
							that.tab = res.wifi.SSID;
							that.tag = '连接成功';
							uni.hideLoading();
						}
					}
				});
			}
			// console.log(wifi1, 'llss');
			wx.connectWifi({
				SSID: wifi.ssid, //wifi名
				password: wifi.password, //wifi密码
				success: function(res) {
					// console.log(wifi1, 'yyyy');
					if (that.platform == 'ios') {
						// 是否是IOS可通过提前调用getSystemInfo知道
						// console.log(res, 'llooo');
						wx.getConnectedWifi({
							success: function(res) {
								// console.log(res);
								// 获取连接信息
								if (res.wifi.SSID == wifi.ssid) {
									that.tab = res.wifi.SSID;
									that.tag = '连接成功';
									wx.showToast({
										title: '连接成功！',
										icon: 'none',
										duration: 1500
									});
									uni.hideLoading();
								} else {
									// 连接失败
									that.tab = wifi.ssid;
									that.tag = '连接失败';

									setTimeout(res => {
										wx.getConnectedWifi({
											success: function(res) {
												// 获取连接信息
												that.tab = res.wifi.SSID;
												that.tag = '连接成功';
												wx.showToast({
													title: 'wifi连接失败！已为您自动切换到可用WIFI',
													icon: 'none',
													duration: 1500
												});
												uni.hideLoading();
											}
										});
									}, 1000);
									uni.hideLoading();
								}
							},
							fail(rs) {
								uni.hideLoading();
								that.tab = wifi.ssid;
								that.tag = '连接失败';
								wx.showToast({
									title: 'wifi连接失败！请重试',
									icon: 'none',
									duration: 1500
								});
								// console.log(rs);
							}
						});
					} else {
						// 安卓连接成功
						that.tab = wifi.ssid;
						that.tag = '连接成功';
						wx.showToast({
							title: 'wifi连接成功'
						});
					}
				},
				fail: function(res) {
					// console.log(res, 'llnn');
					if (that.platform != 'ios') {
						// console.log(res, 'llnnn');
						that.tab = wifi1.ssid;
						that.tag = '连接失败';
						setTimeout(res => {
							wx.getConnectedWifi({
								success: function(res) {
									// 获取连接信息
									that.tab = res.wifi.SSID;
									that.tag = '连接成功';
									wx.showToast({
										title: 'wifi连接失败！已为您自动切换到可用WIFI',
										icon: 'none',
										duration: 1500
									});
									uni.hideLoading();
								}
							});
						}, 1000);
						uni.hideLoading();
					} else {
						// console.log('k');
						wx.showToast({
							title: '已取消',
							icon: 'none'
						});
						uni.hideLoading();
					}
				}
			});
		}
	},
	// // 上拉加载
	onReachBottom() {
	
		if (this.bool) {
			++this.params.page
			uni.showLoading({
				title: '加载中...'
			})
			this.$iBox.http('getWifiList', this.params)({
				method: 'post'
			}).then(res => {
				let new_list = this.default_wifi.concat(res.data.list)
				this.default_wifi = new_list
				if (this.default_wifi.length == res.data.count) {
					this.bool = false
				}
				uni.hideLoading()
			}).catch(function(error) {
				console.log('网络错误', error)
			})
		}
	
	}
};
</script>

<style>
.tab_hover {
	opacity: 0.9;
	background: #f7f7f7;
}
</style>
