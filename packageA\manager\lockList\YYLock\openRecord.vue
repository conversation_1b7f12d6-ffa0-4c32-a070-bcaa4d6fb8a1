<template>
	<view>
		<view class="" style="height: 100rpx;position: fixed;top: 0;width: 100%;background-color: #fff;display: flex;align-items: center;padding: 0 20rpx;">
			<text>筛选时间:</text>
			<uni-datetime-picker :hide-second="true" v-model="datetimerange" type="datetimerange"
				rangeSeparator="至" />
		</view>
		<view class="" style="height: 100rpx;">
			
		</view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="logList.length==0">
			<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无记录</p>
		</view>
		<view v-for="(item,index) in logList" :key="item in recordId" class="box" v-if="logList.length>0">
			<p>第{{index + 1}}条记录</p>
			<p>锁名称:{{item.lock_name}}</p>
			<p>人员名称:{{item.user_name}}</p>
			<p>开锁时间:{{item.open_time | moment1}}</p>
		</view>
		
	</view>
</template>

<script>
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				logList:[],
				params:{
					lock_id:'',
					page:1,
					limit:10,
					start_time:'',
					end_time:''
				},
				bool:true,
				datetimerange: [],
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		onLoad() {
			uni.showLoading({
				title:'loading...'
			})
			this.params.lock_id = this.lockDetail.id
			this.params.page = 1
			this.getRecord()
		},
		watch:{
			datetimerange(){
				this.params.page = 1
				this.params.start_time = this.$moment(this.datetimerange[0]).format('x')/1000
				this.params.end_time = this.$moment(this.datetimerange[1]).format('x')/1000
				console.log(this.params.start_time,this.params.end_time,'dsd');
				this.getRecord()
			}
		},
		methods: {
			getRecord(){
				this.$iBox.http('getYaYaLockOpenRecord', this.params)({
					method: 'post'
				}).then(res => {
					this.logList = res.data.list
					uni.hideLoading()
				})
			},
			formatPass(e){
				return e.substring(0, 2) + '**' + e.substring(4,6)
			}
		},
		onReachBottom() {
			if (this.bool) {
				++this.params.page
				this.$iBox.http('getYaYaLockOpenRecord', this.params)({
					method: 'post'
				}).then(res => {
					console.log('我是返回', res.data)
					let new_list = this.logList.concat(res.data.list)
					this.logList = new_list
					if (this.logList.length == res.data.total) {
						this.bool = false
					}
		
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		
		}
	}
</script>

<style lang="scss" scoped>
	.box{
		width: 100vw;
		min-height: 200rpx;
		border-bottom: 1px solid #CCCCCC;
		display: flex;
		flex-direction: column;
		
		padding: 30rpx;
		background: #fff;
	}
</style>
