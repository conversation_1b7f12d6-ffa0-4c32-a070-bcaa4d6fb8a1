<template>
	<view>
		<uni-transition :mode-class="['slide-bottom']" :show="!!cartNum||source=='form'" :styles="cartBarStyles">
			<view class="left"  @tap="details">
				<view class="detail-action">
					<image src="/static/images/index/icon_shopping_bag.png" class="shopbag-btn"></image>
					<view class="badge" :style="'background:'+themeColor.main_color">{{ cartNum }}</view>
				</view>
				<view class="" >
					<view class="price" :style="'color:'+themeColor.main_color">
						<text>￥{{ cartPrice.toFixed(2) }}</text>
						<view class="" style="display: flex;align-items: center;color: #00000066;font-size: 24rpx;margin-left: 20rpx;">
							<text style="text-decoration: line-through;">￥{{memPrice}}</text>
							<view :class="!ifOpen?'open':'acOpen'">
								<uni-icons type="up" size="20" color="#00000066"></uni-icons>
							</view>
						</view>
					</view>
					<p style="font-size: 24rpx;" v-if="haveMem" :style="'color:'+themeColor.main_color">已享会员权益/优惠券抵扣</p>
				</view>
				
			</view>
			<view class="right" :style="{'background-image': 'linear-gradient(-90deg,'+themeColor.main_color+','+themeColor.com_color1+')'}" @tap="pay">下一步</view>
		</uni-transition>
		<cart-popup :cart="cart" ref="cartPopup" @add="add" @minus="minus" @change="changePop" @clear="clear"></cart-popup>
	</view>
</template>

<script>
	import cartPopup from '../cart-popup/cart-popup.vue'
	import {
		mapState,
		mapMutations
	} from 'vuex'
	export default {
		name: 'CartBar',
		components: {
			cartPopup
		},
		props: {
			cart: {
				type: Array,
				default: () => []
			},
			source: {
				type: String,
				default:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel']),
			cartNum() { //计算购物车总数
				return this.cart.reduce((acc, cur) => acc + cur.number, 0)
			},
			cartPrice() { //计算购物车总价
				let prices = 0
				this.cart.forEach(item => {
					if(!item.ifMem){
						prices += item.number * item.price
					}
				})
				
				return prices
				// return this.cart.reduce((acc, cur) => acc + cur.number * cur.price, 0)
			}
		},
		data() {
			return {
				cartBarStyles: {
					'position': 'fixed',
					'bottom': 0,
					// #ifdef H5
					'bottom': 'var(--window-bottom)',
					// #endif
					'width': '100%',
					'z-index': '995',
					'height': '140rpx',
					'background-color': '#f0f0f1',
					'border-bottom': '2rpx solid #c8c7cc',
					'display': 'flex',
					'justify-content': 'space-between',
					'align-items': 'center',
					'padding':'10rpx'
				},
				haveMem:false,
				memPrice:0,
				ifOpen:false
			}
		},
		methods: {
			changePop(e){
				console.log(e,'jj');
				if(e){
					this.ifOpen = true
					this.$refs['cartPopup'].open()
				}else{
					this.ifOpen = false
					this.$refs['cartPopup'].close()
				}
			},
			details() {
				if(this.ifOpen){
					this.ifOpen = false
					this.$refs['cartPopup'].close()
				}else{
					this.ifOpen = true
					this.$refs['cartPopup'].open()
				}
				
				
			},
			add(product) {
				// 购物车的物品，先判断是否点击的服务商品，是的话，ifMem为false的那个商品加
				
				this.$emit('add', {
					...product,
					number: product.number++
				})
			},
			minus(product) {
				this.$emit('minus', product)
			},
			clear() {
				this.$emit('clear')
			},
			pay() {
				this.$emit('pay')
			}
			
		},
		watch: {
			cartNum(val) {
				if (!val) {
					this.$refs['cartPopup'].close()
				}
			},
			cart:{
				handler(newVal, oldVal){
					console.log(newVal,'newVal');
					// 判断购物车的变动，是否免费服务商品大于可用服务数量,有的话将列表的服务全部改成不免费
					let prices = 0
					newVal.forEach(item2 => {
						//判断购物车中是否有数量大于2的同一件服务商品，大于2只算一件
						if (item2.ifMem) {
							this.haveMem =true
							 prices += item2.price
						}
					})
					this.memPrice = prices
				},
				immediate:true,
				deep:true
			}
		}
	};
</script>

<style lang="scss" scoped>
	
	.left {
		display: flex;
		align-items: center;

		.detail-action {
			height: 100%;
			border-radius: 100%;
			// background-color: #ffffff;
			// box-shadow: 0 10rpx 10rpx 0 rgba($color: $border-color, $alpha: 0.2);
			position: relative;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100rpx;
			height: 100rpx;
			margin-top: -10rpx;
			margin-right: 20rpx;
			margin-left: 20rpx;

			.shopbag-btn {
				width: 70rpx;
				height: 70rpx;
			}

			.badge {
				background-color: $color-primary;
				font-size: $font-size-sm;
				color: $bg-color-white;
				line-height: 1rem;
				width: 1rem;
				text-align: center;
				border-radius: 100%;
				position: absolute;
				right: 0;
				top: 0;
			}
		}

		.price {
			font-size: $font-size-extra-lg;
			font-weight: bold;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
	}

	.right {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 0 70rpx;
		font-size: $font-size-extra-lg;
		height: 88rpx;
		width: 320rpx;
		border-radius: 44rpx !important;
		width: fit-content;
		color: #ffffff;
	}
	
	.acOpen {
		transform: rotate(0deg);
		// animation: spin .5s linear;
	}
	
	.open {
		transform: rotate(-180deg);
	}
	
	@keyframes spin {
	  0% { transform: rotate(0deg); }
	  100% { transform: rotate(180deg); }
	}
</style>
