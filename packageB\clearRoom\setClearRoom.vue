<template>
	<view>
		<m-tabs :list="list" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="tab_click"
			:activeIndex="current" :config="{color:themeColor.main_color,
						  fontSize:30,
						  activeColor:themeColor.main_color,
						  underLineColor:themeColor.main_color,
						  underLineWidth:80,
						  underLineHeight:10}">
		</m-tabs>
		<view class="" style="display: flex;flex-direction: column;align-items: center;" v-if="index==0">
			<view class="" v-for="item in roomList"
				style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;padding: 18rpx 32rpx;display: flex;flex-direction: column;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" v-if="item.remark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;display: flex;align-items: center;">
					<view class=""
						style="padding:0 10rpx;position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;border-top-right-radius: 20rpx;border-top-left-radius: 16rpx;border-bottom-right-radius:20rpx;align-items: center;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">前台</text>
					</view>
					<text>{{item.remark}}</text>
				</view>
				<view class="" v-if="item.user_emark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;align-items: center;display: flex;">
					<view class=""
						style="position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;padding:0 10rpx;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">房扫</text>
					</view>
					<text>{{item.user_remark}}</text>
				</view>
				<view class="" style="width: 100%;display: flex;justify-content: flex-end;margin-top: 20rpx;">
					<view class="" @click="showAdmin(item)"
						style="height: 60rpx;width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
						:style="{background:'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, ' +themeColor.bg_main1_color +' 100%)'}">
						<text style="color: #FFFFFF;font-size: 28rpx;">点击分配</text>
					</view>
				</view>
			</view>
		</view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;" v-if="index==1">
			<view class="" v-for="item in roomList"
				style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;padding: 18rpx 32rpx;display: flex;flex-direction: column;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" v-if="item.remark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;display: flex;align-items: center;">
					<view class=""
						style="padding:0 10rpx;position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;border-top-right-radius: 20rpx;border-top-left-radius: 16rpx;border-bottom-right-radius:20rpx;align-items: center;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">前台</text>
					</view>
					<text>{{item.remark}}</text>
				</view>
				<view class="" v-if="item.user_emark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;align-items: center;display: flex;">
					<view class=""
						style="position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;padding:0 10rpx;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">房扫</text>
					</view>
					<text>{{item.user_remark}}</text>
				</view>
				<view class="" style="width: 100%;display: flex;justify-content: flex-end;margin-top: 20rpx;">
					<view class="" @click="changeAdmin(item)"
						style="height: 60rpx;width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
						:style="{background:'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, ' +themeColor.bg_main1_color +' 100%)'}">
						<text style="color: #FFFFFF;font-size: 28rpx;">重新编辑</text>
					</view>
				</view>
			</view>
		</view>

		<view class="" style="display: flex;flex-direction: column;align-items: center;" v-if="index==2">
			<view class="" v-for="item in roomList"
				style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;padding: 18rpx 32rpx;display: flex;flex-direction: column;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" v-if="item.remark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;display: flex;align-items: center;">
					<view class=""
						style="padding:0 10rpx;position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;border-top-right-radius: 20rpx;border-top-left-radius: 16rpx;border-bottom-right-radius:20rpx;align-items: center;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">前台</text>
					</view>
					<text>{{item.remark}}</text>
				</view>
				<view class="" v-if="item.user_emark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;align-items: center;display: flex;">
					<view class=""
						style="position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;padding:0 10rpx;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">房扫</text>
					</view>
					<text>{{item.user_remark}}</text>
				</view>
				<view class=""
					style="width: 100%;display: flex;justify-content: space-between;margin-top: 20rpx;align-items: center;">
					<view class=""
						style="font-size: 24rpx;color: #00000066;border: 1px solid #EBEBEB;padding: 10rpx;display: flex;align-items: center;justify-content: center;">
						已打扫{{timeArr(item.start_time)}}分钟
					</view>
					<view class="" @click="changeAdmin(item)"
						style="height: 60rpx;width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
						:style="{background:'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, ' +themeColor.bg_main1_color +' 100%)'}">
						<text style="color: #FFFFFF;font-size: 28rpx;">重新编辑</text>
					</view>
				</view>
			</view>
		</view>

		<view class="" style="display: flex;flex-direction: column;align-items: center;" v-if="index==3">
			<view class="" v-for="item in roomList"
				style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;padding: 18rpx 32rpx;display: flex;flex-direction: column;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" v-if="item.remark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;display: flex;align-items: center;">
					<view class=""
						style="padding:0 10rpx;position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;border-top-right-radius: 20rpx;border-top-left-radius: 16rpx;border-bottom-right-radius:20rpx;align-items: center;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">前台</text>
					</view>
					<text>{{item.remark}}</text>
				</view>
				<view class="" v-if="item.user_emark"
					style="background: #f5f5f5;width: 686rpx;height: 118rpx;border-radius: 16rpx;padding: 20rpx;position: relative;margin-top: 20rpx;align-items: center;display: flex;">
					<view class=""
						style="position: absolute;top: 0;left: 0;display: flex;align-items: center;justify-content: center;padding:0 10rpx;"
						:style="{background:themeColor.icon_color}">
						<text style="font-size: 24rpx;color: #FFFFFF;">房扫</text>
					</view>
					<text>{{item.user_remark}}</text>
				</view>
				<view class=""
					style="width: 100%;display: flex;justify-content: space-between;margin-top: 20rpx;align-items: center;">
					<view class=""
						style="font-size: 24rpx;color: #00000066;border: 1px solid #EBEBEB;padding: 10rpx;display: flex;align-items: center;justify-content: center;">
						已完成
					</view>
					<view class="" @click="changeAdmin(item)"
						style="height: 60rpx;width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
						:style="{background:'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, ' +themeColor.bg_main1_color +' 100%)'}">
						<text style="color: #FFFFFF;font-size: 28rpx;">重新编辑</text>
					</view>
				</view>
			</view>
		</view>
		<view class="" style="height: 176rpx;width: 100%;background: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #00000050 100%);position: fixed;bottom: 0;display: flex;justify-content: center;
">
			<view class=""
				style="background-color: #FFFFFF;width: 100rpx;height: 100rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;font-size: 28rpx;"
				@click="toSearch">
				搜索
			</view>

		</view>
		<!-- 未分配 -->
		<m-popup :show="pop" @closePop="closePop" mode="bottom">
			<view class="" style="height: 80vh;border-radius: 32rpx;padding: 36rpx;">
				<p style="margin-bottom: 10rpx;">选择房扫人员</p>
				<uni-data-select v-model="value" :localdata="range" @change="change"></uni-data-select>
				<view class="" style="margin-top: 60rpx;">
					<p style="margin-bottom: 10rpx;margin-bottom: 10rpx;">选择是否紧急</p>
					<radio-group @change="radioChange" style="display: flex;">
						<label class="" v-for="(item, index) in items" :key="item.value"
							style="margin-right: 200rpx;display: flex;align-items: center;width: fit-content;">
							<view>
								<radio :value="item.value" :checked="index === current" />
							</view>
							<view style="margin-left: 20rpx;width: 100rpx;">{{item.name}}</view>
						</label>
					</radio-group>
				</view>
				<view class="" style="margin-top: 60rpx;">
					<p style="margin-bottom: 10rpx;margin-bottom: 10rpx;">备注:</p>
					<uni-easyinput type="textarea" v-model="remark" placeholder="请输入备注说明"></uni-easyinput>
				</view>
				<view class=""
					style="position: absolute;bottom: 80rpx;left: 0;right: 0;margin: 0 auto;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="sure"
						style="display: flex;align-items: center;justify-content: center;border-radius: 48rpx;width: 500rpx;height: 80rpx;padding: 20rpx 0;color: #FFFFFF;"
						:style="{background:themeColor.main_color}">
						确定分配
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 已分配 -->
		<m-popup :show="pop1" @closePop="closePop1" mode="bottom">
			<view class="" style="height: 80vh;border-radius: 32rpx;padding: 36rpx;">
				<p style="margin-bottom: 10rpx;">选择房扫人员</p>
				<uni-data-select v-model="value1" :localdata="range" @change="change"></uni-data-select>
				<view class="" style="margin-top: 60rpx;">
					<p style="margin-bottom: 10rpx;margin-bottom: 10rpx;">选择是否紧急</p>
					<radio-group @change="radioChange1" style="display: flex;">
						<label class="" v-for="(item, index) in items" :key="item.value"
							style="margin-right: 200rpx;display: flex;align-items: center;width: fit-content;">
							<view>
								<radio :value="item.value" :checked="index === current1" />
							</view>
							<view style="margin-left: 20rpx;width: 100rpx;">{{item.name}}</view>
						</label>
					</radio-group>
				</view>
				<view class="" style="margin-top: 60rpx;">
					<p style="margin-bottom: 10rpx;margin-bottom: 10rpx;">改变状态</p>
					<radio-group @change="radioChangeType" style="display: flex;flex-wrap: wrap;">
						<label class="" v-for="(item, index) in items1" :key="item.value"
							style="margin-right: 40rpx;display: flex;align-items: center;width: fit-content;margin-top: 20rpx;">
							<view>
								<radio :value="item.value" :checked="index === currentType" />
							</view>
							<view style="margin-left: 20rpx;width: 100rpx;">{{item.name}}</view>
						</label>
					</radio-group>
				</view>
				<view class="" style="margin-top: 60rpx;">
					<p style="margin-bottom: 10rpx;margin-bottom: 10rpx;">备注:</p>
					<uni-easyinput type="textarea" v-model="remark1" placeholder="请输入备注说明"></uni-easyinput>
				</view>
				<view class=""
					style="position: absolute;bottom: 80rpx;left: 0;right: 0;margin: 0 auto;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="sure1"
						style="display: flex;align-items: center;justify-content: center;border-radius: 48rpx;width: 500rpx;height: 80rpx;padding: 20rpx 0;color: #FFFFFF;"
						:style="{background:themeColor.main_color}">
						确定分配
					</view>
				</view>
			</view>
		</m-popup>

		<m-popup :show="popSearch" @closePop="closePopSearch" mode="bottom">
			<view class="" style="height: 50vh;padding: 30rpx;position: relative;">
				<p style="margin-bottom: 30rpx;">搜索</p>
				<uni-easyinput prefixIcon="search" v-model="searchValue" placeholder="搜索房间号" @iconClick="iconClick">
				</uni-easyinput>
				<view class="" style="margin-top: 60rpx;" v-if="index!=0">
					<p style="margin-bottom: 30rpx;">分配时间</p>
					<uni-datetime-picker v-model="timeRange" type="daterange"
						@maskClick="maskClick"></uni-datetime-picker>
				</view>
				<view class=""
					style="position: absolute;bottom: 60rpx;display: flex;align-items: center;justify-content: center;box-sizing: border-box;width: 680rpx;">
					
					<view class="" @click="toSearchData"
						style="display: flex;align-items: center;justify-content: center;border-radius: 48rpx;width: 600rpx;height: 80rpx;padding: 20rpx 0;color: #FFFFFF;"
						:style="{background:themeColor.main_color}">
						确定
					</view>
				</view>
			</view>
		</m-popup>


		<view class="" style="height: 140rpx;">

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				list: [{
					name: '未分配',
					status: 1
				}, {
					name: '已分配',
					status: 2
				}, {
					name: '打扫中',
					status: 3
				}, {
					name: '已完成',
					status: 4
				}],
				bool: true,
				params: {
					page: 1,
					limit: 10,
					status: 1
				},
				roomList: [],
				index: 0,
				pop: false,
				range: [],
				value: '',
				room: null,
				items: [{
						value: '1',
						name: '普通',
					},
					{
						value: '2',
						name: '紧急'
					}
				],
				items1: [{
						value: '1',
						name: '未分配',
					},
					{
						value: '2',
						name: '已分配'
					},
					{
						value: '3',
						name: '打扫中'
					},
					{
						value: '4',
						name: '已完成'
					}
				],
				current: 0,
				remark: '',
				pop1: false,
				value1: '',
				current1: '',
				remark1: '',
				currentType: '',
				popSearch: false,
				searchValue: '',
				timeRange: ''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		watch: {
			timeRange(newval) {
				console.log('范围选:', this.timeRange);
			}
		},
		onLoad() {
			this.bool = true
			this.params.status = 1
			this.params.page = 1
			this.getList()

			this.$iBox.http('getRoomCleanAdmin', {})({
				method: 'post'
			}).then(res => {
				let a = []
				res.data.forEach(item => {
					let b = {
						text: item.name + '(' + item.nickname + ')',
						value: item.id
					}
					a.push(b)
				})
				this.range = a
			}).catch(function(error) {
				console.log('网络错误', error)
			})

		},
		methods: {
			tab_click(e) {
				console.log(e);
				this.index = e
				this.params.status = e + 1
				this.params.page = 1
				this.bool = true
				this.getList()
			},
			change(e) {
				console.log("e:", e, this.value);
			},
			radioChange: function(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			toSearchData() {
				this.params.room_number = this.searchValue
				this.params.page = 1
				
				if(this.timeRange.length>0){
					this.params.set_start_time = this.$moment(this.timeRange[0],'YYYY-MM-DD HH:mm:ss').unix() 
					this.params.set_end_time = this.$moment(this.timeRange[1],'YYYY-MM-DD HH:mm:ss').unix() 
				}
				
				this.getList()
				this.popSearch = false
			},
			maskClick(e) {
				console.log('maskClick事件:', e);

			},
			timeArr(e) {
				let a = e

				let b = this.$moment().unix()

				let c = ((b - a) / (60)).toFixed(0)
				console.log(b - a);
				return c
			},
			radioChange1: function(evt) {
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current1 = i;
						break;
					}
				}
				console.log(this.current1);
			},
			radioChangeType: function(evt) {
				for (let i = 0; i < this.items1.length; i++) {
					if (this.items1[i].value === evt.detail.value) {
						this.currentType = i;
						break;
					}
				}
			},
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			toSearch() {
				this.popSearch = true
			},
			closePopSearch() {
				this.popSearch = false
			},
			changeAdmin(e) {
				console.log(this.value, e, this.current);
				this.pop1 = true
				this.value1 = e.admin_id
				this.current1 = e.level - 1
				this.remark1 = e.remark
				this.currentType = e.status - 1
				this.room = e
			},
			sure() {

				if (!this.value) {
					uni.showToast({
						icon: 'none',
						title: '请选择房扫人员!'
					})
					return
				}

				let params = {
					id: this.room.id,
					admin_id: this.value,
					level: this.current,
					remark: this.remark
				}

				this.$iBox.http('setUserCleanRoom', params)({
					method: 'post'
				}).then(res => {
					this.pop = false
					this.index = 0
					this.params.status = 1
					this.params.page = 1
					this.bool = true
					this.getList()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			sure1() {

				if (!this.value1) {
					uni.showToast({
						icon: 'none',
						title: '请选择房扫人员!'
					})
					return
				}

				let params = {
					id: this.room.id,
					admin_id: this.value1,
					level: this.current1 + 1,
					remark: this.remark1,
					status: this.currentType + 1
				}

				this.$iBox.http('updateCleanRoomStatus', params)({
					method: 'post'
				}).then(res => {
					this.pop1 = false
					if (this.index == 1) {
						this.params.status = 2
					} else if (this.index == 2) {
						this.params.status = 3
					} else if (this.index == 3) {
						this.params.status = 4
					}

					this.params.page = 1
					this.bool = true

					this.getList()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			getList() {
				this.$iBox.http('getRoomCleanList', this.params)({
					method: 'post'
				}).then(res => {
					this.roomList = res.data.list
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			showAdmin(e) {
				this.pop = true
				this.room = e
			}

		},
		// // 上拉加载
		onReachBottom() {

			if (this.bool) {
				++this.params.page
				this.params.status = this.index + 1
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getRoomCleanList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = thisroomListconcat(res.data.list)
					this.roomList = new_list
					if (this.roomList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #f5f5f5;
	}
</style>
<style scoped lang="scss">

</style>