<template>
	<view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="breakList.length==0">
			<view class="icon-quesheng<PERSON>_zan<PERSON><PERSON>" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无报修记录</p>
		</view>
		<view class="breakBox" v-for="item in breakList" v-else @click="showQr(item)">
			<view class="titleBox">
				<p class="title">{{item.content}}</p>
			</view>
			<view class="" style="width: 100%;display: flex;flex-wrap: wrap;">
				<image :src="item1" mode="" v-for="item1 in item.images" style="width: 120rpx;height: 120rpx;margin: 10rpx;"></image>
			</view>
			<p class="content" style="color:darkgray">报修日期:{{item.create_time | moment}}</p>
		</view>

		
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				params:{
					page:1,
					limit:10
				},
				breakList:[],
				bool:true,
				pop:false,
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options,'billid');
			this.params.page = 1
			this.getBreakList()
			
		},
		methods:{
			getBreakList(){
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getRepairsList', this.params)({
					method: 'post'
				}).then(res => {
					this.breakList = res.data.list
				})
			},
			closePop(){
				this.pop = false
			}
		},
		// // 上拉加载
		onReachBottom() {
		
			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getRepairsList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.breakList.concat(res.data.list)
					this.breakList = new_list
					if (this.breakList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		
		}
	}
</script>

<style lang="scss" scoped>
	.breakBox {
		width: 700rpx;
		margin: 30rpx auto;
		min-height: 200rpx;
		border-radius: 20rpx; 
		background-color: #fff;
		padding: 30rpx;
		position: relative;
		.titleBox {
			display: flex;
			height: 80rpx;
			width: 100%;
			align-items: center;
			justify-content: space-between;
			.title {
				font-size: 40rpx;
				font-weight: 700;
			}
		}
		
		
		
		.content {
			font-size: 30rpx;
			color: #454545;
		}
		
		.qrBox {
			display: flex;
			flex-direction: column;
			align-items: center;
		}
	}
</style>
