<template>
	<view class="mainContent"
		:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.main_color+'A6,'+themeColor.com_color2+'A6)'}">
		<view class="box">
			<view class="title">
				<text style="font-size: 40rpx;">{{manager.shop_name}}</text>
				<view class="">
					<text style="font-size: 26rpx;">总间数:{{total_count}}间</text>
					<text style="padding-left: 30rpx;font-size: 26rpx;">入住中:{{staying_count}}间</text>
					<text style="padding-left: 30rpx;font-size: 26rpx;"
						:style="Number(stay_rate)<50?'color:red':'color:green'">入住率:{{stay_rate}}%</text>
				</view>

			</view>
			<view class="chooseBox">
				<text>房型筛选：</text>
				<view class="pickerBox" @click="pickRoomType" hover-class="getFocs" hover-stay-time="1000">
					<text>{{changeName}}</text>
					<view class="icon-down" :class="pop?'arrow_ac':'arrow'"
						style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
					</view>
				</view>
			</view>
			<view class="chooseBox">
				<text>房间号搜索：</text>
				<view class="" style="position: relative;">
					<input type="text" @input="roomNumSearch" v-model="roomNumber"
						style="border: 1px solid #eee;padding:0 10rpx;width: 280rpx;border-radius: 16rpx;height: 60rpx;margin-top: 20rpx;"
						placeholder="房间号">
					</input>
					<view class="icon-search"
						style="position: absolute;right: 15rpx;top:35rpx;bottom: 0;margin: auto 0;font-size: 32rpx;">
					</view>
				</view>


			</view>

			<checkbox-group @change="checkboxChange">
				<view class="roomStatusList" :style="{background:themeColor.com_color1+'40'}">
					<view class="item" v-for="(item, index) in roomStatusList" :key="index">
						<label>
							<checkbox style="transform:scale(0.7)" :value="item.id" />
						</label>
						<view class="itemAround" :style="{background:item.color}">

						</view>
						<text style="font-size: 24rpx;padding-left: 8rpx;">{{item.status_name}}</text>
					</view>
				</view>
			</checkbox-group>


			<checkbox-group @change="checkboxChange1">
				<view class="roomWorkStatusList" :style="{background:themeColor.com_color1+'40'}">
					<view class="item" v-for="(item,index) in roomWorkStatusList" :key="index">
						<label>
							<checkbox style="transform:scale(0.7)" :value="item.id" />
						</label>
						<view class="itemAround" :style="{background:item.color}">

						</view>
						<text style="font-size: 24rpx;padding-left: 8rpx;">{{item.status_name}}</text>
					</view>
				</view>
			</checkbox-group>

			<view class="floorAndBulidBox">
				<view class="bulid">
					<view class="bulidBox" v-for="(item, index) in buildingList" :key="index"
						:style="bulidCurrent==item.id?'color:'+themeColor.com_color1+';border:1px solid '+themeColor.com_color1:''"
						@click="chooseBuilding(item)">
						{{item.name}}
					</view>
				</view>
				<view class="floor">
					<view class="floorBox" v-for="(item, index) in floorList" :key="index"
						:style="floorCurrent==item.id?'color:'+themeColor.com_color1+';border:1px solid '+themeColor.com_color1:''"
						@click="chooseFloor(item)">
						{{item.name}}
					</view>
				</view>
			</view>

			<view class="roomBox">
				<mRoomStatus :roomList="list" @roomGet="roomClick"></mRoomStatus>
			</view>

		</view>
		<mRoomClearBox :roomList="listCom" :poprc="showRc" @closeZj="closeRc" @sureRc="getRcIds"
			:ids="chooseIds" :rooms="chooseRooms"></mRoomClearBox>
		<!-- 房型筛选 -->
		<m-popup :show="pop" @closePop="closePop" :closeable="false">
			<view class="picker-view_box">
				<view class="" style="position: absolute;top: 20rpx;right: 30rpx;z-index: 99999999;">
					<text @click="sure">确定</text>
				</view>
				<picker-view :value="value" @change="bindChange" class="picker-view">
					<picker-view-column>
						<view class="item" v-for="(item,index) in roomStatusBox" :key="index">{{item.name}}</view>
					</picker-view-column>
				</picker-view>
			</view>

		</m-popup>

		<!-- 点击房态弹出框 -->
		<m-popup :show="pop1" @closePop="closePop1" v-if="roomInfo">
			<view class="roomStatusContent">
				<p class="title"><text
						style="padding-right: 10rpx;">{{roomInfo.room_number}}</text><text>{{roomInfo.room_type_name}}</text>
				</p>
				<view class="content" style="">
					<view class="statusRoom">
						<text :style="{color:roomInfo.room_status_color}">{{roomInfo.room_status_name}}</text>
						<text style="padding: 0 8rpx;">-</text>
						<text :style="{color:roomInfo.clear_color}">{{roomInfo.clear_status_name}}</text>
					</view>
					<view class="statusRoom" v-if="roomBillInfo&&roomBillInfo.bill_status == 3">
						<p class="timeOver" v-if="roomBillInfo"
							:style="leaveAndTime(roomBillInfo.enter_time_plan).includes('-')||leaveAndTime(roomBillInfo.enter_time_plan).includes('m')?'background:#ff0105':'background:#00a61b'">
							{{leaveAndTime(roomBillInfo.enter_time_plan)}}
						</p>
						<text
							style="font-size: 24rpx;padding-left: 10rpx;">到店时间{{leaveAndTime(roomBillInfo.enter_time_plan).includes('-')?'已超时：':'还剩下：'}}{{formatTime(roomBillInfo.enter_time_plan).trim()}}</text>
					</view>
					<view class="statusRoom" v-if="roomBillInfo&&roomBillInfo.bill_status == 4">
						<p class="timeOver" v-if="roomBillInfo&&roomBillInfo"
							:style="leaveAndTime(roomBillInfo.leave_time_plan).includes('-')||leaveAndTime(roomBillInfo.leave_time_plan).includes('m')?'background:#ff0105':'background:#00a61b'">
							{{leaveAndTime(roomBillInfo.leave_time_plan)}}
						</p>
						<text
							style="font-size: 24rpx;padding-left: 10rpx;">离店时间{{leaveAndTime(roomBillInfo.leave_time_plan).includes('-')?'已超时：':'还剩下：'}}{{formatTime(roomBillInfo.leave_time_plan).trim()}}</text>
					</view>
					<view class="statusRoom" v-if="roomInfo&&roomBillInfo.total_bill_balance < 0">
						<p class="timeOver" style="background:#ff0105">
							欠
						</p>
						<text
							style="font-size: 24rpx;padding-left: 10rpx;">此单已欠费：{{roomBillInfo.total_bill_balance}}元</text>
					</view>
					<view class="btnBox1">
						<view class="btnBox" v-if="arrRecode(item)" v-for="(item, index) in btnStatusList" :key="index"
							@click="changeRoom(item)">
							<view class="btn" :style="item.name.length==5?'font-size:29rpx':''">
								{{item.name}}
							</view>

						</view>
						<view class="btnBox" v-if="arrClear(item)" v-for="(item, index) in btnClearStatus" :key="index"
							@click="changeRoom(item)">
							<view class="btn">
								{{item.name}}
							</view>

						</view>
					</view>
					<view class="billDetail" v-if="roomBillInfo">
						<view class="billDetail_title">
							<text>订单状态：{{roomInfo.room_status_name}}</text>
							<view class="" v-if="role('room_status_bill_view')" @click="goBill(roomInfo.bill_id)"
								style="width: fit-content;padding: 10rpx;border: 1px solid #333;border-radius: 6rpx;color: #333;">
								查看订单详情
							</view>
						</view>
						<text>联系人：{{roomBillInfo.link_man}}</text>
						<view class="" style="display: flex;" @click="callPhone(roomBillInfo.link_phone)">
							<text>联系电话：{{roomBillInfo.link_phone}}</text>
							<view class="icon-dianhua" style="color: #5500ff;font-size:34rpx">

							</view>
						</view>

						<text>价格方案：{{roomBillInfo.tempGrade_name}}</text>
						<text>订单来源：{{roomBillInfo.bill_source_name}}</text>
						<text>预抵时间：{{roomBillInfo.enter_time_plan | moment1}}</text>
						<text
							v-if="roomBillInfo.enter_time > 0">入住时间：{{roomBillInfo.enter_time | moment1}}</text>
						<text>预离时间：{{roomBillInfo.leave_time_plan | moment1}}</text>
					</view>

					<view class="futureBox">
						<view class="futureBill" :style="'background:' + item.color"
							v-for="(item, index) in roomInfo.room_status_record_future" :key="index"
							@click="goBill(item.bill_id)">
							<view class="" v-if="item.bill_info"
								style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
								<text>{{item.room_status_name}}({{item.bill_info.link_man}})</text>
								<text style="padding-left: 10rpx;">{{item.bill_info.enter_time_plan | moment1}}</text>
								至
								<text style="padding-left: 10rpx;">{{item.bill_info.leave_time_plan | moment1}}</text>
							</view>
							<view class="" v-else style="white-space: nowrap;text-overflow: ellipsis;overflow: hidden;">
								<text>{{item.room_status_name}}</text>
								<text style="padding-left: 10rpx;">{{item.start_time_plan | moment1}}</text>
								至
								<text style="padding-left: 10rpx;">{{item.end_time_plan | moment1}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 置净弹窗 -->
		<m-popup :show="popZj" @closePop="closePopZj">
			<view class="zjBox" style="">
				<p>置净</p>
				<view class="" style="display: flex;align-items: center;margin-top: 50rpx;width: 100%;">
					<view class="" style="width: 100rpx;">
						<text style="color: red;">*</text>
						<text>房间:</text>
					</view>

					<scroll-view scroll-y="true" style="max-height: 70vh;min-height: 60rpx;width: 560rpx;">
						<view class="contenBox" @click="rangeChoose">
							<view class="innerBox" v-for="(item, index) in  chooseRooms" :key="index">
								{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
							</view>

						</view>
					</scroll-view>
				</view>
				<p style="padding: 20rpx;color: brown;">*点击输入框可多选房间</p>
				<view class="sureBtn" style="" @click="sureZj">
					确定
				</view>
				
				<view @click="closePopZj" class="icon-close"
					style="position: absolute;top: 30rpx;right:30rpx;font-size: 40rpx;color: #383838;">
				</view>
			</view>
		</m-popup>

		<!-- 置脏 -->
		<m-popup :show="popZz" @closePop="closePopZz">
			<view class="zjBox" style="">
				<p>置脏</p>
				<view class="" style="display: flex;align-items: center;margin-top: 50rpx;width: 100%;">
					<view class="" style="width: 100rpx;">
						<text style="color: red;">*</text>
						<text>房间:</text>
					</view>

					<scroll-view scroll-y="true" style="max-height: 70vh;min-height: 60rpx;width: 560rpx;">
						<view class="contenBox" @click="rangeChoose">
							<view class="innerBox" v-for="(item, index) in  chooseRooms" :key="index">
								{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
							</view>

						</view>
					</scroll-view>
				</view>
				<p style="padding: 20rpx;color: brown;">*点击输入框可多选房间</p>
				<view class="sureBtn" style="" @click="sureZz">
					确定
				</view>
				<!-- <mRoomClearBox :roomList="listCom" :poprc="showRc" @closeZj="closeRc" @sureRc="getRcIds"
					:ids="chooseIds" :rooms="chooseRooms"></mRoomClearBox> -->
				<view @click="closePopZz" class="icon-close"
					style="position: absolute;top: 30rpx;right:30rpx;font-size: 40rpx;color: #383838;">
				</view>
			</view>
		</m-popup>

		<!-- 置净清洁中 -->
		<m-popup :show="popZc" @closePop="closePopZc">
			<view class="zjBox" style="">
				<p>置清洁中</p>
				<view class="" style="display: flex;align-items: center;margin-top: 50rpx;width: 100%;">
					<view class="" style="width: 100rpx;">
						<text style="color: red;">*</text>
						<text>房间:</text>
					</view>

					<scroll-view scroll-y="true" style="max-height: 70vh;min-height: 60rpx;width: 560rpx;">
						<view class="contenBox" @click="rangeChoose">
							<view class="innerBox" v-for="(item, index) in  chooseRooms" :key="index">
								{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
							</view>

						</view>
					</scroll-view>
				</view>
				<p style="padding: 20rpx;color: brown;">*点击输入框可多选房间</p>
				<view class="sureBtn" style="" @click="sureZc">
					确定
				</view>
				<!-- <mRoomClearBox :roomList="listCom" :poprc="showRc" @closeZj="closeRc" @sureRc="getRcIds"
					:ids="chooseIds" :rooms="chooseRooms"></mRoomClearBox> -->
				<view @click="closePopZc" class="icon-close"
					style="position: absolute;top: 30rpx;right:30rpx;font-size: 40rpx;color: #383838;">
				</view>
			</view>
		</m-popup>

		<!-- 置净维修 -->
		<m-popup :show="popZw" @closePop="closePopZw">
			<view class="zjBox" style="">
				<p>置维修</p>
				<view class="" style="display: flex;align-items: center;margin-top: 50rpx;width: 100%;">
					<view class="" style="width: 100rpx;">
						<text style="color: red;">*</text>
						<text>房间:</text>
					</view>

					<scroll-view scroll-y="true" style="max-height: 70vh;min-height: 60rpx;width: 560rpx;">
						<view class="contenBox" @click="rangeChoose">
							<view class="innerBox" v-for="(item, index) in  chooseRooms" :key="index">
								{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
							</view>

						</view>
					</scroll-view>
				</view>
				<p style="padding: 20rpx;color: brown;">*点击输入框可多选房间</p>
				<view class="sureBtn" style="" @click="sureZw">
					确定
				</view>
				<!-- <mRoomClearBox :roomList="listCom" :poprc="showRc" @closeZj="closeRc" @sureRc="getRcIds"
					:ids="chooseIds" :rooms="chooseRooms"></mRoomClearBox> -->
				<view @click="closePopZw" class="icon-close"
					style="position: absolute;top: 30rpx;right:30rpx;font-size: 40rpx;color: #383838;">
				</view>
			</view>
		</m-popup>

		<!-- 置锁房 -->
		<m-popup :show="popZl" @closePop="closePopZl">
			<view class="zjBox" style="">
				<p>置锁房</p>
				<view class="" style="display: flex;align-items: center;margin-top: 50rpx;width: 100%;">
					<view class="" style="width: 100rpx;">
						<text style="color: red;">*</text>
						<text>房间:</text>
					</view>

					<scroll-view scroll-y="true" style="max-height: 50vh;min-height: 60rpx;width: 560rpx;">
						<view class="contenBox" @click="rangeChoose">
							<view class="innerBox" v-for="(item, index) in  chooseRooms" :key="index">
								{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
							</view>

						</view>
					</scroll-view>
				</view>
				<p style="padding: 20rpx;color: brown;">*点击输入框可多选房间</p>
				<view class="" style="margin:  20rpx 0;display: flex;align-items: center;">
					<view class="" style="width: 140rpx;font-size: 28rpx;">
						<text style="color: red;">*</text>
						<text>时间范围:</text>
					</view>
					<uni-datetime-picker v-model="datetimerange" :start="startTime" type="datetimerange"
						rangeSeparator="至" />
				</view>
				<view class="sureBtn" style="" @click="sureZl">
					确定
				</view>
				<!-- <mRoomClearBox :roomList="listCom" :poprc="showRc" @closeZj="closeRc" @sureRc="getRcIds"
					:ids="chooseIds" :rooms="chooseRooms"></mRoomClearBox> -->
				<view @click="closePopZl" class="icon-close"
					style="position: absolute;top: 30rpx;right:30rpx;font-size: 40rpx;color: #383838;">
				</view>
			</view>
		</m-popup>

		<!-- 置关房 -->
		<m-popup :show="popZg" @closePop="closePopZg">
			<view class="zjBox" style="">
				<p>置关房</p>
				<view class="" style="display: flex;align-items: center;margin-top: 50rpx;width: 100%;">
					<view class="" style="width: 100rpx;">
						<text style="color: red;">*</text>
						<text>房间:</text>
					</view>

					<scroll-view scroll-y="true" style="max-height: 50vh;min-height: 60rpx;width: 560rpx;">
						<view class="contenBox" @click="rangeChoose">
							<view class="innerBox" v-for="(item, index) in  chooseRooms" :key="index">
								{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
							</view>

						</view>
					</scroll-view>
				</view>
				<p style="padding: 20rpx;color: brown;">*点击输入框可多选房间</p>
				<view class="" style="margin:  20rpx 0;display: flex;align-items: center;">
					<view class="" style="width: 140rpx;font-size: 28rpx;">
						<text style="color: red;">*</text>
						<text>时间范围:</text>
					</view>
					<uni-datetime-picker v-model="datetimerange" :start="startTime" type="datetimerange"
						rangeSeparator="至" />
				</view>
				<view class="sureBtn" style="" @click="sureZg">
					确定
				</view>
				<!-- <mRoomClearBox :roomList="listCom" :poprc="showRc" @closeZj="closeRc" @sureRc="getRcIds"
					:ids="chooseIds" :rooms="chooseRooms"></mRoomClearBox> -->
				<view @click="closePopZg" class="icon-close"
					style="position: absolute;top: 30rpx;right:30rpx;font-size: 40rpx;color: #383838;">
				</view>
			</view>
		</m-popup>

		<!-- 置空房 -->
		<m-popup :show="popZk" @closePop="closePopZk">
			<view class="zjBox" style="">
				<p>置空房</p>
				<view class="" style="display: flex;align-items: center;margin-top: 50rpx;width: 100%;">
					<view class="" style="width: 100rpx;">
						<text style="color: red;">*</text>
						<text>房间:</text>
					</view>

					<scroll-view scroll-y="true" style="max-height: 70vh;min-height: 60rpx;width: 560rpx;">
						<view class="contenBox" @click="rangeChoose">
							<view class="innerBox" v-for="(item, index) in  chooseRooms" :key="index">
								{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
							</view>

						</view>
					</scroll-view>
				</view>
				<p style="padding: 20rpx;color: brown;">*点击输入框可多选房间</p>
				<view class="sureBtn" style="" @click="sureZk">
					确定
				</view>
				<!-- <mRoomClearBox :roomList="listCom" :poprc="showRc" @closeZj="closeRc" @sureRc="getRcIds"
					:ids="chooseIds" :rooms="chooseRooms"></mRoomClearBox> -->
				<view @click="closePopZk" class="icon-close"
					style="position: absolute;top: 30rpx;right:30rpx;font-size: 40rpx;color: #383838;">
				</view>
			</view>
		</m-popup>

		<!-- 预订转入住 -->
		<m-popup :show="popBookToIn" @closePop="closePopBookToIn">
			<view class="bookIn" style="">
				<scroll-view scroll-y="true" style="height: 100%;width: 100%;">
					<p style="margin: 20rpx auto;font-weight: 600;display: flex;justify-content: center;">预订转入住</p>
					<checkbox-group @change="checkboxBookToIn">
						<view class="bookBill" :style="{background:themeColor.com_color1+'40'}">
							<view class="item" v-for="(item, index) in connectBill" :key="index">
								<label style="width: 80rpx;height: 100%;">
									<checkbox style="transform:scale(0.7)" :checked="false" :value="item.id" />
								</label>
								<view class="item_roomBox">
									<view class="item_roomBox_title">
										<view class="item_roomBox_title_text">{{item.room_type_name}}</view>
										<view class="item_roomBox_title_text">房号:{{item.room_number}}</view>
									</view>
									<view class="item1" style="width: 100%;display: flex;margin: 14rpx 0;">
										<text>价格:</text>
										<text>{{item.room_date_price[0].room_price}}</text>
									
										<text style="font-size: 28rpx;color: blue;padding-left: 6rpx;"
											@click="changePrice(item)">多日房价</text>
									</view>
									<view class="">
										<view class="item_msg" v-for="(item1, index1) in item.userMsg" :key="index1">
											<input type="text" v-model="item1.name" style="width: 140rpx;"
												class="item_msg_inpt" placeholder="姓名">
											<view class="msgItem"
												@click.stop="getGender({'item':item,'index1':index1})">
												<picker @change.stop="bindChange1" :value="changeIndex1"
													range-key="name" :range="genderList">
													<view class="pickerBox">
														{{item1.gender==0?'保密':(item1.gender==1?'男':'女')}}
														<view class="icon-down"
															style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
														</view>
													</view>
												</picker>
											</view>
											<input type="number" v-model="item1.phone" style="width: 240rpx;"
												class="item_msg_inpt" placeholder="电话">
											<view class="msgItem"
												@click.stop="getGender({'item':item,'index1':index1})">
												<picker @change.stop="bindChange2" :value="changeIndex2"
													range-key="name" :range="cardList">
													<view class="pickerBox" style="width: 142rpx;">
														{{item1.identification_type==1?'身份证':(item1.identification_type==2?'港澳通行证':(item1.identification_type==3?'驾驶证':(item1.identification_type==4?'军官证':(item1.identification_type==5?'护照':'台湾身份证'))))}}
													</view>
												</picker>
											</view>
											<input type="idcard" v-model="item1.identification_number"
												style="width: 300rpx;font-size: 24rpx;" class="item_msg_inpt"
												placeholder="证件号码">
											<view class="icon-tianjia" style="font-size: 40rpx;margin:0 20rpx;"
												@click="addUserMsg(item)">
											</view>
											<image src="../../../static/images/jian.png"
												style="width: 44rpx;height:44rpx"
												@click="reduceUserMsg({'item':item,'index1':index1})" mode=""
												v-if="item.userMsg.length>1"></image>
										</view>
									</view>

								</view>
							</view>
						</view>
					</checkbox-group>
					<view class="" style="height: 120rpx;">

					</view>
				</scroll-view>
				<view class=""
					style="position: fixed;bottom: 0;width: 100%;height: 120rpx;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="sureCheckInTh"
						style="width: 500rpx;height: 80rpx;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;font-size: 38rpx;"
						:style="{background:themeColor.main_color }">
						确认
					</view>
				</view>

			</view>
		</m-popup>
		<!-- 价格弹窗 -->
		<m-popup :show="popPrice" @closePop="closepopPrice">
			<view class="" style="height: 80vh;width: 100%;padding: 30rpx;">
				<scroll-view scroll-y="true" style="height:100%;position: relative;">
					<view class="" v-for="(item3, index3) in pricesBox" :key="index3" style="margin-top: 30rpx;">
						<view class=""
							style="display: flex;align-items: center;height: 50rpx;justify-content: space-between;">
							<text>{{item3.date}}:</text>
							<view class="" style="width: 400rpx;height: 44rpx;display: flex;align-items: center;">
								<uni-easyinput :disabled="true" type="digit" v-model="item3.room_price" trim="all" @change="changePrice(item)" :clearable="false"></uni-easyinput>
								<!-- <uni-number-box v-model="item3.room_price" :disabled="true" max="1000000" :step="1" /> -->
							</view>
						</view>

					</view>
					<view class=""
						style="width: 100%;height: 60rpx;display: flex;align-items: center;justify-content: center;margin-top: 80rpx;">
						<view class=""
							style="width: 480rpx;height: 70rpx;border-radius: 30rpx;background-color: #2c7f08;color: #fff;display: flex;align-items: center;justify-content: center;"
							@click="closepopPrice">
							<text>确认</text>
						</view>

					</view>
				</scroll-view>

			</view>
		</m-popup>

		<!-- 联房 -->
		<mConnectRoom :poprc="showConnect" @closeConnect="showConnectClose" :room="roomInfo" @sureConnect="sureconn">
		</mConnectRoom>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'

	import mRoomStatus from '../../components/m-roomStatus.vue'
	import mRoomClearBox from '../../components/m-roomClearBox.vue'
	import mConnectRoom from '../../components/m-connectRoom.vue'
	export default {
		data() {
			return {
				stay_rate: '',
				total_count: 0,
				staying_count: 0,
				list: [], //房态图使用的房态数据
				listCom: [], //筛选组件使用的房态数据
				roomStatusList: [],
				roomWorkStatusList: [],
				roomStatusBox: [{
					id: 0,
					name: '全部'
				}],
				value: [0],
				roomNumber: '',
				changeName: '',
				changeIndex: 0,
				pop: false,
				pop1: false,
				popZj: false, //置净
				popZz: false, //置脏
				popZc: false, //置清洁中
				popZw: false, //置维修
				popZl: false, //置锁房
				popZg: false, //置关房
				popZk: false, //置空房
				popBookToIn: false, //转入住
				focusClass: false,
				buildingList: [{
					id: 0,
					name: '全部'
				}], //楼栋列表
				floorList: [], //楼层列表
				bulidCurrent: 0,
				floorCurrent: 0,
				params: {
					room_clear_status: [],
					room_record_status: [],
					floor_id: '',
					building_id: "",
					room_type_id: "",
					room_number: "",
					room_sale_type: "",
					grade_id: "",
					intermediaries_id: "",
					start_time: "",
					end_time: ""
				},
				roomInfo: null,
				btnStatusList: [{
					id: 1,
					name: '办理入住',
					sign: [3, 5]
				}, {
					id: 2,
					name: '办理预订',
					sign: [1, 2, 3, 4, 5, 6]
				}, {
					id: 3,
					name: '预订转入住',
					sign: [4]
				}, {
					id: 4,
					name: '置锁房',
					sign: [2, 3, 5]
				}, {
					id: 5,
					name: '置关房',
					sign: [2, 3, 5]
				}, {
					id: 6,
					name: '置空房',
					sign: [2, 3, 5]
				}, {
					id: 7,
					name: '联房',
					sign: [1, 4, 6]
				}],
				btnClearStatus: [{
					id: 1,
					name: '置净',
					sign: [2, 3, 4]
				}, {
					id: 2,
					name: '置脏',
					sign: [1, 3, 4]
				}, {
					id: 3,
					name: '置清洁中',
					sign: [1, 2, 4]
				}, {
					id: 4,
					name: '置维修',
					sign: [1, 2, 3]
				}],
				showRc: false,
				chooseIds: [],
				chooseRooms: [],
				datetimerange: [],
				startTime: '',
				connectBill: [],
				genderList: [{
					id: 0,
					name: '保密'
				}, {
					id: 1,
					name: '男'
				}, {
					id: 2,
					name: '女'
				}],
				cardList: [],
				changeIndex1: 0,
				changeIndex2: 0,
				chooseConn: null,
				msgIndex: 0,
				chooseConnBill: [], //多选要办理入住得
				showConnect: false, //
				//价格弹窗
				popPrice: false,
				pricesBox: [],
				itemPrice: null, //暂时存储点击的item
				// 房态订单
				roomBillInfo:null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		components: {
			mRoomStatus,
			mRoomClearBox,
			mConnectRoom
		},
		watch: {
			changeIndex1() {
				this.connectBill.forEach(item => {
					if (this.chooseConn.id == item.id) {
						item.userMsg.forEach((item1, index1) => {
							if (this.msgIndex == index1) {
								item1.gender = this.genderList[this.changeIndex1].id
							}

						})
					}
				})
			},
			changeIndex2() {
				this.connectBill.forEach(item => {
					if (this.chooseConn.id == item.id) {
						item.userMsg.forEach((item1, index1) => {
							if (this.msgIndex == index1) {
								item1.identification_type = this.cardList[this.changeIndex2].id
							}

						})
					}
				})
			},
			connectBill: {
				handler(newVal, oldVal) {
					this.connectBill = newVal
					console.log('ddddd');
				},
				immediate: true,
				deep: true
			}
		},
		onLoad() {
			this.changeName = this.roomStatusBox[0].name
			this.$iBox
				.http('bossGetRoomType', {})({
					method: 'post'
				})
				.then(res => {
					res.data.forEach(item => {
						this.roomStatusBox.push(item)
					})

				})

			this.$iBox
				.http('getRoomClearStatus', {})({
					method: 'post'
				})
				.then(res => {
					let list = res.data
					list.forEach(item => {
						item.checked = false
					})
					this.roomStatusList = list
				})
			// getRoomRecordStatus getRoomBuilding
			this.$iBox.http('getRoomRecordStatus', {})({
					method: 'post'
				})
				.then(res => {
					this.roomWorkStatusList = res.data
				})

			this.$iBox.http('getRoomBuilding', {
					page: 1,
					limit: 10000
				})({
					method: 'post'
				})
				.then(res => {
					res.data.list.forEach(item => {
						this.buildingList.push(item)
					})
				})
		},
		onShow() {

			console.log('show');
			this.getUseableRoom()

		},
		methods: {
			...mapActions('room', ['getRoomInfo']),
			role(e) {
				let a = this.roles_list.filter(item => {
					return item.permission == e
				}).length
				return a
			},
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			closePopZj() {
				this.popZj = false
				this.chooseIds = []
				this.chooseRooms = []
				// 关掉置净窗口重新查询房态
				this.params.room_clear_status = []
				this.getUseableRoom()
			},
			closeRc() {
				this.showRc = false
			},
			sureZj() {
				this.$iBox.http('updateRoomClearStatus', {
						room_ids: this.chooseIds,
						clear_status: 1
					})({
						method: 'post'
					})
					.then(res => {
						this.chooseIds = []
						this.chooseRooms = []
						this.params.room_clear_status = []
						this.getUseableRoom()
						this.popZj = false
						this.pop1 = false
					})
			},
			getRcIds(e) {
				console.log(e, 'ids');
				this.chooseIds = e.ids
				this.chooseRooms = e.rooms
			},
			closePopZz() {
				this.popZz = false
				this.chooseIds = []
				this.chooseRooms = []
				// 关掉置净窗口重新查询房态
				this.params.room_clear_status = []
				this.getUseableRoom()
			},

			sureZz() {
				this.$iBox.http('updateRoomClearStatus', {
						room_ids: this.chooseIds,
						clear_status: 2
					})({
						method: 'post'
					})
					.then(res => {
						this.chooseIds = []
						this.chooseRooms = []
						this.params.room_clear_status = []
						this.getUseableRoom()
						this.popZz = false
						this.pop1 = false
					})
			},

			closePopZc() {
				this.popZc = false
				this.chooseIds = []
				this.chooseRooms = []
				// 关掉置净窗口重新查询房态
				this.params.room_clear_status = []
				this.getUseableRoom()
			},

			sureZc() {
				this.$iBox.http('updateRoomClearStatus', {
						room_ids: this.chooseIds,
						clear_status: 3
					})({
						method: 'post'
					})
					.then(res => {
						this.chooseIds = []
						this.chooseRooms = []
						this.params.room_clear_status = []
						this.getUseableRoom()
						this.popZc = false
						this.pop1 = false

					})
			},
			callPhone(e) {
				uni.makePhoneCall({
					phoneNumber: e
				})
			},

			closePopZw() {
				this.popZw = false
				this.chooseIds = []
				this.chooseRooms = []
				// 关掉置净窗口重新查询房态
				this.params.room_clear_status = []
				this.getUseableRoom()
			},

			sureZw() {
				this.$iBox.http('updateRoomClearStatus', {
						room_ids: this.chooseIds,
						clear_status: 4
					})({
						method: 'post'
					})
					.then(res => {
						this.chooseIds = []
						this.chooseRooms = []
						this.params.room_clear_status = []
						this.getUseableRoom()
						this.popZw = false
						this.pop1 = false

					})
			},
			closePopZl() {
				this.popZl = false
				this.chooseIds = []
				this.chooseRooms = []
				// 关掉置净窗口重新查询房态
				this.params.room_clear_status = []
				this.params.room_record_status = []
				this.getUseableRoom()
			},
			sureZl() {
				console.log(this.datetimerange.length);
				if (this.datetimerange.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择时间'
					})
					return;
				}


				this.$iBox.http('updateRoomRecordStatus', {
						room_ids: this.chooseIds,
						record_status: 2,
						start_time: this.$moment(this.datetimerange[0]).unix(),
						end_time: this.$moment(this.datetimerange[1]).unix()
					})({
						method: 'post'
					})
					.then(res => {
						this.chooseIds = []
						this.chooseRooms = []
						this.params.room_clear_status = []
						this.params.room_record_status = []
						this.datetimerange = []
						this.getUseableRoom()
						this.popZl = false
						this.pop1 = false

					})
			},
			closepopPrice() {
				this.popPrice = false
			},
			closePopZg() {
				this.popZg = false
				this.chooseIds = []
				this.chooseRooms = []
				// 关掉窗口重新查询房态
				this.params.room_clear_status = []
				this.params.room_record_status = []
				this.getUseableRoom()
			},
			sureZg() {
				console.log(this.datetimerange.length);
				if (this.datetimerange.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择时间'
					})
					return;
				}


				this.$iBox.http('updateRoomRecordStatus', {
						room_ids: this.chooseIds,
						record_status: 3,
						start_time: this.$moment(this.datetimerange[0]).unix(),
						end_time: this.$moment(this.datetimerange[1]).unix()
					})({
						method: 'post'
					})
					.then(res => {
						this.chooseIds = []
						this.chooseRooms = []
						this.params.room_clear_status = []
						this.params.room_record_status = []
						this.datetimerange = []
						this.getUseableRoom()
						this.popZg = false
						this.pop1 = false

					})
			},
			closePopZk() {
				this.popZk = false
				this.chooseIds = []
				this.chooseRooms = []
				// 关掉窗口重新查询房态
				this.params.room_clear_status = []
				this.params.room_record_status = []
				this.getUseableRoom()
			},
			sureZk() {
				console.log(this.datetimerange.length);

				this.$iBox.http('updateRoomRecordStatus', {
						room_ids: this.chooseIds,
						record_status: 5
					})({
						method: 'post'
					})
					.then(res => {
						this.chooseIds = []
						this.chooseRooms = []
						this.params.room_clear_status = []
						this.params.room_record_status = []
						this.getUseableRoom()
						this.popZk = false
						this.pop1 = false

					})
			},
			pickRoomType() {
				this.pop = true
				console.log(this.roomStatusBox, 'fangxing');
			},
			arrRecode(e) {
				console.log(e, 'dfdd', this.roomInfo.room_status_record_id);
				return e.sign.includes(this.roomInfo.room_status_record_id)

			},
			arrClear(e) {
				return e.sign.includes(this.roomInfo.clean_status)

			},

			changeRoom(e) {
				if (e.name.includes('置净')) {
					this.popZj = true
					this.chooseIds.push(this.roomInfo.id)
					this.chooseRooms.push(this.roomInfo)
					this.params.room_clear_status = [2, 3, 4]
					this.getUseableRoomCom()
				} else if (e.name.includes('置脏')) {
					this.popZz = true
					this.chooseIds.push(this.roomInfo.id)
					this.chooseRooms.push(this.roomInfo)
					this.params.room_clear_status = [1, 3, 4]
					this.getUseableRoomCom()
				} else if (e.name.includes('置清洁中')) {
					this.popZc = true
					this.chooseIds.push(this.roomInfo.id)
					this.chooseRooms.push(this.roomInfo)
					this.params.room_clear_status = [1, 2, 4]
					this.getUseableRoomCom()
				} else if (e.name.includes('置维修')) {
					this.popZw = true
					this.chooseIds.push(this.roomInfo.id)
					this.chooseRooms.push(this.roomInfo)
					this.params.room_clear_status = [1, 2, 3]
					this.getUseableRoomCom()
				} else if (e.name.includes('置锁房')) {
					this.popZl = true
					this.startTime = this.$moment().format('YYYY-MM-DD')
					this.datetimerange = [this.$moment().format('YYYY-MM-DD HH:mm:ss'), this.$moment().add(1, 'days')
						.format('YYYY-MM-DD HH:mm:ss')
					]
					this.chooseIds.push(this.roomInfo.id)
					this.chooseRooms.push(this.roomInfo)
					this.params.room_clear_status = [1, 2, 3, 4]
					this.params.room_record_status = [2, 3, 5]
					this.getUseableRoomCom()
				} else if (e.name.includes('置关房')) {
					this.popZg = true
					this.startTime = this.$moment().format('YYYY-MM-DD')
					this.datetimerange = [this.$moment().format('YYYY-MM-DD HH:mm:ss'), this.$moment().add(1, 'days')
						.format('YYYY-MM-DD HH:mm:ss')
					]
					this.chooseIds.push(this.roomInfo.id)
					this.chooseRooms.push(this.roomInfo)
					this.params.room_clear_status = [1, 2, 3, 4]
					this.params.room_record_status = [2, 3, 5]
					this.getUseableRoomCom()
				} else if (e.name.includes('置空房')) {
					this.popZk = true
					this.startTime = this.$moment().format('YYYY-MM-DD')
					this.chooseIds.push(this.roomInfo.id)
					this.chooseRooms.push(this.roomInfo)
					this.params.room_clear_status = [1, 2, 3, 4]
					this.params.room_record_status = [2, 3, 5]
					this.getUseableRoomCom()
				} else if (e.name.includes('办理入住')) {
					console.log(this.roomInfo, 'ruzhu');
					if (this.roomInfo.clean_status == 2) {
						uni.showModal({
							title: '提示',
							content: '此房间为脏房，是否继续办理?',
							success: res => {
								if (res.confirm) {
									let a = this.roles_list.filter(item => {
										return item.permission == 'room_status'
									}).length

									if (a == 0) {
										uni.showToast({
											icon: 'none',
											title: '暂无查看订单权限'
										})
										return;
									}
									uni.navigateTo({
										url: '/packageA/manager/checkIn/checkIn'
									})
									this.closePop1()
								} else {

								}
							}
						})
					} else {
						let a = this.roles_list.filter(item => {
							return item.permission == 'room_status'
						}).length

						if (a == 0) {
							uni.showToast({
								icon: 'none',
								title: '暂无查看订单权限'
							})
							return;
						}
						uni.navigateTo({
							url: '/packageA/manager/checkIn/checkIn'
						})
						this.closePop1()
					}


				} else if (e.name.includes('办理预订')) {
					uni.navigateTo({
						url: '/packageA/manager/ording/ording'
					})
					this.closePop1()
				} else if (e.name.includes('预订转入住')) {
					// 查询连房
					console.log(e);
					this.$iBox.http('getConnectBill', {
							bill_id: this.roomInfo.bill_id
						})({
							method: 'post'
						})
						.then(res => {
							this.connectBill = res.data.filter(item => {
								return item.bill_status == 3
							})


							this.connectBill.forEach(item => {
								let userInfo = []
								let msg = {
									name: item.link_man ? item.link_man : '',
									gender: 0,
									phone: item.link_phone ? item.link_phone : '',
									identification_type: 1,
									identification_number: ''
								}
								userInfo.push(msg)
								item.userMsg = userInfo
							})
							console.log(this.connectBill, 'this.connectBill');
							// 查询证件类型
							this.$iBox.http('getIdentificationTypeList', {

								})({
									method: 'post'
								})
								.then(res => {
									this.cardList = res.data
								})

							this.popBookToIn = true
						})

				} else if (e.name.includes('联房')) {
					this.showConnect = true
				}
			},
			// 确定回调事件
			treeConfirm(e) {
				console.log(e)
			},
			// 取消回调事件
			treeCancel(e) {
				console.log(e)
			},
			rangeChoose() {
				this.showRc = true
			},
			leaveAndTime(e) {
				let lastTime = this.$moment(e * 1000).locale('zh-cn').fromNow()
				console.log(lastTime);
				if(lastTime=='1 小时'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "hours")+'小时';
				}else if(lastTime=='1 天'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}else if(lastTime.includes('月')){
					
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}
				console.log(lastTime, 'lastTime');
				if (lastTime.includes('前')) {
					if (lastTime.includes('秒')) {
						lastTime = '-' + lastTime.split('秒')[0].trim() + 's'
					} else if (lastTime.includes('分')) {
						lastTime = '-' + lastTime.split('分')[0].trim() + 'm'
					} else if (lastTime.includes('时')) {
						lastTime = '-' + lastTime.split('小时')[0].trim() + 'h'
					} else if (lastTime.includes('天')) {
						lastTime = '-' + lastTime.split('天')[0].trim() + 'd'
					} else if (lastTime.includes('月')) {
						lastTime = '-' + lastTime.split('个')[0].trim() + '月'
					}
				} else {
					if (lastTime.includes('秒')) {
						lastTime = lastTime.split('秒')[0].trim() + 's'
					} else if (lastTime.includes('分')) {
						lastTime = lastTime.split('分')[0].trim() + 'm'
					} else if (lastTime.includes('时')) {
						lastTime = lastTime.split('小时')[0].trim() + 'h'
					} else if (lastTime.includes('天')) {
						lastTime = lastTime.split('天')[0].trim() + 'd'
					} else if (lastTime.includes('月')) {
						lastTime = lastTime.split('个')[0].trim() + '月'
					}
				}
				return lastTime;
			},
			formatTime(e) {
				let lastTime = this.$moment(e * 1000).locale('zh-cn').fromNow(true)
				console.log(lastTime);
				if(lastTime=='1 小时'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "hours")+'小时';
				}else if(lastTime=='1 天'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}else if(lastTime.includes('月')){
					
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}
				//秒
				
				return lastTime
			},

			getUseableRoom() {
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox
					.http('getUsableRoom', this.params)({
						method: 'post'
					})
					.then(res => {
						this.stay_rate = res.data.stay_rate
						this.total_count = res.data.total_count
						this.staying_count = res.data.staying_count
						this.list = res.data.list

						uni.hideLoading()
					})
			},
			getUseableRoomCom() {
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox
					.http('getUsableRoom', this.params)({
						method: 'post'
					})
					.then(res => {
						this.listCom = res.data.list
						uni.hideLoading()
					})
			},
			roomNumSearch() {
				this.params.room_number = this.roomNumber
				this.getUseableRoom()
			},
			sureconn() {
				this.getUseableRoom()
			},
			checkboxChange(e) {

				this.params.room_clear_status = e.detail.value.map(Number)
				this.getUseableRoom()
			},
			checkboxChange1(e) {

				this.params.room_record_status = e.detail.value.map(Number)
				this.getUseableRoom()
			},
			chooseBuilding(e) {
				this.bulidCurrent = e.id
				if (e.id != 0) {
					this.$iBox.http('getRoomFloor', {
							page: 1,
							limit: 10000,
							building_id: e.id == 0 ? '' : e.id
						})({
							method: 'post'
						})
						.then(res => {
							this.floorList = [{
								id: 0,
								name: '全部'
							}]
							res.data.list.forEach(item => {
								this.floorList.push(item)
							})

						})
				} else {
					this.floorList = []
				}
				this.params.building_id = this.bulidCurrent == 0 ? '' : this.bulidCurrent
				this.params.floor_id = ''
				this.getUseableRoom()
			},
			chooseFloor(e) {
				this.floorCurrent = e.id
				this.params.building_id = this.bulidCurrent
				this.params.floor_id = this.floorCurrent == 0 ? '' : this.floorCurrent
				this.getUseableRoom()
			},
			bindChange(e) {
				console.log(e);
				this.changeIndex = e.detail.value[0]
			},
			sure() {
				this.pop = false
				this.changeName = this.roomStatusBox[this.changeIndex].name
				this.params.room_type_id = this.changeIndex == 0 ? '' : this.roomStatusBox[this.changeIndex].id
				this.getUseableRoom()
			},
			roomClick(e) {
				console.log(e, '房态');
				this.pop1 = true
				this.roomInfo = e
				this.getRoomInfo(e)
				
				if(e.bill_id){
					// 获取订单状态
					this.$iBox.http('getRoomStatusBillInfo', {bill_id:e.bill_id})({
							method: 'post'
						}).then(res => {
							this.roomBillInfo = res.data
						})
							
				}else{
					this.roomBillInfo = null
				}
				
				
			},
			closePopBookToIn() {
				this.popBookToIn = false
				this.chooseConnBill = []
			},
			bindChange1(e) {
				this.changeIndex1 = e.detail.value[0]
			},
			bindChange2(e) {
				this.changeIndex2 = e.detail.value[0]
			},
			getGender(e) {
				this.chooseConn = e.item
				this.msgIndex = e.index1
			},
			changePrice(e) {
				this.itemPrice = e
				this.pricesBox = e.room_date_price
				this.popPrice = true
			},
			addUserMsg(e) {
				let connectBill = this.connectBill
				connectBill.forEach(item => {
					if (e.id == item.id) {
						let msg = {
							name: '',
							gender: 0,
							phone: '',
							identification_type: 1,
							identification_number: ''
						}
						item.userMsg.push(msg)
					}
				})
				this.connectBill = JSON.parse(JSON.stringify(connectBill))
			},
			reduceUserMsg(e) {
				let connectBill = this.connectBill
				connectBill.forEach(item => {
					if (e.item.id == item.id) {
						item.userMsg.splice(e.index1, 1)
					}
				})
				this.connectBill = JSON.parse(JSON.stringify(connectBill))
			},
			goBill(e) {
				console.log(e, 'bill');
				this.closePop1()
				uni.navigateTo({
					url: '../bill/billInfo/billInfo?id=' + e
				})
			},
			checkboxBookToIn(e) {

				let bills = []
				this.connectBill.forEach(item => {
					console.log(e.detail.value, item.id);
					if (e.detail.value.includes(item.id.toString())) {
						bills.push(item)
					}

				})

				this.chooseConnBill = bills
			},
			showConnectClose() {
				this.showConnect = false
			},
			// 预定转入住 - 防重复触发版本
			sureCheckInTh() {
				this.$iBox.throttle1(() => {
					this.sureCheckIn()
				}, 2000);
			},

			sureCheckIn() {
				if (this.chooseConnBill.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择房间'
					})
					return
				}
				console.log(this.chooseConnBill, 'lll');
				for (let item of this.chooseConnBill) {
					for (let item1 of item.userMsg) {
						console.log(item1);
						if (!item1.name && !item1.phone && !item1.identification_number) {
							uni.showToast({
								icon: 'none',
								title: '请完善入住人信息'
							})
							return;
							break;
						}
					}
				}

				let params = []

				this.chooseConnBill.forEach(item => {
					let bill = {
						bill_id: '',
						room_id: '',
						user_info: []
					}

					bill.bill_id = item.id
					bill.room_id = item.room_id
					bill.user_info = item.userMsg

					params.push(bill)
				})

				// 显示加载状态
				uni.showLoading({
					title: '转入住中...'
				});

				if (this.chooseConnBill[0].room_sale_type_sign == 'hour') {
					this.$iBox.http('HourbookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.hideLoading();
							this.getUseableRoom()
							this.popBookToIn = false
							this.pop1 = false
							uni.showToast({
								title: '转入住成功',
								icon: 'success'
							});
						})
						.catch(err => {
							uni.hideLoading();
							console.error('预定转入住失败:', err);
						})
				} else if (this.chooseConnBill[0].room_sale_type_sign == 'standard') {
					this.$iBox.http('bookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.hideLoading();
							this.getUseableRoom()
							this.popBookToIn = false
							this.pop1 = false
							uni.showToast({
								title: '转入住成功',
								icon: 'success'
							});
						})
						.catch(err => {
							uni.hideLoading();
							console.error('预定转入住失败:', err);
						})
				} else if (this.chooseConnBill[0].room_sale_type_sign == 'long_standard') {
					this.$iBox.http('LongbookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.hideLoading();
							this.getUseableRoom()
							this.popBookToIn = false
							this.pop1 = false
							uni.showToast({
								title: '转入住成功',
								icon: 'success'
							});
						})
						.catch(err => {
							uni.hideLoading();
							console.error('预定转入住失败:', err);
						})
				} else {
					this.$iBox.http('ConferencebookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							uni.hideLoading();
							this.getUseableRoom()
							this.popBookToIn = false
							this.pop1 = false
							uni.showToast({
								title: '转入住成功',
								icon: 'success'
							});
						})
						.catch(err => {
							uni.hideLoading();
							console.error('预定转入住失败:', err);
						})
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.mainContent {
		border: 1px solid transparent;

		.box {
			height: auto;
			width: 700rpx;
			border-radius: 20rpx;
			background: #ffffff;
			margin: 20rpx auto;
			padding: 30rpx;

			.title {
				height: 110rpx;
				width: 100%;
				border-bottom: 1px solid #eee;
			}

			.chooseBox {
				height: 170rpx;
				width: 100%;
				padding: 20rpx 0;
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				border-bottom: 1px solid #eee;
				position: relative;

				.getFocs {
					border: 1px solid #ee607a !important;
				}

				.pickerBox {
					margin-top: 20rpx;
					position: relative;
					height: 60rpx;
					width: 280rpx;
					border-radius: 14rpx;
					border: 1px solid #eee;
					display: flex;
					padding: 0 20rpx;
					font-size: 30rpx;
					align-items: center;

					.arrow {
						animation-name: to_bottom_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					.arrow_ac {
						animation-name: to_up_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					/* 箭头动画 */

					@keyframes to_up_show {
						0% {
							transform: rotate(0);
						}

						50% {
							transform: rotate(90deg);
						}

						100% {
							transform: rotate(180deg);
						}
					}

					@keyframes to_bottom_show {
						0% {
							transform: rotate(180deg);
							animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
						}

						50% {
							transform: rotate(90deg);
							animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
						}

						100% {
							transform: rotate(0deg);
						}
					}
				}
			}

			.roomStatusList {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				min-height: 80rpx;
				// background: #d8e6ee;
				border-radius: 10rpx;
				width: 100%;
				margin-top: 20rpx;

				.item {
					display: flex;
					align-items: center;
					width: 25%;
					justify-content: center;

					.itemAround {
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
					}
				}

			}

			.roomWorkStatusList {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				min-height: 140rpx;
				// background: #d8e6ee;
				border-radius: 10rpx;
				width: 100%;
				margin-top: 20rpx;

				.item {
					display: flex;
					align-items: center;
					width: 30%;
					justify-content: center;

					.itemAround {
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
					}
				}

			}

			.roomBox {
				border-top: 1px solid #eee;
				margin-top: 20rpx;
			}

			.floorAndBulidBox {
				min-height: 120rpx;
				border-top: 1px solid #eee;
				width: 100%;
				margin-top: 20rpx;
				padding: 20rpx;

				.bulid {
					width: 100%;
					display: flex;
					flex-wrap: wrap;

					.bulidBox {
						width: fit-content;
						padding: 0rpx 8rpx;
						border-radius: 6rpx;
						color: #333;
						border: 1px solid #333;
						font-size: 30rpx;
						margin-right: 20rpx;
						margin-top: 14rpx;
					}
				}

				.floor {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					margin-top: 30rpx;

					.floorBox {
						width: fit-content;
						padding: 0rpx 8rpx;
						border-radius: 6rpx;
						color: #333;
						border: 1px solid #333;
						font-size: 30rpx;
						margin-right: 20rpx;
						margin-top: 14rpx;
					}
				}
			}
		}
	}

	.picker-view_box {
		position: relative;
		height: 650rpx;
		width: 100vw;

		.picker-view {
			height: 600rpx;
			width: 100vw;

			.item {
				width: 100%;
				align-items: center;
				justify-content: center;
				text-align: center;
			}
		}
	}

	.roomStatusContent {
		width: 100%;
		min-height: 700rpx;

		.title {
			padding: 20rpx;
			font-size: 40rpx;
			font-weight: 600;
			padding-right: 10rpx;
		}

		.content {
			border-top: 1px solid #e5e3e3;
			padding: 20rpx;

			.statusRoom {
				width: 94%;
				margin: 14rpx auto;
				background-color: #f7f7f7;
				padding: 10rpx;
				font-size: 38rpx;
				font-weight: 600;
				display: flex;
				align-items: center;

				.timeOver {
					border-radius: 50%;

					font-size: 24rpx;
					width: 48rpx;
					height: 48rpx;
					border-radius: 50%;

					color: #fff;
					font-weight: 600;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 18rpx;
				}
			}

			.btnBox1 {
				display: flex;
				flex-wrap: wrap;

				.btnBox {
					width: 25%;
					display: flex;
					align-items: center;
					justify-content: center;

					.btn {
						width: 96%;
						padding: 8rpx;
						border: 1px solid #b7b7b7;
						margin-top: 14rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 6rpx;
					}
				}

			}

			.billDetail {
				background-color: #f7f7f7;
				width: 94%;
				margin: 24rpx auto;
				background-color: #f7f7f7;
				padding: 20rpx;
				font-size: 26rpx;
				display: flex;
				flex-direction: column;
				line-height: 40rpx;

				.billDetail_title {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 26rpx;
				}
			}


			.futureBox {
				.futureBill {
					width: 96%;
					padding: 10rpx;
					color: #fff;
					font-size: 24rpx;
					margin-top: 22rpx;
					margin-left: 12rpx;
				}
			}
		}
	}

	.zjBox {
		height: 80vh;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;

		.contenBox {
			display: flex;
			border: 1px solid #ccc;
			border-radius: 6rpx;
			width: 560rpx;
			padding: 10rpx;
			flex-wrap: wrap;
			min-height: 60rpx;

			.innerBox {
				width: fit-content;
				padding: 10rpx;
				border: 1px solid #ccc;
				background-color: #fafafa;
				margin-right: 10rpx;
				margin-top: 10rpx;
				border-radius: 4rpx;
				font-size: 24rpx;
			}
		}

		.sureBtn {
			width: 400rpx;
			padding: 20rpx;
			margin-top: 20rpx;
			background-color: #55aa7f;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 14rpx;
			position: absolute;
			bottom: 20rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}
	}

	.bookIn {
		height: 80vh;
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 10rpx;

		.bookBill {
			display: flex;
			flex-direction: column;
			align-items: center;

			.item {
				margin: 20rpx 0;
				display: flex;
				// flex-direction: column;
				align-items: center;

				// border-bottom: 1px solid #eee;
				.item_roomBox {
					width: 640rpx;

					.item_roomBox_title {
						width: 100%;
						display: flex;
						align-items: center;

						.item_roomBox_title_text {
							width: 33%;
							font-size: 28rpx;

						}
					}
				}

				.item_msg {
					width: 640rpx;
					display: flex;
					flex-wrap: wrap;
					align-items: center;

					.item_msg_inpt {
						border: 1px solid #eee;
						padding: 0 10rpx;
						border-radius: 4rpx;
						height: 40rpx;
						background: #fff;
						margin: 4rpx;
					}

					.msgItem {
						display: flex;
						align-items: center;
						padding: 10rpx 0;

						.pickerBox {
							position: relative;
							height: 43rpx;
							width: 110rpx;
							border-radius: 4rpx;
							border: 1px solid #eee;
							display: flex;
							font-size: 30rpx;
							align-items: center;
							background-color: #fff;
							padding-left: 6rpx;
							font-size: 26rpx;

							.arrow {
								animation-name: to_bottom_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}

							.arrow_ac {
								animation-name: to_up_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}


						}
					}
				}
			}
		}
	}
</style>
