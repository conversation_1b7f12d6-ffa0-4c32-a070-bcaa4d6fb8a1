<template>
	<view style="display: flex;align-items: center;justify-content: center;min-height: 100vh;">
		<!-- 当没有订单时显示 -->
		<view class="noBillBox" v-if="mode==1">
			<view class="noBillBg">

			</view>

			<view class="noBill">
				<!-- logo显示 -->
				<view class="logoBox">
					<image src="https://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/v3/component/zzrz.png" mode=""
						style="width: 160rpx;height: 160rpx;"></image>
				</view>
				<p :style="{color:themeColor.text_main_color}" style="font-size: 40rpx;">尊敬的客人，您还没有入住订单！</p>
				<p :style="{color:themeColor.text_main_color}" style="font-size: 28rpx;font-weight: 600;">
					您可以搜索以下信息之一来获取同住人订单！</p>
				<view class="search">
					<view class="SearchBox" v-for="item in searchList" :key="item.id">
						<view class="itemBox" :style="'background:'+themeColor.main_color+'33'">
							<view :class="item.key" :style="{color:themeColor.main_color}" style="font-size: 40rpx;">
							</view>
							<text style="font-size: 24rpx;padding-top: 10rpx;"
								:style="{color:themeColor.text_main_color}">{{item.name}}</text>
						</view>
					</view>
				</view>
				<view class="" style="height: 140rpx;margin: 50rpx;">
					<input type="text"
						style="background: #FFFFFF;padding: 10rpx;height: 60rpx;border-radius: 10rpx;margin: 20rpx auto;"
						:style="{color:themeColor.text_main_color}" :placeholder="`请输入入住信息进行搜索`" v-model="searchKey" @focus="toFocus"/>
				</view>


			</view>

			<view class="noBillBtn" :style="'background:'+themeColor.main_color+';color:'+ themeColor.bg_color"
				@click="search">
				<text>搜索订单</text>
			</view>
			<view class="elseBox">
				<p @click="goMain">去订房 <text class="icon-jiantou"></text></p>
				<p @click="makePhone">联系酒店<text class="icon-kefu"></text></p>
			</view>
		</view>

		<!-- 搜索到订单 -->
		<m-popup mode="bottom" :show="billShow" @closePop="closePop">
			<view class="BillBox" v-if="billList.length > 0">
			<text @click="closePop" style="position: absolute;top: 10rpx;right: 22rpx;font-size: 32rpx;font-weight: 600;">关闭</text>
							
				<scroll-view scroll-y="true" style="height: 100%;width: 100%;">
					
					<p style="font-size: 34rpx;"><text class="icon-fengefu"
							:style="{color:themeColor.main_color}"></text>
						订单列表：请选择一个订单入住</p>
					<view class="billCard" v-for="item in billList" style="background:#c5c3ca59">
						<view class="" style="width: 80%;padding-right: 20rpx;">
							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text>{{item.room_type_name}}</text>
								<text>{{item.link_man}}</text>
							</view>
							<view class=""
								style="display: flex;align-items: center;justify-content: space-between;margin-top: 14rpx;">
								<text>{{formatPass(item.link_phone)}}</text>
								<text>{{item.enter_time_plan | moment}}</text>

							</view>
						</view>
						<view class="" style="width: 20%;display: flex;align-items: center;justify-content: center;">
							<view class=""
								style="height: 60rpx;width: fit-content;padding:10rpx 20rpx;border-radius: 30rpx;display: flex;align-items: center;justify-content: center;"
								:style="{'background':themeColor.main_color}">
								<text style="font-size: 24rpx;font-weight: 600;color: #FFFFFF;"
									@click="chooseRoom(item)">去选房</text>
							</view>

						</view>


					</view>
				</scroll-view>

			</view>
		</m-popup>


	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-searchBill",
		data() {
			return {
				searchList: [{
					id: 1,
					name: '姓名',
					key: 'icon-xingming'
				}, {
					id: 2,
					name: '手机号',
					key: 'icon-shoujihao'
				}, {
					id: 3,
					name: '验证码',
					key: 'icon-yanzhengma'
				}, {
					id: 4,
					name: '订单号',
					key: 'icon-dingdanhao'
				}],
				searchKey: '',
				billList: [],
				billShow: false,
				hotelBill: null,
			};
		},
		props: {
			mode: {
				type: Number,
				default: 1
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		methods: {
			...mapActions('room', ['getHardWareList', 'getBillDetail', 'getRoomBillUser']),
			search(e) {
				this.$iBox.throttle(() => {
					this.toSearch()
				}, 2000);
			},
			toFocus(){
				
			},
			toSearch() {
				// 查询订单,如果订单数量为1则自动跳转选房页，多于1则选择订单
				this.$iBox.http('getUserRoomBillList', {
					search_word: this.searchKey.trim()
				})({
					method: 'post'
				}).then(res => {
					if (res.data != 'no_login_bill') {
						if (res.data.length > 1) {
							this.billShow = true
							this.billList = res.data
						} else if (res.data.length == 1) {
							this.$iBox.http('getRoomBillInfo', {
								bill_id: res.data[0].id
							})({
								method: 'post'
							}).then(res1 => {
								this.hotelBill = res1.data
								this.getBillDetail(res1.data)
								this.$iBox.http('getRoomBillUser', {
									bill_id: res.data[0].id
								})({
									method: 'post'
								}).then(res2 => {
									this.getRoomBillUser(res2.data)
									this.billShow = false
									uni.navigateTo({
										url: '/packageA/autoRoom/chooseRoom/chooseRoom'
									})
									uni.hideLoading()
								})

							})

						} else {
							uni.showToast({
								icon: 'none',
								title: '未搜索到相关订单，请重新搜索！',
								duration: 1000
							})
						}
					}
				})
			},
			closePop() {
				this.billShow = false
			},
			formatPass(e) {
				return e.substring(0, 3) + '****' + e.substring(7, 11)
			},
			chooseRoom(e) {
				this.$iBox.http('getRoomBillInfo', {
					bill_id: e.id
				})({
					method: 'post'
				}).then(res1 => {
					let detail = null
					this.hotelBill = res1.data
					this.getBillDetail(res1.data)
					this.$iBox.http('getRoomBillUser', {
						bill_id: e.id
					})({
						method: 'post'
					}).then(res2 => {
						this.getRoomBillUser(res2.data)
						uni.navigateTo({
							url: '/packageA/autoRoom/chooseRoom/chooseRoom'
						})
						uni.hideLoading()
					})

				})
			},
			goMain() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			makePhone() {
				uni.navigateTo({
					url: '/packageA/customers/customers'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.noBillBox {
		position: relative;

		.noBillBg {
			position: absolute;
			top: 0rpx;
			width: 680rpx;
			border-radius: 20rpx;
			height: 500rpx;

			left: 0;
			right: 0;
			bottom: 0;
			margin: 0 auto;
		}

		.noBill {
			margin: 30rpx auto;
			width: 680rpx;
			// min-height: 300rpx;
			// min-height: 500rpx;
			padding: 30rpx;
			position: relative;

			background-color: rgba(255, 255, 255, 0.3);
			backdrop-filter: blur(30px);
			-webkit-backdrop-filter: blur(30px);
			border: 0.666667px solid rgba(255, 255, 255, 0.18);
			box-shadow: rgba(142, 142, 142, 0.19) 0px 6px 15px 0px;
			-webkit-box-shadow: rgba(142, 142, 142, 0.19) 0px 6px 15px 0px;
			border-radius: 12px;
			-webkit-border-radius: 12px;
			color: rgb(255, 255, 255);

			.logoBox {
				// width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 70rpx;
			}

			.search {
				height: 150rpx;
				padding: 20rpx 0;
				display: flex;
				width: 100%;
				margin-top: 20rpx;

				.SearchBox {
					width: 24%;
					// min-height: 120rpx;
					border-radius: 50%;
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: center;

					.itemBox {
						display: flex;
						flex-direction: column;
						align-items: center;
						padding: 10rpx;
						justify-content: center;
						width: 120rpx;
						height: 120rpx;
						background-color: #c6c6ca;
						border-radius: 20rpx;

					}
				}
			}
		}

		.noBillBtn {
			height: 100rpx;
			width: 660rpx;
			border-radius: 20rpx;
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
		}

		.elseBox {
			height: 100rpx;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			color: #ffffff;
		}
	}


	.BillBox {
		width: 100%;
		margin-top: 30rpx;
		padding: 30rpx;
		height: 70vh;

		.billCard {
			width: 660rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 10rpx;
			font-weight: 600;
			margin: 20rpx auto;
		}
	}
</style>