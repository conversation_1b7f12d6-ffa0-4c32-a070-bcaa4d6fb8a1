<template>
	<view class="hotel-skeleton">
		<!-- 酒店图片骨架 -->
		<view class="skeleton-image">
			<view class="skeleton-shimmer"></view>
		</view>
		
		<!-- 酒店名称骨架 -->
		<view class="skeleton-title">
			<view class="skeleton-shimmer"></view>
		</view>
		
		<!-- 可选的额外信息骨架 -->
		<view v-if="showDetails" class="skeleton-details">
			<view class="skeleton-line short">
				<view class="skeleton-shimmer"></view>
			</view>
			<view class="skeleton-line medium">
				<view class="skeleton-shimmer"></view>
			</view>
			<view class="skeleton-line long">
				<view class="skeleton-shimmer"></view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'HotelSkeleton',
	props: {
		// 是否显示详细信息骨架
		showDetails: {
			type: Boolean,
			default: false
		},
		// 自定义样式
		customStyle: {
			type: Object,
			default: () => ({})
		}
	},
	data() {
		return {}
	}
}
</script>

<style lang="scss" scoped>
.hotel-skeleton {
	display: flex;
	flex-direction: column;
	align-items: center;
	padding: 40rpx;
	
	.skeleton-image {
		width: 260rpx;
		height: 260rpx;
		border-radius: 50%;
		background-color: #f0f0f0;
		position: relative;
		overflow: hidden;
		margin-bottom: 30rpx;
	}
	
	.skeleton-title {
		width: 200rpx;
		height: 34rpx;
		background-color: #f0f0f0;
		border-radius: 4rpx;
		position: relative;
		overflow: hidden;
		margin-bottom: 20rpx;
	}
	
	.skeleton-details {
		width: 100%;
		max-width: 400rpx;
		
		.skeleton-line {
			height: 24rpx;
			background-color: #f0f0f0;
			border-radius: 4rpx;
			position: relative;
			overflow: hidden;
			margin-bottom: 16rpx;
			
			&.short {
				width: 60%;
			}
			
			&.medium {
				width: 80%;
			}
			
			&.long {
				width: 100%;
			}
		}
	}
	
	.skeleton-shimmer {
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(
			90deg,
			transparent,
			rgba(255, 255, 255, 0.6),
			transparent
		);
		animation: shimmer 1.5s infinite;
	}
}

@keyframes shimmer {
	0% {
		left: -100%;
	}
	100% {
		left: 100%;
	}
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
	.hotel-skeleton {
		.skeleton-image,
		.skeleton-title,
		.skeleton-line {
			background-color: #2a2a2a;
		}
		
		.skeleton-shimmer {
			background: linear-gradient(
				90deg,
				transparent,
				rgba(255, 255, 255, 0.1),
				transparent
			);
		}
	}
}
</style>
