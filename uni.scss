/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */

/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */

/* 行为相关颜色 */
$color-primary: #DBA871;
$color-success: #4cd964;
$color-warning: #f0ad4e;
$color-error: #dd524d;

/* 文字基本颜色 */
$text-color-base: #343434; //基本色
$text-color-inverse: #fff; //反色
$text-color-assist: #999; //辅助灰色，如加载更多的提示信息
$text-color-warning: #432A21;
$text-color-grey: #666;
$text-color-placeholder: #808080;
$text-color-disable: #c0c0c0;

/* 背景颜色 */
$bg-color: #F8F8F8;
$bg-color-white: #ffffff;
$bg-color-grey: #f9f9f9;
$bg-color-hover: #f1f1f1; //点击状态颜色
$bg-color-mask: rgba(0, 0, 0, 0.4); //遮罩颜色

/* 边框颜色 */
$border-color: #c8c7cc;

/* 尺寸变量 */

/* 文字尺寸 */
$font-size-extra-sm: 22rpx;
$font-size-sm: 24rpx;
$font-size-base: 26rpx;
$font-size-medium: 28rpx;
$font-size-lg: 32rpx;
$font-size-extra-lg: 36rpx;

/* 图片尺寸 */
$img-size-sm: 40rpx;
$img-size-base: 52rpx;
$img-size-lg: 80rpx;

/* Border Radius */
$border-radius-sm: 4rpx;
$border-radius-base: 6rpx;
$border-radius-lg: 12rpx;
$border-radius-circle: 50%;

/* 水平间距 */
$spacing-row-sm: 10rpx;
$spacing-row-base: 20rpx;
$spacing-row-lg: 30rpx;

/* 垂直间距 */
$spacing-col-sm: 10rpx;
$spacing-col-base: 20rpx;
$spacing-col-lg: 30rpx;

/* 透明度 */
$opacity-disabled: 0.3; // 组件禁用态的透明度

$box-shadow: 0 20rpx 20rpx -20rpx rgba($color: #333, $alpha: 0.1);

/* 文章场景相关 */
$color-title: #2c405a; // 文章标题颜色
$font-size-title: 40rpx;
$color-subtitle: #555555; // 二级标题颜色
$font-size-subtitle: 36rpx;
$color-paragraph: #3f536e; // 文章段落颜色
$font-size-paragraph: 30rpx;


page{
	background-color: #f3f4f6;
}