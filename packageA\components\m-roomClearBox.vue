<template>
	<view>
		<m-popup :show="poprc" :closeable="false" :customStyles="{zindex:1001}">
			<view class="chooseBox">
				<view class="btnBox">
					<!-- <text @click="closePoprc">取消</text> -->
					<text>请选择房间</text>
					<text :style="{color:themeColor.com_color1}" @click="sure">确认</text>
					
				</view>
				<view class="" style="display: flex;padding: 20rpx;align-items: center;height: 120rpx;">
					<p><text style="font-size: 30rpx;">房型:</text></p>
					<picker @change="bindChangeType" :value="changeTypeIndex" range-key="name" :range="roomTypeList">
						<view class="pickerBox">
							{{roomTypeList[changeTypeIndex].name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>
				<scroll-view scroll-y="true" style="height: 72vh;">
					<view class="roomBoxContent" v-for="(item, index) in allList" :key="index" >
						<p class="title" :style="{color:themeColor.main_color}">{{item.building}}({{item.room_count}})
						</p>
						<view class="roomBoxContentBox" v-for="item1 in item.floor_list" :key="item1">
							<m-divider>{{item1.floor}}({{item1.room_count}})</m-divider>
							<view class="statusBox">
								<view class="room" :style="{background:item2.room_status_color}"
									v-for="item2 in item1.room_list" :key="item2.id" @click="roomStatusGet(item2)">
									<p
										style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;font-size: 24rpx;">
										{{item2.room_number}}({{item2.room_status_name}}-{{item2.clear_status_name}})
									</p>
									<view class="choosed" style="" v-if="chooseId.includes(item2.id)">
										<text class="">已选择</text>
									</view>
								</view>

							</view>
						</view>
					</view>
				</scroll-view>

			</view>
		</m-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				chooseId: [],
				chooseRoom: [],
				roomTypeList:[{id:'',name:'全部'}],
				changeTypeIndex:0,
				allList:[]
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		props: {
			roomList: {
				type: Array,
				default: []
			},
			poprc: {
				type: Boolean,
				default: false
			},
			ids: {
				type: Array,
				default: []
			},
			rooms: {
				type: Array,
				default: []
			}
		},
		watch: {
			poprc: {
				handler(oldValue, newValue) {
					console.log('fangxiong',this.roomList);
					if(this.poprc){
						this.chooseId = this.ids
						this.chooseRoom = this.rooms
						
						
					}else{
						this.chooseId = []
						this.chooseRoom = []
					}
				},
				immediate: true,
				deep:true
			},
			roomList: {
				handler(oldValue, newValue) {
					this.allList = this.roomList
				},
				immediate: true,
				deep:true
			}
		},
		mounted() {
			
			this.$iBox
				.http('bossGetRoomType', {})({
					method: 'post'
				})
				.then(res => {
					
					this.roomTypeList = [...this.roomTypeList,...res.data]
				})
			
		},

		methods: {
			// closePoprc() {
			// 	this.$emit('closeZj', '')
			// 	this.chooseId = []
			// 	this.chooseRoom = []
			// },
			roomStatusGet(e) {
				console.log(e,this.chooseId,this.chooseRoom);
				if (this.chooseId.includes(e.id)) {
					this.chooseId = this.chooseId.filter(item => {
						return item != e.id
					})

					this.chooseRoom = this.chooseRoom.filter(item => {
						return item.id != e.id
					})

				} else {
					this.chooseId.push(e.id)
					this.chooseRoom.push(e)
				}
			},
			bindChangeType(e){
				
				this.changeTypeIndex = e.detail.value[0]
				this.allList = JSON.parse(JSON.stringify(this.roomList)) 
				if(e.detail.value[0] != 0){
					this.allList.forEach(item=>{
						item.floor_list.forEach(item1=>{
							item1.room_list = item1.room_list.filter(item2=>{
								return item2.room_type_id == this.roomTypeList[this.changeTypeIndex].id
							})
						})
					})
				}else {
					this.allList = JSON.parse(JSON.stringify(this.roomList)) 
				}
				
				console.log(e.detail.value[0],this.roomTypeList[this.changeTypeIndex].id,this.allList);
				
			},
			sure() {
				
				let a = {
					ids: this.chooseId,
					rooms: this.chooseRoom
				}
				console.log(a,'a');
				this.$emit('sureRc', a)
				this.$emit('closeZj', '')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.chooseBox {
		height: 80vh;
		width: 100%;
		position: relative;

		.btnBox {
			height: 80rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		.pickerBox {
			margin-top: 20rpx;
			position: relative;
			height: 60rpx;
			width: 280rpx;
			border-radius: 14rpx;
			border: 1px solid #eee;
			display: flex;
			padding: 0 20rpx;
			font-size: 30rpx;
			align-items: center;
		
			.arrow {
				animation-name: to_bottom_show;
				animation-duration: 0.2s;
				animation-timing-function: linear;
				/* animation-delay: 1s; */
				/* animation-iteration-count: infinite; */
				animation-direction: normal;
				animation-play-state: running;
				animation-fill-mode: forwards;
			}
		
			.arrow_ac {
				animation-name: to_up_show;
				animation-duration: 0.2s;
				animation-timing-function: linear;
				/* animation-delay: 1s; */
				/* animation-iteration-count: infinite; */
				animation-direction: normal;
				animation-play-state: running;
				animation-fill-mode: forwards;
			}
		
			/* 箭头动画 */
		
			@keyframes to_up_show {
				0% {
					transform: rotate(0);
				}
		
				50% {
					transform: rotate(90deg);
				}
		
				100% {
					transform: rotate(180deg);
				}
			}
		
			@keyframes to_bottom_show {
				0% {
					transform: rotate(180deg);
					animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
				}
		
				50% {
					transform: rotate(90deg);
					animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
				}
		
				100% {
					transform: rotate(0deg);
				}
			}
		}

		.roomBoxContent {
			width: 100%;
			padding: 20rpx;

			.title {
				// padding: 30rpx 0;
				font-size: 36rpx;
				font-weight: 600;
			}

			display: flex;
			flex-direction: column;

			.roomBoxContentBox {
				width: 100%;

				.statusBox {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					align-items: center;


					.room {
						width: fit-content;
						border-radius: 4rpx;
						padding: 12rpx;
						color: #fff;
						position: relative;
						margin-right: 12rpx;
						margin-top: 16rpx;

						.choosed {
							position: absolute;
							width: 100%;
							height: 100%;
							background: rgba(36, 42, 47, 0.8);
							color: #ffffff;
							border: 2px solid #ff0000;
							font-size: 24rpx;
							font-weight: 600;
							bottom: 0;
							top: 0;
							right: 0;
							left: 0;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}

				}
			}
		}
	}
</style>
