<template>
	<view>
	
		<view class="" style="display: flex;flex-direction: column;align-items: center;">
			<view class="" v-for="item in roomList"
				style="width: 100%;background-color: #FFFFFF;min-height: 222rpx;border-radius: 32rpx;margin-top: 40rpx;padding: 18rpx 32rpx;display: flex;flex-direction: column;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<text style="font-size: 40rpx;">房号：{{item.room_number}}</text>
					<view class=""
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.com_color1,background:themeColor.com_color1+'1A'}">
						<text>{{item.clean_type==1?'续房打扫':(item.clean_type==2?'退房打扫':'客人要求打扫')}}</text>
					</view>
					<view class="" v-if="item.level==1"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>普通</text>
					</view>
					<view class="" v-if="item.level==2"
						style="padding: 8rpx 14rpx;display: flex;align-items: center;justify-content: center;margin-left: 10rpx;"
						:style="{color:themeColor.main_color,background:themeColor.main_color+'1A'}">
						<text>紧急</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class="" style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;"> 
						<p style="margin-right: 30rpx;">生成时间:</p>
						<text>{{item.create_time | moment1}}</text>
					</view>
				</view>
				<view class="" style="margin-top: 20rpx;width: 100%;">
					<view class="" style="display: flex;align-items: center;width: 100%;color: #00000066;font-size: 28rpx;"> 
						<p style="margin-right: 30rpx;">备注:</p>
						<text>{{item.remark?item.remark:'暂无'}}</text>
					</view>
				</view>
				<view class="" style="width: 100%;display: flex;justify-content: flex-end;margin-top: 20rpx;">
					<view class="" @click="sure(item)"
						style="height: 60rpx;width: 152rpx;border-radius: 30rpx;padding: 20rpx;display: flex;align-items: center;justify-content: center;"
						:style="{background:'linear-gradient(90deg, '+ themeColor.bg_main_color+' 0%, ' +themeColor.bg_main1_color +' 100%)'}">
						<text style="color: #FFFFFF;font-size: 28rpx;">领取打扫</text>
					</view>
				</view>
			</view>
		</view>

		<view class="" style="height: 140rpx;">

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				bool: true,
				params: {
					page: 1,
					limit: 10,
					status: 1
				},
				roomList: [],
				index: 0,
				room:null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		watch: {
			timeRange(newval) {
				console.log('范围选:', this.timeRange);
			}
		},
		onShow() {
			this.bool = true
			this.params.status = 1
			this.params.page = 1
			this.getList()

			this.$iBox.http('getRoomCleanAdmin', {})({
				method: 'post'
			}).then(res => {
				let a = []
				res.data.forEach(item => {
					let b = {
						text: item.name + '(' + item.nickname + ')',
						value: item.id
					}
					a.push(b)
				})
				this.range = a
			}).catch(function(error) {
				console.log('网络错误', error)
			})

		},
		methods: {
			sure(e) {
				let params = {
					id: e.id
				}

				this.$iBox.http('userGetRoomClean', params)({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title:'提示',
						content:'领取成功!',
						cancelText:'去打扫',
						confirmText:'继续领取',
						success:res=>{
							if(res.confirm){
								this.params.page = 1
								this.bool = true
								this.getList()
							}else {
								uni.navigateTo({
									url:'/packageB/clearRoom/clearRoom'
								})
							}
						}
					})
					
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},

			getList() {
				this.$iBox.http('getRoomCleanList', this.params)({
					method: 'post'
				}).then(res => {
					this.roomList = res.data.list
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			},
			showAdmin(e) {
				this.pop = true
				this.room = e
			}

		},
		// // 上拉加载
		onReachBottom() {

			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getRoomCleanList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = thisroomListconcat(res.data.list)
					this.roomList = new_list
					if (this.roomList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #f5f5f5;
	}
</style>
<style scoped lang="scss">

</style>