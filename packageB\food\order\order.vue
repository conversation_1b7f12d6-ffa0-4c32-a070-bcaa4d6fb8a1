<template>
	<view>
		<m-tabs :list="list1" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="tab_click"
			:activeIndex="current" :config="{color:themeColor.text_main_color,
						  fontSize:30,
						  activeColor:themeColor.com_color1,
						  underLineColor:themeColor.com_color1,
						  underLineWidth:80,
						  underLineHeight:5}">
		</m-tabs>
		<view class="contentBox">
			<view class=""
				style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
				v-if="bill_list.length==0">
				<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;"
					:style="{color:themeColor.com_color1}">
				</view>
				<p :style="{color:themeColor.com_color1}">暂无订单</p>
			</view>
			<view class="wrapper" v-else>
				<view class="order-list">
					<navigator class="order" v-for="(order, index) in bill_list" @click="toDetail(order)" :key="index"
						open-type="navigate" :url="'/pages/order/detail?id=' + order.id">
						<view class="header">
							<view class="flex-fill font-size-medium">{{ order.shop_name }}</view>
							<view class="status">
								<view>{{billStatus(order.bill_status)}}</view>
								<image src="/static/images/black_arrow_right.png"></image>
							</view>
						</view>
						<scroll-view scroll-x>
							<view class="images">
								<image :src="item.goods_cover_pic" v-for="(item, index) in order.goods_list"
									:key="index"></image>
							</view>
						</scroll-view>
						<view class="info" >
							<view class="" v-if="order.room_number">
								房号:{{order.room_number}}
							</view>
							<view class="" v-if="order.table_number">
								桌号:{{order.table_number}}
							</view>
						</view>
						<view class="info">
							<view class="left">
								<view>订单编号：{{ order.bill_code }}</view>
								<view>下单时间：{{ order.create_time | moment1}}</view>
							</view>
							<view class="right">
								￥{{ order.bill_amount }}
							</view>
						</view>
						<view class="action">
							<!-- 	<button type="default" hover-class="none">开发票</button>
							<button type="default" hover-class="none">查看评论</button> -->
							<view class="" style="display: flex;">
								<button type="default" hover-class="none" v-if="order.bill_status==2" @click.stop="cancelBill(order)">申请取消</button>
								<button type="primary" plain hover-class="none" @click.stop="toDetail(order)">查看详情 </button>
							</view>

						</view>
					</navigator>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list1: [
					{
						id: 1,
						name: '待确认'
					},
					{
						id: 2,
						name: '已确认'
					},{
						id: 3,
						name: '已完成'
					},{
						id: 4,
						name: '申请取消'
					},{
						id:5,
						name: '已取消'
					}
				],
				current: 0,
				params: {
					page: 1,
					limit: 10,
					bill_status: ''
				},
				bill_list: []
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			this.params.bill_status = 2
			this.params.page = 1
			this.getBill()

			},
			async onShow() {
					await this.$onLaunched;

				},
				methods: {
					...mapActions('food', ['getCateringOrderDetail']),
					toDetail(e) {
						this.getCateringOrderDetail(e)
						uni.navigateTo({
							url: './detail'
						})
					},
					billStatus(e) {
						let a = ''
						if (e == 1) {
							a = '待付款'
						} else if (e == 2) {
							a = '待确认'
						} else if (e == 3) {
							a = '已确认'
						} else if (e == 4) {
							a = '已完成'
						}else if (e == 5) {
							a = '已取消'
						}else if (e == 6) {
							a = '申请取消'
						}
						return a
					},
					tab_click(e) {
						console.log(e);
						if (e == 0) {
							this.params.bill_status = 2
						} else if (e == 1) {
							this.params.bill_status = 3
						} else if (e == 2) {
							this.params.bill_status = 4
						} else if (e == 3) {
							this.params.bill_status = 6
						}else if (e == 4) {
							this.params.bill_status = 5
						}
						this.getBill()
					},
					getBill(e) {
						// 查询订单
						uni.showLoading({
							title: '加载中...'
						})
						this.params.page = 1
						this.$iBox.http('getCateringGoodsBillList', this.params)({
							method: 'post'
						}).then(res => {
							this.bill_list = res.data.list
							uni.hideLoading()
						})
					},
					again() {
						uni.navigateTo({
							url: '/packageB/food/food'
						})
					},
					cancelBill(e){
						uni.showModal({
							title:'申请取消',
							content:'是否申请取消本订单?',
							success:(res)=> {
								if(res.confirm){
									this.$iBox.http('toCancelCateringBill', {id:e.id})({
										method: 'post'
									}).then(res => {
										this.params.page = 1
										this.getBill()
										uni.hideLoading()
									})
								}else if(res.cancel){
									
								}
							}
							
						})
					
					}
				},
				// // 上拉加载
				onReachBottom() {

					if (this.bool) {
						++this.params.page
						uni.showLoading({
							title: '加载中...'
						})
						this.$iBox.http('getCateringGoodsBillList', this.params)({
							method: 'post'
						}).then(res => {
							let new_list = this.rechargeList.concat(res.data.list)
							this.rechargeList = new_list
							if (this.rechargeList.length == res.data.count) {
								this.bool = false
							}
							uni.hideLoading()
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					}

				}
		}
</script>

<style lang="scss" scoped>
	.contentBox {
		width: 100%;
		height: auto;
		margin-top: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.wrapper {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;

			.order-list {
				display: flex;
				width: 90%;
				flex-direction: column;

				.order {
					background-color: $bg-color-white;
					padding: 30rpx 40rpx;
					margin-bottom: 18rpx;

					.header {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.status {
							font-size: $font-size-base;
							color: $text-color-grey;
							display: flex;
							align-items: center;

							image {
								width: 30rpx;
								height: 30rpx;
								margin-left: $spacing-row-sm;
							}
						}
					}

					.images {
						display: flex;
						padding: 30rpx 0;

						image {
							flex-shrink: 0;
							width: 150rpx;
							height: 112.5rpx;
						}
					}

					.info {
						display: flex;
						align-items: center;
						margin-bottom: 30rpx;

						.left {
							flex: 1;
							display: flex;
							flex-direction: column;
							font-size: $font-size-base;
							color: $text-color-grey;
						}

						.right {
							font-size: $font-size-lg;
							color: $text-color-base;
						}
					}

					.action {
						display: flex;
						justify-content: flex-end;
						align-items: center;
						width: 100%;

						button {
							margin-left: 30rpx;
							font-size: $font-size-sm;
						}
					}
				}
			}
		}

	}
</style>
