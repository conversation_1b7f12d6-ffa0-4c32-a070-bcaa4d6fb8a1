<template>
	<view class="m-searchBox" :style="{color:themeColor.text_main_color}">
		<view class="" style="display: flex;" v-if="mode==1">
			<view class="m-searchBox_content" :style="{background:themeColor.bg_color}">
				<view class="icon-search" style="font-weight: 400;font-size: 30rpx;color: #74767b;">
				</view>
				<input confirm-type="search" class="m-searchBox_content_input" placeholder="酒店名称" v-model="keyword"/>
			</view>
			<view class="m-searchBox_bt" @click="search" :style="{color:themeColor.com_color1}">
				<text>搜索</text>
			</view>
		</view>
		<view class="m-searchBox_content1" :style="{background:themeColor.bg_color}" v-if="mode==2">
			<view class="" v-if="if_city" style="font-size: 22rpx;padding: 0 10rpx;" @click="getCity">
				<text>{{city.name}}</text>
			</view>
			<view class="icon-hr" v-if="if_city" style="transform:rotate(90deg);" >
			</view>
			<view class="m-searchBox_content1_date" @click="chooseDate">
				<view class="m-searchBox_content1_date_t" >
					<text>住</text>
					<text>{{startDate_show}}</text>
				</view>
				<view class="m-searchBox_content1_date_t" v-if="unit=='standard'">
					<text>离</text>
					<text>{{endDate_show}}</text>
				</view>
			</view>
			<view class="m-searchBox_content1_date_t2" @click="chooseDate" :style="{color:themeColor.main_color}" v-if="unit=='standard'">
				<text>{{countDay_show}}晚</text>
			</view>
			<view class="icon-hr" style="transform:rotate(90deg);" >
			</view>
			<view class="icon-search" style="font-weight: 400;font-size: 30rpx;color: #74767b;">
			</view>
			<input confirm-type="search" class="m-searchBox_content1_input" placeholder="酒店名称" @click="toSearPage">

		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-searchBox",
		data() {
			return {
				keyword:''
			};
		},
		props: {
			mode: {
				type: Number,
				default: 1
			},
			if_city:{
				type:Boolean,
				default:""
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['unit', 'startDate', 'endDate','hotel','city']),
			startDate_show() {
				//正常双日期情况：如果是凌晨显示时间按照昨天-今天的，其他月租房和钟点房则正常
				//有两种情况：1，默认进入如果是凌晨会赋予开始时间为昨天结束时间为今天，2，日历选择凌晨开始时间选择昨天，结束时间为今天
				//首先判断当前时间是否大于开始时间就代表的是选择的凌晨
				if (this.$moment().startOf('day').unix() >this.$moment(this.startDate).format('x')*1) {
					return '凌晨入住'
				} else {
					return this.$moment.unix(this.startDate).format('MM.DD')
				}
			},
			endDate_show() {
				return this.$moment.unix(this.endDate).format('MM.DD')
			},
			countDay_show() {
				let s = this.$moment.unix(this.startDate)
				let e = this.$moment.unix(this.endDate)
				   
				let c = 0
				if (this.startDate == this.endDate) {
					c = 1
				} else {
					c = e.diff(s, 'days')
				}
				return c
			},
		},
		mounted() {
			
		},
		watch: {
			keyword(nVal) {
				// 双向绑定值，让v-model绑定的值双向变化
				this.$emit('toSearch', nVal);
				// 触发change事件，事件效果和v-model双向绑定的效果一样，让用户多一个选择
				this.$emit('change', nVal);
			},
			value: {
				immediate: true,
				handler(nVal) {
					this.keyword = nVal;
				}
			}
		},
		methods: {
			// 目前HX2.6.9 v-model双向绑定无效，故监听input事件获取输入框内容的变化
			inputChange(e) {
				this.keyword = e.detail.value;
				console.log(e, 'key');
			},
			getCity(){
				uni.navigateTo({
					url:'/pages/cityList/cityList'
				})
			},
			search(){
				this.$emit('toSearch',this.keyword)
			},
			toSearPage(){
				uni.navigateTo({
					url:'/pages/searchHotel/searchHotel'
				})
			},
			chooseDate(){
				console.log(this.unit,'ddssssssss');
				if (this.unit=='standard') {
						uni.navigateTo({
							url: '/pages/chooseDate/chooseDate?startDate=' + this.$moment.unix(this.startDate).format('YYYY/MM/DD') + '&endDate=' + this.$moment.unix(this.endDate).format('YYYY/MM/DD') + '&type=2'
						})
				} else if (this.unit =='hour'||this.unit=='conference_room') {
					uni.navigateTo({
						url: '/pages/chooseDate/chooseDate?startDate=' + this.$moment.unix(this.startDate)
							.format('YYYY/MM/DD') + '&type=1'
					})
				
				}else{
					// 长租房有最短日数限制
				}
				
			}
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.m-searchBox {
		position: sticky;
		top: 0;
		padding: 20rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;
		
		// background-color: #FFFFFF;
		z-index: 10;
		// box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
		&_content {
			height: 80rpx;
			width: 600rpx;
			border: 1px solid #eee;
			// background-color: #f2f5f9;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
			padding: 0 20rpx;
			&_input {
				height: 80rpx;
				width: 560rpx;
				border-radius: 40rpx;
				line-height: 80rpx;
				padding: 0 20rpx;
				textAlign: inputAlign;
			}
		}
		
		
		&_content1 {
			height: 80rpx;
			width: 686rpx;
			border: 1px solid #eee;
			// background-color: #f2f5f9;
			border-radius: 40rpx;
			display: flex;
			align-items: center;
		
			padding: 0 10rpx;
			
			&_date {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 0 16rpx;
				&_t {
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 22rpx;
					
				}
				
				&_t2 {
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 22rpx;
					padding: 0 10rpx;
				}
			}
			
			&_input {
				height: 80rpx;
				width: 200rpx;
				border-radius: 40rpx;
				font-size: 24rpx;
				line-height: 80rpx;
				padding: 0 20rpx;
				textAlign: inputAlign;
			}
		}
		&_bt {
			width: 130rpx;
			text-align: center;
			display: flex;
			align-items: center;
			padding-left: 30rpx;
		}
	}
</style>