<template>
	<view>
		<m-tabs :list="list1" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="tab_click"
			:activeIndex="current" :config="{color:themeColor.text_main_color,
						  fontSize:30,
						  activeColor:themeColor.com_color1,
						  underLineColor:themeColor.com_color1,
						  underLineWidth:80,
						  underLineHeight:10}">
		</m-tabs>
		<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="recordList.length==0">
			<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无记录</p>
		</view>
		<view class="listBox" v-for="item in recordList" v-else>
			<view class="title">
				<text style="font-size: 38rpx;font-weight: 600;">申请金额:{{item.amount}}</text> 
				<text style="color: #a2a2a2;">{{item.create_time | moment1}}</text>
			</view>
			<text style="" v-if="item.status==0">待审核</text>
			<text style="color: darkgreen;" v-if="item.status==1">通过</text>
			<text style="color: brown;" v-if="item.status==2">拒绝</text>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list1:[{
						id: 1,
						name: '待审核',
						tip: 0
					},
					{
						id: 2,
						name: '通过',
						tip: 0
					},
					{
						id: 3,
						name: '拒绝',
						tip: 0
					}],
				params:{
					page:1,
					limit:10,
					status:0
				},
				recordList:[],
				bool:true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		async onShow() { 
			this.params.page = 1
			this.params.status = 0
			this.getRecord()
		},
		methods: {
			tab_click(e){
				console.log(e);
				if (e == 0) {
					this.params.status = 0
				} else if (e == 1) {
					this.params.status = 1
				} else if (e == 2) {
					this.params.status = 2
				} 
				this.params.page = 1
				this.getRecord()
			},
			getRecord(){
				// 查询提现记录
				this.$iBox.http('getDistributionWithdrawalBillList', this.params)({
					method: 'post'
				}).then(res => {
					this.recordList = res.data.list
				})
			}
		},
		onReachBottom() {
		
			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getDistributionWithdrawalBillList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.recordList.concat(res.data.list)
					this.recordList = new_list
					if (this.recordList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		
		}
	}
</script>

<style scoped lang="scss">
	.listBox {
		height: 150rpx;
		padding: 30rpx;
		border-bottom: 1px solid #e4e7ed;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: #FFFFFF;
		.title { 
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
		}
	}
</style>
