<template>
	<view class="linkMan">
		<p class="title" v-if="userList.length==0">添加入住人</p>
		<p class="title" v-else>点击选择入住人</p>
		<view class="userList" v-for="item in userList" :key="item.id" @click="choose(item)">
			<text>{{item.name}}</text>
			<text>{{item.phone}}</text>
			<view class="userList_icon">
				<!-- <view class="icon-bianji" @click="editMsg(item)"></view> -->
				<view class="icon-shanchutianchong" @click.stop="delMsg(item)"></view>
			</view>
		</view>
		<view class="box">
			<view class="btn_add" @click="addUser" :style="{background:themeColor.com_color1,color:themeColor.text_main_color}">
				添加常住人
			</view>
		</view>
		
		<!-- 添加弹窗 -->
		<m-popup :show="pop" @closePop="closePop" mode="center">
			<view class="addUser">
				<p>添加常住人</p>
				<view class="nameBox">
					<text style="padding-right: 40rpx;">入住人</text>
					<view class="" style="width: 300rpx;">
						<input type="text" placeholder="请输入入住人姓名" v-model="name" />
					</view>
				</view>
				<view class="nameBox">
					<text style="padding-right: 40rpx;">手机号</text>
					<view class="" style="width: 300rpx;">
						<input type="number" placeholder="请输入手机号码" v-model="phone" />
					</view>
				</view>
				<view class="">
					<view class="btn_add" @click="sureAdd" :style="{background:themeColor.com_color1,color:themeColor.text_main_color}">
						添加常住人
					</view>
				</view>
				
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				userList: [],
				pop:false,
				name:'',
				phone:''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		methods:{
			...mapActions('hotel', ['getLinkMan']),
			closePop(){
				this.pop = false
				this.name = ""
				this.phone = ""
			},
			addUser(){
				this.pop = true
			},
			sureAdd(){
				let user = {}
				user.name = this.name
				user.phone = this.phone
				this.userList.push(user)
				this.pop = false
			},
			choose(e){
				this.getLinkMan(e)
				uni.navigateBack()
			},
			delMsg(e){
				let list = this.userList
				this.userList = list.filter(item=>{
					item.id != e.id
				})
			}
		}

	}
</script>

<style lang="scss" scoped>
	.linkMan {

		.title {
			font-size: 24rpx;
			color: #666;
			padding: 30rpx;
		}

		.userList {
			width: 94%;
			padding: 30rpx;
			margin: 0 auto;
			background: #ffffff;
			border-radius: 14rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			.userList_icon{
				width: 120rpx;
				display: flex;
				align-items: center;
				justify-content: space-around;
				font-size: 40rpx;
			}
		}

		.box {
			position: absolute;
			bottom: 30rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 120rpx;

			.btn_add {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 500rpx;
				height: 80rpx;
				border-radius: 10rpx;
			}
		}
		
		.addUser{
			height: 400rpx;
			width: 600rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			padding: 30rpx;
			.nameBox {
				padding: 30rpx 0;
				display: flex;
				align-items: center;
				border-bottom: 1px solid #e4e7ed;
				// justify-content: space-between;
			}
			
			.btn_add {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 500rpx;
				height: 80rpx;
				border-radius: 10rpx;
			}
		}
	}
</style>
