<template>
	<view class="payBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
		<p style="margin: 0rpx auto;padding: 30rpx 0;font-size: 26rpx;" :style="{color:themeColor.text_main_color}">
			选择一种支付方式</p>

		<radio-group @change="radioChange">
			<label class="wx" v-for="(item, index) in items" :key="item.value" v-if="payType.includes(item.sign)">
				<view class="" style="display: flex;align-items: center;">
					<view :class="item.value" style="font-size: 60rpx;padding-right: 20rpx;"
						:style="index==0?'color: #53C21D;':'color:'+themeColor.main_color"></view>
					<view>{{item.name}}</view>
				</view>

				<view>
					<radio :value="item.value" :checked="index === current" />
				</view>
			</label>
		</radio-group>

		<view class="btn_pay" @click="toPay">
			<view class="btn_style" :style="{background:themeColor.main_color,color:themeColor.bg_color}">
				<text>确认支付</text>
			</view>
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				items: [{
					name: '微信支付',
					value: 'icon-weixin',
					checked: 'true',
					sign:'weixin'
				}, {
					name: '会员支付',
					value: 'icon-huiyuanka'
				}],
				current: 0
			};
		},
		props: {
			payType: {
				type: Array,
				default: ['weixin', 'tongyong', 'duli', 'daodian','point']
			},
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'unit', 'startDate', 'endDate', 'roomInfo', 'linkMan', 'shopSetting']),
		},
		async mounted() {
			await this.$onLaunched;
			// 每次更新重新获取个人信息并保存
			this.$iBox.http('getUserInfo', {
				simple: false
			})({
				method: 'post'
			}).then(resUser => {
				let userInfo = resUser.data
				userInfo.session_key = this.userInfo.session_key
				this.updateUserInfo(userInfo)
				let duli = this.$iBox.getSettingStatus("independent_recharge");
				
				if (duli) {
					this.items[1].name = this.items[1].name + '(通用余额:' + '￥' + userInfo.balance + ')'
					this.items[1].sign = 'tongyong'
					let items = {
						name: '',
						value: 'icon-dingdan'
					}
					console.log(this.items, 'duli');
					if (userInfo.user_shop_balance && userInfo.user_shop_balance.length > 0) {
						userInfo.user_shop_balance.forEach(item => {
							if (item.shop_id == this.hotel.id) {
								items.name = '独立余额 ' + '(' + this.hotel.shop_name + ':￥' + item.balance + ')'
								items.sign = 'duli'
								this.items.push(items)
							}
						})
					}
				
				} else {
					this.items[1].name = this.items[1].name + '(余额:' + userInfo.balance + ')'
					this.items[1].sign = 'tongyong'
				}
				console.log(this.payType,'paytype');
				if(this.payType.includes('daodian')){
					// 是否开启到店付
					this.items.push({
						name: '到店付(线上预订，到店再付)',
						value: 'icon-tanweifeiyong',
						sign:'daodian'
					})
				}
				 
				 if(this.payType.includes('point')){
					// 是否开启积分支付
					this.items.push({
						name: '积分支付(剩余积分:'+this.userInfo.point+')',
						value: 'icon-tanweifeiyong',
						sign:'point'
					})
				}
				
				console.log(this.items,'paytype');
				
			})

			
		},

		methods: {
			...mapActions('login', ['updateUserInfo']),
			radioChange: function(evt) {
				console.log(evt);
				for (let i = 0; i < this.items.length; i++) {
					if (this.items[i].value === evt.detail.value) {
						this.current = i;
						break;
					}
				}
			},
			toPay() {
				this.$emit('toPay',this.items[this.current].sign )
			}
		}

	}
</script>

<style lang="scss" scoped>
	.payBox {
		min-height: 800rpx;
		position: relative;
		width: 100%;
		padding: 20rpx;
		z-index: 9999999999;

		.wx {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
		}

		.btn_pay {
			position: absolute;
			bottom: 0;
			width: 100%;

			.btn_style {
				border-radius: 50rpx;
				width: 80%;
				margin: 30rpx auto;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 20rpx;
				height: 100rpx;
			}
		}
	}
</style>