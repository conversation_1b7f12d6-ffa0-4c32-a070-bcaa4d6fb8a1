<template>
	<view>
		<view class="style_mode">
			<view class="box">
				<p :style="{color:themeColor.bg_color}" style="display: flex;align-items: center;">
					<text class="icon-dengji" style="font-size: 50rpx;"></text>
					<text>{{userInfo.grade_name}}</text>
				</p>
				<view class="qrBox">
					<image :src="url" style="height: 350rpx;width: 350rpx;" mode=""></image>
				</view>
				<p :style="{color:themeColor.tex_main_color}">ID:{{userInfo.code}}</p>
				<p :style="{color:themeColor.tex_main_color}" style="display: flex;align-items: center;margin-top: 10rpx;"><uni-icons type="refreshempty" size="23" :color="themeColor.tex_main_color" @click="refresh"></uni-icons>刷新</p>
			</view>
			<view class="box1" >
				<view class="item" @click="tobalance">
					<text>积分</text>
					<text>{{userInfo.point}}</text>
				</view>
				<view class="item" @click="recharge">
					<text>通用余额</text>
					<text>￥{{userInfo.balance}}</text>
				</view>
			</view>
		</view>
		
		<view class="" style="width: 100%;display: flex;align-items: center;justify-content: center;margin-top: 40rpx;" @click="recharge">
			<view class="" style="height: 100rpx;width: 700rpx;border-radius: 34rpx;color: #FFFFFF;display: flex;align-items: center;justify-content: center;" :style="{background:themeColor.main_color}">
				<text>会员充值</text>
			</view>
			
		</view>
		
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	import QR from '@/plugins/wxqrcode.js';
	export default {
		data() {
			return {
				url:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		async onShow() {
			await this.$onLaunched;
			if(this.userInfo.code){
				let gd = ''
				var randNum =  Math.floor(Math.random() * 17) + 1
				if(randNum<10){
					gd = '0' + randNum
				}else {
					gd = randNum
				}
				
				let qr =gd + this.userInfo.code.slice(0,randNum) + this.$moment().unix().toString().substring(4,10) + this.userInfo.code.slice(randNum,18)
				
				
				console.log(randNum,qr);
				this.url = QR.createQrCodeImg(qr)
			}
		},
		methods: {
			recharge(){
				uni.navigateTo({
					url:'/pages/recharge/recharge'
				})
			},
			tobalance(){
				uni.navigateTo({
					url:'/pages/pointList/pointList'
				})
			},
			refresh(){
				let gd = ''
				var randNum =  Math.floor(Math.random() * 17) + 1
				if(randNum<10){
					gd = '0' + randNum
				}else {
					gd = randNum
				}
				
				let qr =gd + this.userInfo.code.slice(0,randNum) + this.$moment().unix().toString().substring(4,10) + this.userInfo.code.slice(randNum,18)
				// console.log(qr);
				this.url = QR.createQrCodeImg(qr)
			}
		}
	}
</script>

<style scoped lang="scss">
	.style_mode {
		.box {
			// height: 500rpx;
			width: 700rpx;
			margin: 30rpx auto;
			border-radius: 30rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 30rpx;
		}
		
		.qrBox {
			width: 400rpx;
			height: 400rpx;
			margin: 20rpx auto;
			background-color: #ffffff;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.box1 {
			min-height: 100rpx;
			width: 700rpx;
			margin: 30rpx auto;
			border-radius: 30rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 30rpx;
			background-color: #ffffff;
			.item {
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: 80rpx;
				width: 100%;
			}
		}
	}
	
</style>
