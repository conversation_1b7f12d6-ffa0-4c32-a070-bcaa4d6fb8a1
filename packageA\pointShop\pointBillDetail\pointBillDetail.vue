<template>
	<view class="container">
		<view class="header">
			<button type="default" plain class="service-btn" @click="callPhone">
				<view class="icon-kefu" ></view>
				<text>联系客服</text>
			</button>
		</view>
		<view class="content" v-if="Object.keys(order).length>0">
			<view class="order-box">
				<view class="icon-tanweifeiyong status-icon" style="font-size: 84rpx;" :style="{color:themeColor.main_color}"></view>
				<view class="" :style="{color:themeColor.main_color}" style="font-size: 44rpx;font-weight: 600;">{{order.bill_status==2?'订单正在确认请等待~':(order.bill_status==3?'待发货':(order.bill_status==4?'已发货':(order.bill_status==5?'已收货':(order.bill_status==6?'已完成':(order.bill_status==7?'订单已取消':'申请取消中')))))}}</view>
				<view class="" style="font-size: 28rpx;">欢迎再次购买</view>
				<button type="primary" plain hover-class="none" @click.stop="sureGet(order)" size="mini" v-if="order.bill_status==4" style="margin-top: 8rpx;">确认收货</button>
			</view>
			<view class="flex-fill overflow-auto">
				<view class="order-info">
					<list-cell padding="50rpx 40rpx" line-right bgcolor="#FFFFFF"> 
						<view class="addressBox" v-if="order.delivery_type==1">
							<view class="item1">
								<text>{{order.address}}</text>
							</view>
							<view class="item2">
								<text>{{order.user_name}}</text>
								<text style="margin-left: 40rpx;">{{order.user_phone}}</text>
							</view>
							<p @click="goLogistics(order.id)" v-if="order.bill_status==4||order.bill_status==5||order.bill_status==6" style="margin-top: 10rpx;color: darkgreen;">查看物流信息></p>
						</view>
						<view class="" v-if="order.delivery_type==2">
							<view class="" style="font-size: 40rpx;">
								前台自提
							</view>
						</view>
						<view class="" v-if="order.delivery_type==3">
							<view class="" style="font-size: 40rpx;">
								房号:{{order.room_number}}
							</view>
						</view>
					</list-cell> 
					<list-cell padding="0 40rpx" line-right bgcolor="#ffffff">
						<view class="" style="width: 100%;display: flex;flex-direction: column;background-color: #FFFFFF;">
							<view class="" style="display: flex;align-items: center;margin-top: 40rpx;overflow: hidden;" v-for="(item, index) in order.goods_list">
								<view class="" style="display: flex;flex-direction: column;width: 70%;justify-content: center;">
									<image :src="item.cover_pic" mode="" style="width: 60rpx;height: 60rpx;"></image>
									<view class="" style="">{{ item.goods_name }}</view>
								</view>
								<view style="display: flex;flex-shrink: 0;font-weight: 600;margin-left: 40rpx;" class="">x{{ item.goods_count }}</view>
								<view style="display: flex;flex-shrink: 0;font-weight: 600;margin-left: 40rpx;">{{item.goods_point}}积分
								<text v-if="item.goods_pric>0">+{{ item.goods_price }}元</text>
								</view>
							</view>
				
							<view style="display: flex;justify-content: space-between;align-items: center;font-size: 40rpx;font-weight: 600;">
								<view>合计</view>
								<view style="font-size: 30rpx;">{{ order.point_amount }}积分
								
								
								<text v-if="order.bill_amount > 0">+{{ order.bill_amount }}元</text> </view>
							</view>
						</view>
					</list-cell>
					<list-cell padding="30rpx 40rpx" last bgcolor="#FFFFFF" style="border-radius: 0 0 30rpx 30rpx;">
						<view class="" style="width: 100%;display: flex;flex-direction: column;font-size: 24rpx;color: #707070;background-color: #FFFFFF;">
							<view style="margin-bottom: 10rpx;">如需退款，请致电客服</view>
							<view style="margin-bottom: 10rpx;">下单时间：{{ order.create_time | moment }}</view>
							<view style="margin-bottom: 10rpx;">订单编号：{{ order.bill_code }}</view>
						</view>
					</list-cell>
				</view>
				<view class="order-invoice" @click="invoiceTo">
					<view>订单发票</view>
					<view class="" style="display: flex;align-items: center;" >
						<view>立即开票</view>
						<image src="/static/images/common_icon_jump_gold.png" class="jump-icon"></image>
					</view>
				</view>
				 
				<view class="order-invoice" @click="afterService" v-if="order.bill_status==4||order.bill_status==5">
					<view style="color: brown;">订单有问题?</view>
					<view class="" style="display: flex;align-items: center;color: brown;" >
						<view>申请售后</view>
						<image src="/static/images/common_icon_jump_gold.png" class="jump-icon"></image>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import listCell from '@/components/m-list-cell/m-list-cell.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		components: {
			listCell
		},
		data() {
			return {
				order: {}
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel']),
			...mapState('pointShop', ['pointShopDetail']),
			materialsText() {
				return materials => {
					let arr = []
					materials.forEach(item => arr.push(item.name))
					return arr.join(',')
				}
			}
		},
		async onLoad(options) {
			/* 为了方便测试，这里使用同一个订单数据 */
			console.log(this.pointShopDetail);
			this.order = this.pointShopDetail
		},
		methods:{
			callPhone(){
				uni.navigateTo({
					url:'/packageA/customser/customser'
				})
			},
			invoiceTo(){
				uni.navigateTo({
					url:'/packageA/invoiceBills/invoiceBills'
				})
			},
			goLogistics(e){
				uni.navigateTo({
					url:'/packageA/logistics/logistics?id=' + e
				})
			},
			sureGet(e){
				
				this.$iBox.http('takePointMallGoods', {id:e.id})({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title:'提示',
						content:'确认收货成功!',
						showCancel:false,
						success() {
							uni.navigateBack()
						}
					})
					
				})
			},
			afterService(){
				uni.navigateTo({
					url:'/packageA/afterService/afterService'
				})
			}
			
		}
	}
</script>

<style lang="scss" scoped>
.container {
	background-color: #EAEAEA;
	padding: 0 40rpx;
	display: flex;
	flex-direction: column;
}

.header {
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding: 20rpx 0;

	.service-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		border-radius: 50rem !important;
		font-size: $font-size-sm;
		image {
			width: 30rpx;
			height: 30rpx;
			margin-right: 20rpx;
		}
	}	
}

.content {
	flex: 1;
	display: flex;
	flex-direction: column;
	overflow: hidden;
	
	.order-box {
		z-index: 10;
		height: 25vh;
		border-radius: 30rpx 30rpx 0 0;
		box-shadow: 0 0 20rpx 0 rgba($color: #333, $alpha: 0.1);
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		background-color: $bg-color-white;
		flex-shrink: 0;
		
		.status-icon {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100rpx;
			height: 100rpx;
			margin-bottom: 30rpx;
		}
	}
}

.phone-icon, .map-icon {
	width: 64rpx;
	height: 64rpx;
}

.phone-icon {
	margin-right: 40rpx;
}

.border-dashed {
	border-bottom: 1rpx dashed $border-color;
}

.order-info {
	border-radius: 0 0 30rpx 30rpx;
	box-shadow: 0 0 10rpx 0 rgba($color: #333, $alpha: 0.1);
	margin-bottom: 30rpx;
	background-color: $bg-color-white;
	.addressBox {
			width: 100%;
			height: 170rpx;
			padding: 20rpx;
			position: relative;
			line-height: 44rpx;
			border-bottom: 1px solid #eee;

			.item {
				font-size: 24rpx;
				color: #7f7f7f;
				overflow: hidden;
			}

			.item1 {
				font-size: 30rpx;
				color: #000000;
				font-weight: 600;
				overflow: hidden;
			}

			.item2 {
				display: flex;
				align-items: center;
				font-size: 26rpx;
			}

			.edit {
				position: absolute;
				right: 10rpx;
				height: 100%;
				top: 0;
				display: flex;
				align-items: center;
			}
		}
}

.order-invoice {
	padding: 30rpx 40rpx;
	border-radius: 30rpx;
	box-shadow: 0 0 10rpx 0 rgba($color: #333, $alpha: 0.1);
	margin-bottom: 60rpx;
	background-color: #ffffff;
	display: flex;
	justify-content: space-between;
	align-items: center;
	font-size: 30rpx;
	  
}

.jump-icon {
	width: 24rpx;
	height: 48rpx;
	margin-left: 20rpx;
}
</style>
