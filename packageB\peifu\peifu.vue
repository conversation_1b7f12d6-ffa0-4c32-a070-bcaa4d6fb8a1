<template>
	<view>
		<mpeiFu :billId="bill_id"></mpeiFu>
	</view>
</template>

<script>
	import mpeiFu from '../components/m-peiFu/m-peifu.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				bill_id:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager'])
		},
		components:{
			mpeiFu
		},
		async onShow() {
			await this.$onLaunched;
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options,'ll');
			this.bill_id = options.id
		},
		methods: {

		},
		onReachBottom() {
			
		}
	}
</script>

<style>

</style>
<style scoped lang="scss">

</style>