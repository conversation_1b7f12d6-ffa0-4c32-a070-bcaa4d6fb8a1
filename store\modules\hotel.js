import Vue from 'vue'

const state = {
	timeSlot: '',
	unit:'', //全局记录售卖模式
	startDate:"",//全局开始时间
	endDate:'',//全局结束时间
	roomInfo:{},//预定的房间信息
	hotelList:[], //酒店列表
	saleTypes:[],//售卖方式
	hotel:{},//选择的酒店,
	city:{},//选择的城市
	cityList:[],//酒店城市列表
	cityModel:0,//0不是城市模式，1城市模式
	linkMan:null,//长住人
	roomData:{},//订房数据
	setting:{}, //全局设置
	shopSetting:{},//酒店设置
	hotelBillDetail:{},//酒店订单详情
	roles_list:[], //权限列表
	manager:{}, //选择的管理员	
	gradeList:[],//会员等级设置信息
	addressDetail:null,//选择地址
	shareId:null,
	active:null
}

const getters = {
	
}

const mutations = {
	PUSHTIMESLOT: (state, timeSlot) => {
		state.timeSlot = timeSlot
	},
	PUSHUNIT: (state, unit) => {
		state.unit = unit
	},
	PUSHDATE: (state, date) => {
		state.startDate = date.startDate
		state.endDate = date.endDate
	},
	PUSHROOMINFO: (state, roomInfo) => {
		state.roomInfo = roomInfo
	},
	PUSHHOTELLIST:(state, hotelList) => {
		state.hotelList = hotelList
	},
	PUSHHOTEL:(state, hotel) => {
		console.log(hotel,'l');
		state.hotel = hotel
	},
	PUSHCITY:(state, city) => {
		state.city = city
	},
	PUSHCITYLIST:(state, cityList) => {
		state.cityList = cityList
	},
	PUSHCITYMODEL:(state,cityModel)=>{
		state.cityModel= cityModel
	},
	PUSHLINKMAN:(state,linkMan)=>{
		state.linkMan= linkMan
	},
	PUSHSALETYPES:(state,saleTypes)=>{
		state.saleTypes= saleTypes
	},
	PUSHROOMDATA:(state,roomData)=>{
		state.roomData= roomData
	},
	PUSHSETTING:(state,setting)=>{
		state.setting= setting
	},
	PUSHSHOPSETTING:(state,shopSetting)=>{
		state.shopSetting= shopSetting
	},
	PUSHHOTELBILLDETAIL:(state,hotelBillDetail)=>{
		state.hotelBillDetail= hotelBillDetail
	},
	PUSHROLE: (state, roles_list) => {
		state.roles_list = roles_list
	},
	PUSHMANAGER:(state, manager) => {
		state.manager = manager
	},
	PUSHGRADE:(state, gradeList) => {
		state.gradeList = gradeList
	},
	PUSHADDRESS:(state, addressDetail) => {
		state.addressDetail = addressDetail
	},
	PUSHSHAREID:(state, shareId) => {
		state.shareId = shareId
	},
	PUSHACTIVE:(state, active) => {
		state.active = active
	},
}

const actions = {
	// 登录
	
	getTimeSlot({
		commit
	}, params) {
		commit('PUSHTIMESLOT', params)
	},
	getUnit({
		commit
	}, params) {
		commit('PUSHUNIT', params)
	},
	getChooseDate({
		commit
	}, params) {
		console.log(params,'params');
		commit('PUSHDATE', params)
	},
	getRoomInfo({
		commit
	}, params) {
		
		commit('PUSHROOMINFO', params)
	},
	getHotelList({
		commit
	}, params) {
		commit('PUSHHOTELLIST', params)
	},
	getHotel({
		commit
	}, params) {
		commit('PUSHHOTEL', params)
	},
	getCity({
		commit
	}, params) {
		commit('PUSHCITY', params)
	},
	getCityList({
		commit
	}, params) {
		commit('PUSHCITYLIST', params)
	},
	getCityModel({
		commit
	}, params) {
		commit('PUSHCITYMODEL', params)
	},
	getLinkMan({
		commit
	}, params) {
		commit('PUSHLINKMAN', params)
	},
	
	getSaleTypes({
		commit
	}, params) {
		commit('PUSHSALETYPES', params)
	},
	
	getRoomData({
		commit
	}, params) {
		commit('PUSHROOMDATA', params)
	},
	getSetting({
		commit
	}, params) {
		commit('PUSHSETTING', params)
	},
	getShopSetting({commit},params){
		commit('PUSHSHOPSETTING',params)
	},
	getHotelBillDetail({commit},params){
		commit('PUSHHOTELBILLDETAIL',params)
	},
	// 存储权限
	getRole({commit},params){
		commit('PUSHROLE',params)
	},
	
	getManager({commit},params){
		commit('PUSHMANAGER',params)
	},
	getGrade({commit},params){
		commit('PUSHGRADE',params)
	},
	getAddress({commit},params){
		commit('PUSHADDRESS',params)
	},
	
	getShareId({commit},params){
		commit('PUSHSHAREID',params)
	},
	
	getActive({commit},params){
		commit('PUSHACTIVE',params)
	},
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}
