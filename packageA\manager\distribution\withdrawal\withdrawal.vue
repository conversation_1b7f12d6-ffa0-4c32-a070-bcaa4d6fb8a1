<template>
	<view>
		<view class="cashBox" style="">
			<text class="" style="margin-left: 20rpx;font-size: 40rpx;">提现金额</text>
			<view class="" style="width:90vw;margin: 0 auto;border-bottom: 1px solid #ccc;display: flex;align-items: center;margin-top: 30rpx;">
				<text class="" style="height: 120rpx;font-size: 60rpx;font-weight: 600;display: flex;align-items: center;">￥</text><input type="decimal"
				 style="font-size: 60rpx;height: 120rpx;width:400rpx;" placeholder="0" v-model="money" @keyup="checkInput($event)" />
			</view>
			<view class="" style="margin: 30rpx;">
				<text style="color: #909399;">可提现金额:￥{{ke_tixian}}</text>
			</view>
			
			<view class="" style="display: flex;justify-content: center;width: 100%;margin-top: 40rpx;">
				<button  style="width: 600rpx;" type="primary" @click="tixianTh">申请提现</button>
			</view>
		</view>
		<!-- <view class="" style="color:#909399;display: flex;flex-direction: column;margin: 30rpx;">
			<text class="" style="font-weight: 600;">提现规则：</text>
			<text>满{{min_amount}}元起提</text>
			<text v-if="whole==1">提现金额必须为{{whole_number}}元的倍数</text>
		</view> -->
		
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				money: '',
				list: '',
				ke_tixian: 0,
				// size: [],
				// whole_number: 0,
				// whole: 0,
				// min_amount: 0,
				// max_amount: 0
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		onLoad() {
			this.$iBox.http('getDistributionInfo',{})({
				method: 'post'
			}).then(res => {
				// 同步线上线下用户信息用户信息
				this.ke_tixian = res.data.distribution_amount
			}).catch(function(error) {
				console.log('网络错误', error)
			})

		},
		methods: {
			...mapActions('hotel', ['getCenterMsg', 'getDistribution']),

			// 申请提现 - 防重复触发版本
			tixianTh() {
				this.$iBox.throttle1(() => {
					this.tixian()
				}, 2000);
			},

			tixian() {

				var reg = /^[-?\d]+(.)?(\d{1,2})?/; //匹配正则
				if (this.money == '' || this.money == 0) {
					uni.showToast({
						icon: 'none',
						title: '请输入提现金额！'
					})
				} else if (!reg.test(this.money)) {
					uni.showToast({
						icon: 'none',
						title: '请输入正确的金额，最多保留两位小数！'
					})
				} else if (parseFloat(this.money) > parseFloat(this.ke_tixian)) {
					uni.showToast({
						icon: 'none',
						title: '提现金额大于可提现金额！'
					})
				} else {
					// 显示加载状态
					uni.showLoading({
						title: '申请中...'
					});

					this.$iBox.http('addDistributionWithdrawalBill',{amount:this.money})({
						method: 'post'
					}).then(res => {
						// 同步线上线下用户信息用户信息
						uni.hideLoading();
						uni.showToast({
							icon:'success',
							title:'申请成功',
							duration:600
						})
						this.money = 0
						this.$iBox.http('getDistributionInfo',{})({
							method: 'post'
						}).then(res => {
							// 同步线上线下用户信息用户信息
							this.ke_tixian = res.data.distribution_amount
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					}).catch(function(error) {
						uni.hideLoading();
						console.log('申请提现失败:', error)
					})

				}

			}
		}
	}
</script>

<style scoped>
	page {
		background: #f3f4f6;
	}
</style>

<style lang="scss" scoped>
	.cashBox{
		height: 560rpx;
		background-color: #FFFFFF;
		padding: 30rpx;
	}
</style>