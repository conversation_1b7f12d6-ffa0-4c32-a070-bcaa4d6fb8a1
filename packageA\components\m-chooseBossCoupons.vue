<template>
	<view class="content">
		<view class="" style="padding: 30rpx;display: flex;align-items: center;justify-content: space-between;">
			<p style="font-weight: 600;font-size: 40rpx;">优惠券</p>
			<!-- <view class="icon-close" style="font-size: 40rpx;">
			</view> -->
		</view>

		<view class="tabHeads">
			<view class="tabLabel" @click="tabsClick(item)" v-for="(item,index) in tabsArray" :key="index">
				<view :class="[isNum === item.inx?'isTabActive':'default']"
					:style="isNum === item.inx?'color:#000;font-weight:700;border-bottom: 2px solid '+themeColor.main_color:'default'">
					{{item.name}}
				</view>
			</view>
		</view>

		<scroll-view style="height:900rpx" :scroll-top="scrollTop" scroll-y="true">
			<view class="couponMain">
				<view class=""
					style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
					v-if="coupons.length==0">
					<view class="icon-zanwuyouhuiquan1" style="font-size: 260rpx;color: #c1c1c1;">
					</view>
					<p style="color: #c1c1c1;">暂无优惠券</p>
				</view>
				<checkbox-group @change="checkboxChange" v-else>
					<view class="coupon" v-for="(item,index) in coupons" :key="index">

						<view class="couponTop"
							:class="[isNum ===1	?item.coupon_info.type_id ===1?'bgColorTop1':item.coupon_info.type_id ===2?'bgColorTop1':'bgColorTop1':'bgColorTop1']">
							<view class="couponTopLeft">
								<view class="couponTypeDiv">
									<view class="tpyeNameSty"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'useBtnBgColor1':item.coupon_info.type_id ===2?'useBtnBgColor1':'useBtnBgColor1':'useBtnBgColor1']">
										{{item.coupon_info.type_id ===1?'订房券':item.coupon_info.type_id ===2?'超市券':(item.coupon_info.type_id ===3?'周边券':(item.coupon_info.type_id ===4?'点餐券':'商城券'))}}
									</view>
								</view>
								<view class="valueSty"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'color1':item.coupon_info.type_id ===2?'color1':'color1':'color1']">
									<text v-if="item.coupon_info.type_id ===1" class="symbolMoney">￥</text>
									<text v-if="item.coupon_info.type_id ===1"
										class="moneyVal">{{item.coupon_info.discounts}}</text>
									<!-- <text v-if="item.type_id ===2" class="moneyVal">{{item.discount}}</text>
								<text v-if="item.type_id ===2" style="font-size: 14px;">折</text>
								<text v-if="item.type_id ===3" class="moneyVal">{{item.num}}</text>
								<text v-if="item.type_id ===3" style="font-size: 14px;">件</text> -->
								</view>
								<view v-if="item.coupon_info.use_condition ===0 " class="useCondition"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'borderColor1 color1':item.coupon_info.type_id ===2?'borderColor1 color1':'borderColor1 color1':'borderColor1 color1']">
									无门槛使用
								</view>
								<view v-else class="useCondition"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'borderColor1 color1':item.coupon_info.type_id ===2?'borderColor1 color1':'borderColor1 color1':'borderColor1 color1']">
									<text>满{{item.coupon_info.use_condition}}元可用</text>
									<!-- <text>{{item.type_id ===3?'兑':'用'}}</text> -->
								</view>
							</view>
							<view class="couponTopRight">
								<view class="ctr-left">
									<view class="couponName"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'color1':item.coupon_info.type_id ===2?'color1':'color1':'color1']">
										{{item.coupon_info.name}}
									</view>
									<view class="couponStore"
										:class="[isNum ===1?item.coupon_info.coupon_info.type_id ===1?'color2':item.coupon_info.coupon_info.type_id ===2?'color2':'color2':'color2']">
										{{item.coupon_info.desc}}
									</view>
									<view class="couponDate"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color2':'color2':'color2']">
										{{item.coupon_info.start_time | moment}}~{{item.coupon_info.end_time | moment}}
									</view>
								</view>
								<view class="ctr-right">

									<label>
										<checkbox v-if="isNum ===1" class="round" :checked="item.ifChecked"
											:value="item.id" />
									</label>
								</view>
							</view>
						</view>

						<view class="couponBottom"
							:class="[isNum ===1?item.coupon_info.type_id ===1?'bgColorTBottom1':item.coupon_info.type_id ===2?'bgColorTBottom1':'bgColorTBottom1':'bgColorTBottom1']">
							<view class="ruleLabel">
								<view class="ruleLabel-left">
									<text class="overlay"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'color1 bgColor1':item.coupon_info.type_id ===2?'color1 bgColor21':'color1 bgColor1':'color1 bgColor1']">
										{{item.coupon_info.usable_date.length > 0?'使用日期限制':'不限日期'}}
									</text>
									<text class="limit"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'color1':item.coupon_info.type_id ===2?'color1':'color1':'color1']">
										{{item.coupon_info.usable_time?'使用时间限制':'不限使用时间'}}
									</text>
									<text class="limit"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'color1':item.coupon_info.type_id ===2?'color1':'color1':'color1']">/</text>
									<text class="limit"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'color1':item.coupon_info.type_id ===2?'color1':'color1':'color1']">
										{{item.coupon_info.usable_week.length > 0?'使用星期限制':'不限使用星期'}}
									</text>
								</view>
								<view class="ruleBtn"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color2':'color2':'color2']"
									@click.stop="viewRules(item)">
									<text style="margin-right: 6px;">使用规则</text>
									<view class="arrowIcon"
										:class="[item.coupon_info.isViewRule?isNum ===1?item.coupon_info.type_id ===1?'rotate arrowIcon1':item.coupon_info.type_id ===2?'rotate arrowIcon1':'rotate arrowIcon1':'rotate arrowIcon1':isNum ===1?item.coupon_info.type_id ===1?'backRotate arrowIcon1':item.coupon_info.type_id ===2?'backRotate arrowIcon2':'backRotate arrowIcon3':'backRotate arrowIcon4']">
									</view>
								</view>
							</view>
							<view v-if="priceIndex.includes(item.coupon_info.id)" class="ruleDetail"
								:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color2':'color2':'color2']">
								<view class="ruleList" v-if="item.coupon_info.usable_date.length > 0">
									使用日期：<text v-for="(item1, index1) in item.coupon_info.usable_date" :key="index1">{{item1}}可用</text>
								</view>
								<view class="ruleList" v-if="item.coupon_info.usable_time">
									使用时间:{{item.coupon_info.usable_time}}前可用
								</view>
								<view class="ruleList" v-if="item.coupon_info.usable_week.length > 0">
									使用星期: <text style="margin-right: 10rpx;"
										v-for="(item1, index1) in item.coupon_info.usable_week" :key="index1">星期{{item1==1?'一':(item1==2?'二':(item1==3?'三':(item1==4?'四':(item1==5?'五':(item1==6?'六':'日')))))}}可用</text>
								</view>
							</view>
						</view>
					</view>
				</checkbox-group>
			</view>
		</scroll-view>
		<view class="" style="width: 500rpx;position: absolute;bottom: 20rpx;left: 0;right: 0;margin: 0 auto;"
			v-if="isNum ===1">
			<view class="btn_register" :style="{background:themeColor.com_color1,color:themeColor.bg_color}"
				@click="register">确定</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				wHeight: 0,
				scrollTop: 0,
				isNum: 1,
				tabsArray: [{
					inx: 1,
					name: '可用优惠券'
				}, {
					inx: 2,
					name: '不可用优惠券'
				}],
				priceIndex: [],
				coupons: [],
				num1: 0,
				num2: 0
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		props: {
			coupType: {
				type: Number
			},
			limitNum: {
				type: Number
			},
			list: {
				type: Array
			}
		},
		watch: {
			limitNum: {
				handler(newVal, oldVal) {

					let coupons = this.list.forEach(item => {
						item.ifChecked = false
					})
					this.coupons = coupons

					this.coupons = this.list.filter(item => {
						console.log(item.coupon_info.use_condition, item.coupon_info.type_id, this.coupType);
						return this.coupType == item.coupon_info.type_id && Number(item.coupon_info
							.use_condition) <= this.limitNum
					})

					let coupons1 = this.list.filter(item => {
						return this.coupType != item.coupon_info.type_id || Number(item.coupon_info
							.use_condition) > this.limitNum
					})
					// 默认显示可以使用的优惠券
					// coupType==item.coupon_info.type_id&&item.coupon_info.use_condition <= limitNum
					console.log(this.coupons.length, 'limitNum');
					this.num1 = this.coupons.length
					this.num2 = coupons1.length
				},
				immediate: true,
				deep: true
			}
		},
		mounted() {


		},
		methods: {
			tabsClick(e) {
				this.isNum = e.inx
				let coupons = this.list.forEach(item => {
					item.ifChecked = false
				})
				this.coupons = coupons
				if (e.inx == 1) {
					this.coupons = this.list.filter(item => {
						return this.coupType == item.coupon_info.type_id && Number(item.coupon_info
							.use_condition) <= this.limitNum
					})
					// this.tabsArray[e.inx-1].name = this.tabsArray[e.inx-1].name.split('(')[0] + '('+ this.coupons.length +')'
					this.num1 = this.coupons.length
				} else {
					this.coupons = this.list.filter(item => {

						return this.coupType != item.coupon_info.type_id || Number(item.coupon_info
							.use_condition) > this.limitNum
					})
					// this.tabsArray[e.inx-1].name = this.tabsArray[e.inx-1].name.split('(')[0] + '('+ this.coupons.length +')'
					this.num2 = this.coupons.length
				}

			},

			checkboxChange(e) {
				console.log(e);
				if (e.detail.value.length == 1) {
					this.coupons.forEach(item => {
						if (item.id == e.detail.value[0]) {
							item.ifChecked = true
						} else {
							item.ifChecked = false
						}
					})
				} else if (e.detail.value.length == 2) {
					this.coupons.forEach(item => {
						if (item.id == e.detail.value[1]) {
							item.ifChecked = true
						} else {
							item.ifChecked = false
						}
					})
				}
			},
			register() {
				let chooseCoupon = this.coupons.filter(item => {
					return item.ifChecked == true
				})[0].coupon_info

				this.$emit('getCouponIfo', chooseCoupon)
			},
			viewRules(e) {
				if (this.priceIndex.includes(e.coupon_info.id)) {
					this.priceIndex = this.priceIndex.filter(item => {
						return item != e.coupon_info.id
					})
				} else {
					this.priceIndex.push(e.coupon_info.id)
				}
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 0;
		overflow: hidden;
		// height:800rpx;
		position: relative;
	}

	.tabHeads {
		display: flex;
		background-color: #fff;
		margin-bottom: 1px;
	}

	.tabLabel {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
	}

	.default {
		color: #999999;
		padding: 10px 0;
		border-bottom: 1px solid #ffffff;
	}

	.isTabActive {
		padding: 10px 0;
	}

	.headWrap {
		padding: 10px;
		background-color: #fff;
		display: flex;
		align-items: center;
		/* justify-content: space-between; */
		flex-wrap: wrap;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
	}

	.labelDiv {
		width: 25%;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;

		.labelDivItem {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.active {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			// background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

	}



	.couponMain {
		padding: 10px;
		display: flex;
		flex-direction: column;
		// background: #ffffff;
	}

	.coupon {
		margin-bottom: 20px;
	}

	.couponTop {
		display: flex;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
	}

	.couponTopLeft {
		flex: 1.2;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		position: relative;
		overflow: hidden;
	}

	.couponTypeDiv {
		/* overflow: hidden; */
		/* position: relative; */
	}

	.imgType {
		width: 46px;
		height: 40px;
		position: absolute;
		top: 0;
		left: 0;
	}

	.tpyeNameSty {
		font-size: 19rpx;
		transform: rotate(-40deg);
		position: absolute;
		top: 10rpx;
		left: -52rpx;
		background-color: grey; // 背景色
		color: #fff;
		// 以下属性会影响斜边标签的显示
		width: 80%;
		height: 18px;
		line-height: 18px;
		transform: rotate(-40deg);
		text-align: center;
	}

	.valueSty {
		margin-bottom: 10px;
	}

	.symbolMoney {
		font-size: 16px;
	}

	.moneyVal {
		font-size: 24px;
	}

	.useCondition {
		font-size: 10px;
		padding: 4px 10px;
		border-radius: 12px;
	}

	.couponTopRight {
		flex: 3;
		display: flex;
		padding-bottom: 12px;
	}

	.ctr-left {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.couponName {
		font-size: 16px;
		padding: 14px 0;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.couponStore {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
		padding-bottom: 8px;
	}

	.couponDate {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.ctr-right {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10px;
	}

	.useBtn {
		color: #fff;
		font-size: 12px;
		padding: 6px 20px;
		border-radius: 18px;
	}

	.couponBottom {
		display: flex;
		flex-direction: column;
		padding: 10px;
		border-bottom-left-radius: 4px;
		border-bottom-right-radius: 4px;
	}

	.ruleLabel {
		display: flex;
		align-items: center;
	}

	.ruleLabel-left {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.overlay {
		margin-right: 10px;
		padding: 4px 10px;
		font-size: 10px;
		border-radius: 11px;
	}

	.limit {
		font-size: 12px;
	}

	.ruleBtn {
		display: flex;
		align-items: center;
		font-size: 12px;
	}

	.arrowIcon {
		position: relative;
		width: 6px;
		height: 6px;
		transform: rotate(135deg);

	}

	.rotate {
		transform: rotate(-45deg);
		bottom: -2px;
	}

	.backRotate {
		transform: rotate(135deg);
		top: -2px;
	}

	.ruleDetail {
		display: flex;
		flex-direction: column;
	}

	.ruleList {
		padding-top: 10px;
		font-size: 12px;
	}

	/* 颜色配置 */
	.borderColor1 {
		border: 1px solid #5f98ff;
	}

	.borderColor2 {
		border: 1px solid #ff7979;
	}

	.borderColor3 {
		border: 1px solid #fc932c;
	}

	.borderColor4 {
		border: 1px solid #c3c3c3;
	}

	.arrowIcon1 {
		border-top: 1px solid #5f98ff;
		border-right: 1px solid #5f98ff;
	}

	.arrowIcon2 {
		border-top: 1px solid #ff7979;
		border-right: 1px solid #ff7979;
	}

	.arrowIcon3 {
		border-top: 1px solid #fc932c;
		border-right: 1px solid #fc932c;
	}

	.arrowIcon4 {
		border-top: 1px solid #c3c3c3;
		border-right: 1px solid #c3c3c3;
	}

	.useBtnBgColor1 {
		background-color: #2b6feb;
	}

	.useBtnBgColor2 {
		background-color: #ff5555;
	}

	.useBtnBgColor3 {
		background-color: #fa830e;
	}

	.useBtnBgColor4 {
		background-color: #bebebe;
	}

	.color1 {
		color: #2b6feb;
	}

	.color2 {
		color: #5f98ff;
	}

	.color3 {
		color: #ff5555;
	}

	.color4 {
		color: #ff7979;
	}

	.color5 {
		color: #fa830e;
	}

	.color6 {
		color: #fc932c;
	}

	.color7 {
		color: #bebebe;
	}

	.color8 {
		color: #c3c3c3;
	}

	.bgColor1 {
		background-color: #edf4ff;
	}

	.bgColor2 {
		background-color: #ffeeee;
	}

	.bgColor3 {
		background-color: #fff2e5;
	}

	.bgColor4 {
		background-color: #f3f3f3;
	}

	.bgColorTop1 {
		background: radial-gradient(circle at left bottom, transparent 6px, #edf4ff 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #edf4ff 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom1 {
		background: radial-gradient(circle at left top, transparent 6px, #dae6ff 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #dae6ff 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop2 {
		background: radial-gradient(circle at left bottom, transparent 6px, #ffeeee 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #ffeeee 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom2 {
		background: radial-gradient(circle at left top, transparent 6px, #ffd7d7 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffd7d7 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop3 {
		background: radial-gradient(circle at left bottom, transparent 6px, #fff2e5 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #fff2e5 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom3 {
		background: radial-gradient(circle at left top, transparent 6px, #ffe0c1 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffe0c1 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop4 {
		background: radial-gradient(circle at left bottom, transparent 6px, #f3f3f3 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #f3f3f3 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom4 {
		background: radial-gradient(circle at left top, transparent 6px, #ededed 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ededed 0) top right / 50% 100% no-repeat;
	}

	.btn_register {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 500rpx;
		height: 90rpx;
		border-radius: 20rpx;
	}
</style>
