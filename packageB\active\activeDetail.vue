<template>
	<view style="padding: 30rpx;">
		<rich-text :nodes="formatRichText(strings)"></rich-text>
		<view class="" style="position: fixed;bottom: 30rpx;left: 0;right: 0;margin: 0 auto;height: 80rpx;width: 686rpx;display: flex;align-items: center;justify-content: center;border-radius: 32rpx;" @click="join"
		 v-if="active.use_condition=='JOIN'||active.use_condition=='RECHARGE'||active.use_condition=='BOOK_ROOM'"
			:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
			<text style="color: #FFFFFF;">参与活动</text>
		</view>
		
		<view class="" style="position: fixed;bottom: 30rpx;left: 0;right: 0;margin: 0 auto;height: 80rpx;width: 686rpx;display: flex;align-items: center;justify-content: center;border-radius: 32rpx;"
		 v-if="active.use_condition=='DISTRIBUTION'"
			:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
			<text style="color: #FFFFFF;">参与活动</text>
			<button class="shareBtn"
				style="position: absolute;bottom: 0;right: 0;width: 100%;height: 100%;z-index: 999099;opacity: 0;"
				id="shareBtn" open-type="share" type="primary">
			</button>
		</view>
		
		<view class="" style="position: fixed;bottom: 30rpx;left: 0;right: 0;margin: 0 auto;height: 80rpx;width: 686rpx;display: flex;align-items: center;justify-content: center;border-radius: 32rpx;background-color: #999;" @click="joinAuto"
		 v-if="active.use_condition=='BIRTHDAY'||active.use_condition=='GROW_UP'||active.use_condition=='REGISTER'">
			<text style="color: #FFFFFF;">自动参与</text>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list:[],
				strings: '',
				ifJoin:false
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'setting', 'active']),
		},
		async onShow() {
			await this.$onLaunched;
			console.log(this.active,'active');
			this.strings = this.active.remark
			
			this.$iBox.http('getUseCondition', {})({
				method: 'post'
			}).then(res => { 
				this.list = res.data
			})
		},
		methods: {
			join(){
				this.$iBox.http('joinActive', {id:this.active.id})({
					method: 'post'
				}).then(res => { 
					if(this.active.use_condition == 'JOIN'){
							uni.showModal({
								title:'提示',
								content:'参与活动成功!',
								showCancel:false,
								success() {
									uni.navigateBack()
								}
							})
					}else if(this.active.use_condition == 'RECHARGE'){
						uni.showModal({
							title:'提示',
							content:'参与活动成功!',
							showCancel:false,
							success() {
								uni.navigateTo({
									url:'/pages/recharge/recharge'
								})
							}
						})
					}else if(this.active.use_condition == 'BOOK_ROOM'){
						uni.showModal({
							title:'提示',
							content:'参与活动成功!订房后即可获得活动奖励!',
							showCancel:false,
							success() {
								uni.navigateTo({
									url:'/pages/index/index'
								})
							}
						})
					}
				}).catch(err=>{
					uni.showModal({
						title:'提示',
						content:err,
						showCancel:false,
						success() {
							uni.navigateBack()
						}
					})
				})
			
			},
			joinAuto(){
				uni.showModal({
					title:'提示',
					content:'此活动达到条件后将自动参与!',
					showCancel:false,
					success() {
						uni.navigateBack()
					}
				})
			},
			formatRichText(richText) {
				if (richText != null) {
					let newRichText = richText.replace(/<img[^>]*>/gi, function(match, capture) {
						match = match.replace(/style="[^"]+"/gi, '').replace(/style='[^']+'/gi, '');
						match = match.replace(/width="[^"]+"/gi, '').replace(/width='[^']+'/gi, '');
						match = match.replace(/height="[^"]+"/gi, '').replace(/height='[^']+'/gi, '');
						return match;
					});
					newRichText = newRichText.replace(/style="[^"]+"/gi, function(match, capture) {
						match = match.replace(/width:[^;]+;/gi, 'width:100%;').replace(/width:[^;]+;/gi,
							'width:100%;');
						return match;
					});
					newRichText = newRichText.replace(/<br[^>]*\/>/gi, '');
					newRichText = newRichText.replace(/\<img/gi,
						'<img style="width:100%;height:auto;display:block;margin:10px 0;"');
					return newRichText;
				} else {
					return null;
				}
			}
		},
		onShareAppMessage() {
			// 1.返回节点对象
			let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
			let currentPage = pages[pages.length - 1]; //获取当前页面的对象
			let url = currentPage.route //当前页面url
			return {
				path: '/pages/index/index?share_id=' + this.userInfo.id
			};
		}
	}
</script>

<style scoped lang="scss">
	view {
		box-sizing: border-box;
	}

	.sharebtn {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 40%;
		opacity: 0;
		z-index: 99999;
	}
</style>