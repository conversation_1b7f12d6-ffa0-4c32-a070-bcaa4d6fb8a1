<template>
	<view class="content">
		<view class="tabHeads">
			<view class="tabLabel" @click="toggle(item)" v-for="(item,index) in labels" :key="index">
				<view :class="[isActive === item.id?'isTabActive':'default']"
					:style="isActive === item.id?'border-bottom: 3px solid '+themeColor.main_color:'default'">
					{{item.name}}
				</view>
			</view>
		</view>
		<!-- <view class="headWrap">
			<view class="labelDiv"  v-for="(item,index) in labels"
				:key="index" @click.stop="toggle(item)">
				<view class="labelDivItem" :style="isActive === item.id?'background:' + themeColor.main_color+';color:'+themeColor.button_text_color:''">
					{{item.name}}
				</view>
				
			</view>
		</view> -->
		<scroll-view :style="'height:'+wHeight+'px'" :scroll-top="scrollTop" scroll-y="true">
			<view class="couponMain">
				<view class=""
					style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
					v-if="coupons.length==0">
					<view class="icon-zanwuyouhuiquan1" style="font-size: 260rpx;color: #c1c1c1;">
					</view>
					<p style="color: #c1c1c1;">暂无优惠券</p>
				</view>
				<view class="coupon" v-else v-for="(item,index) in coupons" :key="index">
					<view class="couponTop"
						:class="[isNum ===1?item.type_id ===1?'bgColorTop1':item.type_id ===2?'bgColorTop2':'bgColorTop3':'bgColorTop4']">
						<view class="couponTopLeft">
							<view class="couponTypeDiv">
								<view class="tpyeNameSty"
									:class="[isNum ===1?item.type_id ===1?'useBtnBgColor1':item.type_id ===2?'useBtnBgColor2':'useBtnBgColor3':'useBtnBgColor4']">
									{{item.type_id ===1?'订房券':item.type_id ===2?'超市券':(item.type_id ===3?'餐饮券':(item.type_id ===4?'商城券':'商城券'))}}
								</view>
							</view>
							<view class="valueSty" v-if="item.discount_type==1"
								:class="[isNum ===1?item.type_id ===1?'color1':item.type_id ===2?'color3':'color5':'color7']">
								<text class="symbolMoney">￥</text>
								<text class="moneyVal">{{item.discounts}}</text>
								<!-- <text v-if="item.type_id ===2" style="font-size: 14px;">折</text>
								<text v-if="item.type_id ===3" class="moneyVal">{{item.num}}</text>
								<text v-if="item.type_id ===3" style="font-size: 14px;">件</text> -->
							</view>
							<view class="valueSty" v-if="item.discount_type==2"
								:class="[isNum ===1?item.type_id ===1?'color1':item.type_id ===2?'color3':'color5':'color7']">
								<text class="moneyVal">{{item.discount_rate*10}}</text>
								<text class="symbolMoney">折</text>
								<!-- <text v-if="item.type_id ===2" style="font-size: 14px;">折</text>
								<text v-if="item.type_id ===3" class="moneyVal">{{item.num}}</text>
								<text v-if="item.type_id ===3" style="font-size: 14px;">件</text> -->
							</view>
							<view class="valueSty" :class="[isNum ===1?item.type_id ===1?'color1':item.type_id ===2?'color3':'color5':'color7']" v-if="item.discount_type==2&&item.discount_max>0">
								<text class="" style="font-size: 22rpx;">最多抵扣{{item.discount_max}}元</text>
							</view>
							<view v-if="item.use_condition ===0 " class="useCondition"
								:class="[isNum ===1?item.type_id ===1?'borderColor1 color1':item.type_id ===2?'borderColor2 color3':'borderColor3 color5':'borderColor4 color7']">
								无门槛使用
							</view>
							<view v-else class="useCondition"
								:class="[isNum ===1?item.type_id ===1?'borderColor1 color1':item.type_id ===2?'borderColor2 color3':'borderColor3 color5':'borderColor4 color7']">
								<text>满{{item.use_condition}}元可用</text>
								<!-- <text>{{item.type_id ===3?'兑':'用'}}</text> -->
							</view>
						</view>
						<view class="couponTopRight">
							<view class="ctr-left">
								<view class="couponName"
									:class="[isNum ===1?item.type_id ===1?'color1':item.type_id ===2?'color3':'color5':'color7']">
									{{item.name}}
								</view>
								<view class="couponStore"
									:class="[isNum ===1?item.type_id ===1?'color2':item.type_id ===2?'color4':'color6':'color8']">
									{{item.desc}}
								</view>
								<view class="couponDate"
									:class="[isNum ===1?item.type_id ===1?'color2':item.type_id ===2?'color4':'color6':'color8']">
									发放时间：{{item.start_time | moment}}~{{item.end_time | moment}}
								</view>
							</view>
							<view class="ctr-right">
								<text class="useBtn" @click="toGet(item)" v-if="item.point==0"
									:class="[isNum ===1?item.type_id ===1?'useBtnBgColor1':item.type_id ===2?'useBtnBgColor2':'useBtnBgColor3':'useBtnBgColor4']">{{isNum ===1?'去领取':''}}</text>
								<text class="useBtn" @click="toGet(item)" v-if="item.point>0"
									:class="[isNum ===1?item.type_id ===1?'useBtnBgColor1':item.type_id ===2?'useBtnBgColor2':'useBtnBgColor3':'useBtnBgColor4']">{{isNum ===1?'积分兑换':''}}</text>
							</view>
						</view>
					</view>
					<view class="couponBottom"
						:class="[isNum ===1?item.type_id ===1?'bgColorTBottom1':item.type_id ===2?'bgColorTBottom2':'bgColorTBottom3':'bgColorTBottom4']">
						<view class="ruleLabel">
							<view class="ruleLabel-left" @click.stop="viewRules(item)">
								<text class="limit" style="margin-right: 30rpx;" :class="[isNum ===1?item.type_id ===1?'color2':item.type_id ===2?'color4':'color6':'color8']">
									{{item.usable_date.length > 0||item.usable_time||item.usable_week.length > 0||item.usable_shop_List.length > 0?'有使用限制':'无限制条件'}}
								</text>
							</view>
							<view class="ruleBtn"
								:class="[isNum ===1?item.type_id ===1?'color2':item.type_id ===2?'color4':'color6':'color8']"
								@click.stop="viewRules(item)">
								<text style="margin-right: 6px;">使用规则</text>
								<view class="arrowIcon"
									:class="[item.isViewRule?isNum ===1?item.type_id ===1?'rotate arrowIcon1':item.type_id ===2?'rotate arrowIcon2':'rotate arrowIcon3':'rotate arrowIcon4':isNum ===1?item.type_id ===1?'backRotate arrowIcon1':item.type_id ===2?'backRotate arrowIcon2':'backRotate arrowIcon3':'backRotate arrowIcon4']">
								</view>
							</view>
						</view>
						<view v-if="priceIndex.includes(item.id)" class="ruleDetail" style="display: flex;"
							:class="[isNum ===1?item.type_id ===1?'color2':item.type_id ===2?'color4':'color6':'color8']">
							<view class="" style="display: flex;align-items: flex-start;" v-if="item.usable_shop_List.length > 0">
								<text style="font-size: 24rpx;margin-top: 16rpx;max-width: 258rpx;min-width: 116rpx;">可用酒店：</text>
								<view class="ruleList" style="flex-wrap: wrap;margin-top: 16rpx;padding-top: 0;">
									<view style="margin-left:20rpx;display: flex;align-items: center;flex-wrap: wrap;" v-for="item1 in item.usable_shop_List">
										<view class="">
											{{item1.shop_name}}可用
										</view>
										<view class="" style="margin-left: 40rpx;">
											适用商品:<text v-for="item2 in item1.usable_goods_list">{{item2.goods_name}}、</text>
										</view>
									</view>
								</view>
							</view>
						
							<view class="ruleList" v-if="item.usable_date.length > 0" style="font-size: 24rpx;">
								使用日期：<text v-for="item1 in item.usable_date">{{item1}}可用</text>
							</view>
							<view class="ruleList" v-if="item.usable_time" style="font-size: 24rpx;">
								使用时间:{{item.usable_time}}前可用
							</view>
							<view class="ruleList" v-if="item.usable_week.length > 0" style="font-size: 24rpx;">
								使用星期: <text style="margin-right: 10rpx;"
									v-for="item1 in item.usable_week">星期{{item1==1?'一':(item1==2?'二':(item1==3?'三':(item1==4?'四':(item1==5?'五':(item1==6?'六':'日')))))}}可用</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</scroll-view>

		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				wHeight: 0,
				scrollTop: 0,
				isNum: 1,
				priceIndex: [],
				isActive: 1,
				labels: [{
						id: 1,
						name: '订房券'
					},
					{
						id: 2,
						name: '超市券'
					},
					{
						id: 3,
						name: '餐饮券'
					}
				],
				coupons: [],
				hackReset: true,
				if_login: false
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'setting'])
		},
		async onLoad() {
			await this.$onLaunched;
			uni.hideTabBar()
			const res = uni.getSystemInfoSync()
			this.wHeight = res.windowHeight - 86
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})

		},
		async onShow() {
			await this.$onLaunched;
			this.isActive = 1
			this.getList()
			let set = this.setting.filter(item => {
				return item.sign == 'auto_register_member'
			})
			if (set[0].property) {
				let a = set[0].property.value
				if (a == 2) {
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
						.upgrade_growth_value > -
						1) {
						this.if_login = false

					} else {
						this.if_login = true
						console.log(this.userInfo.phone, 'kk');
					}

				} else if (a == 1) {
					// this.pop = true
					if (this.userInfo.phone) {
						this.if_login = false

					} else {
						this.if_login = true
					}
				}
			}


		},
		methods: {
			loginSucess() {

				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true

					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					if (set[0].property) {
						let a = set[0].property.value
						if (a == 2) {
							if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
								.upgrade_growth_value > -1) {
								this.if_login = false

							} else {
								this.if_login = true
							}

						} else if (a == 1) {
							// this.pop = true
							if (this.userInfo.phone) {
								this.if_login = false

							} else {
								this.if_login = true
							}
						}
					}


				})
			},
			tabsClick(item) {
				this.isNum = item.inx
			},
			toggle(item) {
				this.isActive = item.id
				this.getList()
			},
			viewRules(e) {
				if (this.priceIndex.includes(e.id)) {
					this.priceIndex = this.priceIndex.filter(item => {
						return item != e.id
					})
				} else {
					this.priceIndex.push(e.id)
				}
			},
			getList() {
				
				if(this.isActive==1){
					this.$iBox.http('getRoomCouponList', {
						coupon_type: this.isActive
					})({
						method: 'post'
					}).then(res => {
						this.coupons = res.data
						
					})
				}else if(this.isActive==2){
					this.$iBox.http('getMarketCouponList', {
						coupon_type: this.isActive
					})({
						method: 'post'
					}).then(res => {
						this.coupons = res.data
						
					})
				}else if(this.isActive==3){
					this.$iBox.http('getFoodCouponList', {
						coupon_type: this.isActive
					})({
						method: 'post'
					}).then(res => {
						this.coupons = res.data
						
					})
				}
			
			},
			toGet(e) {

				if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
					1) {
					this.if_login = false
					if(e.point>0){
						uni.showModal({
							title:'提示',
							content:'是否确认花费'+e.point+'积分兑换此优惠券?',
							success:res=>{
								if(res.confirm){
									uni.showLoading({
										title: '加载中...'
									})
									this.$iBox.http('getCoupon', {
										coupon_id: e.id
									})({
										method: 'post'
									}).then(res => {
										uni.showToast({
											icon: 'none',
											title: '兑换成功',
											duration: 2000
										})
										this.getList()
									
									})
								}
							}
						})
					}else {
						uni.showLoading({
							title: '加载中...'
						})
						this.$iBox.http('getCoupon', {
							coupon_id: e.id
						})({
							method: 'post'
						}).then(res => {
							uni.showToast({
								icon: 'none',
								title: '领取成功',
								duration: 2000
							})
							this.getList()
						
						})
					}
						
					
					
				} else {
					this.if_login = true
					console.log(this.userInfo.phone, 'kk');
				}


			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 0;
		overflow: hidden;
		background-color: #fff;
	}

	.tabHeads {
		display: flex;
		background-color: #fff;
		margin-bottom: 1px;
	}

	.tabLabel {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
	}

	.default {
		color: #999999;
		padding: 10px 0;
		border-bottom: 1px solid #ffffff;
	}

	.isTabActive {

		padding: 10px 0;
		font-weight: 600;
	}

	.headWrap {
		padding: 10px;
		// background-color: #fff;
		display: flex;
		align-items: center;
		/* justify-content: space-between; */
		flex-wrap: wrap;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
	}

	.labelDiv {
		width: 25%;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;

		.labelDivItem {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.active {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			// background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

	}



	.couponMain {
		padding: 10px;
		display: flex;
		flex-direction: column;
		background: #ffffff;
	}

	.coupon {
		margin-bottom: 20px;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
	}

	.couponTop {
		display: flex;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
	}

	.couponTopLeft {
		flex: 1.2;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		position: relative;
		overflow: hidden;
	}

	.couponTypeDiv {
		/* overflow: hidden; */
		/* position: relative; */
	}

	.imgType {
		width: 46px;
		height: 40px;
		position: absolute;
		top: 0;
		left: 0;
	}

	.tpyeNameSty {
		font-size: 19rpx;
		transform: rotate(-40deg);
		position: absolute;
		top: 10rpx;
		left: -52rpx;
		background-color: grey; // 背景色
		color: #fff;
		// 以下属性会影响斜边标签的显示
		width: 80%;
		height: 18px;
		line-height: 18px;
		transform: rotate(-40deg);
		text-align: center;
	}

	.valueSty {
		margin-bottom: 10px;
	}

	.symbolMoney {
		font-size: 16px;
	}

	.moneyVal {
		font-size: 24px;
	}

	.useCondition {
		font-size: 10px;
		padding: 4px 10px;
		border-radius: 12px;
	}

	.couponTopRight {
		flex: 3;
		display: flex;
		padding-bottom: 12px;
	}

	.ctr-left {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.couponName {
		font-size: 16px;
		padding: 14px 0;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.couponStore {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
		padding-bottom: 8px;
	}

	.couponDate {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.ctr-right {
		display: flex;
		align-items: flex-end;
		justify-content: center;
		padding: 0 10px;
	}

	.useBtn {
		color: #fff;
		font-size: 12px;
		padding: 6px 20px;
		border-radius: 18px;
	}

	.couponBottom {
		display: flex;
		flex-direction: column;
		padding: 10px;
		border-bottom-left-radius: 4px;
		border-bottom-right-radius: 4px;
	}

	.ruleLabel {
		display: flex;
		align-items: center;
	}

	.ruleLabel-left {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.overlay {
		margin-right: 10px;
		padding: 4px 10px;
		font-size: 10px;
		border-radius: 11px;
	}

	.limit {
		font-size: 12px;
	}

	.ruleBtn {
		display: flex;
		align-items: center;
		font-size: 12px;
	}

	.arrowIcon {
		position: relative;
		width: 6px;
		height: 6px;
		transform: rotate(135deg);

	}

	.rotate {
		transform: rotate(-45deg);
		bottom: -2px;
	}

	.backRotate {
		transform: rotate(135deg);
		top: -2px;
	}

	.ruleDetail {
		display: flex;
		flex-direction: column;
	}

	.ruleList {
		padding-top: 10px;
		font-size: 12px;
	}

	/* 颜色配置 */
	.borderColor1 {
		border: 1px solid #5f98ff;
	}

	.borderColor2 {
		border: 1px solid #ff7979;
	}

	.borderColor3 {
		border: 1px solid #fc932c;
	}

	.borderColor4 {
		border: 1px solid #c3c3c3;
	}

	.arrowIcon1 {
		border-top: 1px solid #5f98ff;
		border-right: 1px solid #5f98ff;
	}

	.arrowIcon2 {
		border-top: 1px solid #ff7979;
		border-right: 1px solid #ff7979;
	}

	.arrowIcon3 {
		border-top: 1px solid #fc932c;
		border-right: 1px solid #fc932c;
	}

	.arrowIcon4 {
		border-top: 1px solid #c3c3c3;
		border-right: 1px solid #c3c3c3;
	}

	.useBtnBgColor1 {
		background-color: #2b6feb;
	}

	.useBtnBgColor2 {
		background-color: #ff5555;
	}

	.useBtnBgColor3 {
		background-color: #fa830e;
	}

	.useBtnBgColor4 {
		background-color: #bebebe;
	}

	.color1 {
		color: #2b6feb;
	}

	.color2 {
		color: #5f98ff;
	}

	.color3 {
		color: #ff5555;
	}

	.color4 {
		color: #ff7979;
	}

	.color5 {
		color: #fa830e;
	}

	.color6 {
		color: #fc932c;
	}

	.color7 {
		color: #bebebe;
	}

	.color8 {
		color: #c3c3c3;
	}

	.bgColor1 {
		background-color: #edf4ff;
	}

	.bgColor2 {
		background-color: #ffeeee;
	}

	.bgColor3 {
		background-color: #fff2e5;
	}

	.bgColor4 {
		background-color: #f3f3f3;
	}

	.bgColorTop1 {
		background: radial-gradient(circle at left bottom, transparent 6px, #edf4ff 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #edf4ff 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom1 {
		background: radial-gradient(circle at left top, transparent 6px, #dae6ff 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #dae6ff 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop2 {
		background: radial-gradient(circle at left bottom, transparent 6px, #ffeeee 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #ffeeee 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom2 {
		background: radial-gradient(circle at left top, transparent 6px, #ffd7d7 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffd7d7 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop3 {
		background: radial-gradient(circle at left bottom, transparent 6px, #fff2e5 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #fff2e5 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom3 {
		background: radial-gradient(circle at left top, transparent 6px, #ffe0c1 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffe0c1 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop4 {
		background: radial-gradient(circle at left bottom, transparent 6px, #f3f3f3 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #f3f3f3 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom4 {
		background: radial-gradient(circle at left top, transparent 6px, #ededed 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ededed 0) top right / 50% 100% no-repeat;
	}
</style>