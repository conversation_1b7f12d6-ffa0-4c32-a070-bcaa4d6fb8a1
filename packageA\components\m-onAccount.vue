<template>
	<view>
		<m-popup :show="poprc" mode="center" @closePop="closePopPay">
			<view class="payBox">
				<p style="width: 100%;display: flex;justify-content: center;">挂帐</p>
				<view class="itemBox">
					<p style="font-size: 28rpx;">挂帐类型</p>
					<uni-data-checkbox mode="button" @change="changePay" class="radioPad" v-model="payName"
						:localdata="payList"></uni-data-checkbox>
				</view>
				<view class="itemBox" style="background-color: #ffffff;">
					<p style="font-size: 28rpx;">挂帐账户</p>
					<picker @change="bindChange" :value="changeIndex" range-key="account_name" :range="groupList">
						<view class="pickerBox">
							{{groupList[changeIndex].account_name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>
				<view class="itemBox" style="background-color: #ffffff;">
					<p style="font-size: 28rpx;">挂帐金额</p>
					<uni-easyinput v-model="billMoney" placeholder="0" type="digit"></uni-easyinput>
					
				</view>

				<view class="itemBox" style="background-color: #ffffff;">
					<p style="font-size: 28rpx;">挂帐备注</p>
					<uni-easyinput type="textarea" v-model="remark" placeholder="请输入内容"></uni-easyinput>
				</view>
				<view class="btnClass" @click="getPayTh">
					挂帐
				</view>
			</view>
		</m-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				payList: [{
					value: 6,
					text: '单位'
				}, {
					value: 7,
					text: '中介'
				}],
				payName: 6,
				groupList: [],
				changeIndex: 0,
				remark: '',
				billMoney: 0
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		props: {
			poprc: {
				type: Boolean,
				default: false
			},
			bill: {
				type: Object
			},
			money: {
				type: Number
			}
		},
		watch: {
			poprc: {
				handler(newVal, oldVal) {
					console.log(this.poprc);
					if (this.poprc) {
						this.$iBox
							.http('getAccountList', {
								type: 6
							})({
								method: 'post'
							})
							.then(res => {

								this.groupList = res.data

								uni.hideLoading()
							})
					}
				},
				immediate: true,
				deep: true
			},
			money: {
				handler(newVal, oldVal) {
					console.log(this.money, 'money,onacc');
					this.billMoney = this.money
				},
				immediate: true,
				deep: true
			},

		},
		mounted() {




		},

		methods: {
			closePopPay() {
				this.$emit('closeAccount', '')
			},
			changePay(e) {
				console.log(e);
				this.payName = e.detail.value
				this.$iBox
					.http('getAccountList', {
						type: this.payName
					})({
						method: 'post'
					})
					.then(res => {

						this.groupList = res.data

						uni.hideLoading()
					})
			},

			bindChange(e) {
				this.changeIndex = e.detail.value[0]
			},
			getPayTh() {
				this.$iBox.throttle(() => {
					this.getPay()
				}, 2000);
			},
			getPay() {

				let params = {
					bill_id: this.bill.id,
					account_id: this.groupList[this.changeIndex].id,
					receive_money: this.billMoney,
					memo: this.remark,

				}

				this.$iBox.http('addRoomPayDetail', params)({
					method: 'post'
				}).then(res => {
					this.$emit('closeAccount', '')
					this.$emit('upBillDetail', '')
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.payBox {
		height: 80vh;
		width: 680rpx;
		padding: 10rpx;

		.itemBox {
			width: 98%;
			background: #f6f6f6;
			margin: 0 auto;
			padding: 20rpx 10rpx;

			.pickerBox {
				position: relative;
				height: 60rpx;
				width: 380rpx;
				border-radius: 14rpx;
				border: 1px solid #eee;
				display: flex;
				margin-top: 14rpx;
				font-size: 30rpx;
				align-items: center;
				padding: 0 10rpx;
			}
		}
	}

	.btnClass {
		width: fit-content;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		// border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		background-color: cornflowerblue;
		margin: 10rpx auto;
	}
</style>