<template>
	<view style="width: 100%;">
		<view class="" style="margin: 180rpx auto;display: flex;flex-direction: column;align-items: center;">
			<image src="https://www.kemanfang.net/xcx_resource/load.gif" style="width: 180rpx;height: 180rpx;" mode="">
			</image>
			<p>为您分配房间中，请稍后......</p>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				params: {
					team_id: '',
					share_code: ''
				}
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options.team_id);
			if (options.team_id && !options.share_code) {

				this.params.team_id = options['team_id']
				this.team_id = options['team_id']
			} else {
				this.params.team_id = options['team_id']
				this.params.share_code = options['share_code']
				this.team_id = options['team_id']
				this.share_code = options['share_code']
			}

			let self = this
			this.timeInfo && clearInterval(this.timeInfo)
			this.timeInfo = setInterval((function target() {
				self.scanExc()
				return target
			})(), 5000)

		},
		methods: {
			scanExc() {
				this.$iBox
					.http('getTeamRoomBillInfo', this.params)({
						method: 'post'
					}).then(res => {
						let auth = res.data.users.filter(item => {
							return item.common_code == this
								.userInfo.common_code
						})[0]
							if (auth.team_confirm == 0) {

							} else if (auth.team_confirm == 1) {
								clearInterval(this.timeInfo)
								uni.reLaunch({
									url: '/pages/myRoom/myRoom'
								})
								// uni.showModal({
								// 	title: '提示',
								// 	content: '认证成功!已为你安排房间!',
								// 	showCancel: false,
								// 	success: res => {
								// 		uni.reLaunch({
								// 			url: '/packageB/teamCheckIn/teamCheckIn'
								// 		})
								// 	}
								// })
							} else {
								clearInterval(this.timeInfo)
								uni.showModal({
									title: '提示',
									content: '认证失败!请重新认证！',
									showCancel: false,
									success: res => {
										if (this.share_code && this.team_id) {
											uni.navigateTo({
												url: '/packageB/teamCheckIn/confirmTeam?team_id=' +this.team_id + '&share_code=' + this.share_code
											})
										} else {
											uni.navigateTo({
												url: '/packageB/teamCheckIn/confirmTeam?team_id=' +this.team_id
											})
										}
									}
								})

					}
			})
	}
	}
	}
</script>

<style scoped lang="scss">

</style>