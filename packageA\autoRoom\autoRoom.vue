<template>
	<view>
		<view class="" style="height: 160rpx;width: 100%;background-color: #FFFFFF;padding: 36rpx 128rpx;">
			<view class=""
				style="width: 496rpx;height: 90rpx;display: flex;align-items: center;justify-content: space-between;">
				<view class=""
					style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<view class=""
						style="width: 48rpx;height: 48rpx;border-radius: 50%;background-color: #6262D6;display: flex;align-items: center;justify-content: center;">
						<image src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/setp1.png" mode=""
							style="display: flex;align-items: center;justify-content: center;width: 32rpx;height: 32rpx;">
						</image>
					</view>
					<text style="color: #000000E0;font-size: 24rpx;margin-top: 8rpx;">上传身份证</text>
				</view>
				<view class="" style="">
					<uni-icons type="right" size="20"></uni-icons>
				</view>
				<view class=""
					style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<view class=""
						style="width: 48rpx;height: 48rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;"
						:style="step==2?'background-color: #6262D6;':'background:#e8e8e8;'">
						<image src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/setp2.png" mode=""
							style="display: flex;align-items: center;justify-content: center;width: 32rpx;height: 32rpx;">
						</image>
					</view>
					<text style="color: #000000E0;font-size: 24rpx;margin-top: 8rpx;">拍摄照片对比</text>
				</view>
			</view>

		</view>

		<view class="" style="margin-top: 30rpx;background-color: #FFFFFF;min-height: 80vh;" v-if="billDetail">
			<view class="idCardBox" style="height: 70vh;" v-if="step==1">
				<view class="title" >第1步：请拍摄您的身份证人像面</view>
				<p style="font-size: 28rpx;margin: 0 auto;display: flex;align-items: center;justify-content: center;color: #00000066;">请确保身份证完整清晰(证件底纹、文字、照片清晰)</p>
				<view class="picBox">
					<view class="picBox_content" style="" @click="toTakePhone">
						<view class="" v-if="idCard_img" style="height: 100%;width: 100%;border: 2px solid #DEEAF0;">
							<image :src="idCard_img" mode="" style="height: 100%;width: 100%;border-radius: 16rpx;"></image>
						</view>
						<view class="" v-else>
							<text class="icon-shenfenzheng" style="font-size: 300rpx;"
								:style="{color:themeColor.com_color1+'33'}">
							</text>
							<view class="takePhoto" :style="{background:themeColor.main_color+'CC'}">
								<text class="icon-paizhao" style="font-size: 60rpx;"
									:style="{color:themeColor.bg_color}">

								</text>
							</view>
						</view>
					</view>
					<view class="msgBox" v-if="idCard_img">
						<p style="font-weight: 600;">请核对您的身份信息:</p>
						<view class="name">
							<p style="width: 180rpx;">姓名</p>
							<input type="text" placeholder="请上传身份证识别" v-model="name" />
						</view>
						<view class="id_number">
							<p style="width: 180rpx;">身份证号</p>
							<input type="text" placeholder="请上传身份证识别" v-model="id_number" />

						</view>
					</view>
				</view>
				<view class=""
					style="display: flex;align-items: center;justify-content: center;position: fixed;bottom: 160rpx;">
					<view class="" v-if="!id_number||!name||!idCard_img"
						style="display: flex;align-items: center;justify-content: center;opacity: 0.3;height: 96rpx;width: 622rpx;border-radius: 48rpx;"
						:style="{background:'linear-gradient(90deg, '+themeColor.com_color1+' 0%, '+themeColor.com_color2+' 100%); '}">
						<text>下一步</text>
					</view>
					<view class="" v-else @click="nextStep"
						style="display: flex;align-items: center;justify-content: center;opacity: 1;height: 96rpx;width: 622rpx;border-radius: 48rpx;"
						:style="{background:'linear-gradient(90deg, '+themeColor.com_color1+' 0%, '+themeColor.com_color2+' 100%); '}">
						<text>下一步</text>
					</view>

				</view>
			</view>

			<view class="faceBox" v-if="step==2">
				<view class="title">第二步：请拍摄您的人脸正面照</view>
				<p style="font-size: 28rpx;margin: 0 auto;width: 700rpx;color: #00000066;">拍摄自拍照时请摘掉眼镜进行拍摄！</p>
				<view class="" style="margin: 30rpx 0;width: 200rpx;height: 120rpx;">
					<image :src="idCard_img" mode="" style="height: 100%;width: 100%;border-radius: 16rpx;"></image>
				</view>
				<view class="picBox">
					<view class="picBox_content" style="" @click="takeMan">
						<text class="icon-renlianyingyong" style="font-size: 200rpx;"
							:style="{color:themeColor.com_color1+'33'}">
						</text>
						<view class="takePhoto" :style="{background:themeColor.main_color+'CC'}">
							<text class="icon-paizhao" style="font-size: 60rpx;" :style="{color:themeColor.bg_color}">

							</text>
						</view>
					</view>
				</view>
				<view class=""
					style="display: flex;align-items: center;position: fixed;bottom: 160rpx;flex-direction: column;">
					<view class="" @click="takeMan"
						style="display: flex;align-items: center;justify-content: center;opacity: 1;height: 96rpx;width: 622rpx;border-radius: 48rpx;"
						:style="{background:'linear-gradient(90deg, '+themeColor.com_color1+' 0%, '+themeColor.com_color2+' 100%); '}">
						<text>下一步</text>
					</view>
					<view class="" @click="preStep"
						style="display: flex;align-items: center;justify-content: center;opacity: 1;height: 96rpx;width: 622rpx;border-radius: 48rpx;background-color: #F5F5F5;margin-top: 40rpx;">
						<text>重新上传身份证照</text>
					</view>
				</view>
				
			</view>
		</view>

		<!-- <view class="" style="width: 750rpx;display: flex;align-items: center;justify-content: center;">
			<button type="primary" @click="sure" style="width: 600rpx;">确认</button>
		</view> -->
		<mChooseMan v-if="hackReset1" @selectMan="chooseMan" :if_show="ifChoose"></mChooseMan>
		<!-- 识别状态弹窗 -->
		<m-popup :show="popLoading" mode="center">
			<view class=""
				style="width: 400rpx;height: 300rpx;border-radius: 20rpx;display: flex;align-items: center;justify-content: center;">
				<text>正在识别...</text>
			</view>
		</m-popup>
		<!-- 同住人二维码 -->
		<m-popup :show="popManQr" mode="center" :closeable="false">
			<view class=""
				style="height: 600rpx;width: 700rpx;border-radius: 30rpx;display: flex;flex-direction: column;justify-content: space-between;align-items: center;padding: 30rpx;">
				<p>请将本码截图保存或出示给同住人扫码认证！</p>
				<image :src="qrcode" mode="" style="height: 300rpx;width: 300rpx;"></image>
				<view class="" style="width: 100%;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="toAuth" :style="{background:themeColor.main_color,color:themeColor.bg_color}"
						style="height: 80rpx;width: 400rpx;border-radius: 40rpx;display: flex;align-items: center;justify-content: center;">
						关闭
					</view>
				</view>
			</view>
		</m-popup>
		<mFenceDevice v-if="blueDeviceSetting"></mFenceDevice>
	</view>
</template>

<script>
	import mFenceDevice from './components/m-fenceDevice.vue'
	import mChooseMan from './components/m-chooseMan.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				idCard_img: '',
				user_img: '',
				name: '',
				id_number: '',
				action_idCard: '',
				popLoading: false,
				authInfo: null,
				blueDeviceSetting: false,
				idAgain: false,
				hackReset1: true,
				userCount: false,
				ifChoose: false,
				popManQr: false,
				step: 1
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting']),
			...mapState('room', ['billDetail', 'roomBillUser']),
		},
		components: {
			mFenceDevice,
			mChooseMan
		},
		async onLoad() {
			await this.$onLaunched;
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					this.action_idCard = res.data + '/wx/User/userOcr'
				}
			});


		},
		async onShow() {
			await this.$onLaunched;
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			this.getWxAuthorizeLocation()

			// 获取失败次数大于两次需重新拍摄身份证
			uni.getStorage({
				key: 'authErr',
				success: (res) => {
					console.log(res.data, 'err');
					if (res.data == 2) {
						this.idAgain = true
					}
				}
			});

			this.blueDeviceSetting = this.shopSetting.filter(item => {
				return item.sign == 'u_dun_device'
			})[0].property.status

			// 刷新订单
			this.$iBox.http('getRoomBillInfo', {
				bill_id: this.billDetail.id
			})({
				method: 'post'
			}).then(res1 => {
				this.getBillDetail(res1.data)
				// 查询身份证信息
				console.log(!res1.select_user_count);
				if (res1.data.select_user_count == 0 || !res1.data.select_user_count) {
					this.hackReset1 = false
					this.$nextTick(() => {
						this.hackReset1 = true
					})
					this.ifChoose = true
				} else {
					this.ifChoose = false
				}
				this.$iBox.http('getIdentificationInfo', {})({
					method: 'post'
				}).then(res2 => {
					this.authInfo = res2.data

					// 判断是否有身份证记录,有的话则直接展示
					if (this.authInfo && this.authInfo.id_image && !this.idAgain) {
						uni.showToast({
							icon: 'none',
							title: '检测到您已入住过本酒店，请和对您的信息是否正确！'
						})
						if (this.authInfo.id_image.includes('.')) {
							this.idCard_img = this.authInfo.id_image
						} else {
							this.idCard_img = ''
						}

						this.name = this.authInfo.name
						this.id_number = this.authInfo.identification_number
					}
					uni.hideLoading()
				})

			})

		},

		methods: {
			...mapActions('room', ['getHardWareList', 'getBillDetail', 'getRoomBillUser']),
			chooseMan(e) {
				console.log(e);
				this.$iBox
					.http('selectUserCount', {
						bill_id: this.billDetail.id,
						user_count: e
					})({
						method: 'post'
					})
					.then(res => {
						if (e == 1) {
							this.ifChoose = false

						} else {
							this.$iBox
								.http('createRoomBillQrCode', {
									bill_id: this.billDetail.id,
									type: 2
								})({
									method: 'post'
								})
								.then(res => {
									this.qrcode = res.data.url
									this.popManQr = true
									this.ifChoose = false
								})

						}
					})

			},
			toAuth() {
				this.popManQr = false
			},
			toTakePhone() {
				uni.chooseMedia({
					count: 9,
					mediaType: ['image'],
					sourceType: ['album', 'camera'],
					sizeType: ['compressed'],
					camera: 'back',
					success: (res) => {
						console.log(res, 'id');
						this.popLoading = true
						uni.uploadFile({
							url: this.action_idCard, //
							header: {
								'AUTHTOKEN': this.userInfo.user_token,
								'Content-Type': 'application/x-www-form-urlencoded',
								'chartset': 'utf-8'
							},
							filePath: res.tempFiles[0].tempFilePath,
							name: 'file',
							formData: {
								'shop_id': this.hotel.id
							},
							success: (uploadFileRes) => {
								if (JSON.parse(uploadFileRes.data).data) {
									let dataInfo = JSON.parse(uploadFileRes.data).data
									this.idCard_img = dataInfo.id_image
									this.name = dataInfo.name
									this.id_number = dataInfo.identification_number
									this.popLoading = false
								} else {
									uni.showModal({
										title: '提示',
										content: '身份证照片识别失败，请重新上传身份证！',
										showCancel: false,
										success: res1 => {
											this.popLoading = false
											this.idCard_img = ''
											this.name = ''
											this.id_number = ''
										}
									})
								}

							},
							fail: () => {
								this.popLoading = false
							}
						});

					},
					fail: err => {
						console.log(err, 'err');
						this.popLoading = false
					}
				})
			},
			nextStep(){
				this.step = 2
			},
			preStep(){
				this.step = 1
			},
			takeMan() {
				if (!this.idCard_img) {
					uni.showToast({
						icon: 'error',
						title: '请先上传身份证照片!'
					})
					return
				}
				
				if (!this.name) {
					uni.showToast({
						icon: 'error',
						title: '请先填写证件照姓名'
					})
					return
				}
				
				if (!this.id_number) {
					uni.showToast({
						icon: 'error',
						title: '请先填写证件照号'
					})
					return
				}

				uni.navigateTo({
					url: '/packageA/autoRoom/cameraMan/cameraMan'
				})

			},
			sure() {
				uni.switchTab({
					url: '/pages/myRoom/myRoom'
				})
			},
			//===========================================权限验证=================================================
			getWxAuthorizeLocation() {
				uni.getSetting({
					success: (res) => {
						if (res.authSetting['scope.camera'] == null) {
							uni.authorize({
								scope: 'scope.camera',
								success() {
									// 用户同意
									// 相关操作
								},
								fail() {
									uni.showToast({
										title: '无法申请摄像头权限,请确认是否已经授权摄像头权限',
										icon: "none",
										duration: 2000
									})
								}
							})
							return
						}

						// 如果已经有权限，就查询
						if (res.authSetting['scope.camera']) {
							// 相关操作
						} else { // 被拒绝过授权，重新申请
							uni.showModal({
								title: '信息授权',
								content: '拒绝摄像头授权将无法进行自主入住，是否继续授权？',
								cancelText: '仍然拒绝',
								confirmText: '开启授权',
								success: (res) => {
									if (res.confirm) {
										uni.openSetting({
											fail: function() {

											}
										})
									} else {
										uni.openSetting({
											fail: function() {

											}
										})
									}
								}
							})
						}
					}
				});
			},
		}


	}
</script>

<style>
	page {
		background-color: #FFFFFF;
	}
</style>

<style lang="scss" scoped>
	.idCardBox {

		margin: 50rpx auto;
		background-color: #FFFFFF;
		width: 700rpx;
		padding: 48rpx 32rpx;

		.title {
			font-size: 32rpx;
		}

		.picBox {

			width: 100%;

			overflow: auto;

			&_content {
				height: 320rpx;
				width: 500rpx;
				margin: 30rpx auto;
				border-radius: 16rpx;

				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
			}

			.takePhoto {
				position: absolute;
				z-index: 3;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
				width: 140rpx;
				height: 140rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
			}

			.msgBox {
				padding: 30rpx;
				font-size: 36rpx;

				color: #303133;

				.name {
					font-size: 30rpx;
					padding: 30rpx 0;
					border-bottom: 1px solid #e4e7ed;
					display: flex;
					align-items: center;

				}

				.id_number {
					padding: 30rpx 0;
					font-size: 30rpx;
					border-bottom: 1px solid #e4e7ed;
					display: flex;
					align-items: center;

				}
			}
		}



	}

	.faceBox {

		margin: 50rpx auto;
		padding: 48rpx 32rpx;
		width: 700rpx;
		background-color: #FFFFFF;
		.title {
			font-size: 30rpx;
		}

		.picBox {
			// border-right: 1px dashed #bbbcbd;
			// border-bottom: 1px dashed #bbbcbd;
			// border-left: 1px dashed #bbbcbd;
			width: 100%;
			min-height: 400rpx;
			overflow: auto;

			&_content {
				height: 320rpx;
				width: 500rpx;
				margin: 30rpx auto;
				border-radius: 16rpx;
				background: #f3f4f6;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
			}

			.takePhoto {
				position: absolute;
				z-index: 3;
				left: 0;
				right: 0;
				top: 0;
				bottom: 0;
				margin: auto;
				width: 140rpx;
				height: 140rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
			}
		}

	}
</style>