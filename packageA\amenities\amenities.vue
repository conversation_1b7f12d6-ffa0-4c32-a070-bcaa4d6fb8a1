<template>
	<view :style="{background:themeColor.bg_color}" style="height: 100vh;">
		<view class="" style="position: sticky;top: 0;width: 100%;z-index: 99;height: 10vh;">
			<m-tabs :list="list1"
			
					@tabClick="tab_click"
					:config="{color:themeColor.text_main_color,
							  activeColor:themeColor.com_color1,
							  underLineColor:themeColor.com_color1,
							  underLineWidth:60,
							  underLineHeight:6}">
			</m-tabs>
		</view>
		
		<scroll-view scroll-y="true" style="height: 90vh;" :scroll-into-view="idIndex">
			<!-- 酒店介绍 -->
			<view class="hotelSec" id="tab0" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
				<view class="title">
					<text>酒店介绍</text>
				</view>
				<view class="content">
					<rich-text :nodes="strings"></rich-text>
				</view>
			</view>
			
			<m-hotelAmenities :detailShow="false" id="tab1"></m-hotelAmenities>
			<view class="hotelSec" id="tab2" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
				<view class="title">
					<text>入住须知</text>
				
				</view>
				<view class="" style="padding: 30rpx;">
					<rich-text :nodes="strings1"></rich-text>
				</view>
				
			</view>
		</scroll-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list1: [{
						id: 0,
						name: '酒店介绍'
					},
					{
						id: 1,
						name: '酒店设施'
					},
					{
						id: 2,
						name: '入住须知'
					},
				],
				idIndex:'',
				c_height:'',
				strings:'',
				strings1:''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		mounted() {
			this.strings = this.$iBox.formatRichText(this.hotel.desc)
			this.strings1 = this.$iBox.formatRichText(this.hotel.policy)
		},
		methods:{
			tab_click(e){
				console.log(e)
				this.idIndex = 'tab' + e
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hotelSec {
		width: 750rpx;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;
		
		.title {
			height: 80rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
			border: 1px solid #e4e7ed;
		}
		
		.content {
			padding: 30rpx;
		}
	}
</style>
