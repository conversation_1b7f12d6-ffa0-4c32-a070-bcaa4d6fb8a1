<template>
	<view class="demo-page">
		<!-- 初始化动画演示 -->
		<InitAnimation 
			:visible="showAnimation" 
			:progress="progress" 
		/>
		
		<!-- 控制面板 -->
		<view class="control-panel" v-if="!showAnimation">
			<view class="panel-header">
				<text class="title">🎬 初始化动画演示</text>
			</view>
			
			<view class="control-section">
				<view class="section-title">动画控制</view>
				<view class="button-group">
					<button class="demo-btn primary" @click="startAnimation">
						开始动画
					</button>
					<button class="demo-btn" @click="resetAnimation">
						重置
					</button>
				</view>
			</view>
			
			<view class="control-section">
				<view class="section-title">进度控制</view>
				<view class="progress-controls">
					<button class="progress-btn" @click="setProgress(20)">20%</button>
					<button class="progress-btn" @click="setProgress(40)">40%</button>
					<button class="progress-btn" @click="setProgress(60)">60%</button>
					<button class="progress-btn" @click="setProgress(80)">80%</button>
					<button class="progress-btn" @click="setProgress(100)">100%</button>
				</view>
			</view>
			
			<view class="control-section">
				<view class="section-title">自动演示</view>
				<button class="demo-btn success" @click="autoDemo">
					自动演示完整流程
				</button>
			</view>
			
			<view class="info-section">
				<view class="info-title">✨ 动画特性</view>
				<view class="feature-list">
					<text class="feature-item">🏨 精致的酒店图标动画</text>
					<text class="feature-item">📊 实时进度指示器</text>
					<text class="feature-item">💫 优雅的粒子效果</text>
					<text class="feature-item">🎨 渐变背景和平滑过渡</text>
					<text class="feature-item">📱 响应式设计</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import InitAnimation from '@/components/InitAnimation/InitAnimation.vue'

export default {
	name: 'AnimationDemo',
	components: {
		InitAnimation
	},
	data() {
		return {
			showAnimation: false,
			progress: 0,
			autoTimer: null
		}
	},
	methods: {
		// 开始动画
		startAnimation() {
			this.showAnimation = true
			this.progress = 0
		},
		
		// 重置动画
		resetAnimation() {
			this.showAnimation = false
			this.progress = 0
			if (this.autoTimer) {
				clearInterval(this.autoTimer)
				this.autoTimer = null
			}
		},
		
		// 设置进度
		setProgress(value) {
			if (this.showAnimation) {
				this.progress = value
				
				// 如果达到100%，延迟隐藏
				if (value >= 100) {
					setTimeout(() => {
						this.showAnimation = false
					}, 1000)
				}
			}
		},
		
		// 自动演示
		autoDemo() {
			this.startAnimation()
			
			let currentProgress = 0
			this.autoTimer = setInterval(() => {
				currentProgress += 5
				this.progress = currentProgress
				
				if (currentProgress >= 100) {
					clearInterval(this.autoTimer)
					this.autoTimer = null
					
					// 演示完成后自动隐藏
					setTimeout(() => {
						this.showAnimation = false
					}, 1500)
				}
			}, 200) // 每200ms增加5%
		}
	},
	
	onUnload() {
		// 页面卸载时清理定时器
		if (this.autoTimer) {
			clearInterval(this.autoTimer)
		}
	}
}
</script>

<style lang="scss" scoped>
.demo-page {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.control-panel {
	padding: 40rpx;
	
	.panel-header {
		text-align: center;
		margin-bottom: 60rpx;
		
		.title {
			font-size: 48rpx;
			font-weight: 700;
			color: #2d3748;
		}
	}
	
	.control-section {
		margin-bottom: 60rpx;
		background: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
		
		.section-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #4a5568;
			margin-bottom: 30rpx;
		}
		
		.button-group {
			display: flex;
			gap: 20rpx;
		}
		
		.demo-btn {
			flex: 1;
			height: 80rpx;
			border-radius: 40rpx;
			border: none;
			font-size: 28rpx;
			font-weight: 600;
			background: #e2e8f0;
			color: #4a5568;
			
			&.primary {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				color: #fff;
			}
			
			&.success {
				background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
				color: #fff;
			}
		}
		
		.progress-controls {
			display: flex;
			gap: 15rpx;
			flex-wrap: wrap;
		}
		
		.progress-btn {
			width: 120rpx;
			height: 60rpx;
			border-radius: 30rpx;
			border: 2rpx solid #667eea;
			background: #fff;
			color: #667eea;
			font-size: 24rpx;
			font-weight: 600;
			
			&:active {
				background: #667eea;
				color: #fff;
			}
		}
	}
	
	.info-section {
		background: #fff;
		border-radius: 20rpx;
		padding: 40rpx;
		box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.1);
		
		.info-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #4a5568;
			margin-bottom: 30rpx;
		}
		
		.feature-list {
			display: flex;
			flex-direction: column;
			gap: 20rpx;
		}
		
		.feature-item {
			font-size: 28rpx;
			color: #718096;
			line-height: 1.5;
		}
	}
}
</style>
