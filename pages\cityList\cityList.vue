<template>
	<view>
		<selectCity :lists="cityLists" @choose="chooseCity"></selectCity>
	</view>
</template>

<script>
	import selectCity from '@/components/m-selectCity/m-selectCity.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				cityLists:[]
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityList'])
		},
		components:{
			selectCity
		},
		onLoad() {
			console.log(this.cityList);
			this.cityLists = this.cityList
		},
		methods:{
			...mapActions('hotel',['getCity']),
			chooseCity(e){
				console.log(e,'d');
				this.getCity(e)
				uni.navigateBack({
					delta:1	
				})
			}
		}
	}
</script>

<style lang="scss">

</style>
