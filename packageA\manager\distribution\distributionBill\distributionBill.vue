<template>
	<view>
		<view class="dataPanel">
			<p style="padding: 10rpx;font-size: 30rpx;font-weight: 600;">数据统计:</p>
			<view class="" style="display: flex;flex-wrap: wrap;">
				<view class="title1">
					<text>平均订单金额 :{{totalData.avg_amount}}</text>
				</view>
				<view class="title1">
					<text>平均分销提成金额:{{totalData.avg_distribution_amount}}</text>
				</view>
				<view class="title1">
					<text>最大订单金额:{{totalData.max_amount}}</text>
				</view>
				<view class="title1">
					<text>最大分销提成金额:{{totalData.max_distribution_amount}}</text>
				</view>
				<view class="title1">
					<text>最小订单金额:{{totalData.min_amount}}</text>
				</view>
				<view class="title1">
					<text>最小分销提成金额:{{totalData.min_distribution_amount}}</text>
				</view>
				<view class="title1">
					<text>总计金额:{{totalData.sum_amount}}</text>
				</view>
				<view class="title1">
					<text>总计分销提成金额:{{totalData.sum_distribution_amount}}</text>
				</view>
			</view>
			
		</view>
		<view class=""
			style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
			v-if="recordList.length==0">
			<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无订单</p>
		</view>
		<view class="listBox" v-for="item in recordList" v-else>
			<view class="title1">
				<text>酒店:{{item.shop_name}}</text>
			</view>
			<view class="title1">
				<text>房号:{{item.room_number}}</text>
			</view>
			<view class="title1">
				<text>分销人:{{item.distribution_user_name}}</text>
			</view>
			<view class="title1">
				<text>下单人:{{item.user_name?item.user_name:'无'}}</text>
			</view>
			<view class="title2">
				<text>下单人电话:{{item.user_phone}}</text>
			</view>
			<view class="title1">
				<text>订单金额:￥{{item.amount}}</text>
			</view>
			<view class="title1">
				<text>提成金额:￥{{item.distribution_amount}}</text>
			</view>
			<view class="title1">
				<text>订单类别:{{item.bill_type_name}}</text>
			</view>
			<view class="title2">
				<text>订单号:{{item.bill_code}}</text>
			</view>
			<view class="title1">
				<text>订单金额:{{item.amount}}</text>
			</view>
			<view class="title2" v-if="item.enter_time">
				<text>入住时间:{{item.enter_time | moment1}}</text>
			</view>
			<view class="title2" v-if="item.leave_time">
				<text>离店时间:{{item.leave_time | moment1}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				params: {
					page: 1,
					limit: 10,
					status: 2
				},
				recordList: [],
				totalData: null,
				bool: true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		async onShow() {
			this.params.page = 1
			this.params.status = ''
			this.getRecord()
		},
		methods: {
			getRecord() {
				// 查询提现记录
				this.$iBox.http('getDistributionBill', this.params)({
					method: 'post'
				}).then(res => {
					this.totalData = res.data.data
					this.recordList = res.data.list
				})
			}
		},
		onReachBottom() {

			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getDistributionBill', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.recordList.concat(res.data.list)
					this.recordList = new_list
					if (this.recordList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style scoped lang="scss">
	.dataPanel {
		margin: 0 0 30rpx 0;
		width: 750rpx;
		height: 380rpx;
		background: #FFFFFF;
		padding: 20rpx;
		.title1 {
			padding: 10rpx;
			width: 50%;
			display: flex;
			align-items: center;
			// justify-content: center;
			font-size: 28rpx;
		}
	}

	.listBox {
		padding: 30rpx;
		border-bottom: 1px solid #e4e7ed;
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		background: #FFFFFF;
		width: 700rpx;
		margin: 20rpx auto;
		border-radius: 30rpx;
		box-shadow: rgba(0, 0, 0, 0.19) 0px 10px 20px, rgba(0, 0, 0, 0.23) 0px 6px 6px;

		.title {
			padding: 10rpx;
			width: 33%;
			display: flex;
			align-items: center;
			// justify-content: center;
			font-size: 28rpx;
		}

		.title1 {
			padding: 10rpx;
			width: 50%;
			display: flex;
			align-items: center;
			// justify-content: center;
			font-size: 28rpx;
		}

		.title2 {
			padding: 10rpx;
			width: 100%;
			display: flex;
			align-items: center;
			// justify-content: center;
			font-size: 28rpx;
		}
	}
</style>