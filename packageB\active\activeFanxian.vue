<template>
	<view>
		<view class="" style="margin: 100rpx auto;display: flex;flex-direction: column;align-items: center;">
			<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/12/hd.png" style="height: 100rpx;width: 100rpx;"
				mode=""></image>
			<view class="" style="width: 608rpx;height: 300rpx;border-radius: 30rpx;border-radius: 30rpx;margin-top: -10rpx;
			display: flex;flex-direction:column;align-items: center;justify-content: center;background: linear-gradient(296.97deg, #FFFBF8 23.9%, #FFD5C5 74.96%);
">
				<p style="font-size: 40rpx;font-weight: 700;;color: rgba(63, 63, 63, 0.8);">邀请好友，立即参加返现活动</p>
				<p style="margin-top: 10rpx;color: rgba(63, 63, 63, 0.8);">点击下方按钮，获得返现现金!</p>
			</view>
			<view class=""
				style="width: 600rpx;height: 80rpx;background: linear-gradient(18.96deg, #FFFDF8 14.7%, #FFCF94 97.54%);margin-top: 100rpx;
border-radius: 36rpx;border: 1px solid #FFE2D0;display: flex;align-items: center;justify-content: center;position: relative;">
				<view class=""
					style="position: absolute;height: 60rpx;width: fit-content;padding: 0 20rpx;display: flex;align-items: center;justify-content: center;color: #aa557f;font-size: 30rpx;margin: 0 auto;">
					<uni-icons type="weixin" size="30"></uni-icons>立即邀请
				</view>
				<button class="shareBtn"
					style="position: absolute;bottom: 0;right: 0;width: 100%;height: 100%;z-index: 999099;opacity: 0;"
					id="shareBtn" open-type="share" type="primary">
				</button>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {

			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'setting']),
		},
		methods: {

		},
		onShareAppMessage() {
			// 1.返回节点对象
			let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
			let currentPage = pages[pages.length - 1]; //获取当前页面的对象
			let url = currentPage.route //当前页面url
			return {
				path: '/pages/index/index?share_id='+this.userInfo.id
			};
		}
	}
</script>

<style scoped lang="scss">
	view {
		box-sizing: border-box;
	}

	.sharebtn {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 40%;
		opacity: 0;
		z-index: 99999;
	}
</style>