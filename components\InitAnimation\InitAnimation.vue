<template>
	<view class="init-animation" v-if="visible">
		<!-- 背景遮罩 -->
		<view class="animation-overlay">
			<!-- 主动画容器 -->
			<view class="animation-container">
				<!-- 酒店图标动画 -->
				<view class="hotel-icon-wrapper">
					<view class="hotel-icon">
						<view class="building">
							<view class="floor" v-for="(floor, index) in 4" :key="index" 
								  :style="{ animationDelay: (index * 0.2) + 's' }">
							</view>
						</view>
						<view class="door"></view>
					</view>
				</view>
				
				<!-- 加载文字 -->
				<view class="loading-text">
					<text class="text-char" v-for="(char, index) in loadingChars" :key="index"
						  :style="{ animationDelay: (index * 0.1) + 's' }">
						{{ char }}
					</text>
				</view>
				
				<!-- 进度指示器 -->
				<view class="progress-container">
					<view class="progress-bar">
						<view class="progress-fill" :style="{ width: progress + '%' }"></view>
					</view>
					<view class="progress-dots">
						<view class="dot" v-for="n in 5" :key="n" 
							  :class="{ active: n <= Math.ceil(progress / 20) }"
							  :style="{ animationDelay: (n * 0.15) + 's' }">
						</view>
					</view>
				</view>
				
				<!-- 底部提示 -->
				<view class="tip-text">
					<text class="tip">{{ currentTip }}</text>
				</view>
			</view>
			
			<!-- 装饰性粒子 -->
			<view class="particles">
				<view class="particle" v-for="n in 8" :key="n"
					  :style="{ 
						  left: Math.random() * 100 + '%',
						  animationDelay: Math.random() * 2 + 's',
						  animationDuration: (2 + Math.random() * 2) + 's'
					  }">
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'InitAnimation',
	props: {
		visible: {
			type: Boolean,
			default: true
		},
		progress: {
			type: Number,
			default: 0
		}
	},
	data() {
		return {
			loadingChars: ['初', '始', '化', '中', '...'],
			tips: [
				'正在连接服务器...',
				'获取酒店信息...',
				'加载用户数据...',
				'准备界面...',
				'即将完成...'
			],
			currentTipIndex: 0,
			tipTimer: null
		}
	},
	computed: {
		currentTip() {
			return this.tips[this.currentTipIndex] || this.tips[0]
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				this.startTipRotation()
			} else {
				this.stopTipRotation()
			}
		},
		progress(newVal) {
			// 根据进度更新提示
			const tipIndex = Math.min(Math.floor(newVal / 20), this.tips.length - 1)
			this.currentTipIndex = tipIndex
		}
	},
	mounted() {
		if (this.visible) {
			this.startTipRotation()
		}
	},
	beforeDestroy() {
		this.stopTipRotation()
	},
	methods: {
		startTipRotation() {
			this.tipTimer = setInterval(() => {
				this.currentTipIndex = (this.currentTipIndex + 1) % this.tips.length
			}, 2000)
		},
		
		stopTipRotation() {
			if (this.tipTimer) {
				clearInterval(this.tipTimer)
				this.tipTimer = null
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.init-animation {
	position: fixed;
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	
	.animation-overlay {
		width: 100%;
		height: 100%;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		display: flex;
		align-items: center;
		justify-content: center;
		position: relative;
		overflow: hidden;
	}
	
	.animation-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		z-index: 2;
	}
	
	// 酒店图标动画
	.hotel-icon-wrapper {
		margin-bottom: 60rpx;
		
		.hotel-icon {
			width: 120rpx;
			height: 120rpx;
			position: relative;
			animation: float 3s ease-in-out infinite;
			
			.building {
				width: 80rpx;
				height: 100rpx;
				background: #fff;
				border-radius: 8rpx 8rpx 0 0;
				position: relative;
				margin: 0 auto;
				box-shadow: 0 10rpx 30rpx rgba(0,0,0,0.2);
				
				.floor {
					width: 60rpx;
					height: 16rpx;
					background: #4facfe;
					margin: 8rpx auto;
					border-radius: 2rpx;
					opacity: 0;
					animation: buildUp 0.8s ease-out forwards;
					
					&:nth-child(even) {
						background: #00f2fe;
					}
				}
			}
			
			.door {
				width: 20rpx;
				height: 30rpx;
				background: #8b5cf6;
				border-radius: 4rpx 4rpx 0 0;
				position: absolute;
				bottom: 0;
				left: 50%;
				transform: translateX(-50%);
				animation: doorOpen 1s ease-out 1s forwards;
			}
		}
	}
	
	// 加载文字动画
	.loading-text {
		margin-bottom: 80rpx;
		display: flex;
		
		.text-char {
			font-size: 36rpx;
			font-weight: 600;
			color: #fff;
			margin: 0 4rpx;
			opacity: 0;
			transform: translateY(20rpx);
			animation: textAppear 0.6s ease-out forwards;
		}
	}
	
	// 进度指示器
	.progress-container {
		width: 400rpx;
		margin-bottom: 60rpx;
		
		.progress-bar {
			width: 100%;
			height: 6rpx;
			background: rgba(255,255,255,0.2);
			border-radius: 3rpx;
			overflow: hidden;
			margin-bottom: 40rpx;
			
			.progress-fill {
				height: 100%;
				background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
				border-radius: 3rpx;
				transition: width 0.3s ease;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					top: 0;
					right: 0;
					width: 20rpx;
					height: 100%;
					background: linear-gradient(90deg, transparent, rgba(255,255,255,0.6));
					animation: shine 1.5s ease-in-out infinite;
				}
			}
		}
		
		.progress-dots {
			display: flex;
			justify-content: space-between;
			
			.dot {
				width: 16rpx;
				height: 16rpx;
				border-radius: 50%;
				background: rgba(255,255,255,0.3);
				transform: scale(0);
				animation: dotPulse 1.5s ease-in-out infinite;
				
				&.active {
					background: #fff;
					transform: scale(1);
				}
			}
		}
	}
	
	// 提示文字
	.tip-text {
		.tip {
			font-size: 28rpx;
			color: rgba(255,255,255,0.8);
			animation: tipFade 2s ease-in-out infinite;
		}
	}
	
	// 装饰粒子
	.particles {
		position: absolute;
		width: 100%;
		height: 100%;
		pointer-events: none;
		
		.particle {
			position: absolute;
			width: 8rpx;
			height: 8rpx;
			background: rgba(255,255,255,0.6);
			border-radius: 50%;
			animation: particleFloat 3s linear infinite;
		}
	}
}

// 动画定义
@keyframes float {
	0%, 100% { transform: translateY(0); }
	50% { transform: translateY(-20rpx); }
}

@keyframes buildUp {
	0% { 
		opacity: 0; 
		transform: translateY(20rpx) scaleY(0); 
	}
	100% { 
		opacity: 1; 
		transform: translateY(0) scaleY(1); 
	}
}

@keyframes doorOpen {
	0% { transform: translateX(-50%) scaleY(1); }
	50% { transform: translateX(-50%) scaleY(0.7); }
	100% { transform: translateX(-50%) scaleY(1); }
}

@keyframes textAppear {
	0% { 
		opacity: 0; 
		transform: translateY(20rpx); 
	}
	100% { 
		opacity: 1; 
		transform: translateY(0); 
	}
}

@keyframes dotPulse {
	0%, 100% { transform: scale(0.8); opacity: 0.5; }
	50% { transform: scale(1.2); opacity: 1; }
}

@keyframes shine {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

@keyframes tipFade {
	0%, 100% { opacity: 0.8; }
	50% { opacity: 1; }
}

@keyframes particleFloat {
	0% { 
		transform: translateY(100vh) rotate(0deg); 
		opacity: 0; 
	}
	10% { opacity: 1; }
	90% { opacity: 1; }
	100% { 
		transform: translateY(-100rpx) rotate(360deg); 
		opacity: 0; 
	}
}

// 深色模式适配
@media (prefers-color-scheme: dark) {
	.init-animation {
		.animation-overlay {
			background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
		}
	}
}
</style>
