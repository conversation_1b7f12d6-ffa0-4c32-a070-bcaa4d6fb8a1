<template>
	<view class="page" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
		<scroll-view class="scrollList" scroll-y :scroll-into-view="scrollViewId" :style="{height:winHeight+'px'}">
			<view class="search-bar" :style="{background:themeColor.bg_color}">
				<view class="search-bar-form" :style="{background:themeColor.bg1_color}">
					<view class="search-bar-box">
						<!-- <icon class="icon-search-in-box" type="search" size="16"></icon> -->
						<input confirm-type="search" class="search-bar-input"  placeholder="输入城市名称查询"
							placeholder-class="phcolor" :value="inputVal" :focus="inputShowed" @input="inputTyping" />
						<view class="icon-clear" v-if="inputVal" >
							<view class="icon-close" style="font-size: 34rpx;" @tap="clearInput">
								
							</view>
						</view>
					</view>
					<label class="search-bar-label" v-if="!inputShowed" @tap="showInput">
						<!-- <icon class="icon-search" type="search" size="16"></icon> -->
						<!-- <view class="search-bar-text">输入城市名称或首字母查询</view> -->
					</label>
				</view>
			</view>
			<view class="hwx-list search-result" v-if="inputShowed">
				<view class="hwx-list-cell" hover-class="hwx-list-cell-hover" v-for="(item,index) in searchResult"
					:key="index" @tap="selectCity(item)" :data-name="item.name" :hover-stay-time='150'>
					<view class="hwx-list-cell-navigate">
						{{item.name}}
					</view>
				</view>
			</view>
			<view v-if="!inputVal">
				<view class="current-city" :style="{background:themeColor.bg_color}">
					<view class="title">当前选择</view>
					<view class="city-name">
						<view class="icon-Location" :style="{color: themeColor.main_color}">
							
						</view>
						{{localCity}}
					</view>
				</view>
				<!-- <view class="hot-city">
					<view class="title">热门城市</view>
					<view class="city-names">
						<view class="city-name-item" v-for="(item,index) in hotCity" :key="index" hover-class="tap-city"
							:hover-stay-time="150" @click="selectCity" :data-name="item">
							{{item}}
						</view>
					</view>
				</view> -->
				<view class="hwx-list city-list" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
					<view v-for="(list,index) in lists" :key="index">
						<template v-if="list.data[0]">
							<view class="hwx-list-cell-divider" :style="{background:themeColor.bg1_color,color:themeColor.text_main_color}" :id="index === 0 ? 'suoyin' : list.letter">
								{{list.letter}}
							</view>
							<view class="hwx-list-cell" hover-class="hwx-list-cell-hover" v-for="(item,index2) in list.data"
								:key="index2" @click="selectCity(item)" :data-name="item.name" :hover-stay-time='150'>
								<view class="hwx-list-cell-navigate" :style="{'border-bottom': '1rpx solid'+themeColor.border_color}" :class="[list.data.length-1==index?'last':'']">
									{{item.name}}
								</view>
							</view>
						</template>
					</view>
				</view>
			</view>
		</scroll-view>
		<view class="hwx-indexed-list-bar" :style="{height:indexBarHeight+'px'}" @touchstart="touchStart"
			@touchmove.stop.prevent="touchMove" @touchend.stop="touchEnd" @touchcancel.stop="touchCancel"
			v-if="!inputVal">
			<text class="hwx-indexed-list-text">
				索引
			</text>
			<text v-for="(items,index) in lists" :key="index" @tap="handleClick(index)" class="hwx-indexed-list-text"
				:style="{height:indexBarItemHeight+'px'}">
				{{items.letter}}
			</text>
		</view>
		<view class="hwx-indexed-list-alert" v-if="touchmove && lists[touchmoveIndex].letter">
			{{lists[touchmoveIndex].letter}}
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name:'m-selectCity',
		data() {
			return {
				
				touchmove: false, // 是否在索引表上滑动
				touchmoveIndex: -1,
				titleHeight: 0, // 索引二字距离窗口顶部的高度
				indexBarHeight: 0, // 索引表高度
				indexBarItemHeight: 0, // 索引表子项的高度
				scrollViewId: '', // scroll-view滚动到的子元素的id
				winHeight: 0,
				inputShowed: false, // 输入框是否显示
				inputVal: '', // 搜索框输入的内容
				// hotCity: ['北京', '上海', '广州', '深圳', '杭州', '长沙', '武汉', '厦门', '西安', '昆明', '成都', '重庆'], // 热门城市
				searchResult: [], // 搜索城市的结果
				localCity: ''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityList','city'])
		},
		props:{
			lists:{
				type:Array
			}
		},
		watch:{
			// lists : {
			// 	handler (oldVal,newVal) {
			// 		// 组件加载的时候就存储时间模式，全局通用
			// 		console.log(newVal,'pppoop');
			// 	},
			// 	immediate:true,
			// 	deep:true
			// }
		},

		mounted() {
			console.log(this.cityList,this.city);
			const that = this;
			that.localCity =  this.city.name;
			
			setTimeout(() => {
				uni.getSystemInfo({
					success: function(res) {
						let winHeight = res.windowHeight
						let barHeight = winHeight - uni.upx2px(204);
						that.indexBarHeight = barHeight;
						that.winHeight = winHeight;
						that.indexBarItemHeight = barHeight / 25;
						that.titleHeight = uni.upx2px(132);
					}
				})
			}, 50)
		},
		methods: {
			showInput() {
				this.inputShowed = true
			},
			clearInput() {
				console.log('clear');
				this.inputVal = "";
				this.inputShowed = false;
				this.searchResult = [];
				uni.hideKeyboard() //强行隐藏键盘
			},
			inputTyping(e) {
				this.inputVal = e.detail.value;
				this.searchCity()
			},
			// 搜索城市
			searchCity() {
				let result = []
				this.lists.forEach((item1, index1) => {
					item1.data.forEach((item2, index2) => {
						if (item2.name.indexOf(this.inputVal) !== -1) {
							result.push(item2)
						}
					})
				})
				this.searchResult = result
			},
			// 选择城市
			selectCity(e) {
				let cityName = e;
				this.$emit('choose', cityName)
				
			},
			touchStart(e) {
				this.touchmove = true
				let pageY = e.touches[0].pageY
				let index = Math.floor((pageY - this.titleHeight) / this.indexBarItemHeight)
				let item = this.lists[index === 0 ? 1 : index]
				if (item) {
					this.scrollViewId = item.letter;
					this.touchmoveIndex = index;
				}
			},
			touchMove(e) {
				let pageY = e.touches[0].pageY;
				let index = Math.floor((pageY - this.titleHeight) / this.indexBarItemHeight)
				let item = this.lists[index === 0 ? 1 : index]
				if (item) {
					this.scrollViewId = item.letter;
					this.touchmoveIndex = index
				}
			},
			touchEnd() {
				this.touchmove = false;
				this.touchmoveIndex = -1;
			},
			touchCancel() {
				this.touchmove = false;
				this.touchmoveIndex = -1;
			},
			handleClick(index) {
				if (index === undefined || this.touchmove) return;
				let item = this.lists[index]
				if (item) {
					this.scrollViewId = item.letter;
					this.touchmoveIndex = index;
				}
			}
		}
	}
</script>

<style>
	page {
		height: 100%;
		overflow: hidden;
	}

	.page {
		height: 100%;
		overflow: hidden;
	}

	.scrollList {
		flex: 1;
	}

	.search-bar {
		display: flex;
		align-items: center;
		position: relative;
		padding: 27rpx 30rpx 35rpx;
		/* background-color: #fff; */
	}

	.search-bar-form {
		flex: 1;
		position: relative;
		border-radius: 32rpx;
		display: flex;
		/* background: #f2f5f7; */
	}

	.search-bar-box {
		display: flex;
		align-items: center;
		position: relative;
		padding-left: 20rpx;
		padding-right: 20rpx;
		height: 64rpx;
		z-index: 1;
	}

	.search-bar-input {
		line-height: normal;
		
		width: 600rpx;
		padding-left: 20rpx;
		font-size: 30rpx;
		/* color: #333; */
	}

	.phcolor {
		font-size: 30rpx;
	}

	.icon-clear {
		display: flex;
		height: 40rpx;
		align-items: center;
	}

	.icon-clear .hwx-icon-class {
		display: block
	}

	.search-bar-label {
		height: 64rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 2;
		border-radius: 32rpx;
		/* color: #ccc; */
		/* background: #f2f5f7; */
	}

	.icon-search {
		position: relative;
		height: 26rpx;
		margin-right: 20rpx;
		font-size: inherit;
	}

	.search-bar-text {
		font-size: 30rpx;
		line-height: 32rpx;
	}

	.cancel-btn {
		padding-left: 30rpx;
	}

	.search-result::before {
		display: none;
	}

	.search-result::after {
		display: none;
	}

	.hwx-list-cell {
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		width: 100%;
	}

	.hwx-list-cell-hover {
		/* background-color: #eee !important; */
	}

	.hwx-list-cell-navigate {
		width: 100%;
		position: relative;
		padding: 30rpx 0 30rpx 30rpx;
		font-size: 28rpx;
		/* color: #333; */
	}

	.hwx-list-cell-navigate::after {
		content: '';
		position: absolute;
		/* border-bottom: 1rpx solid #eaeef1; */
		-webkit-transform: scaleY(0.5);
		transform: scaleY(0.5);
		bottom: 0;
		right: 0;
		left: 30rpx;
	}

	.current-city {
		padding: 30rpx 30rpx;
		/* background: #fff; */
	}

	.hwx-icon-class {
		margin-right: 10rpx;
	}

	.current-city .title {
		font-size: 24rpx;
		line-height: 24rpx;
		/* color: #999; */
	}

	.city-name {
		display: flex;
		align-items: center;
		margin-top: 17rpx;
		font-size: 30rpx;
		font-weight: bold;
		line-height: 30rpx;
		/* color: #333; */
	}

	.hot-city .title {
		height: 48rpx !important;
		padding-left: 30rpx;
		font-size: 24rpx !important;
		line-height: 48rpx !important;
		/* color: #999;/* 
		/* background: #f2f5f7 !important; */ 
	}

	.city-names {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-content: space-between;
		padding: 12rpx 90rpx 26rpx 30rpx;
		/* background: #fff; */
	}

	.city-name-item {
		display: flex;
		justify-content: center;
		align-items: center;
		width: 140rpx;
		height: 56rpx;
		margin-top: 16rpx;
		/* border: solid 1rpx #ccc; */
		border-radius: 28rpx;
		font-size: 28rpx;
		/* color: #333; */
		position: relative;
	}

	.city-name-item::before {
		content: "";
		position: absolute;
		width: 200%;
		height: 200%;
		-webkit-transform-origin: 0 0;
		transform-origin: 0 0;
		-webkit-transform: scale(0.5, 0.5);
		transform: scale(0.5, 0.5);
		-webkit-box-sizing: border-box;
		box-sizing: border-box;
		left: 0;
		top: 0;
		border-radius: 56rpx;
		/* border: 1px solid #ccc; */
	}

	.tap-city {
		/* color: #fff; */
		/* background: #5677fc; */
		/* border: solid 1rpx #5677fc; */
	}

	.hwx-list {
		
		position: relative;
		width: 100%;
		display: flex;
		flex-direction: column;
		padding-bottom: env(safe-area-inset-bottom);
	}

	.hwx-list-cell-divider {
		height: 48rpx;
		padding-left: 30rpx;
		font-size: 24rpx;
		/* color: #999; */
		/* background: #f2f5f7; */
		padding: 0 30rpx;
		display: flex;
		align-items: center;
	}

	.hwx-indexed-list-bar {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: flex-start;
		z-index: 9999;
		position: absolute;
		top: 132rpx;
		right: 0;
		padding-right: 10rpx;
		width: 44rpx;
	}

	.hwx-indexed-list-text {
		font-size: 22rpx;
		white-space: nowrap;
	}

	.hwx-indexed-list-bar.active {
		/* background-color: rgb(200, 200, 200); */
	}

	.hwx-indexed-list-alert {
		position: absolute;
		z-index: 20;
		width: 160rpx;
		height: 160rpx;
		left: 50%;
		top: 50%;
		margin-left: -80rpx;
		margin-top: -80rpx;
		border-radius: 80rpx;
		text-align: center;
		line-height: 160rpx;
		font-size: 70rpx;
		/* color: #fff; */
		/* background-color: rgba(0, 0, 0, 0.5); */
	}
</style>
