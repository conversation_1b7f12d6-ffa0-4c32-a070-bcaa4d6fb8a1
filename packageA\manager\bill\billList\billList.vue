<template>
	<view>
		<view class="roomManager">
			<p class="title">客房订单管理-{{manager.shop_name}}</p>
			<view class="roomManager_item">
				<text style="font-size: 24rpx;margin-right: 6rpx;">预抵时间</text>
				<view class="" v-for="(item, index) in readyIn" :key="index">
					<view class="itemBox" style="" :class="readyIndex==item.id?'clicked':''" @click="readyChoose(item)">
						<text>{{item.name}}</text>
					</view>
				</view>
				<view class="title" v-if="datetimerangeReadyIn.length > 0">
					<uni-datetime-picker @change="chooseDate" v-model="datetimerangeReadyIn" :clear-icon="false" type="daterange"
						rangeSeparator="至" :disabled="ifCustomReadyIn" />
				</view>
			</view>
			<view class="roomManager_item">
				<text style="font-size: 24rpx;margin-right: 6rpx;">实住时间</text>
				<view class="" v-for="(item, index) in checkIn" :key="index">
					<view class="itemBox" style="" :class="checkIndex==item.id?'clicked':''" @click="checkChoose(item)">
						<text>{{item.name}}</text>
					</view>
				</view>
				<view class="title" v-if="datetimerangeCheckIn.length > 0">
					<uni-datetime-picker v-model="datetimerangeCheckIn" :clear-icon="false" type="daterange"
						rangeSeparator="至" :disabled="ifCustomCheckIn" />
				</view>
			</view>
			<view class="roomManager_item">
				<text style="font-size: 24rpx;margin-right: 6rpx;">预离时间</text>
				<view class="" v-for="(item, index) in readyOut" :key="index">
					<view class="itemBox" style="" :class="readyOutIndex==item.id?'clicked':''"
						@click="readyOutChoose(item)">
						<text>{{item.name}}</text>
					</view>
				</view>
				<view class="title" v-if="datetimerangeReadyOut.length > 0">
					<uni-datetime-picker v-model="datetimerangeReadyOut" :clear-icon="false" type="daterange"
						rangeSeparator="至" :disabled="ifCustomReadyOut" />
				</view>
			</view>
			<view class="roomManager_item">
				<text style="font-size: 24rpx;margin-right: 6rpx;">实离时间</text>
				<view class="" v-for="(item, index) in checkOut" :key="index">
					<view class="itemBox" style="" :class="checkOutIndex==item.id?'clicked':''"
						@click="checkOutChoose(item)">
						<text>{{item.name}}</text>
					</view>
				</view>
				<view class="title" v-if="datetimerangeCheckOut.length > 0">
					<uni-datetime-picker v-model="datetimerangeCheckOut" :clear-icon="false" type="daterange"
						rangeSeparator="至" :disabled="ifCustomCheckOut" />
				</view>
			</view>

			<view class="msgItem">
				<p style="width: 100rpx;"><text style="font-size: 24rpx;">订单来源</text></p>
				<picker @change="bindChangeSource" :value="changeSourceIndex" range-key="source_name"
					:range="billSource">
					<view class="pickerBox">
						{{billSource[changeSourceIndex].source_name}}
						<view class="icon-down"
							style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
						</view>
					</view>
				</picker>
			</view>

			<view class="msgItem">
				<p style="width: 100rpx;"><text style="font-size: 24rpx;">入住类型</text></p>
				<picker @change="bindChangeType" :value="changeTypeIndex" range-key="name" :range="roomStatusBox">
					<view class="pickerBox">
						{{roomStatusBox[changeTypeIndex].name}}
						<view class="icon-down"
							style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
						</view>
					</view>
				</picker>
			</view>

			<view class="msgItem">
				<p style="width: 100rpx;"><text style="font-size: 24rpx;">房型</text></p>
				<picker @change="bindRoomType" :value="changeRoomTypeIndex" range-key="name" :range="roomTypeBox">
					<view class="pickerBox">
						{{roomTypeBox[changeRoomTypeIndex].name}}
						<view class="icon-down"
							style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
						</view>
					</view>
				</picker>
			</view>
			
			<view class="msgItem">
				<p style="width: 100rpx;"><text style="font-size: 24rpx;">预定人</text></p>
				<view class="" style="padding-left: 10rpx;">
					<input type="text" v-model="linkMan" placeholder="请输入预定人姓名" style="padding:0 10rpx;border: 1px solid #eee;height: 40rpx;">
				</view>
				<text style="font-size: 28rpx;color: darkgreen;margin-left: 20rpx;" @click="searchBill">搜索</text>
			</view>
		</view>

		<m-tabs :list="list1" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="tab_click"
			:activeIndex="current" :config="{color:themeColor.text_main_color,
						  fontSize:30,
						  activeColor:themeColor.com_color1,
						  underLineColor:themeColor.com_color1,
						  underLineWidth:80,
						  underLineHeight:0}">
		</m-tabs>
		<view class="contentBox">
			<view class=""
				style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
				v-if="bill_list.length==0">
				<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;"
					:style="{color:themeColor.com_color1}">
				</view>
				<p :style="{color:themeColor.com_color1}">暂无订单</p>
			</view>
			<view class="billCard" @click="billDetail(item)" v-for="(item, index) in bill_list" :key="index" v-else>
				<view class="item">
					<text class="item_title">房间:</text><text
						style="font-weight: 600;color: dodgerblue;">{{item.room_number}}{{item.connect_code?`(联房)`:''}}</text>
				</view>
				<view class="item">
					<text class="item_title">订单id:</text><text>{{item.id}}</text>
				</view>
				<view class="item">
					<text class="item_title">订单号:</text><text>{{item.bill_code}}</text>
				</view>

				<view class="item">
					<view v-if="item.bill_status==4||item.bill_status==5||item.bill_status==10"><text
							class="item_title">实住:</text><text class="">{{item.enter_time | moment3}}</text></view>
					<view v-else> <text class="item_title">预住:</text> <text>{{item.enter_time_plan | moment3}}</text>
					</view>
				</view>
				<view class="item">
					<view v-if="item.bill_status==5||item.bill_status==10"> <text
							class="item_title">实离:</text> <text>{{item.leave_time | moment3}}</text> </view>
					<view v-else> <text class="item_title">预离:</text> <text>{{item.leave_time_plan | moment3}}</text>
					</view>
				</view>
				<view class="item">
					<text class="item_title">下单时间:</text><text>{{item.create_time | moment3}}</text>
				</view>
				<view class="item">
					<text class="item_title">订单来源:</text><text>{{item.bill_source_name}}</text>
				</view>
				<view class="item">
					<text class="item_title">预定人:</text><text>{{item.link_man}} | {{item.link_phone}}</text>
				</view>
				<view class="item">
					<text class="item_title">订单状态:</text><text>{{billFormat(item.bill_status)}}</text>
				</view>
				<view class="item">
					<text class="item_title">销售方式:</text><text>{{item.room_sale_type_name}}</text>
				</view>
				<view class="item">
					<text class="item_title">房型:</text><text>{{item.room_type_name}}</text>
				</view>

				<view class="item">
					<text class="item_title">总房价:</text><text>{{item.bill_amount}}</text>
				</view>
				<view class="item">
					<text class="item_title">账单:</text>
					<text v-if="item.bill_balance < 0" style="color: brown;">欠费：{{item.bill_balance}}</text>
					<text v-else style="color: green;">余额：{{item.bill_balance}}</text>
				</view>
				<view class="" style="width: 100%;height: 80rpx;direction: rtl;margin-top: 30rpx;">
					<button size="mini" @click="billDetail(item)" style="margin-right: 10rpx;">订单详情</button>
					<button size="mini" type="primary" @click.stop="sureAccessTh(item)" v-if="item.bill_status == 2"
						style="margin-right: 10rpx;">确认接受订单</button>
					<button size="mini" type="warn" @click.stop="cancelAccessTh(item)"
						v-if="item.bill_status == 2||item.bill_status == 3" style="margin-right: 10rpx;">主动取消</button>
					<button size="mini" type="warn" @click.stop="noShowAccess(item)" v-if="item.bill_status == 3"
						style="margin-right: 10rpx;">noShow</button>
				</view>
			</view>
			<view class="" style="height: 60rpx;">

			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				linkMan:'',
				list1: [{
						id: 1,
						name: '全部',
						tip: 0
					},
					{
						id: 2,
						name: '待确认',
						tip: 0
					},
					{
						id: 3,
						name: '待入住',
						tip: 0
					},
					{
						id: 4,
						name: '入住中',
						tip: 0
					}, {
						id: 5,
						name: '已完成',
						tip: 0
					}, {
						id: 9,
						name: '预订未到',
						tip: 0
					}, {
						id: 10,
						name: '走结',
						tip: 0
					}, {
						id: 8,
						name: '申请退房',
						tip: 0
					}, {
						id: 6,
						name: '待取消',
						tip: 0
					}, {
						id: 7,
						name: '已取消',
						tip: 0
					}, {
						id: 11,
						name: 'noShow',
						tip: 0
					},
				],
				readyIn: [{
					id: 0,
					name: '不限',
				}, {
					id: 1,
					name: '上周',
				}, {
					id: 2,
					name: '昨日',
				}, {
					id: 3,
					name: '今日',
				}, {
					id: 4,
					name: '本周',
				}, {
					id: 5,
					name: '下周',
				}, {
					id: 6,
					name: '自定义',
				}],
				checkIn: [{
						id: 0,
						name: '不限'
					},
					{
						id: 1,
						name: '昨日'
					},
					{
						id: 2,
						name: '今日'
					},
					{
						id: 3,
						name: '自定义'
					}
				],
				readyOut: [{
						id: 0,
						name: '不限',
					}, {
						id: 1,
						name: '上月',
					}, {
						id: 2,
						name: '近七日',
					}, {
						id: 3,
						name: '近三日',
					}, {
						id: 4,
						name: '昨天',
					}, {
						id: 5,
						name: '今天',
					}, {
						id: 6,
						name: '明天',
					}, {
						id: 7,
						name: '后三天',
					}, {
						id: 8,
						name: '后七天',
					}, {
						id: 9,
						name: '本月',
					}, {
						id: 10,
						name: '下月',
					},
					{
						id: 11,
						name: '自定义',
					}

				],
				checkOut: [{
						id: 0,
						name: '不限',
					}, {
						id: 1,
						name: '上月',
					}, {
						id: 2,
						name: '近七日',
					}, {
						id: 3,
						name: '近三日',
					}, {
						id: 4,
						name: '昨天',
					}, {
						id: 5,
						name: '今天',
					}, {
						id: 6,
						name: '本月',
					},
					{
						id: 7,
						name: '自定义',
					}
				],
				readyIndex: 0, //预抵时间 
				checkIndex: 0, //实住时间
				readyOutIndex: 0, //预离
				checkOutIndex: 0, //预离
				ifCustomReadyIn: false,
				ifCustomCheckIn: false,
				ifCustomReadyOut: false,
				ifCustomCheckOut: false,
				datetimerangeReadyIn: [],
				datetimerangeCheckIn: [],
				datetimerangeReadyOut: [],
				datetimerangeCheckOut: [],
				// 订单来源
				billSource: [{
					id: 0,
					source_name: "全部",
					sign: "all"
				}],
				changeSourceIndex: 0,
				roomStatusBox: [{
					id: 0,
					name: "全部",
					sign: "all"
				}],
				changeTypeIndex: 0,
				roomTypeBox: [{
					id: 0,
					name: "全部",
					sign: "all"
				}],
				changeRoomTypeIndex: 0,
				tips: [],
				current: 0,
				params: {
					admin_id: "",
					arranged: null,
					bill_code: "",
					bill_source: "",
					bill_status: "",
					enter_time: {
						start_time: null,
						end_time: null
					},
					enter_time_plan: {
						start_time: null,
						end_time: null
					},
					intermediary_id: "",
					leave_time: {
						start_time: null,
						end_time: null
					},
					leave_time_plan: {
						start_time: null,
						end_time: null
					},
					limit: 10,
					link_man: "",
					link_phone: "",
					memo: "",
					page: 1,
					room_number: "",
					room_sale_type: "",
					room_type_id: "",
					room_user: ""

				},
				bill_list: [],
				bool: true
			};
		},
		watch: {
			datetimerangeReadyIn: {
				handler(newVal, oldVal) {
					console.log('ready',newVal);
					this.params.enter_time_plan.start_time = this.$moment(this.datetimerangeReadyIn[0],'YYYY-MM-DD HH:mm:ss').unix() 
					this.params.enter_time_plan.end_time = this.$moment(this.datetimerangeReadyIn[1],'YYYY-MM-DD HH:mm:ss').unix()
					this.getBill()
				},
				immediate: true,
				deep: true
			},
			datetimerangeCheckIn: {
				handler(newVal, oldVal) {
					this.params.enter_time.start_time = this.$moment(this.datetimerangeCheckIn[0],'YYYY-MM-DD HH:mm:ss').unix()
					this.params.enter_time.end_time = this.$moment(this.datetimerangeCheckIn[1],'YYYY-MM-DD HH:mm:ss').unix()
					this.getBill()
				},
				immediate: true,
				deep: true
			},
			datetimerangeReadyOut: {
				handler(newVal, oldVal) {
					this.params.leave_time_plan.start_time = this.$moment(this.datetimerangeReadyOut[0],'YYYY-MM-DD HH:mm:ss').unix()
					this.params.leave_time_plan.end_time = this.$moment(this.datetimerangeReadyOut[1],'YYYY-MM-DD HH:mm:ss').unix()
					this.getBill()
				},
				immediate: true,
				deep: true
			},
			datetimerangeCheckOut: {
				handler(newVal, oldVal) {
					this.params.leave_time.start_time =this.$moment(this.datetimerangeCheckOut[0],'YYYY-MM-DD HH:mm:ss').unix()
					this.params.leave_time.end_time = this.$moment(this.datetimerangeCheckOut[1],'YYYY-MM-DD HH:mm:ss').unix()
					this.getBill()
				},
				immediate: true,
				deep: true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list','manager']),
		},
		async onLoad(options) {
			await this.$onLaunched;
			this.$iBox
				.http('getRoomSellType', {})({
					method: 'post'
				})
				.then(res => {
					res.data.forEach(item => {
						this.roomStatusBox.push(item)
					})

					this.$iBox
						.http('bossGetRoomType', {})({
							method: 'post'
						})
						.then(res1 => {
							res1.data.forEach(item => {
								this.roomTypeBox.push(item)
							})
							console.log(this.roomStatusBox, this.roomTypeBox);
						})
				})
			// 订单来源
			this.$iBox
				.http('getBillSource', {})({
					method: 'post'
				})
				.then(res => {
					this.billSource = [...this.billSource, ...res.data]
				})
			this.params.page = 1
			this.getBill()

		},
		async onShow() {
			await this.$onLaunched;

		},
		methods: {
			readyChoose(e) {
				this.readyIndex = e.id
				this.dateFomateReadyIn(e)
			},
			checkChoose(e) {
				this.checkIndex = e.id
				this.dateFomateCheckIn(e)
			},
			readyOutChoose(e) {
				this.readyOutIndex = e.id
				this.dateFomateReadyOut(e)
			},
			checkOutChoose(e) {
				this.checkOutIndex = e.id
				this.dateFomateCheckOut(e)
			},
			// 确认订单 - 防重复触发版本
			sureAccessTh(e) {
				this.$iBox.throttle1(() => {
					this.sureAccess(e)
				}, 2000);
			},

			sureAccess(e) {
				// confirmRoomBill

				uni.showModal({
					title: '提示',
					content: '是否确认接受订单',
					success: (res) => {
						if (res.confirm) {
							// 显示加载状态
							uni.showLoading({
								title: '确认中...'
							});

							if (e.room_sale_type_sign == 'hour') {
								this.$iBox
									.http('HourconfirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '确认成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('确认订单失败:', err);
									})
							} else if (e.room_sale_type_sign == 'standard') {
								this.$iBox
									.http('confirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '确认成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('确认订单失败:', err);
									})
							} else if (e.room_sale_type_sign == 'long_standard') {
								this.$iBox
									.http('LongconfirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '确认成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('确认订单失败:', err);
									})
							} else {
								this.$iBox
									.http('ConferenceconfirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '确认成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('确认订单失败:', err);
									})
							}

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 取消订单 - 防重复触发版本
			cancelAccessTh(e) {
				this.$iBox.throttle1(() => {
					this.cancelAccess(e)
				}, 2000);
			},

			cancelAccess(e) {
				uni.showModal({
					title: '提示',
					content: '是否取消订单',
					success: (res) => {
						if (res.confirm) {
							// 显示加载状态
							uni.showLoading({
								title: '取消中...'
							});

							if (e.room_sale_type_sign == 'hour') {
								this.$iBox
									.http('HourcancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							} else if (e.room_sale_type_sign == 'standard') {
								this.$iBox
									.http('cancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							} else if (e.room_sale_type_sign == 'long_standard') {
								this.$iBox
									.http('LongcancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							} else {
								this.$iBox
									.http('ConferencecancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.params.page = 1
										this.getBill()
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							}

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			noShowAccess(e) {
				uni.showModal({
					title: '提示',
					content: '是否处置为noShow?',
					success: (res) => {
						if (res.confirm) {
							if (e.room_sale_type_sign == 'hour') {
								this.$iBox
									.http('HourtoNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										this.params.page = 1
										this.getBill()
									})
							} else if (e.room_sale_type_sign == 'standard') {
								this.$iBox
									.http('toNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										this.params.page = 1
										this.getBill()
									})
							} else if (e.room_sale_type_sign == 'long_standard') {
								this.$iBox
									.http('LongtoNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										this.params.page = 1
										this.getBill()
									})
							} else {
								this.$iBox
									.http('ConferencetoNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										this.params.page = 1
										this.getBill()
									})
							}


						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			billStatus(e) {
				let a = ''
				if (e == 2) {
					a = '待确认'
				} else if (e == 3) {
					a = '待入住'
				} else if (e == 4) {
					a = '入住中'
				} else if (e == 5) {
					a = '已完成'
				} else if (e == 6) {
					a = '待取消'
				} else if (e == 7) {
					a = '已取消'
				} else if (e == 8) {
					a = '申请退房'
				} else if (e == 9) {
					a = '预定未到'
				} else if (e == 10) {
					a = '走结'
				} else if (e == 11) {
					a = 'noshow'
				}
				return a
			},
			chooseDate(e){
				console.log(e,'eeee');
				this.params.page = 1
				this.params.enter_time_plan.start_time = this.datetimerangeReadyIn[0] / 1000
				this.params.enter_time_plan.end_time = this.datetimerangeReadyIn[1] / 1000
				this.getBill()
			},
			dateFomateReadyIn(e) {
				if (e.name == '不限') {
					this.datetimerangeReadyIn = []
				} else if (e.name == '上周') {
					let start = this.$moment().isoWeek(this.$moment().isoWeek() - 1).startOf('isoWeek')
						.unix() //.format('YYYY-MM-DD') // 周一日期 
					let end = this.$moment().isoWeek(this.$moment().isoWeek() - 1).endOf('isoWeek')
						.unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyIn[0] = start * 1000
					this.datetimerangeReadyIn[1] = end * 1000
					this.ifCustomReadyIn = true
				} else if (e.name == '昨日') {
					let start = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeReadyIn[0] = start * 1000
					this.datetimerangeReadyIn[1] = end * 1000
					this.ifCustomReadyIn = true
				} else if (e.name == '今日') {
					let start = this.$moment(this.$moment().format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeReadyIn[0] = start * 1000
					this.datetimerangeReadyIn[1] = end * 1000
					this.ifCustomReadyIn = true
				} else if (e.name == '本周') {
					let start = this.$moment().isoWeek(this.$moment().isoWeek()).startOf('isoWeek')
						.unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().isoWeek(this.$moment().isoWeek()).endOf('isoWeek')
						.unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyIn[0] = start * 1000
					this.datetimerangeReadyIn[1] = end * 1000
					this.ifCustomReadyIn = true
				} else if (e.name == '下周') {
					let start = this.$moment().isoWeek(this.$moment().isoWeek() + 1).startOf('isoWeek')
						.unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().isoWeek(this.$moment().isoWeek() + 1).endOf('isoWeek')
						.unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyIn[0] = start * 1000
					this.datetimerangeReadyIn[1] = end * 1000
					this.ifCustomReadyIn = true
				} else if (e.name == '自定义') {
					console.log('点了自定义');
					let start = this.$moment().unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().add(1, 'days').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyIn[0] = start * 1000
					this.datetimerangeReadyIn[1] = end * 1000
					this.ifCustomReadyIn = false
				}
				console.log(this.datetimerangeReadyIn,'this.datetimerangeReadyIn');
				this.params.page = 1
				this.params.enter_time_plan.start_time = this.datetimerangeReadyIn[0] / 1000
				this.params.enter_time_plan.end_time = this.datetimerangeReadyIn[1] / 1000
				this.getBill()
			},
			dateFomateCheckIn(e) {
				if (e.name == '不限') {
					this.datetimerangeCheckIn = []
				} else if (e.name == '昨日') {
					let start = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeCheckIn[0] = start * 1000
					this.datetimerangeCheckIn[1] = end * 1000
					this.ifCustomCheckIn = true
				} else if (e.name == '今日') {
					let start = this.$moment(this.$moment().format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeCheckIn[0] = start * 1000
					this.datetimerangeCheckIn[1] = end * 1000
					this.ifCustomCheckIn = true
				} else if (e.name == '自定义') {
					let start = this.$moment().unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().add(1, 'days').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeCheckIn[0] = start * 1000
					this.datetimerangeCheckIn[1] = end * 1000
					this.ifCustomCheckIn = false
				}
				this.params.page = 1
				this.params.enter_time.start_time = this.datetimerangeCheckIn[0] / 1000
				this.params.enter_time.end_time = this.datetimerangeCheckIn[1] / 1000
				this.getBill()
			},
			dateFomateReadyOut(e) {
				if (e.name == '不限') {
					this.datetimerangeReadyOut = []
				} else if (e.name == '上月') {
					let start = this.$moment().month(this.$moment().month() - 1).startOf('month')
						.unix() //.format('YYYY-MM-DD') // 周一日期 
					let end = this.$moment().month(this.$moment().month() - 1).endOf('month')
						.unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '近七日') {
					let start = this.$moment(this.$moment().add(-7, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周一日期 
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '近三日') {
					let start = this.$moment(this.$moment().add(-3, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周一日期 
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '昨天') {
					let start = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '今天') {
					let start = this.$moment(this.$moment().format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '明天') {
					let start = this.$moment(this.$moment().add(1, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().add(1, 'days').format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '后三天') {
					let start = this.$moment(this.$moment().format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().add(3, 'days').format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '后七天') {
					let start = this.$moment(this.$moment().format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().add(7, 'days').format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '本月') {
					let start = this.$moment().month(this.$moment().month()).startOf('month')
						.unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().month(this.$moment().month()).endOf('month').unix()
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '下月') {
					let start = this.$moment().month(this.$moment().month() + 1).startOf('month')
						.unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().month(this.$moment().month() + 1).endOf('month').unix()
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = true
				} else if (e.name == '自定义') {
					let start = this.$moment().unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().add(1, 'days').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeReadyOut[0] = start * 1000
					this.datetimerangeReadyOut[1] = end * 1000
					this.ifCustomReadyOut = false
				}
				this.params.page = 1
				this.params.leave_time_plan.start_time = this.datetimerangeReadyOut[0] / 1000
				this.params.leave_time_plan.end_time = this.datetimerangeReadyOut[1] / 1000
				this.getBill()
			},
			dateFomateCheckOut(e) {
				if (e.name == '不限') {
					this.datetimerangeCheckOut = []
				} else if (e.name == '上月') {
					let start = this.$moment().month(this.$moment().month() - 1).startOf('month')
						.unix() //.format('YYYY-MM-DD') // 周一日期 
					let end = this.$moment().month(this.$moment().month() - 1).endOf('month')
						.unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeCheckOut[0] = start * 1000
					this.datetimerangeCheckOut[1] = end * 1000
					this.ifCustomCheckOut = true
				} else if (e.name == '近七日') {
					let start = this.$moment(this.$moment().add(-7, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周一日期 
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeCheckOut[0] = start * 1000
					this.datetimerangeCheckOut[1] = end * 1000
					this.ifCustomCheckOut = true
				} else if (e.name == '近三日') {
					let start = this.$moment(this.$moment().add(-3, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周一日期 
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeCheckOut[0] = start * 1000
					this.datetimerangeCheckOut[1] = end * 1000
					this.ifCustomCheckOut = true
				} else if (e.name == '昨天') {
					let start = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().add(-1, 'days').format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeCheckOut[0] = start * 1000
					this.datetimerangeCheckOut[1] = end * 1000
					this.ifCustomCheckOut = true
				} else if (e.name == '今天') {
					let start = this.$moment(this.$moment().format('YYYY/MM/DD') + `00:00:00`,
						'YYYY/MM/DD HH:mm:ss').unix()
					let end = this.$moment(this.$moment().format('YYYY/MM/DD') + `23:59:59`,
						'YYYY/MM/DD HH:mm:ss').unix()
					this.datetimerangeCheckOut[0] = start * 1000
					this.datetimerangeCheckOut[1] = end * 1000
					this.ifCustomCheckOut = true
				} else if (e.name == '本月') {
					let start = this.$moment().month(this.$moment().month()).startOf('month')
						.unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().month(this.$moment().month()).endOf('month').unix()
					this.datetimerangeCheckOut[0] = start * 1000
					this.datetimerangeCheckOut[1] = end * 1000
					this.ifCustomCheckOut = true
				} else if (e.name == '自定义') {
					let start = this.$moment().unix() //.format('YYYY-MM-DD') // 周一日期
					let end = this.$moment().add(1, 'days').unix() //.format('YYYY-MM-DD') // 周日日期
					this.datetimerangeCheckOut[0] = start * 1000
					this.datetimerangeCheckOut[1] = end * 1000
					this.ifCustomCheckOut = false
				}
				this.params.page = 1
				this.params.leave_time.start_time = this.datetimerangeCheckOut[0] / 1000
				this.params.leave_time.end_time = this.datetimerangeCheckOut[1] / 1000
				this.getBill()
			},
			bindChangeSource(e) {
				this.params.page = 1
				this.changeSourceIndex = e.detail.value[0]
				this.params.bill_source = this.billSource[e.detail.value[0]].id == 0 ? '' : this.billSource[e.detail.value[
					0]].id
				this.getBill()
			},
			bindChangeType(e) {
				this.params.page = 1
				this.changeTypeIndex = e.detail.value[0]
				this.params.room_sale_type = this.roomStatusBox[e.detail.value[0]].id == 0 ? '' : this.roomStatusBox[e
					.detail.value[0]].id
				this.getBill()
			},
			bindRoomType(e) {
				this.params.page = 1
				this.changeRoomTypeIndex = e.detail.value[0]
				this.params.room_type_id = this.roomTypeBox[this.changeRoomTypeIndex].id == 0 ? '' : this.roomTypeBox[this
					.changeRoomTypeIndex].id
				this.getBill()
			},
			searchBill(){
				this.params.page = 1
				this.params.link_man = this.linkMan
				this.getBill()
			},
			tab_click(e) {
				console.log(e);
				if (e == 0) {
					this.params.bill_status = ''
				} else if (e == 1) {
					this.params.bill_status = 2
				} else if (e == 2) {
					this.params.bill_status = 3
				} else if (e == 3) {
					this.params.bill_status = 4
				} else if (e == 4) {
					this.params.bill_status = 5
				} else if (e == 5) {
					this.params.bill_status = 9
				} else if (e == 6) {
					this.params.bill_status = 10
				} else if (e == 7) {
					this.params.bill_status = 8
				} else if (e == 8) {
					this.params.bill_status = 6
				} else if (e == 9) {
					this.params.bill_status = 7
				} else if (e == 10) {
					this.params.bill_status = 11
				}
				this.params.page = 1
				this.getBill()
			},
			billFormat(e) {
				switch (e) {
					case 1:
						return '待付款'
						break;
					case 2:
						return '待确认'
						break;
					case 3:
						return '待入住'
						break;
					case 4:
						return '入住中'
						break;
					case 5:
						return '已完成'
						break;
					case 6:
						return '待取消'
						break;
					case 7:
						return '已取消'
						break;
					case 8:
						return '申请退房'
						break;
					case 9:
						return '预定未到'
						break;
					case 10:
						return '走结'
						break;
					case 11:
						return 'noShow'
						break;
					default:
						break;
				}
			},
			getBill(e) {
				// 查询订单
				uni.showLoading({
					title: '加载中...'
				})
				this.params.page = 1
				this.$iBox.http('getBossRoomBillList', this.params)({
					method: 'post'
				}).then(res => {

					this.bill_list = res.data.list
					this.tips = res.data.tips
					this.list1.forEach(item => {
						if (item.id == 1) {
							item.tip = res.data.tips.tip_all
						} else if (item.id == 2) {
							item.tip = res.data.tips.tip_2
						} else if (item.id == 3) {
							item.tip = res.data.tips.tip_3
						} else if (item.id == 4) {
							item.tip = res.data.tips.tip_4
						} else if (item.id == 5) {
							item.tip = res.data.tips.tip_5
						} else if (item.id == 6) {
							item.tip = res.data.tips.tip_6
						} else if (item.id == 7) {
							item.tip = res.data.tips.tip_7
						} else if (item.id == 8) {
							item.tip = res.data.tips.tip_8
						} else if (item.id == 9) {
							item.tip = res.data.tips.tip_9
						} else if (item.id == 10) {
							item.tip = res.data.tips.tip_10
						} else if (item.id == 11) {
							item.tip = res.data.tips.tip_11
						}
					})
					console.log(this.list1, 'this.list1');
					uni.hideLoading()
				})
			},
			billDetail(e) {
				uni.navigateTo({
					url: '../billInfo/billInfo?id=' + e.id
				})
			}
		},
		// // 上拉加载
		onReachBottom() {
			console.log('下zai');
			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getBossRoomBillList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.bill_list.concat(res.data.list)
					this.bill_list = new_list
					if (this.bill_list.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	.roomManager {
		width: 96%;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin: 20rpx auto;

		.title {
			font-size: 34rpx;
			font-weight: 600;
			margin: 14rpx 0;
			display: flex;
			align-items: center;
		}

		.roomManager_item {
			display: flex;
			flex-wrap: wrap;
			// justify-content: center;
			align-items: center;
			margin: 10rpx 0;

			.itemBox {
				width: 80rpx;
				height: 44rpx;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 24rpx;
				border: 1px solid #eee;

			}

			.clicked {
				background-color: #0055ff;
				color: #ffffff;
			}
		}

		.msgItem {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			margin: 6rpx 0;

			.msgInput {
				border: 1px solid #ece8e8;
				height: 30rpx;
				width: 340rpx;
				border-radius: 4px;
				padding: 4px 10px;
				font-size: 24rpx;
				margin-left: 36rpx;
			}

			.pickerBox {
				margin-left: 16rpx;
				position: relative;
				height: 42rpx;
				width: 380rpx;
				border-radius: 4rpx;
				border: 1px solid #eee;
				display: flex;
				padding: 0 20rpx;
				font-size: 24rpx;
				align-items: center;

				.arrow {
					animation-name: to_bottom_show;
					animation-duration: 0.2s;
					animation-timing-function: linear;
					/* animation-delay: 1s; */
					/* animation-iteration-count: infinite; */
					animation-direction: normal;
					animation-play-state: running;
					animation-fill-mode: forwards;
				}

				.arrow_ac {
					animation-name: to_up_show;
					animation-duration: 0.2s;
					animation-timing-function: linear;
					/* animation-delay: 1s; */
					/* animation-iteration-count: infinite; */
					animation-direction: normal;
					animation-play-state: running;
					animation-fill-mode: forwards;
				}

				/* 箭头动画 */

				@keyframes to_up_show {
					0% {
						transform: rotate(0);
					}

					50% {
						transform: rotate(90deg);
					}

					100% {
						transform: rotate(180deg);
					}
				}

				@keyframes to_bottom_show {
					0% {
						transform: rotate(180deg);
						animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
					}

					50% {
						transform: rotate(90deg);
						animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
					}

					100% {
						transform: rotate(0deg);
					}
				}
			}
		}
	}

	.contentBox {
		width: 100%;
		height: auto;
		margin-top: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.billCard {
			min-height: 240rpx;
			background: #ffffff;
			border-radius: 16rpx;
			width: 94%;
			padding: 20rpx 20rpx;
			margin-top: 30rpx;
			display: flex;
			flex-wrap: wrap;
			justify-content: space-between;

			.item {
				width: fit-content;
				padding-right: 20rpx;
				line-height: 44rpx;

				.item_title {
					color: #909399;
				}
			}

		}
	}
</style>
