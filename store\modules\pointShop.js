import Vue from 'vue'

const state = {
	pointShopDetail:null,
}

const mutations = {
	// 自定义tabbar栏
	PUSHDETAIL: (state, pointShopDetail) => {
		state.pointShopDetail = pointShopDetail
		console.log(pointShopDetail,'pointShopDetail');
	}
	
}

const actions = {
	getPointShopDetail({
		commit
	}, params) {
		commit('PUSHDETAIL', params)
	},
}

export default {
	namespaced: true,
	state,
	mutations,
	actions
}
