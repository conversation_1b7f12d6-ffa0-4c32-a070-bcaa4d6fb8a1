<template>
	<view>
		<view class="" style="width: 700rpx;margin: 0 auto;">
			<uni-easyinput prefixIcon="search" v-model="lock_alias" placeholder="请输入房间号" @input="inputLock">
				</uni-easyinput>
		</view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="lockListSearch.length==0">
			<view class="icon-suoding" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无门锁</p>
		</view>
		<view class="box" style="background:#fff" v-else v-for="item in lockListSearch" @click="goLock(item)"
			hover-class="bind_lock">
			<text class="icon-mimasuo" style="font-size: 80rpx;"></text>
			<view class="content" style="width: 70%;">
				<view class="" style="display: flex;align-items: center;width: 100%;justify-content: space-between;">
					<view class="">
						<text>{{item.lock_alias}}</text>
						<text style="color: #55aa00;" v-if="item.isShow==1">（已连接）</text>
						<text style="color: #C51A15;" v-else>（未连接）</text>
						
					</view>
				</view>

				<view style="display: flex;align-items: center;font-size: 22rpx;">
					<view class="icon-iconset0252">
					</view>
					<text>{{item.electric_quantity}}%</text>
				</view>
			</view>
			<view class="" style="width: 30%;height: 100%;display: flex;flex-direction: column;justify-content: center;align-items: center;">
				<view style="padding: 10rpx 20rpx;;width: fit-content;border-radius: 10rpx;display: flex;align-items: cenrer;justify-content: center;text-align: center;font-size: 24rpx;" 
				:style="{background:themeColor.main_color,color:themeColor.bg_color}" @click.stop="goLock(item)">查看详情</view>
			</view>
		</view>
		<view class="" style="height: 100rpx;"></view>
		<!-- 添加按钮 -->
		<view class="addLock" @click="info" v-if="roleType('lock_del_add')" :style="{background:themeColor.com_color1,color:themeColor.bg_color}">
			<view class="icon-tianjia"></view>
			<text style="font-size: 24rpx;">添加门锁</text>
		</view>
	</view>
</template>

<script>
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				params: {
					page: 1,
					limit: 500
				},
				bool: true,
				lockList: [],
				lockListSearch:[],
				lock_alias:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
		},
		watch:{
			lockListSearch:{
				handler(newDate, oldDate){
					this.lockListSearch = this.lockListSearch
				},
				immediate:true,
				deep:true
			}
		},
		onLoad(options) {
		},
		async onShow() {
			await this.$onLaunched;
			this.bool = true;
			this.params.page = 1;
			this.getTTList();
		},
		methods: {
			...mapActions('room', ['getLockData']),
			inputLock(){
				this.params.lock_alias = this.lock_alias
				this.params.page = 1
				this.bool = true;
				this.getTTList();
			},
			getTTList() {
				uni.showLoading({
					title: '正在搜索设备'
				})
				this.params.page = 1
				this.$iBox.http('getTtLockList', this.params)({
						method: 'post'
					})
					.then(res => {
						this.lockList = res.data.list
						//首先通过蓝牙搜索
						this.startScan()
						uni.hideLoading()
					})
			},
			
			goLock(e) {
					this.getLockData(e)
					if(this.roleType('lock_edit') || this.roleType('view_password') || this.roleType('open_lock')){
						uni.navigateTo({
							url:'/packageA/manager/lockList/TTLock/lockSet'
						})
						
					}else{
						uni.showModal({
							title: '提示',
							content: '您没有编辑权限',
							showCancel: false,
							success() {
						
							}
						})
					}
			},
			roleType(e){
				let role= this.roles_list.filter(item => {
					return item.permission == e
				})
				
				if(role.length >0){
					return true
				}else{
					return false
				}
			},
			info() {
				let add = this.roles_list.filter(item => {
					return item.permission == "lock_del_add"
				})
				
				if(add.length > 0){
					console.log(this.roles_list,add,'dad');
					uni.navigateTo({
						url:'/packageA/manager/lockList/TTLock/roomInfo'
					})
				}else{
					uni.showModal({
						title: '提示',
						content: '您没有添加/删除权限',
						showCancel: false,
						success() {
					
						}
					})
				}
				
			},
			
			startScan() {
				// 让门锁先显示出来再更新状态
				this.lockListSearch =  this.lockList
				uni.showLoading({
					title: '正在搜索蓝牙设备'
				})
				plugin.startScanBleDevice((lockDevice, lockList) => {
					let a = JSON.parse(JSON.stringify(this.lockList)) 
					if(lockList.length > 0){
						for (let item of lockList) {
							for (let item1 of a) {
								if (item.lockMac == item1.lock_mac) {
									item1.isShow = 1
								} else {
									item1.isShow = 0
								}
							}
						}
					}
					this.lockListSearch = a
					console.log(this.lockListSearch,'通通锁返回1');
					uni.hideLoading()
				}, err => {
					console.log(this.lockListSearch,'err1');
					this.lockListSearch =  this.lockList
					uni.hideLoading()
					uni.showToast({
						icon: 'none',
						title: err
					})

				})
			}
		},
		// // 上拉加载
		onReachBottom() {
			if (this.bool) {
				++this.params.page
				this.status = 'loadmore'
				this.$iBox.http('getTtLockList', this.params)({
					method: 'post'
				}).then(res => {
					console.log('我是返回', res.data)
					let new_list = this.lockList.concat(res.data.list)
					this.lockList = new_list
					if (this.lockList.length == res.data.count) {
						this.bool = false
					}

					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	.box {
		height: 200rpx;
		width: 90%;
		border: 1px solid #eee;
		display: flex;
		align-items: center;
		padding: 0 30rpx;
		margin: 30rpx auto;
		border-radius: 20rpx;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
		.content {
			display: flex;
			flex-direction: column;

			justify-content: center;
			padding-left: 20rpx;
		}
	}

	.addLock {
		position: fixed;
		bottom: 10rpx;
		right: 0;
		left: 0;
		height: 80rpx;
		width: 600rpx;
		margin: 0 auto;
		border-radius: 20rpx;
		background-color: #CCCCCC;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.bind_lock {
		opacity: 0.9;
		background: #f7f7f7;
	}
</style>
