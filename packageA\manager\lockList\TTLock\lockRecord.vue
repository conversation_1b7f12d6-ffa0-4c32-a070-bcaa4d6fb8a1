<template>
	<view>
		<m-tabs :list="titleList" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="changeType"
			:activeIndex="current" :config="{color:themeColor.text_main_color,
						  fontSize:30,
						  activeColor:themeColor.com_color1,
						  underLineColor:themeColor.com_color1,
						  underLineWidth:80,
						  underLineHeight:5}">
		</m-tabs>
		<view class=""
			style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
			v-if="logList.length==0">
			<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无记录</p>
		</view>
		<view v-for="(item,index) in logList" :key="item in recordId" class="box" v-if="logList.length>0">
			<p>第{{index + 1}}条记录</p>
			<p>开锁类型:{{logType(item.recordType)}}</p>
			<p v-if="item.password">开锁密码:{{item.password}}</p>
			<p>操作时间:{{item.operateDate/1000 | moment1}}</p>
			<p>电量:{{item.electricQuantity}}</p>
		</view>

	</view>
</template>

<script>
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				titleList: [{
					id: 1,
					name: '最新记录'
				},
				{
					id: 2,
					name: '全部记录'
				}],
				current:0,
				logList: []
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		onLoad() {
			this.getNew()
		},
		methods: {
			changeType(e){
				console.log(e);
				if(e==0){
					this.getNew()
				}else{
					this.getAll()
				}
			},
			getNew(){
				uni.showLoading({
					title: 'loading...'
				})
				
				let deviceId = ""
				// let type = event.currentTarget.dataset.type === 1 ? plugin.RecordReadType.ALL : plugin.RecordReadType.NEW;
				uni.showLoading({
					title: `正在读取锁内操作记录`,
				})
				const start = Date.now();
				// 获取操作记录
				plugin.getOperationLog(plugin.RecordReadType.NEW, this.lockDetail.lock_data, res => {
					if (res.errorCode === 10003) {
						console.log("监控到设备连接已断开", res)
					}
				}, deviceId).then(res => {
					uni.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'success',
							title: `操作记录已获取--操作时间::${Date.now() - start}`
						})
						this.logList = JSON.parse(res.log)
						console.log(this.logList, 'this.logList');
					} else {
						uni.showToast({
							icon: 'error',
							title: "读取操作记录失败:" + res.errorMsg
						})
				
					}
				})
			},
			getAll(){
				uni.showLoading({
					title: 'loading...'
				})
				
				let deviceId = ""
				// let type = event.currentTarget.dataset.type === 1 ? plugin.RecordReadType.ALL : plugin.RecordReadType.NEW;
				uni.showLoading({
					title: `正在读取锁内操作记录`,
				})
				const start = Date.now();
				// 获取操作记录
				plugin.getOperationLog(plugin.RecordReadType.ALL, this.lockDetail.lock_data, res => {
					if (res.errorCode === 10003) {
						console.log("监控到设备连接已断开", res)
					}
				}, deviceId).then(res => {
					uni.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'success',
							title: `操作记录已获取--操作时间::${Date.now() - start}`
						})
						this.logList = JSON.parse(res.log)
						console.log(this.logList, 'this.logList');
					} else {
						uni.showToast({
							icon: 'error',
							title: "读取操作记录失败:" + res.errorMsg
						})
				
					}
				})
			},
			logType(e) {
				switch (e) {
					case 1:
						return '蓝牙开锁';
						break;
					case 4:
						return '密码开锁成功';
						break;
					case 5:
						return '在锁上修改密码';
						break;
					case 6:
						return '在锁上删除一个密码';
						break;
					case 7:
						return '密码开锁失败，未知密码';
						break;
					case 8:
						return '清空密码';
						break;
					case 9:
						return '密码被挤掉';
						break;
					case 10:
						return '带删除功能密码第一次开锁，之前密码被清空';
						break;
					case 11:
						return '密码开锁失败，密码已过期或未生效';
						break;
					case 12:
						return '密码开锁失败，存储容量不足';
						break;
					case 13:
						return '密码开锁失败—密码在黑名单';
						break;
					case 14:
						return '门锁重新上电启动了';
						break;
					case 15:
						return '添加IC卡成功';
						break;
					case 16:
						return '清空IC卡';
						break;
					case 17:
						return 'IC卡开门成功';
						break;
					case 27:
						return '机械钥匙开锁';
						break;
					default:
						break;
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		width: 100vw;
		min-height: 200rpx;
		border-bottom: 1px solid #CCCCCC;
		display: flex;
		flex-direction: column;

		padding: 30rpx;
		background: #fff;
	}
</style>
