/**
 * 性能测试脚本
 * 用于测试App.vue和mainPage.vue的优化效果
 */

import performanceMonitor from '../utils/PerformanceMonitor'
import { requestOptimizer } from '../flyio/request'

class PerformanceTest {
	constructor() {
		this.testResults = []
		this.testStartTime = Date.now()
	}

	// 运行所有性能测试
	async runAllTests() {
		console.log('🚀 开始性能测试...')
		
		try {
			// 测试1: 缓存功能测试
			await this.testCachePerformance()
			
			// 测试2: 请求去重测试
			await this.testRequestDeduplication()
			
			// 测试3: 并行请求测试
			await this.testParallelRequests()
			
			// 测试4: 错误处理测试
			await this.testErrorHandling()
			
			// 生成测试报告
			this.generateTestReport()
			
		} catch (error) {
			console.error('❌ 性能测试失败:', error)
		}
	}

	// 测试缓存性能
	async testCachePerformance() {
		console.log('📋 测试缓存性能...')
		
		const testData = { test: 'cache_test', timestamp: Date.now() }
		const url = 'testAPI'
		const params = { id: 1 }

		// 设置缓存
		const startTime = Date.now()
		requestOptimizer.cacheManager.set(url, params, testData)
		const setTime = Date.now() - startTime

		// 获取缓存
		const getStartTime = Date.now()
		const cachedData = requestOptimizer.cacheManager.get(url, params)
		const getTime = Date.now() - getStartTime

		const testResult = {
			test: 'cache_performance',
			setTime,
			getTime,
			dataMatches: JSON.stringify(cachedData) === JSON.stringify(testData),
			passed: setTime < 10 && getTime < 5 && cachedData !== null
		}

		this.testResults.push(testResult)
		console.log('✅ 缓存性能测试完成:', testResult)
	}

	// 测试请求去重
	async testRequestDeduplication() {
		console.log('📋 测试请求去重...')
		
		const url = 'testDeduplication'
		const params = { test: 'dedup' }
		
		// 模拟同时发起多个相同请求
		const startTime = Date.now()
		const promises = []
		
		for (let i = 0; i < 5; i++) {
			promises.push(
				requestOptimizer.deduplicateRequest(url, params, {})
					.catch(() => ({ error: true }))
			)
		}

		const results = await Promise.all(promises)
		const endTime = Date.now()

		const testResult = {
			test: 'request_deduplication',
			requestCount: promises.length,
			duration: endTime - startTime,
			allResultsSame: results.every(r => JSON.stringify(r) === JSON.stringify(results[0])),
			passed: results.length === 5 && (endTime - startTime) < 1000
		}

		this.testResults.push(testResult)
		console.log('✅ 请求去重测试完成:', testResult)
	}

	// 测试并行请求
	async testParallelRequests() {
		console.log('📋 测试并行请求...')
		
		const requests = [
			{ url: 'test1', params: { id: 1 }, config: {} },
			{ url: 'test2', params: { id: 2 }, config: {} },
			{ url: 'test3', params: { id: 3 }, config: {} }
		]

		const startTime = Date.now()
		const results = await requestOptimizer.parallelRequest(requests)
		const endTime = Date.now()

		const testResult = {
			test: 'parallel_requests',
			requestCount: requests.length,
			duration: endTime - startTime,
			allCompleted: results.length === requests.length,
			passed: results.length === requests.length && (endTime - startTime) < 2000
		}

		this.testResults.push(testResult)
		console.log('✅ 并行请求测试完成:', testResult)
	}

	// 测试错误处理
	async testErrorHandling() {
		console.log('📋 测试错误处理...')
		
		const startTime = Date.now()
		
		try {
			// 模拟一个会失败的请求
			await requestOptimizer.deduplicateRequest('nonexistentAPI', {}, {})
		} catch (error) {
			// 预期会有错误
		}

		const endTime = Date.now()

		const testResult = {
			test: 'error_handling',
			duration: endTime - startTime,
			errorHandled: true,
			passed: (endTime - startTime) < 5000 // 错误处理应该很快
		}

		this.testResults.push(testResult)
		console.log('✅ 错误处理测试完成:', testResult)
	}

	// 生成测试报告
	generateTestReport() {
		const totalTests = this.testResults.length
		const passedTests = this.testResults.filter(t => t.passed).length
		const totalDuration = Date.now() - this.testStartTime

		const report = {
			summary: {
				totalTests,
				passedTests,
				failedTests: totalTests - passedTests,
				successRate: ((passedTests / totalTests) * 100).toFixed(2) + '%',
				totalDuration: totalDuration + 'ms'
			},
			details: this.testResults,
			performanceMetrics: performanceMonitor.getPerformanceReport(),
			recommendations: this.generateRecommendations()
		}

		console.log('📊 性能测试报告:')
		console.log(JSON.stringify(report, null, 2))

		return report
	}

	// 生成优化建议
	generateRecommendations() {
		const recommendations = []
		
		// 检查缓存性能
		const cacheTest = this.testResults.find(t => t.test === 'cache_performance')
		if (cacheTest && !cacheTest.passed) {
			recommendations.push('缓存性能需要优化，考虑使用更高效的存储方案')
		}

		// 检查请求性能
		const parallelTest = this.testResults.find(t => t.test === 'parallel_requests')
		if (parallelTest && parallelTest.duration > 1000) {
			recommendations.push('并行请求耗时较长，检查网络连接或服务器响应时间')
		}

		// 检查错误处理
		const errorTest = this.testResults.find(t => t.test === 'error_handling')
		if (errorTest && errorTest.duration > 3000) {
			recommendations.push('错误处理耗时过长，优化错误处理逻辑')
		}

		if (recommendations.length === 0) {
			recommendations.push('所有测试通过，性能表现良好！')
		}

		return recommendations
	}
}

// 导出测试类
export default PerformanceTest

// 如果在浏览器环境中，可以直接运行测试
if (typeof window !== 'undefined') {
	window.runPerformanceTest = async () => {
		const test = new PerformanceTest()
		return await test.runAllTests()
	}
}
