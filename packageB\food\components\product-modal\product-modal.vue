<template>
	<m-popup :show="visible" mode="center" @closePop="closePop" :closeable="true">
		<view class="" style="max-height: 90vh;width: 700rpx;border-radius: 20rpx;padding: 20rpx;">
			<view class="header">
				<!-- <image src="/static/images/index/menupopup_btn_share_normal.png"></image> -->
				<!-- <image src="/static/images/index/round_close_btn.png" @tap="$emit('cancel')"></image> -->
			</view>

			<scroll-view scroll-y class="content">
				<swiper :duration="1000" indicator-dots class="swiper" autoplay :interval="3000">
					<swiper-item :key="index" class="swiper-item">
						<image :src="productData.cover_pic" style="height: 100%;width: 100%;" mode="aspectFit"></image>
					</swiper-item>
				</swiper>
				<view class="wrapper">
					<view class="title">{{ productData.name }}</view>
					<view class="labels">
						<view class="label" v-for="(label, index) in productData.labels" :key="index"
							:style="{color: label.label_color, background: $util.hexToRgba(label.label_color, 0.2)}">
							{{ label.name }}
						</view>
					</view>
					<view class="mb-10">产品描述</view>
					<rich-text :nodes="richDetail"></rich-text>
				</view>
			</scroll-view>
			<view class="materials" v-for="(material, index) in productData.specification_types" :key="index">
				<view class="group-name">{{ material.specification_type_name }}</view>
				<view class="values">
					<view class="value" :class="{selected: value.is_selected}" @tap="changeMaterialSelected(index, key)"
						v-for="(value, key) in material.specifications" :key="key">
						{{ value.specification_name }}
					</view>
				</view>
			</view>
			<view class="bottom" v-if="productData.stock>0"
				:style="{height: !productData.is_single ? '350rpx' : '300rpx'}">
				<view class="" style="display: flex;align-items: center;">
					<view class="price-and-materials">
						<view class="price">￥{{ productData.allPrice }}</view>
						<view class="materials" v-show="getProductSelectedMaterials">{{ getProductSelectedMaterials }}
						</view>
					</view>
					<actions :number="productData.number" @add="add" @minus="minus"></actions>
				</view>
				<view class="add-cart-btn" :style="{background:themeColor.main_color}" @tap="addToCart">加入购物袋</view>
			</view>
			<view class="bottom" v-if="productData.stock==0"
				:style="{height: !productData.is_single ? '250rpx' : '200rpx'}">

				<view class="add-cart-btn" style="color: black;">该商品已售罄</view>
			</view>
		</view>
	</m-popup>
</template>

<script>
	import Actions from '../actions/actions.vue'
	import {
		mapState,
		mapMutations
	} from 'vuex'
	export default {
		name: 'ProductModal',
		components: {
			Actions
		},
		props: {
			visible: {
				type: Boolean,
				default: false
			},
			product: {
				type: Object,
				default: () => {}
			}
		},
		data() {
			return {
				productData: {},
				richDetail: ''
			}
		},
		watch: {
			product(val) {
				this.productData = JSON.parse(JSON.stringify(val))
				if (this.productData.specification_types && this.productData.specification_types.length > 0) {

					this.$set(this.productData, 'number', 1)
					this.richDetail = this.$iBox.formatRichText(this.productData.detail)
				}

			}
		},
		mounted() {


		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel']),
			getProductSelectedMaterials() {

				if (this.productData.specification_types && this.productData.specification_types.length > 0) {
					let materials = []

					this.productData.specification_types.forEach((values) => {
						// console.log(values, 'values');
						values.specifications.forEach((value, index) => {
							if (value.is_selected) {
								materials.push(value.specification_name)
							}
						})
					})
					return materials.length ? materials.join('，') : ''
				}
				return ''
			},
			getProductSelectedMaterialsIds() {

				if (this.productData.specification_types && this.productData.specification_types.length > 0) {
					let materialsIds = []
					this.productData.specification_types.forEach((values) => {
						// console.log(values, 'values');
						values.specifications.forEach((value, index) => {
							if (value.is_selected) {
								materialsIds.push(value.id)
							}
						})
					})
					return materialsIds.length ? materialsIds.join(',') : ''
				}
				return ''
			}
		},
		methods: {
			changeMaterialSelected(index, key) {

				const currentMaterial = this.productData.specification_types[index].specifications[key]
				// console.log(index, key, currentMaterial);
				if (!currentMaterial.is_exclusive) {
					// console.log(1, this.productData);
					if (currentMaterial.is_selected) return
					this.productData.specification_types[index].specifications.forEach(value => this.$set(value,
						'is_selected', 0))
					currentMaterial.is_selected = 1
					this.productData.typePrice = 0

					let specificationsData = JSON.parse(JSON.stringify(this.productData.specification_types))
					for (let itemPrice of specificationsData) {
						for (let item of itemPrice.specifications) {
							if (item.is_selected) {
								this.productData.typePrice += item.price
							}
							// this.productData.price = Number(this.productData.price.toFixed(2) + this.productData.price)
						}


					}
					this.productData.allPrice = this.productData.typePrice + this.productData.price


					// this.productData.specification_types[index].specifications.forEach((itemPrice, index) => {

					// })

				} else {
					currentMaterial.is_selected = !currentMaterial.is_selected
					console.log(1, currentMaterial);
					this.productData.number = 1
				}
			},
			add() {
				this.productData.number += 1
			},
			minus() {
				if (this.productData.number == 1) {
					return
				}
				this.productData.number -= 1
			},
			closePop(e){
				console.log(e);
				this.$emit('cancel')
			},
			addToCart() {
				console.log();
				const product = {
					...this.productData,
					'materials_text': this.getProductSelectedMaterials,
					'specification_ids': this.getProductSelectedMaterialsIds
				}
				this.$emit('add-to-cart', product)
			}
		}
	}
</script>

<style lang="scss" scoped>
	.header {
		padding: 20rpx 30rpx;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: flex-end;
		z-index: 11;
		height: 15vh;

		image {
			width: 60rpx;
			height: 60rpx;

			&:nth-child(1) {
				margin-right: 30rpx;
			}
		}

	}

	.swiper {
		height: 25vh;
	}

	.content {
		display: flex;
		flex-direction: column;
		font-size: $font-size-sm;
		color: $text-color-assist;
		height: 42vh;

		.wrapper {
			width: 100%;
			height: 100%;
			// overflow: hidden;
			padding: 30rpx 30rpx 20rpx;
		}

		.title {
			font-size: $font-size-extra-lg;
			color: $text-color-base;
			font-weight: bold;
			margin-bottom: 10rpx;
		}

		.labels {
			display: flex;
			font-size: 20rpx;
			margin-bottom: 10rpx;
			overflow: hidden;
			flex-wrap: wrap;

			.label {
				max-width: 40%;
				padding: 6rpx 10rpx;
				margin-right: 10rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
		}


	}

	.materials {
		width: 80%;
		margin: 20rpx;

		.group-name {
			padding: 10rpx 0;
		}

		.values {
			display: flex;
			flex-wrap: wrap;
			overflow: hidden;

			.value {
				background-color: #f5f5f7;
				color: $font-size-base;
				padding: 10rpx 20rpx;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				margin-right: 20rpx;
				margin-bottom: 20rpx;
				border-radius: $border-radius-lg;

				&.selected {
					background-color: $color-primary;
					color: $bg-color-white;
				}
			}
		}
	}

	.bottom {
		padding: 20rpx 40rpx;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		border-top: 1rpx solid rgba($color: $border-color, $alpha: 0.3);
		background-color: $bg-color-white;
		position: relative;
		z-index: 11;
		heigh: 245rpx;

		.price-and-materials {
			flex: 1;
			display: flex;
			flex-direction: column;
			overflow: hidden;
			margin-right: 10rpx;

			.price {
				color: $color-primary;
				font-size: $font-size-extra-lg;
				font-weight: bold;
			}

			.materials {
				font-size: $font-size-sm;
				color: $text-color-assist;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				overflow: hidden;
			}
		}

		.add-cart-btn {
			margin: 20rpx auto;
			font-size: $font-size-lg;
			border-radius: $border-radius-base;
			display: flex;
			align-items: center;
			justify-content: center;
			width: 600rpx;
			height: 80rpx;
			color: #ffffff;
		}
	}
</style>