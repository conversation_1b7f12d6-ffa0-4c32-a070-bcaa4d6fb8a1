<template>
	<view>
		<view class="rate-card flex justify-center align-center">
			<view class="title flex justify-center">
				<view style="display: flex;align-items: center;" class="">
					<text style="font-size: 48rpx;font-weight: 600;" :style="{color:themeColor.main_color}"
						class="">{{hotel.socre}}</text>
					<text class="">分</text>
				</view>
				<view class="" style="display: flex;align-items: center;flex-direction: column;padding-left: 16rpx;">
					<m-rate :size="20" :value="Number(hotel.socre)" :readonly="true"></m-rate>
					<text style="letter-spacing:16rpx;color:#EE6363 ;padding-left: 15rpx;"
						class=" text-black ">综合评分</text>
				</view>
			</view>
		</view>
		<view class="content">
			<view class="item" v-for="item in evaluateList" :key="item.id"
				:style="{background:themeColor.bg_color}">
				<view class="item_title">
					<text class="icon-dengji" style="font-size: 50rpx;color: #EE6363;"></text>
					<view class="name">
						<text style="font-size: 28rpx;"
							:style="{color:themeColor.text_main_color}">{{item.user_nickname}}</text>
						<text style="font-size: 22rpx;color:#c0c4cc">{{item.create_time | moment}}</text>
					</view>
					<view class="rate">
						<text :style="{color:themeColor.main_color}" style="font-size: 36rpx;">{{item.score}}分</text>
					</view>
				</view>
				<view style="display: flex;justify-content: flex-start;width: 100%;">{{item.evaluate}}</view>
				<view class="imgList">
					<view class="imgBox" v-for="(item1,index) in item.pic" :key="item.id">
						<image class="img" :src="item1" mode="aspectFill"></image>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				loadding: false,
				show: false,
				count1: 5,
				index: 3,
				params: {
					shop_id: '',
					page: 1,
					limit: 10
				},
				evaluateList: [],
				bool: true,
				count: 0,
				scrollTop: 0,
			}
		},
		components: {

		},

		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel']),
			background() {
				let backgroundColor = {
					backgroundColor: this.themeColor.main_color
				}
				return backgroundColor
			}
		},
		methods: {
			//页面滚动执行方式
			onPageScroll(e) {
				// console.log(e.scrollTop)
				this.scrollTop = e.scrollTop
			},
			backMain() {
				this.$iBox.router()
			},
			primage(e) {
				wx.previewImage({
					current: e[0], // 当前显示图片的http链接  
					urls: e // 需要预览的图片http链接列表  
				})
			}
		},
		onLoad() {
			uni.showLoading({
				title: 'loading...'
			})
			this.index = parseInt(this.hotel.evaluate_avg)
			this.params.shop_id = this.hotel.id
			this.params.page = 1
			this.params.limit = 10
			this.$iBox.http('getShopRoomBillEvaluate', this.params)({
				method: 'get'
			}).then(res => {
				console.log('我是返回', res.data)
				this.evaluateList = res.data.list
				for (let item of res.data.list) {
					console.log(item.u_name)
					if (item.u_name == null) {
						item.u_name = ''
					}
				}
				if (this.evaluateList.length == res.data.count) {
					this.bool = false
				}
				uni.hideLoading()
			}).catch(function(error) {
				console.log('网络错误', error)
			})

		},
		// // 上拉加载
		onReachBottom() {
			console.log(this.bool)
			if (this.bool) {
				++this.params.page
				this.loadding = true;
				this.$iBox.http('getShopRoomBillEvaluate', this.params)({
					method: 'post'
				}).then(res => {

					let new_list = this.evaluateList.concat(res.data.list)
					for (let item1 of new_list) {
						console.log(item1.u_name)
						if (item1.u_name == null) {
							item1.u_name = ''
						}
					}
					this.evaluateList = new_list
					console.log(new_list, '下拉加载')
					if (this.evaluateList.length == res.data.count) {
						this.bool = false
						this.loadding = false;
					}
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		},
		/**
		 * 用户分享自定义
		 */

	}
</script>

<style scoped lang="scss">
	page {
		background-color: #FFFFFF;
	}

	.rate-card {
		height: 260rpx;
		width: 100%;
		background-color: #FFFFFF;
		// border-bottom: 1px solid #eee;
		display: flex;
		justify-content: center;
		align-items: center;

		.title {
			display: flex;
			justify-content: center;
		}
	}

	.content {
		padding: 0 30rpx;
		display: flex;
		flex-direction: column;
		width: 100%;
		background-color: #FFFFFF;

		.item {
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 0 20rpx;
			width: 700rpx;
			margin: 0 auto;
			// background-color: #f8f9fd;
			border-radius: 20rpx;
			margin-bottom: 30rpx;

			.item_title {
				width: 100%;
				height: 150rpx;
				display: flex;
				align-items: center;

				.img {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
				}

				.name {
					padding-left: 18rpx;
					width: 300rpx;
					height: 100%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					line-height: 44rpx;
				}

				.rate {
					display: flex;
					width: 280rpx;
					align-items: center;
					justify-content: flex-end;
				}

			}

			.imgList {
				width: 100%;
				display: flex;
				flex-wrap: wrap;
				border-bottom: 1px solid #eee;
				padding-bottom: 60rpx;

				.imgBox {
					width: 33%;
					padding: 10rpx 20rpx 0rpx 0rpx;

					// margin-top: 20rpx;
					.img {
						height: 200rpx;
						width: 200rpx;
					}
				}

			}
		}

	}
</style>
