<template>
	<view v-if="show==tip">
		<view class="m-couponToast_box" catchtouchmove="true">
			<view class="" style="position: relative;display: flex;flex-direction: column;">
				<image :src="img" class="m-couponToast_box_img" mode="aspectFill" @click="navigate"></image>
				<text class="icon-close m-couponToast_box_dot" @click.stop="close"></text>
			</view>
			
			
		</view>
		
	</view>
</template>

<script>
	export default {
		name:"m-couponToast",
		props:{
			show:{
				type:Boolean,
				default:false
			},
			img:{
				type:String,
				default:''
			},
			url: {
				type:Object,
				default:{}
			}
		},
		data() {
			return {
				tip:true
			};
		},
		methods:{
			close(){
				this.tip = false
			},
			navigate(){
				let e = this.url
				if (e.type == '1') {
					if(e.url == 'pages/myCenter/myCenter'||e.url == 'pages/myRoom/myRoom'){
						uni.switchTab({
							url:'/' + e.url
						})
					}else {
						uni.navigateTo({
							url: '/' + e.url
						})
					}
				} else if (e.type == '2') {
					uni.navigateTo({
						url: '/pages/webView/webView?url=' + e.url
					})
				} else {
					let appid = '';
					let path = '';
					let data = {}
					appid = e.url.split('#')[0]
					if (e.url.split('#').length > 1) {
						let a = e.url.indexOf('#')
						path = e.url.slice(a + 1)
					}
					// #ifdef MP-WEIXIN
					wx.navigateToMiniProgram({
						appId: appid,
						path: path,
						extraData:data,
						success(res) {
							// 打开成功
							console.log(res);
						}
					});
					// #endif
				}
				this.tip = false
			}
		}
		
	}
</script>

<style lang="scss" scoped>
	.m-couponToast_box {
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: rgba($color: #000000, $alpha: 0.6);
		position: absolute;
		top: 0;
		right: 0;
		left: 0;
		bottom: 0;
		width: 100%;
		height: auto;
		z-index: 99999;
		&_img {
			width: 600rpx;
			height: 800rpx;
			z-index: 999999;
			border-radius: 8rpx;
		}
		
		&_dot {
			font-size: 75rpx;
			color: #f9f9fa;
			margin: 40rpx auto;
			// right: 80rpx;
			z-index: 100000;
		}
	}
</style>
