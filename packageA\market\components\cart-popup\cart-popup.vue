<template>
	<uni-popup ref="popup" type="bottom" @maskClick="change">
		<view class="cart-popup" style="padding: 32rpx;">
			<view class="header">
				<view class="order-type">
					<view class="" style="color: #000000E0;font-size: 28rpx;">选购明细</view>
					<!-- <view class="extra">自提/外送</view> -->
					<text style="color: #FA506D;font: 24rpx;">已选会员免费权益{{memCount}}项</text>
				</view>
				<view class="" style="display: flex;align-items: center;" @tap="clear">
					<view class="icon-shanchutianchong"></view>
					<view>清空</view>
				</view>
			</view>
			<scroll-view scroll-y class="content">
				<view class="wrapper">
					<view class="list">
						<view class="item" v-for="(item, index) in cart" :key="index">
							<view class="left">
								<view class="" style="display: flex;height: 182rpx;align-items: center;">
									<image :src="item.image" mode="widthFix" class="image"></image>
								</view>

							</view>
							<view class="right">
								<view class="name-and-materials">
									<view class="name">{{ item.name }}</view>
									<view class="materials" v-if="item.materials_text">{{ item.materials_text }}</view>
								</view>
								<view class="price-and-actions">
									<view class="price">￥{{ item.price }}</view>
									<view class="actions">
										<template v-if="!item.materialsBtn">
											<view class="actions" v-if="!item.ifMem">

												<image src="/static/images/round_minus.png" class="minus-btn"
													v-show="item.number" @tap.stop="minus(item)"></image>
												<view v-show="item.number" style="color: #000;" class="number">
													{{ item.number }}
												</view>
												<image src="/static/images/round_add_normal.png" class="add-btn"
													@tap.stop="add(item)"></image>
											</view>
											<view class="actions" v-if="item.ifMem">
												<image src="/static/images/round_minus.png" class="minus-btn"
													v-show="item.number" @tap.stop="minus(item)"></image>
												<view class="number" style="font-size: 22rpx;"
													:style="'color:' + themeColor.main_color" v-if="item.ifMem">免费
												</view>
												<uni-icons type="plus" size="28" color="#dddddd"></uni-icons>
											</view>
										</template>
										<template v-else>
											<view class="materials-box">
												<button type="primary" size="mini" class="materials-btn"
													@tap="$emit('materials')">选规格</button>
												<view class="number-badge" v-show="item.number">
													<view class="number" style="color: #000;">{{ item.number }}</view>
												</view>
											</view>
										</template>
									</view>
									<!-- <actions :number="item.number" :ifMem="item.ifMem" @add="add(item)" @minus="minus(item)"></actions> -->
								</view>
							</view>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
	</uni-popup>
</template>

<script>
	import actions from '../actions/actions.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		components: {
			actions
		},
		props: {
			cart: {
				type: Array,
				default: () => []
			}
		},
		watch:{
			cart:{
				handler(newVal, oldVal){
					let num = 0
					newVal.forEach(item =>{
						if(item.ifMem){
							num++
						}
					})
					console.log(newVal,'this.cart');
					this.memCount = num
				},
				immediate:true,
				deep:true
			}
		},
		data() {
			return {
				memCount:''
			}
		},
		mounted() {
			
			
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel']),
		},
		methods: {
			open() {
				this.$refs['popup'].open()
			},
			close() {
				this.$refs['popup'].close()
			},
			change(e) {
				this.$emit('change', e)
			},
			add(item) {
				this.$emit('add', item)
			},
			minus(item) {
				this.$emit('minus', item)
			},
			clear() {
				uni.showModal({
					content: '清空购物袋',
					confirmColor: '#DBA871',
					success: res => {
						if (res.confirm) {
							this.$emit('clear')
						}
					}
				})
			}
		}
	};
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.cart-popup {
		background-color: $bg-color-white;
		padding-bottom: 100rpx;
		border-radius: 32rpx;
	}

	.header {
		padding: 0rpx 0rpx 20rpx 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-bottom: 1rpx solid #EBEBEB;
		font-size: $font-size-sm;
		color: $text-color-assist;

		.order-type {
			display: flex;
			flex-direction: column;
			// align-items: center;
			font-size: $font-size-sm;
			color: $text-color-base;

			.extra {
				margin-right: 10rpx;
				border: 2rpx solid $color-primary;
				font-size: 18rpx;
				padding: 2rpx 10rpx;
				color: $color-primary;
				margin-left: 10rpx;
			}
		}

		.delete-btn {
			width: 46rpx;
			height: 46rpx;
			margin-right: 10rpx;
		}
	}

	.content {
		max-height: calc(100vh - 600rpx);
		min-height: 700rpx;

		.wrapper {
			width: 100%;
			height: 100%;
			// padding: 0 30rpx;
		}

		.list {
			display: flex;
			flex-direction: column;
			margin-bottom: 30rpx;

			.item {
				display: flex;
				align-items: stretch;
				padding: 30rpx 0;
				position: relative;
				height: 180rpx;

				&:after {
					content: ' ';
					position: absolute;
					bottom: 0;
					left: 180rpx;
					right: 0;
					// border-bottom: 1rpx solid rgba($color: $border-color, $alpha: 0.6);
				}

				.left {
					flex-shrink: 0;
					display: flex;
					align-items: center;

					.image {
						width: 112rpx;
						height: 112rpx;
						flex-shrink: 0;
						border-radius: 16rpx;
					}
				}

				.right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					font-size: $font-size-medium;
					color: $text-color-base;
					height: 100%;
					margin-left: 10rpx;
					width: fit-content;

					.name-and-materials {
						display: flex;
						flex-direction: column;
						margin-bottom: 20rpx;

						.name {
							font-weight: bold;
						}

						.materials {
							font-size: $font-size-sm;
							color: $text-color-assist;
						}
					}

					.price-and-actions {
						display: flex;
						justify-content: space-between;
						align-items: center;

						.price {
							color: $text-color-grey;
						}
					}
				}
			}
		}
	}

	.actions {
		margin-right: 20rpx;
		display: flex;
		align-items: center;
		height: 80rpx;

		.add-btn,
		.minus-btn {
			width: 44rpx;
			height: 44rpx;
		}

		.number {
			width: 44rpx;
			height: 44rpx;
			margin: 0 20rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: $font-size-extra-lg;
		}

		.materials-box {
			position: relative;
			display: flex;

			.materials-btn {
				border-radius: 50rem !important;
			}

			.number-badge {
				z-index: 4;
				position: absolute;
				right: -16rpx;
				top: -14rpx;
				background-color: $bg-color-white;
				border-radius: 100%;
				width: 1.1rem;
				height: 1.1rem;
				display: flex;
				align-items: center;
				justify-content: center;

				.number {
					font-size: 20rpx;
					flex-shrink: 0;
					background-color: $color-primary;
					// color: $bg-color-white;
					color: #000;
					width: 0.9rem;
					height: 0.9rem;
					line-height: 0.9rem;
					text-align: center;
					border-radius: 100%;
				}
			}
		}
	}
</style>