<template>
	<view class="page" :style="{background:themeColor.bg_color}">
		<view class="billBox">
			<view class="billItem">
				房间号:{{billDetail.room_number?billDetail.room_number:'未排房'}}
			</view>
			<view class="billItem">
				房间类型:{{billDetail.room_type_name}}
			</view>
			<view class="billItem" v-for="item in billDetail.users" v-if="item.is_main">
				入住人:{{item.name}}
			</view>
			<view class="billItem1">
				入住时间:{{billDetail&&billDetail.enter_time | moment1}}
			</view>
		</view>

		<view class="InfoBox" v-if="!is_auth">

			<view class="nameBox">
				<view class="" style="width: 170rpx;">
					<text style="color: brown;">*</text>
					<text style="">手机号:</text>
				</view>
				<view class="" style="width: 360rpx;">
					<input type="number" placeholder="访客必填,请点击获取" disabled="true" v-model="phone" />
				</view>
				<button size="mini" style="width: 150rpx;padding: 0rpx;" open-type="getPhoneNumber"
					@getphonenumber="getPhoneNumber"
					:style="{background:themeColor.com_color1,color:themeColor.bg_color}">一键获取</button>
			</view>
			<view class="nameBox">
				<text style="color: red;">*</text>
				<text style="width: 170rpx;">姓名:</text>
				<view class="" style="width: 400rpx;">
					<input type="text" placeholder="访客必填" v-model="name" />
				</view>
			</view>
			<view class="nameBox">
				<text style="color: red;">*</text>
				<text style="width: 170rpx;">身份证:</text>
				<view class="" style="width: 400rpx;">
					<input type="idcard" placeholder="请填写身份证" v-model="idcard" />
				</view>
			</view>
			<view class="nameBox">
				<text style="width: 170rpx;">访问原因:</text>
				<view class="" style="width: 400rpx;">
					<textarea placeholder="访客非必填" v-model="reason"
						style="border: 1px solid #e2e2e2;height: 200rpx;padding: 20rpx;width: 470rpx;border-radius: 20rpx;"></textarea>
				</view>
			</view>
			<view class="" style="width: 500rpx;margin: 80rpx auto;">
				<view class="btn_register"
					:style="{background:themeColor.com_color1,color:themeColor.bg_color}" @click="register">
					提交访问申请</view>
					
					<view class="btn_register"
						:style="{background:themeColor.com_color2,color:themeColor.bg_color}" @click="gerVisitor">
						刷新访问申请</view>
			</view>

		</view>
		<view class="" style="" v-else>
			<view class="" style="display: flex;flex-direction: column;align-items: center;position: relative;"
				v-if="url">
				<p style="margin: 20rpx auto;font-size:54rpx;font-weight:600">乘坐电梯</p>
				<image :src="url" style="height: 300rpx;width: 300rpx;margin-bottom: 30rpx;" mode=""></image>
				<view class="" style="margin: 30rpx auto;display: flex;flex-direction: column;">
					<text style="font-size: 28rpx;color: #CD1225;">*若二维码没生效请点击下方刷新按钮</text>
					<view class="" style="margin: 30rpx auto;" @click="reload">
						<u-icon name="reload" color="#2979ff" size="34"></u-icon>
						<text style="font-size: 34rpx;color: #2979ff;">刷新</text>
					</view>
				</view>
			</view>
			<view class="" v-if="!url">
				<p style="margin: 20rpx auto;font-size:40rpx;font-weight:600;padding:30rpx">
					申请访客成功！您的访问的房间在{{billDetail.floor_name}},房间号:{{billDetail.room_number}}</p>
			</view>
		</view>

		<m-login v-if="hackReset1&&if_login" @loginTo="loginSucess" @closeToLogin="toCloseLogin"></m-login>
		<view style="height: 160rpx;"></view>
	</view>
</template>

<script>
	import qrcode1 from '@/packageA/plugins/qrcode.js';
	import QR from '@/packageA/plugins/wxqrcode.js';
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				hackReset1: true,
				billDetail: null,
				code: '',
				bill_id: '',
				name: '',
				idcard: '',
				phone: '',
				is_auth: 0, //是否认证
				reason: '',
				floor_list: [],
				url: '', //梯控连接
			}
		},
		components: {},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting','setting']),
		},
		async onLoad(options) {
			await this.$onLaunched;
			this.hackReset1 = false
			this.$nextTick(() => {
				this.hackReset1 = true
			})
			console.log(options, 'options');

			// 普通分享的参数
			if (options) {
				this.bill_id = options.bill_id
				this.code = options.code
			}

			// // 查询订单,如果订单数量为1则自动跳转选房页，多于1则选择订单
			this.$iBox.http('getRoomBillInfoByCode', {
				bill_id: this.bill_id,
				code: this.code
			})({
				method: 'post'
			}).then(res => {
				this.billDetail = res.data
				// 查询访客记录
				this.gerVisitor()
			})

		},
		async onShow() {
			await this.$onLaunched;
			//是否是会员
			let set = this.setting.filter(item => {
				return item.sign == 'auto_register_member'
			})
			if (set[0].property) {
				let a = set[0].property.value
				if (a == 2) {
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value >
						-1) {
						this.if_login = false
						this.phone = this.userInfo.phone
					
					} else {
						this.if_login = true
						console.log(this.userInfo.phone, 'kk');
					}
							
				} else if (a == 1) {
					// this.pop = true
					if (this.userInfo.phone) {
						this.if_login = false
					
					} else {
						this.if_login = true
					}
				}
			}
			
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			...mapActions('hotel', ['getHotelList', 'getHotel', 'getCityModel', 'getSaleTypes']),
			toCloseLogin() {
				uni.showModal({
					title: '提示！',
					content: '为了获得更完整的会员服务请您授权您的手机号！',
					showCancel: false,
					success: res => {
						this.hackReset = false
						this.$nextTick(() => {
							this.hackReset = true
						})
						this.if_login = true
					}
				})
			},
			loginSucess() {
				this.hackReset1 = false
				this.$nextTick(() => {
					this.hackReset1 = true
				
					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					if (set[0].property) {
						let a = set[0].property.value
						if (a == 2) {
							if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
								.upgrade_growth_value > -1) {
								this.if_login = false
							
							} else {
								this.if_login = true
							}
				
						} else if (a == 1) {
							// this.pop = true
							if (this.userInfo.phone) {
								this.if_login = false
							
							} else {
								this.if_login = true
							}
						}
					}
				})
			},
			reload() {
				this.showFloor()
			},
			getPhoneNumber(e) {
				let that = this;
				console.log(e)
				if (e.detail.errMsg === "getPhoneNumber:ok") {
					this.$iBox.http('getUserPhone', {
						iv: e.detail.iv,
						encrypted_data: e.detail.encryptedData,
						session_key: this.userInfo.session_key

					})({
						method: 'post'
					}).then(res2 => {
						// 更新用户信息
						this.phone = res2.data
					})
				}
			},
			gerVisitor() {
				// 查询访客记录
				this.$iBox.http('getRoomBillVisitor', {
					bill_id: this.bill_id,
					code: this.code
				})({
					method: 'post'
				}).then(res => {
					if (res.data) {
						this.is_auth = res.data.status
						// 查询梯控
						this.$iBox
							.http('getLiftConfigList', {
								shop_id: this.billDetail.shop_id
							})({
								method: 'post'
							})
							.then(res => {
								this.floor_list = res.data
								this.showFloor()
							})
							.catch(function(error) {
								console.log('33434', error);
							});
					}
				})
			},
			showFloor() {
				if (this.floor_list && this.floor_list.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '本酒店暂无梯控'
					})
					return
				} else {
					let that = this
					let a = []
					let floorList = []
					this.billDetail && this.billDetail.floor_number ? floorList.push(this.billDetail.floor_number) : ''
					this.floor_list.forEach((item, index) => {
						if (item.public_floor) {
							item.public_floor.forEach(item1 => {
								floorList.push(item1)
							})
						}

					})
					floorList = [...new Set(floorList)]

					floorList.forEach(item => {
						let floor = {
							floor: item
						}
						a.push(floor)

					})

					let floors = []

					this.floor_list.forEach(item => {
						let floor = {}
						floor.sn = item.sn
						floor.floors = a
						floors.push(floor)
					})


					let lifts = {
						floors: floors,
						direct_arrival: 0 //是否直达
					}
					let url = ''
					console.log(this.hotel.shop_name, 'dd', this.hotel.shop_name == '豪瑞特酒店');
					if (this.hotel.shop_name == '豪瑞特酒店') {
						url = qrcode1.generateAccessCode('a', new Date(), 5, Number(this.userInfo.id), lifts, [])
					} else {
						url = qrcode1.generateAccessCode('u', new Date(), 240, Number(this.userInfo.id), lifts, [])
					}
					this.url = QR.createQrCodeImg(url.encrypt)
				}



			},
			register() {
				
				
				if (this.userInfo.phone) {
					if(!this.phone){
						uni.showToast({
							icon:'none',
							title:'请填写手机号!'
						})
						return
					}
					
					if(!this.idcard){
						uni.showToast({
							icon:'none',
							title:'请填写身份证号码!'
						})
						return
					}
					// 提交访客
					this.$iBox
						.http('addRoomBillVisitor', {
							bill_id: this.bill_id,
							code: this.code,
							name: this.name,
							phone: this.phone,
							identification_number: this.idcard,
							reason: this.reason
						})({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getRoomBillVisitor', {
								bill_id: this.bill_id,
								code: this.code
							})({
								method: 'post'
							}).then(res => {
								if (res.data) {
									this.is_auth = res.data.status
									if(!res.data.status){
										uni.showModal({
											title:'提示',
											content:'请等待酒店前台审核访问记录！'
										})
									}else{
										this.gerVisitor()
									}
									
								}
							})
						})
						.catch(function(error) {
							console.log('33434', error);
						});

				} else {
						this.if_login = true
						this.hackReset = false
						this.$nextTick(() => {
							this.hackReset = true
						})
				}
				// } else {
				// 	// 注册手机号
				// 	this.$iBox.http('registerUserPhone', {
				// 		phone: this.phone,
				// 		shop_id: this.hotel.id ? this.hotel.id : ''
				// 	})({
				// 		method: 'post'
				// 	}).then(res => {

				// 		this.$iBox.http('getUserInfo', {
				// 			simple: false
				// 		})({
				// 			method: 'post'
				// 		}).then(resUser => {
				// 			let userInfo = resUser.data
				// 			userInfo.session_key = this.userInfo.session_key
				// 			// 再判断是否是线下会员
				// 			if (userInfo.grade_info && userInfo.grade_info.upgrade_growth_value > -1) {
				// 				this.updateUserInfo(userInfo)
				// 			} else {
				// 				// 注册会员
				// 				this.$iBox.http('memberRegister', {
				// 					phone: this.phone,
				// 					shop_id: this.hotel.id ? this.hotel.id : '',
				// 					identification_number: this.idcard,
				// 					identification_type: 1,
				// 					name: this.name,
				// 				})({
				// 					method: 'post'
				// 				}).then(res => {
				// 					let params = {
				// 						identification_number: this.idcard,
				// 						identification_type: 1,
				// 						name: this.name,
				// 						phone: this.phone
				// 					}
				// 					this.$iBox.http('updateUserInfo', params)({
				// 						method: 'post'
				// 					}).then(res => {
				// 						// 每次进入个人中心要更新用户信息
				// 						this.$iBox.http('getUserInfo', {
				// 							simple: false
				// 						})({
				// 							method: 'post'
				// 						}).then(res1 => {
				// 							let userInfo = res1.data
				// 							userInfo.session_key = this.userInfo
				// 								.session_key
				// 							this.updateUserInfo(userInfo)

				// 							// 注册会员
				// 							// 提交访客
				// 							this.$iBox
				// 								.http('addRoomBillVisitor', {
				// 									bill_id: this.bill_id,
				// 									code: this.code,
				// 									name: this.name,
				// 									phone: this.phone,
				// 									identification_number: this.idcard,
				// 									reason: this.reason
				// 								})({
				// 									method: 'post'
				// 								})
				// 								.then(res => {
				// 									this.gerVisitor()
				// 								})
				// 								.catch(function(error) {
				// 									console.log('33434', error);
				// 								});
				// 						})
				// 					}).catch(function(error) {
				// 						console.log('网络错误', error)
				// 					})

				// 				})

				// 			}
				// 		})

				// 	})
				// }

			}
		}
	}
</script>
<style>
	.page {
		min-height: 100vh;
	}
</style>
<style lang="scss" scoped>
	.billBox {
		width: 700rpx;
		height: 240rpx;
		border-radius: 20rpx;
		margin: 30rpx auto;
		background: #e2e2e2;
		padding: 30rpx;
		display: flex;
		flex-wrap: wrap;

		.billItem {
			width: 50%;
			height: 25%;
			font-size: 26rpx;
		}

		.billItem1 {
			width: 100%;
			height: 25%;
			font-size: 26rpx;
		}
	}

	.InfoBox {
		// padding: 40rpx;

		.nameBox {
			padding: 30rpx;
			display: flex;
			align-items: center;
			border-bottom: 1px solid #e4e7ed;
			// justify-content: space-between;
		}

		.check_contant {
			padding: 30rpx;
		}

		.btn_register {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 500rpx;
			height: 90rpx;
			border-radius: 20rpx;
			margin-top: 20rpx;
		}
	}
</style>