<template>
	<view class="box" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}" @click="goRating">
		<view class="title">
			<text :style="{color:themeColor.text_color}">住客评价</text>
			<view class="detail" :style="{color:themeColor.text_title_color}">
				<view class="" style="font-size: 32rpx;padding-left: 6rpx;" :style="{color:themeColor.com_color1}">
					<text style="font-size: 26rpx;">{{ecount}}+条评论</text>
				</view>
			</view>
		</view>

		<view class="content">
			<view class="item" v-for="item in evaluateList" :key="item.id"
				:style="{background:themeColor.bg1_color}">
				<view class="item_title">
					<image class="img" :src="item.user_avatar_url" mode=""></image>
					<view class="name">
						<text style="font-size: 28rpx;"
							:style="{color:themeColor.text_main_color}">{{item.user_nickname}}</text>
						<text style="font-size: 22rpx;color:#c0c4cc">{{item.create_time | moment}}</text>
					</view>
					<view class="rate">
						<text :style="{color:themeColor.main_color}" style="font-size: 36rpx;">{{item.score}}分</text>
					</view>
				</view>
				<view style="display: flex;justify-content: flex-start;width: 100%;">{{item.evaluate}}</view>
				<view class="imgList">
					<view class="imgBox" v-for="(item1,index) in item.pic" :key="item.id">
						<image class="img" src="item1" mode="aspectFill"></image>
					</view>

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-rateCard",
		data() {
			return {
				params: {
					shop_id: '',
					page: 1,
					limit: 10
				},
				evaluateList: [],
				ecount: 0
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		mounted() {
			this.params.shop_id = this.hotel.id
			this.params.page = 1
			this.params.limit = 10
			this.$iBox.http('getShopRoomBillEvaluate', this.params)({
				method: 'post'
			}).then(res => {
				this.evaluateList = res.data.list
				this.ecount = res.data.count
			})
		},
		methods: {
			goRating(){
				uni.navigateTo({
					url:'/pages/rating/rating'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		width: 750rpx;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;

		.title {
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			// border-bottom: 1px solid #e4e7ed;

			.detail {
				display: flex;
				align-items: center;
				color: #585858;
				font-size: 24rpx;
			}
		}

		.content {
			padding: 0 30rpx;
			display: flex;
			flex-direction: column;
			width: 100%;

			.item {
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 0 20rpx;
				width: 700rpx;
				margin: 0 auto;
				// background-color: #f8f9fd;
				border-radius: 20rpx;
				margin-bottom: 30rpx;

				.item_title {
					width: 100%;
					height: 150rpx;
					display: flex;
					align-items: center;

					.img {
						width: 80rpx;
						height: 80rpx;
						border-radius: 50%;
					}

					.name {
						padding-left: 18rpx;
						width: 300rpx;
						height: 100%;
						display: flex;
						flex-direction: column;
						justify-content: center;
						line-height: 44rpx;
					}

					.rate {
						display: flex;
						width: 280rpx;
						align-items: center;
						justify-content: flex-end;
					}

				}

				.imgList {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					border-bottom: 1px solid #eee;
					padding-bottom: 60rpx;

					.imgBox {
						width: 33%;
						padding: 10rpx 20rpx 0rpx 0rpx;

						// margin-top: 20rpx;
						.img {
							height: 200rpx;
							width: 200rpx;
						}
					}

				}
			}

		}
	}
</style>
