<template>
	<view>

		<view class="nameBox">
			<text style="padding-right: 110rpx;">房间号</text>
			<view class="" style="width: 400rpx;">
				<input type="text" placeholder="请填写要绑定的房间号" v-model="roomNumber" />
			</view>
		</view>

		<view style="width: 400rpx;margin: 100rpx auto;">
			<button type="primary" @click="bind_room">下一步</button>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				roomNumber: ''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		methods: {
			bind_room() {
				console.log(this.roomNumber, 'dd')
				if (this.roomNumber) {
					this.$iBox.http('editYayaLock', {
						 lock_id: this.lockDetail.id,
						 lock_alias: this.roomNumber
					})({
						method: 'post'
					}).then(res => {
							uni.navigateBack({})
						
					});
				} else {
					uni.showToast({
						icon: 'none',
						title: '请先填写房间号'
					})
				}

			}
		}
	}
</script>

<style lang="scss" scoped>
	.nameBox {
		background-color: #fff;
		padding: 30rpx;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e4e7ed;
		// justify-content: space-between;
	}
</style>
