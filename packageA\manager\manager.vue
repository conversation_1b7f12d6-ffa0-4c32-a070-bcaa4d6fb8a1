<template>
	<view class="page-container">
		<!-- 状态栏占位 -->
		<!-- <view class="status-bar"></view> -->

		<!-- 整合头部区域 -->
		<view class="header-container">
			<view class="header-bg"
				:style="{'background': 'linear-gradient(135deg,'+themeColor.main_color+','+themeColor.com_color2+')'}">
			</view>
			<view class="header-content">
				<!-- 顶部导航信息 -->
				<view class="header-top">
					<view class="header-left">
						<view class="header-title">管理中心</view>
						<view class="current-hotel-info">
							<view class="hotel-indicator">🏨</view>
							<view class="hotel-details">
								<text class="hotel-name">{{roles.shop_name || '未选择酒店'}}</text>
								<text class="hotel-status" v-if="roles.shop_name">当前管理</text>
							</view>
						</view>
					</view>
					<view class="header-right" @click="changeRole">
						<image class="header-avatar" :src="userInfo.avatar_url"></image>
						<view class="switch-indicator">
							<u-icon name="arrow-down" size="14" color="rgba(255,255,255,0.8)"></u-icon>
						</view>
					</view>
				</view>

				<!-- 用户信息区域 -->
				<view class="user-info-section">
					<view class="user-main-info">
						<view class="user-greeting">
							<text class="greeting-text">您好，</text>
							<text class="user-name">{{roles.name}}</text>
						</view>
						<view class="user-badges">
							<view class="user-role-badge">管理员</view>
							<view class="hotel-badge" v-if="roles.shop_name">
								<view class="badge-icon">🏨</view>
								<text class="badge-text">{{roles.shop_name}}</text>
							</view>
						</view>
					</view>
					<view class="header-actions">
						<view class="action-btn client-switch-btn" @click="switchToClient">
							<view class="action-icon">📱</view>
							<text class="action-text">客户端</text>
						</view>
						<view class="action-btn" @click="changeRole">
							<view class="action-icon">🔄</view>
							<text class="action-text">切换酒店</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 主要功能区 -->
		<view class="main-functions">
			<view class="section-header">
				<text class="section-title">核心功能</text>
			</view>
			<view class="function-grid">
				<!-- 房态管理 -->
				<view class="function-item" v-if="role('room_status')" @click="roomStatus">
					<view class="function-icon-bg primary-bg">
						<view class="icon-fangjianshu-" :style="{color:'#fff'}" style="font-size: 44rpx;"></view>
					</view>
					<text class="function-title">房态图</text>
				</view>

				<!-- 办理入住 -->
				<view class="function-item" v-if="role('room_stay')" @click="checkIn">
					<view class="function-icon-bg success-bg">
						<view class="icon-bufendingdan" :style="{color:'#fff'}" style="font-size: 44rpx;"></view>
					</view>
					<text class="function-title">办理入住</text>
				</view>

				<!-- 办理预订 -->
				<view class="function-item" v-if="role('room_stay')" @click="booking">
					<view class="function-icon-bg info-bg">
						<view class="icon-lishi" :style="{color:'#fff'}" style="font-size: 44rpx;"></view>
					</view>
					<text class="function-title">办理预订</text>
				</view>

				<!-- 订单管理 -->
				<view class="function-item" v-if="role('room_bill')" @click="bill">
					<view class="function-icon-bg warning-bg">
						<view class="icon-dingdan" :style="{color:'#fff'}" style="font-size: 44rpx;"></view>
					</view>
					<text class="function-title">订单管理</text>
				</view>
			</view>
		</view>

		<!-- 设备管理区 -->
		<view class="device-functions" v-if="role('view_lock') || role('lift')">
			<view class="section-header">
				<text class="section-title">设备管理</text>
			</view>
			<view class="device-list">
				<!-- 门锁管理 -->
				<view class="device-item" v-if="role('view_lock')" @click="lockManager">
					<view class="device-icon lock-icon">
						<view class="icon-suozhutimu" :style="{color:'#fff'}" style="font-size: 36rpx;"></view>
					</view>
					<view class="device-info">
						<text class="device-name">门锁管理</text>
						<text class="device-desc">智能门锁控制</text>
					</view>
					<view class="device-arrow">
						<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
					</view>
				</view>

				<!-- 梯控 -->
				<view class="device-item" v-if="role('lift')" @click="floorIn">
					<view class="device-icon elevator-icon">
						<view class="icon-erweima" :style="{color:'#fff'}" style="font-size: 36rpx;"></view>
					</view>
					<view class="device-info">
						<text class="device-name">员工梯控</text>
						<text class="device-desc">电梯权限控制</text>
					</view>
					<view class="device-arrow">
						<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
					</view>
				</view>
			</view>
		</view>

			<!-- 服务管理区 -->
			<view class="service-functions">
				<view class="section-header">
					<text class="section-title">服务管理</text>
				</view>
				<view class="service-list">
					<!-- 早餐核销 -->
					<view class="service-item" v-if="role('breakfast')" @click="scan">
						<view class="service-icon breakfast-icon">
							<view class="icon-saoma" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="service-info">
							<text class="service-name">早餐券核销</text>
							<text class="service-desc">早餐券扫码核销</text>
						</view>
						<view class="service-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>

					<!-- 会员码核销 -->
					<view class="service-item" v-if="role('scan_update_balance')" @click="scanMember">
						<view class="service-icon member-icon">
							<view class="icon-saoma" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="service-info">
							<text class="service-name">会员码收款</text>
							<text class="service-desc">会员扫码付款</text>
						</view>
						<view class="service-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>

					<!-- 分销管理 -->
					<view class="service-item" v-if="role('breakfast')" @click="distribution">
						<view class="service-icon distribution-icon">
							<view class="icon-duoren" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="service-info">
							<text class="service-name">分销管理</text>
							<text class="service-desc">分销渠道管理</text>
						</view>
						<view class="service-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>

					<!-- 摆渡车 -->
					<view class="service-item" v-if="role('shuttle_bus')" @click="shuttleBus">
						<view class="service-icon shuttle-icon">
							<view class="icon-baiduche" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="service-info">
							<text class="service-name">摆渡车</text>
							<text class="service-desc">接送车辆管理</text>
						</view>
						<view class="service-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>

					<!-- 第三方房态 -->
					<view class="service-item" v-if="role('room_status_third')" @click="thirdStatus">
						<view class="service-icon third-icon">
							<view class="icon-fangjianshu-" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="service-info">
							<text class="service-name">第三方房态</text>
							<text class="service-desc">外部平台房态</text>
						</view>
						<view class="service-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 清洁管理区 -->
			<view class="clean-functions" v-if="role('room_clean_set') || role('room_clean_get')">
				<view class="section-header">
					<text class="section-title">清洁管理</text>
				</view>
				<view class="clean-list">
					<!-- 分配房扫 -->
					<view class="clean-item" v-if="role('room_clean_set')" @click="setRoomClean">
						<view class="clean-icon set-icon">
							<view class="icon-jifenfenpei" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="clean-info">
							<text class="clean-name">分配房扫</text>
							<text class="clean-desc">房间清扫分配</text>
						</view>
						<view class="clean-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>

					<!-- 房扫列表 -->
					<view class="clean-item" v-if="role('room_clean_get')" @click="roomCleanCenter">
						<view class="clean-icon list-icon">
							<view class="icon-readed" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="clean-info">
							<text class="clean-name">房扫列表</text>
							<text class="clean-desc">领取清扫任务</text>
						</view>
						<view class="clean-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>

					<!-- 我的房扫 -->
					<view class="clean-item" v-if="role('room_clean_get')" @click="roomClean">
						<view class="clean-icon my-icon">
							<view class="icon-readed" :style="{color:'#fff'}" style="font-size: 32rpx;"></view>
						</view>
						<view class="clean-info">
							<text class="clean-name">我的房扫</text>
							<text class="clean-desc">我的清扫任务</text>
						</view>
						<view class="clean-arrow">
							<u-icon name="arrow-right" size="16" color="#ccc"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 当前酒店状态卡片 -->
			<view class="current-hotel-card" v-if="roles.shop_name">
				<view class="hotel-card-bg"></view>
				<view class="hotel-card-content">
					<view class="hotel-card-header">
						<view class="hotel-card-icon">🏨</view>
						<view class="hotel-card-title">当前管理酒店</view>
						<view class="hotel-card-status">
							<view class="status-dot"></view>
							<text class="status-text">已连接</text>
						</view>
					</view>
					<view class="hotel-card-info">
						<text class="hotel-card-name">{{roles.shop_name}}</text>
						<text class="hotel-card-account">管理账号：{{roles.name}}</text>
					</view>
					<view class="hotel-card-actions">
						<view class="card-action-btn" @click="changeRole">
							<view class="action-btn-icon">🔄</view>
							<text class="action-btn-text">切换酒店</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部安全区域 -->
			<view class="safe-area-bottom"></view>
	</view>
</template>



<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				roleList: [],
				roles: {},
				message: [],
				timer: '',
				if_close: false
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		async onLoad() {
			await this.$onLaunched;


		},
		async onShow() {
			await this.$onLaunched;


			uni.showLoading({
				title: 'loading...',
				mask: true
			})

			if (this.manager && this.manager.id) {
				this.roles = this.manager
				console.log(this.manager, 'this.manager');
				uni.setNavigationBarTitle({
					title: this.roles.shop_name
				})
				this.$iBox
					.http('getAdminPermission', {})({
						method: 'post'
					})
					.then(res => {
						// this.roleList = this.$iBox.buildTree(res.data)
						this.roleList = res.data
						this.getRole(this.roleList)
						uni.hideLoading()
					})
			} else {
				this.$iBox
					.http('getBossList', {})({
						method: 'post'
					})
					.then(res => {
						this.getManager(res.data[0])

						this.roles = res.data[0]
						
						uni.setNavigationBarTitle({
							title: this.roles.shop_name
						})
						this.$iBox.http('chooseAdmin', {
							admin_id: res.data[0].id
						})({
							method: 'post'
						}).then(res1 => {
							this.$iBox
								.http('getAdminPermission', {})({
									method: 'post'
								})
								.then(res2 => {
									// this.roleList = this.$iBox.buildTree(res.data)
									this.roleList = res2.data
									this.getRole(this.roleList)
									uni.hideLoading()
								})
						})
						
					})
			}


		},

		methods: {
			...mapActions('hotel', ['getRole', 'getManager']),
			...mapActions('room', ['getRoomInfo']),

			// 切换到客户端
			switchToClient() {
				uni.showModal({
					title: '📱 切换到客户端模式',
					content: '您将离开管理端，切换到客户端首页\n\n💡 您可以随时通过主页面重新进入管理端',
					cancelText: '取消',
					confirmText: '切换',
					success: (res) => {
						if (res.confirm) {
							console.log('管理员切换到客户端模式');

							// 显示切换加载提示
							uni.showLoading({
								title: '正在切换到客户端...'
							});

							// 延迟切换，提供更好的用户体验
							setTimeout(() => {
								// 跳转到客户端首页
								uni.reLaunch({
									url: '/pages/index/index',
									success: () => {
										console.log('成功切换到客户端首页');
										uni.hideLoading();
										uni.showToast({
											title: '已切换到客户端',
											icon: 'success',
											duration: 2000
										});
									},
									fail: (err) => {
										console.error('切换到客户端失败:', err);
										uni.hideLoading();
										uni.showToast({
											title: '切换失败，请重试',
											icon: 'none'
										});
									}
								});
							}, 500);
						}
					}
				});
			},

			detail() {
				uni.navigateTo({
					url: './msgDetail/msgDetail'
				})
			},
			changeRole() {
				uni.navigateTo({
					url: './role/role'
				})
			},
		

			lookFund() {
				uni.navigateTo({
					url: './hotelData/hotelData'
				})
			},
			role(e) {
				let a = this.roleList.filter(item => {
					return item.permission == e
				}).length
				return a
			},

			lockManager() {
				uni.navigateTo({
					url: './lockList/lockList'
				})
			},
			roomStatus() {
				uni.navigateTo({
					url: './roomStatus/roomStatus'
				})
			},
			booking() {
				this.getRoomInfo('')
				uni.navigateTo({
					url: './ording/ording'
				})
			},
			checkIn() {
				this.getRoomInfo('')
				uni.navigateTo({
					url: './checkIn/checkIn'
				})
			},
			bill() {
				uni.navigateTo({
					url: './bill/billList/billList'
				})
			},
			scan() {
				uni.navigateTo({
					url: './breakfastScan/breakfastScan'
				})
			},
			distribution(){
				
				uni.navigateTo({
					url:'./distribution/distribution'
				})
			},
			floorIn(){
				uni.navigateTo({
					url:'./floorIn/floorIn'
				})
			},
			scanMember(){
				uni.navigateTo({
					url:'./scanMember/scanMember'
				})
			},
			thirdStatus(){
				uni.navigateTo({
					url:'./roomStatus/thirdRoomStatus'
				})
			},
			shuttleBus(){
				uni.navigateTo({
					url:'./shuttleBus/shuttleBus'
				})
			},
			setRoomClean(){
				uni.navigateTo({
					url:'/packageB/clearRoom/setClearRoom'
				})
			},
			roomClean(){
				uni.navigateTo({
					url:'/packageB/clearRoom/clearRoom'
				})
			},
			roomCleanCenter(){
				uni.navigateTo({
					url:'/packageB/clearRoom/roomCenter'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	/* 页面容器 */
	.page-container {
		min-height: 100vh;
		background: linear-gradient(180deg, #f8faff 0%, #f1f3f8 100%);
	}

	/* 状态栏占位 */
	.status-bar {
		height: var(--status-bar-height, 44rpx);
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	/* 整合头部区域 */
	.header-container {
		position: relative;
		overflow: hidden;
		margin: 30 30rpx 30rpx;
		border-radius: 28rpx;
		box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
	}

	.header-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		opacity: 0.95;
	}

	.header-bg::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
		backdrop-filter: blur(15rpx);
	}

	.header-bg::after {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.08)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.12)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
		opacity: 0.6;
	}

	.header-content {
		position: relative;
		z-index: 2;
		padding: 30rpx 30rpx 35rpx;
	}

	/* 顶部导航信息 */
	.header-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 25rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.15);
		position: relative;
	}

	.header-top::after {
		content: '';
		position: absolute;
		bottom: -1rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 2rpx;
		background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
		border-radius: 1rpx;
	}

	.header-left {
		flex: 1;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: 700;
		color: #fff;
		margin-bottom: 6rpx;
		letter-spacing: 1rpx;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}

	.header-subtitle {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		font-weight: 400;
	}

	/* 当前酒店信息 */
	.current-hotel-info {
		display: flex;
		align-items: center;
		margin-top: 8rpx;
		padding: 8rpx 12rpx;
		background: rgba(255, 255, 255, 0.1);
		border-radius: 16rpx;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.15);
	}

	.hotel-indicator {
		font-size: 20rpx;
		margin-right: 8rpx;
		filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
	}

	.hotel-details {
		flex: 1;
	}

	.hotel-name {
		font-size: 22rpx;
		color: #fff;
		font-weight: 600;
		letter-spacing: 0.5rpx;
		text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
	}

	.hotel-status {
		font-size: 18rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-left: 8rpx;
		background: rgba(255, 255, 255, 0.15);
		padding: 2rpx 8rpx;
		border-radius: 8rpx;
		font-weight: 500;
	}

	.header-right {
		display: flex;
		align-items: center;
		padding: 8rpx 16rpx;
		background: rgba(255, 255, 255, 0.15);
		border-radius: 50rpx;
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		transition: all 0.3s ease;
	}

	.header-right:active {
		background: rgba(255, 255, 255, 0.25);
		transform: scale(0.95);
	}

	.header-avatar {
		width: 56rpx;
		height: 56rpx;
		border-radius: 50%;
		margin-right: 10rpx;
		border: 2rpx solid rgba(255, 255, 255, 0.3);
	}

	.switch-indicator {
		opacity: 0.8;
	}

	/* 用户信息区域 */
	.user-info-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.user-main-info {
		flex: 1;
	}

	.user-greeting {
		margin-bottom: 12rpx;
	}

	.greeting-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.85);
		font-weight: 400;
	}

	.user-name {
		font-size: 34rpx;
		font-weight: 600;
		color: #fff;
		text-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.25);
		letter-spacing: 1rpx;
	}

	/* 用户徽章区域 */
	.user-badges {
		display: flex;
		flex-wrap: wrap;
		gap: 12rpx;
		margin-top: 16rpx;
	}

	.user-role-badge {
		font-size: 22rpx;
		color: #fff;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.15) 100%);
		backdrop-filter: blur(10rpx);
		padding: 8rpx 18rpx;
		border-radius: 20rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.2);
		font-weight: 500;
	}

	.hotel-badge {
		display: flex;
		align-items: center;
		font-size: 20rpx;
		color: #fff;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
		backdrop-filter: blur(10rpx);
		padding: 6rpx 14rpx;
		border-radius: 18rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.15);
		font-weight: 500;
		max-width: 200rpx;
	}

	.badge-icon {
		font-size: 16rpx;
		margin-right: 6rpx;
		filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
	}

	.badge-text {
		flex: 1;
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
	}

	.header-actions {
		display: flex;
		gap: 12rpx;
	}

	.action-btn {
		display: flex;
		flex-direction: column;
		align-items: center;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
		backdrop-filter: blur(15rpx);
		border-radius: 18rpx;
		padding: 18rpx 16rpx;
		min-width: 80rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.25);
		transition: all 0.3s ease;
		box-shadow: 0 4rpx 15rpx rgba(0, 0, 0, 0.1);
	}

	.action-btn:active {
		transform: scale(0.95);
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
	}

	.action-icon {
		font-size: 28rpx;
		margin-bottom: 8rpx;
		filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
	}

	.action-text {
		font-size: 22rpx;
		color: #fff;
		font-weight: 500;
		text-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.2);
	}

	/* 客户端切换按钮特殊样式 */
	.client-switch-btn {
		background: linear-gradient(135deg, rgba(76, 175, 80, 0.3) 0%, rgba(56, 142, 60, 0.2) 100%);
		border: 1rpx solid rgba(76, 175, 80, 0.4);

		&:active {
			background: linear-gradient(135deg, rgba(76, 175, 80, 0.4) 0%, rgba(56, 142, 60, 0.3) 100%);
		}

		.action-icon {
			filter: drop-shadow(0 2rpx 4rpx rgba(76, 175, 80, 0.3));
		}
	}

	/* 功能区域通用样式 */
	.main-functions,
	.device-functions,
	.service-functions,
	.clean-functions {
		margin: 24rpx 30rpx;
		background: #fff;
		border-radius: 24rpx;
		overflow: hidden;
		box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
		position: relative;
	}

	/* 核心功能区 - 主题色边框 */
	.main-functions {
		border: 2rpx solid rgba(102, 126, 234, 0.15);
	}

	.main-functions::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(90deg, transparent 0%, rgba(102, 126, 234, 0.4) 50%, transparent 100%);
	}

	/* 设备管理区 - 橙色边框 */
	.device-functions {
		border: 2rpx solid rgba(225, 112, 85, 0.15);
	}

	.device-functions::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(90deg, transparent 0%, rgba(225, 112, 85, 0.4) 50%, transparent 100%);
	}

	/* 服务管理区 - 粉色边框 */
	.service-functions {
		border: 2rpx solid rgba(253, 121, 168, 0.15);
	}

	.service-functions::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(90deg, transparent 0%, rgba(253, 121, 168, 0.4) 50%, transparent 100%);
	}

	/* 清洁管理区 - 绿色边框 */
	.clean-functions {
		border: 2rpx solid rgba(0, 184, 148, 0.15);
	}

	.clean-functions::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 2rpx;
		background: linear-gradient(90deg, transparent 0%, rgba(0, 184, 148, 0.4) 50%, transparent 100%);
	}

	.section-header {
		padding: 32rpx 30rpx 24rpx;
		border-bottom: 1rpx solid #e8ecf0;
		position: relative;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: 600;
		color: #1f2937;
		letter-spacing: 0.8rpx;
		display: flex;
		align-items: center;
	}

	/* 核心功能区标题样式 */
	.main-functions .section-header {
		background: linear-gradient(135deg, #fafbff 0%, #f6f8fc 100%);
	}

	.main-functions .section-header::before {
		content: '';
		position: absolute;
		left: 30rpx;
		bottom: -1rpx;
		width: 80rpx;
		height: 3rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 2rpx;
		box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.3);
	}

	.main-functions .section-title::before {
		content: '';
		width: 8rpx;
		height: 8rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 50%;
		margin-right: 12rpx;
		box-shadow: 0 0 8rpx rgba(102, 126, 234, 0.4);
	}

	/* 设备管理区标题样式 */
	.device-functions .section-header {
		background: linear-gradient(135deg, #fff8f5 0%, #fef3ec 100%);
	}

	.device-functions .section-header::before {
		content: '';
		position: absolute;
		left: 30rpx;
		bottom: -1rpx;
		width: 80rpx;
		height: 3rpx;
		background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
		border-radius: 2rpx;
		box-shadow: 0 2rpx 8rpx rgba(225, 112, 85, 0.3);
	}

	.device-functions .section-title::before {
		content: '⚙';
		font-size: 16rpx;
		color: #e17055;
		margin-right: 12rpx;
		animation: pulse 2s infinite;
	}

	/* 服务管理区标题样式 */
	.service-functions .section-header {
		background: linear-gradient(135deg, #fef7f7 0%, #fef2f2 100%);
	}

	.service-functions .section-header::before {
		content: '';
		position: absolute;
		left: 30rpx;
		bottom: -1rpx;
		width: 80rpx;
		height: 3rpx;
		background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
		border-radius: 2rpx;
		box-shadow: 0 2rpx 8rpx rgba(253, 121, 168, 0.3);
	}

	.service-functions .section-title::before {
		content: '★';
		font-size: 16rpx;
		color: #fd79a8;
		margin-right: 12rpx;
		animation: twinkle 1.5s infinite;
	}

	/* 清洁管理区标题样式 */
	.clean-functions .section-header {
		background: linear-gradient(135deg, #f0fdfa 0%, #ecfdf5 100%);
	}

	.clean-functions .section-header::before {
		content: '';
		position: absolute;
		left: 30rpx;
		bottom: -1rpx;
		width: 80rpx;
		height: 3rpx;
		background: linear-gradient(135deg, #00b894 0%, #55efc4 100%);
		border-radius: 2rpx;
		box-shadow: 0 2rpx 8rpx rgba(0, 184, 148, 0.3);
	}

	.clean-functions .section-title::before {
		content: '✨';
		font-size: 16rpx;
		color: #00b894;
		margin-right: 12rpx;
		animation: sparkle 2s infinite;
	}

	/* 核心功能网格 - 确保每排2个 */
	.function-grid {
		display: grid;
		grid-template-columns: 1fr 1fr;
		gap: 20rpx;
		padding: 30rpx 20rpx;
	}

	.function-item {
		padding: 30rpx 20rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		border-radius: 20rpx;
		position: relative;
		background: linear-gradient(135deg, #fafbff 0%, #f8f9fe 100%);
		border: 1rpx solid rgba(102, 126, 234, 0.06);
		box-sizing: border-box;
	}

	.function-item::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
		border-radius: 20rpx;
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	.function-item:active {
		transform: scale(0.96) translateY(2rpx);
		box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.15);
	}

	.function-item:active::before {
		opacity: 1;
	}

	.function-icon-bg {
		width: 120rpx;
		height: 120rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 24rpx;
		box-shadow: 0 12rpx 30rpx rgba(0, 0, 0, 0.15);
		position: relative;
		overflow: hidden;
	}

	.function-icon-bg::before {
		content: '';
		position: absolute;
		top: -2rpx;
		left: -2rpx;
		right: -2rpx;
		bottom: -2rpx;
		background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
		border-radius: 32rpx;
		animation: rotate 4s linear infinite;
	}

	.function-icon-bg::after {
		content: '';
		position: absolute;
		top: 8rpx;
		left: 8rpx;
		width: 24rpx;
		height: 24rpx;
		background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
		border-radius: 50%;
	}

	@keyframes rotate {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.primary-bg {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		box-shadow: 0 12rpx 30rpx rgba(102, 126, 234, 0.3);
	}
	.success-bg {
		background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
		box-shadow: 0 12rpx 30rpx rgba(17, 153, 142, 0.3);
	}
	.info-bg {
		background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
		box-shadow: 0 12rpx 30rpx rgba(59, 130, 246, 0.3);
	}
	.warning-bg {
		background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
		box-shadow: 0 12rpx 30rpx rgba(245, 158, 11, 0.3);
	}

	.function-title {
		font-size: 28rpx;
		font-weight: 600;
		color: #1f2937;
		text-align: center;
		letter-spacing: 0.8rpx;
		line-height: 1.3;
	}

	/* 列表样式 */
	.device-list,
	.service-list,
	.clean-list {
		padding: 0;
	}

	.device-item,
	.service-item,
	.clean-item {
		display: flex;
		align-items: center;
		padding: 28rpx 30rpx;
		border-bottom: 1rpx solid #f1f3f4;
		transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		margin: 0 15rpx;
		border-radius: 16rpx;
		margin-bottom: 8rpx;
	}

	.device-item:last-child,
	.service-item:last-child,
	.clean-item:last-child {
		border-bottom: none;
		margin-bottom: 15rpx;
	}

	.device-item:active,
	.service-item:active,
	.clean-item:active {
		background: linear-gradient(135deg, #f8f9ff 0%, #f1f3f8 100%);
		transform: scale(0.98) translateX(4rpx);
		box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.1);
	}

	.device-item::before,
	.service-item::before,
	.clean-item::before {
		content: '';
		position: absolute;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
		width: 4rpx;
		height: 60%;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 2rpx;
		opacity: 0;
		transition: all 0.3s ease;
	}

	.device-item:active::before,
	.service-item:active::before,
	.clean-item:active::before {
		opacity: 1;
		height: 80%;
	}

	.device-item::after,
	.service-item::after,
	.clean-item::after {
		content: '';
		position: absolute;
		right: 20rpx;
		top: 50%;
		transform: translateY(-50%);
		width: 6rpx;
		height: 6rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 50%;
		opacity: 0;
		transition: all 0.3s ease;
	}

	.device-item:active::after,
	.service-item:active::after,
	.clean-item:active::after {
		opacity: 0.6;
		transform: translateY(-50%) scale(1.2);
	}

	.device-icon,
	.service-icon,
	.clean-icon {
		width: 96rpx;
		height: 96rpx;
		border-radius: 24rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-right: 28rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
		border: 2rpx solid rgba(255, 255, 255, 0.2);
	}

	/* 设备管理区图标特殊效果 */
	.device-icon::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: conic-gradient(from 0deg, transparent, rgba(255, 255, 255, 0.3), transparent);
		animation: deviceRotate 4s linear infinite;
	}

	.device-icon::after {
		content: '⚙';
		position: absolute;
		top: 4rpx;
		right: 4rpx;
		font-size: 16rpx;
		color: rgba(255, 255, 255, 0.7);
		animation: pulse 2s infinite;
	}

	/* 服务管理区图标特殊效果 */
	.service-icon::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
		animation: serviceShine 3s infinite;
	}

	.service-icon::after {
		content: '★';
		position: absolute;
		top: 4rpx;
		right: 4rpx;
		font-size: 16rpx;
		color: rgba(255, 255, 255, 0.8);
		animation: twinkle 1.5s infinite;
	}

	/* 清洁管理区图标特殊效果 */
	.clean-icon::before {
		content: '';
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: radial-gradient(circle, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
		animation: cleanPulse 2.5s infinite;
	}

	.clean-icon::after {
		content: '✨';
		position: absolute;
		top: 4rpx;
		right: 4rpx;
		font-size: 16rpx;
		color: rgba(255, 255, 255, 0.8);
		animation: sparkle 2s infinite;
	}

	/* 动画定义 */
	@keyframes deviceRotate {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	@keyframes serviceShine {
		0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
		50% { transform: translateX(100%) translateY(100%) rotate(45deg); }
		100% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
	}

	@keyframes cleanPulse {
		0%, 100% { transform: scale(1); opacity: 0.3; }
		50% { transform: scale(1.2); opacity: 0.6; }
	}

	@keyframes pulse {
		0%, 100% { opacity: 0.7; transform: scale(1); }
		50% { opacity: 1; transform: scale(1.1); }
	}

	@keyframes twinkle {
		0%, 100% { opacity: 0.8; transform: rotate(0deg) scale(1); }
		50% { opacity: 1; transform: rotate(180deg) scale(1.2); }
	}

	@keyframes sparkle {
		0%, 100% { opacity: 0.8; transform: scale(1); }
		25% { opacity: 1; transform: scale(1.3); }
		75% { opacity: 0.6; transform: scale(0.8); }
	}

	/* 设备管理区专用图标样式 */
	.lock-icon {
		background: linear-gradient(135deg, #e17055 0%, #fdcb6e 100%);
		box-shadow: 0 6rpx 20rpx rgba(225, 112, 85, 0.35);
	}
	.elevator-icon {
		background: linear-gradient(135deg, #6c5ce7 0%, #74b9ff 100%);
		box-shadow: 0 6rpx 20rpx rgba(108, 92, 231, 0.35);
	}

	/* 服务管理区精心调配的渐变色彩 */
	.breakfast-icon {
		background: linear-gradient(135deg, #ff7675 0%, #fd79a8 100%);
		box-shadow: 0 6rpx 20rpx rgba(255, 118, 117, 0.35);
	}
	.member-icon {
		background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
		box-shadow: 0 6rpx 20rpx rgba(162, 155, 254, 0.35);
	}
	.distribution-icon {
		background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
		box-shadow: 0 6rpx 20rpx rgba(253, 121, 168, 0.35);
	}
	.shuttle-icon {
		background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
		box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.35);
	}
	.third-icon {
		background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
		box-shadow: 0 6rpx 20rpx rgba(116, 185, 255, 0.35);
	}

	/* 清洁管理区专用图标样式 */
	.set-icon {
		background: linear-gradient(135deg, #55efc4 0%, #81ecec 100%);
		box-shadow: 0 6rpx 20rpx rgba(85, 239, 196, 0.35);
	}
	.list-icon {
		background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
		box-shadow: 0 6rpx 20rpx rgba(116, 185, 255, 0.35);
	}
	.my-icon {
		background: linear-gradient(135deg, #00b894 0%, #55efc4 100%);
		box-shadow: 0 6rpx 20rpx rgba(0, 184, 148, 0.35);
	}

	.device-info,
	.service-info,
	.clean-info {
		flex: 1;
		padding-right: 20rpx;
	}

	.device-name,
	.service-name,
	.clean-name {
		font-size: 32rpx;
		font-weight: 600;
		color: #1f2937;
		margin-bottom: 10rpx;
		letter-spacing: 0.8rpx;
		line-height: 1.2;
	}

	.device-desc,
	.service-desc,
	.clean-desc {
		font-size: 26rpx;
		color: #6b7280;
		line-height: 1.4;
		font-weight: 400;
	}

	.device-arrow,
	.service-arrow,
	.clean-arrow {
		opacity: 0.4;
		transition: all 0.4s ease;
		transform: translateX(0);
	}

	.device-item:active .device-arrow,
	.service-item:active .service-arrow,
	.clean-item:active .clean-arrow {
		opacity: 0.8;
		transform: translateX(6rpx) scale(1.1);
	}

	/* 底部安全区域 */
	.safe-area-bottom {
		height: var(--safe-area-inset-bottom, 34rpx);
		background: linear-gradient(180deg, #f8faff 0%, #f1f3f8 100%);
	}

	/* 全局优化 */
	* {
		box-sizing: border-box;
	}

	/* 当前酒店状态卡片 */
	.current-hotel-card {
		margin: 30rpx;
		border-radius: 24rpx;
		overflow: hidden;
		position: relative;
		box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
	}

	.hotel-card-bg {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		opacity: 0.95;
	}

	.hotel-card-bg::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
		backdrop-filter: blur(10rpx);
	}

	.hotel-card-content {
		position: relative;
		z-index: 2;
		padding: 30rpx;
	}

	.hotel-card-header {
		display: flex;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.hotel-card-icon {
		font-size: 28rpx;
		margin-right: 12rpx;
		filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.2));
	}

	.hotel-card-title {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.9);
		font-weight: 500;
		flex: 1;
	}

	.hotel-card-status {
		display: flex;
		align-items: center;
		background: rgba(255, 255, 255, 0.15);
		padding: 6rpx 12rpx;
		border-radius: 16rpx;
		backdrop-filter: blur(10rpx);
	}

	.status-dot {
		width: 8rpx;
		height: 8rpx;
		background: #10b981;
		border-radius: 50%;
		margin-right: 6rpx;
		animation: statusBlink 2s infinite;
	}

	@keyframes statusBlink {
		0%, 100% { opacity: 1; }
		50% { opacity: 0.5; }
	}

	.status-text {
		font-size: 20rpx;
		color: #fff;
		font-weight: 500;
	}

	.hotel-card-info {
		margin-bottom: 24rpx;
	}

	.hotel-card-name {
		font-size: 32rpx;
		color: #fff;
		font-weight: 600;
		display: block;
		margin-bottom: 8rpx;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
		letter-spacing: 1rpx;
	}

	.hotel-card-account {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
		font-weight: 400;
	}

	.hotel-card-actions {
		display: flex;
		justify-content: flex-end;
	}

	.card-action-btn {
		display: flex;
		align-items: center;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(15rpx);
		padding: 12rpx 20rpx;
		border-radius: 20rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.25);
		transition: all 0.3s ease;
	}

	.card-action-btn:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.3);
	}

	.action-btn-icon {
		font-size: 20rpx;
		margin-right: 8rpx;
		filter: drop-shadow(0 1rpx 2rpx rgba(0, 0, 0, 0.2));
	}

	.action-btn-text {
		font-size: 24rpx;
		color: #fff;
		font-weight: 500;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
	}

	/* 提升整体视觉层次 */
	.page-container::before {
		content: '';
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		height: 200rpx;
		background: linear-gradient(180deg, rgba(102, 126, 234, 0.05) 0%, transparent 100%);
		pointer-events: none;
		z-index: 1;
	}
</style>
