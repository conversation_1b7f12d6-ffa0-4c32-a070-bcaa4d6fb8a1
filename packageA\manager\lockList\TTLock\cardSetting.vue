<template>
	<view>
		<view class="title">请点击写入酒店扇区信息</view>
		<view class="btn" @click="hotelSetting">
			写入酒店扇区信息
		</view>
	</view>
</template>

<script>
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				roomInfo:null
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		onLoad() {
			this.$iBox.http('getRoomByLockId', {
				 lock_id: this.lockDetail.id,
				lock_type: "tongtong"
			})({
					method: 'post'
				}).then(res => { 
					this.roomInfo = res.data
				})
		},
		methods: {
			hotelSetting() {
				console.log(this.lockDetail,'lock');
				uni.showLoading({
					title:'正在写入数据...'
				})
				// 写入酒店信息
				this.$iBox.http('getLockSetting', {
					page: 1,
					limit: 100
				})({
					method: 'post'
				}).then(resSetting => {
					let hotelInfo = resSetting.data.hotel_info
					const buildingNumber = this.roomInfo.building_number
					const floorNumber = this.roomInfo.floor_number
					let deviceId = ''
					let info = {
						hotelInfo:hotelInfo,
						buildingNumber: buildingNumber,
						floorNumber: floorNumber,
					}
					console.log(info,'info');
					// 调用设置酒店信息参数
					plugin.setHotelData({
						hotelData: info,
						lockData: this.lockDetail.lock_data
					}).then(res => {
						console.log(res)
						if (res.errorCode === 0) {
							uni.showToast({
								icon: 'none',
								title: '酒店信息设置成功!'
							})
							const sectors = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]; // 使用前10个扇区
							console.log(this.lockDetail.lock_data,'info1');
							// 调用设置酒店扇区
							plugin.setHotelSector({
								sectors:sectors,
								lockData:this.lockDetail.lock_data}).then(res => {
								if (!!res.deviceId) deviceId = res.deviceId;
								console.log("设置酒店扇区", res)
								uni.hideLoading()
								if (res.errorCode === 0) {
									// 设备已成功初始化，请调用开放平台接口上传lockData
									uni.showToast({
										icon: 'none',
										title: '设置酒店扇区成功!'
									})
								} else {
									uni.showToast({
										icon: 'none',
										title: '设置酒店扇区失败!'
									})
								}
							}, deviceId)
						} else {
							uni.showToast({
								icon: 'none',
								title: '酒店信息设置失败!'
							})
						}
					}, deviceId)


				})
			},
			cardSend(){
				
			}
		}
	}
</script>

<style lang="scss">
	.title {
		font-size:40rpx;
		font-weight: 500;
		display: flex;
		align-items: center;
		justify-content: center;
		background: #f3f4f6;
		padding: 20rpx 30rpx;
	}
	
	.btn {
		height: 100rpx;
		width: 600rpx;
		margin: 40rpx auto;
		border-radius: 30rpx;
		background-color: #55aa00;
		color: #FFFFFF;
		display: flex;
		align-items: center;
		justify-content: center;
	}
</style>