<template>
	<view>
		<p style="padding: 30rpx;color: #00000090;">*一人扫码,快速交换钥匙</p>
		<view class=""
			style="display: flex;align-items: center;margin-top: 30rpx;width: 100%;padding: 0 30rpx;justify-content: space-around;">
			<view class="" @click="showEr" style="background: linear-gradient(92.81deg, rgba(180, 197, 255, 0.6) 0%, rgba(116, 91, 240, 0.6) 98.01%);height: 300rpx;width: 300rpx;border-radius: 32rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;padding: 30rpx;
">
				<view class="icon-erweima" style="font-size: 60rpx;">

				</view>
				<p style="font-size: 34rpx;font-weight: 600;margin-top: 10rpx;">展示换房码</p>
				<p style="font-size: 24rpx;color: #6f6f6f;margin-top: 10rpx;text-align: center;">点击展示换房码,请他人扫码换房</p>
			</view>

			<view class="" @click="toScan" style="background: linear-gradient(92.81deg, rgba(176, 245, 224, 0.6) 0%, rgba(74, 223, 191, 0.6) 98.01%);height: 300rpx;width: 300rpx;border-radius: 32rpx;display: flex;flex-direction: column;align-items: center;justify-content: center;padding: 30rpx;
			">
				<view class="icon-saoma" style="font-size: 70rpx;">

				</view>
				<p style="font-size: 34rpx;font-weight: 600;margin-top: 10rpx;">扫描换房码</p>
				<p style="font-size: 24rpx;color: #6f6f6f;margin-top: 10rpx;text-align: center;">扫描他人二维码,快速换房</p>

			</view>
		</view>

		<!-- 二维码 -->
		<m-popup :show="pop" mode="center" @closePop="closePop">
			<view class=""
				style="width: 600rpx;height: 700rpx;display: flex;align-items: center;flex-direction: column;justify-content: center;">
				<p style="margin-bottom: 30rpx;">请他人扫描换房码</p>
				<img :src="url" style="height: 400rpx;width: 400rpx;" alt="" />
			</view>
		</m-popup>

		<m-popup :show="pop1" mode="center" @closePop="closePop1">
			<view class=""
				style="width: 600rpx;height: 400rpx;display: flex;align-items: center;flex-direction: column;justify-content: space-between;padding: 48rpx 0;">
				<p style="color: #00000090;">换房提醒</p>
				<p style="margin-bottom: 30rpx;width: 480rpx;text-align: center;">你正在与<text
						style="color: brown;font-weight: 600;">{{detail.room_number}}</text>号房的<text
						style="color: brown;font-weight: 600;">{{name}}</text>进行换床操作,换床后双方房卡将进行互换,请核对完成后完成操作!</p>
				<view class="" style="display: flex;align-items: center;justify-content: space-around;width: 600rpx;">
					<view class="" @click="defineTh"
						style="height: 80rpx;width: 240rpx;border-radius: 8rpx;display: flex;align-items: center;justify-content: center;border:1px solid #64D0B1;">
						<text style="color: #64D0B1;">不同意</text>
					</view>
					<view class="" @click="agreeTh"
						style="height: 80rpx;width: 240rpx;border-radius: 8rpx;display: flex;align-items: center;justify-content: center;background: #64D0B1;">
						<text style="color: #FFFFFF;">同意换床</text>
					</view>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				id: '',
				url: '',
				pop: false,
				pop1: false,
				detail: null,
				name: '',
				bill_id: '',
				code: ""
			}
		},
		async onLoad(options) {
			await this.$onLaunched;
			// 小程序二维码带参数,
			let scene = wx.getEnterOptionsSync()
			console.log(scene, 'scene');
			if (scene.query.scene) {
				// 扫码场景
				let query = decodeURIComponent(scene.query.scene)
				console.log(query, 'query', this.$iBox.linkFormat(query, "b"), this.$iBox.linkFormat(query,
					"c"));
				//解析参数
				if (query.includes("c=")) {
					this.bill_id = this.$iBox.linkFormat(query, "b")
					this.code = this.$iBox.linkFormat(query, "c")
					this.$iBox.http('getRoomBillInfo', {
						bill_id: this.bill_id
					})({
						method: 'post'
					}).then(res => {

						this.$iBox.http('bindRoomBill', {
							bill_id: id,
							code: c
						})({
							method: 'post'
						}).then(res => {
							if (res.data) {
								this.detail = res.data
								this.pop1 = true
								this.name = res.data.qr_user.name
								this.$iBox.http('getRoomBillInfoByCode', {
									bill_id: this.bill_id,
									code: this.code
								})({
									method: 'post'
								}).then(res => {
									this.name = res.data.qr_user.name
								})
							} else {
								uni.showToast({
									icon: 'error',
									title: res.msg
								})
							}

						})
					}).catch(err => {

					})

				}

			} else if (options && options.bill_id) {
				this.id = options.bill_id
			}
		},
		methods: {
			showEr() {
				this.$iBox.http('createRoomBillQrCode', {
					bill_id: this.id,
					type: 6
				})({
					method: 'post'
				}).then(res => {
					this.pop = true
					this.url = res.data.url
				})
			},
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			// 拒绝换房 - 防重复触发版本
			defineTh() {
				this.$iBox.throttle1(() => {
					this.define()
				}, 2000);
			},

			define() {
				// 显示加载状态
				uni.showLoading({
					title: '处理中...'
				});

				this.$iBox.http('confirmChangeRoom', {
					bill_id: this.bill_id,
					confirm_status: 2,
					code: this.code
				})({
					method: 'post'
				}).then(res => {
					uni.hideLoading();
					this.pop1 = false
					uni.showToast({
						title: '操作成功',
						icon: 'success'
					});
				})
				.catch(err => {
					uni.hideLoading();
					console.error('拒绝换房失败:', err);
				})
			},

			// 同意换房 - 防重复触发版本
			agreeTh() {
				this.$iBox.throttle1(() => {
					this.agree()
				}, 2000);
			},

			agree() {
				// 显示加载状态
				uni.showLoading({
					title: '处理中...'
				});

				this.$iBox.http('confirmChangeRoom', {
					bill_id: this.bill_id,
					confirm_status: 1,
					code: this.code
				})({
					method: 'post'
				}).then(res => {
					uni.hideLoading();
					this.pop1 = false
					uni.showModal({
						title: '提示',
						content: '换床成功!是否返回房间页!',
						success: res => {
							if (res.confirm) {
								uni.switchTab({
									url: '/pages/myRoom/myRoom'
								})
							} else {

							}
						}
					})
				})
				.catch(err => {
					uni.hideLoading();
					console.error('同意换房失败:', err);
				})
			},
			toScan() {
				uni.scanCode({
					onlyFromCamera: false,
					success: (res) => {
						console.log('条码类型：' + res.scanType);
						console.log('条码内容：' + JSON.stringify(res))

						if (res.scanType == 'WX_CODE') {
							let query = decodeURIComponent(res.path.split('?')[1].split('=')[1])
							console.log(query, 'qu');
							this.bill_id = this.$iBox.linkFormat(query, "b")
							this.code = this.$iBox.linkFormat(query, "c")
							this.$iBox.http('getRoomBillInfo', {
								bill_id: this.bill_id
							})({
								method: 'post'
							}).then(res => {
								this.$iBox.http('bindRoomBill', {
									bill_id: this.bill_id,
									code: this.code
								})({
									method: 'post'
								}).then(res => {
									if (res.data) {
										this.detail = res.data
										this.pop1 = true
										this.name = res.data.qr_user.name
										this.$iBox.http('getRoomBillInfoByCode', {
											bill_id: this.bill_id,
											code: this.code
										})({
											method: 'post'
										}).then(res => {
											this.name = res.data.qr_user.name
										})
									} else {
										uni.showToast({
											icon: 'error',
											title: res.msg
										})
									}
								})

							}).catch(err => {

							})


						}
					}
				});
			}
		}
	}
</script>

<style>

</style>