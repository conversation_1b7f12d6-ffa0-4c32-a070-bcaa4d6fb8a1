<template>
	<view class="box">
		<view class="" style="padding: 10rpx;">
			<rich-text :nodes="strings"></rich-text>
		</view>

	</view>
</template>

<script>
	
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				strings:''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		onLoad() {
			uni.showLoading({
				title:'加载中...'
			})
			this.$iBox.http('getMemberRightInterestRichText', {})({
				method: 'post'
			}).then(res => {
				this.strings = this.$iBox.formatRichText(res.data.content)
				uni.hideLoading()
			})
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		min-height:100vh ;
		.vipCard {
			width: 100%;
			// padding: 20rpx 0 20rpx 20rpx;
			display: flex;
			flex-direction: column;
			.swiper {
				width: 100%;
				height: 320rpx;
				padding:20rpx 0 20rpx 0rpx;
				.swiper-item {
					
					width: 700rpx;
					height: 280rpx;
					margin-left: 20rpx;
					background-color: #bdd7ef;
					border-radius: 20rpx;
					padding: 60rpx 30rpx;
					display: flex;
					flex-direction: column;
					position: relative;
		
					&_name {
						display: flex;
						align-items: center;
					}
		
					.progress {
						padding: 30rpx 0;
						width: 500rpx;
						display: flex;
						flex-direction: column;
					}
		
					.saleBox {
						position: absolute;
						display: flex;
						align-items: center;
						justify-content: center;
						
						width: fit-content;
						padding: 6rpx 10rpx;
						border-radius: 30rpx;
						font-size: 20rpx;
						color: #e3d1b0;
						background: #363434;
						right: 30rpx;
						bottom: 30rpx;
					}
		
					.tips {
						display: flex;
						align-items: center;
						justify-content: center;
						position: absolute;
						width: 140rpx;
						height: 50rpx;
						top: 0;
						right: 0;
						border-top-right-radius: 20rpx;
						border-bottom-left-radius: 20rpx;
						background-color: #dbe9f6;
						font-size: 26rpx;
					}
				}
			}
		
			.vipSign {
				padding: 0 30rpx;
		
				.signBox {
					display: flex;
					flex-wrap: wrap;
		
					.signItem {
						display: flex;
						flex-direction: column;
						align-items: center;
						height: 140rpx;
						width: 25%;
						padding: 20rpx;
					}
				}
		
			}
		}
	}
		
</style>
