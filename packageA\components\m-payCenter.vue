<template>
	<view>
		<m-popup :show="poprc" mode="center" @closePop="closePopPay">
			<scroll-view scroll-y="true" style="height: 94vh;">
				<view class="payBox">
					<p style="width: 100%;display: flex;justify-content: center;">收款</p>
					<view class="itemBox">
						<p style="font-size: 28rpx;">系统生成账户</p>
						<uni-data-checkbox mode="button" @change="changePay" class="radioPad" v-model="payName"
							:localdata="payList"></uni-data-checkbox>
					</view>
					<view class="itemBox">
						<p style="font-size: 28rpx;">自定义账户</p>
						<uni-data-checkbox mode="button" @change="changeSelfPay" class="radioPad" v-model="paySelfName"
							:localdata="selfList"></uni-data-checkbox>
					</view>
					<view class="itemBox" v-if="vipCard">
						<view class="" style="display: flex;align-items: center;">
							<p style="font-size: 28rpx;">会员卡:</p>
							<text>{{vipCard.name}}</text>
							<p style="color: #00aaff;">【跨店卡:{{vipCard.balance}}】</p>
							<p style="color: #00aaff;">【单店卡:{{vipCard.user_shop_balance.balance}}】</p>
						</view>

					</view>
					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">收款金额</p>
						<!-- <uni-number-box v-model="billMoney" max="1000000" /> -->
						<uni-easyinput v-model="billMoney" placeholder="0" type="digit"></uni-easyinput>
					</view>

					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">账务项目分组</p>
						<picker @change="bindChange" :value="changeIndex" range-key="name" :range="groupList">
							<view class="pickerBox">
								{{groupList[changeIndex].name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>

					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">账务项目</p>
						<picker @change="bindChange1" :value="changeIndex1" range-key="name" :range="groupTypeList">
							<view class="pickerBox">
								{{groupTypeList[changeIndex1].name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>

					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">收款备注</p>
						<uni-easyinput type="textarea" v-model="remark" placeholder="请输入内容"></uni-easyinput>
					</view>
					<view class="btnClass" @click="getPayTh">
						收款
					</view>
					<view class="" style="height: 40rpx;">

					</view>
				</view>
			</scroll-view>

		</m-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				payList: [],
				selfList: [],
				payName: 0,
				paySelfName: 0,
				vipCard: null,
				groupList: [],
				groupTypeList: [],
				changeIndex: 0,
				changeIndex1: 0,
				remark: '',
				billMoney: 0
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		props: {
			poprc: {
				type: Boolean,
				default: false
			},
			bill: {
				type: Object
			},
			money: {
				type: Number
			}
		},
		watch: {
			poprc: {
				handler(newVal, oldVal) {
					this.billMoney = this.money
					if(this.poprc){
						this.$iBox.http('getAccountList', {
								type: 2
							})({
								method: 'post'
							})
							.then(res => {
						
								this.payList = res.data.filter(item => {
									return  ['wx_pay_off_line','ali_pay','cash_pay','independent','member_pay','jl_pay'].includes(item.sign)
								})
						
								this.payList.forEach(item => {
									item.text = item.account_name
									item.value = item.id
								})
						
								this.payName = this.payList[0].id
						
								this.selfList = res.data.filter(item => {
									return !['wx_pay_off_line','ali_pay','cash_pay','independent','member_pay','jl_pay'].includes(item.sign)
								})
						
								this.selfList.forEach(item => {
									item.text = item.account_name
									item.value = item.id
								})
						
								uni.hideLoading()
							})
							
							this.$iBox
								.http('getDetailTypeGroup', {
									type: 2
								})({
									method: 'post'
								})
								.then(res => {
									this.groupList = res.data
									
									this.$iBox
										.http('getDetailTypeByGroupId', {
											type: 2,
											group_id:this.groupList[0].id
										})({
											method: 'post'
										})
										.then(res1 => {
											this.groupTypeList = res1.data
										})
								})
							
							
					}
				},
				immediate: true,
				deep: true
			},
			bill: {
				handler(newVal, oldVal) {
					if (this.bill && this.bill.common_code) {
						this.$iBox.http('getUserInfoBoss', {
							common_code: this.bill.common_code
						})({
							method: 'post'
						}).then(res => {
							this.vipCard = res.data
						})
					}

				},
				immediate: true,
				deep: true
			},
			money: {
				handler(newVal, oldVal) {
					console.log(newVal, 'money');
					this.billMoney = this.money
				},
				immediate: true,
				deep: true
			},
		},
		mounted() {
				
			

			

		},

		methods: {
			closePopPay() {
				this.$emit('closePay', '')
			},
			changePay(e) {
				console.log(e);
				this.paySelfName = 0
				if (e.detail.data.pay_type_name == '通用会员' || e.detail.data.pay_type_name == '本店会员卡') {
					this.$iBox.http('getUserInfoBoss', {
						common_code: this.bill.common_code
					})({
						method: 'post'
					}).then(res => {
						this.vipCard = res.data
					})
				} else {
					this.vipCard = ''
				}
			},
			changeSelfPay(e) {
				console.log(e);
				this.payName = 0
				this.vipCard = ''
			},
			bindChange(e) {
				this.changeIndex = e.detail.value[0]
			},
			bindChange1(e) {
				this.changeIndex1 = e.detail.value[0]
			},
			getPayTh() {
				this.$iBox.throttle(() => {
					this.getPay()
				}, 2000);
			},
			getPay() {

				let params = {
					bill_id: this.bill.id,
					account_id: this.payName ? this.payName : this.paySelfName,
					receive_money: this.billMoney,
					auth_code: '',
					memo: this.remark,
					accountItem_id: this.groupList[this.changeIndex].id,
					room_bill_type_id: this.changeIndex1 ? this.groupTypeList[this.changeIndex1].id : '',
					common_code: this.vipCard ? this.vipCard.common_code : '',
					print: 0
				}

				this.$iBox.http('addRoomPayDetail', params)({
					method: 'post'
				}).then(res => {
					this.$emit('closePay', '')
					this.$emit('upBillDetail', '')
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.payBox {
		height: 94vh;
		width: 680rpx;
		padding: 10rpx;

		.itemBox {
			width: 98%;
			background: #f6f6f6;
			margin: 0 auto;
			padding: 20rpx 10rpx;

			.pickerBox {
				position: relative;
				height: 60rpx;
				width: 380rpx;
				border-radius: 14rpx;
				border: 1px solid #eee;
				display: flex;
				margin-top: 14rpx;
				font-size: 30rpx;
				align-items: center;
				padding: 0 10rpx;
			}
		}
	}

	.btnClass {
		width: fit-content;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		// border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		background-color: cornflowerblue;
		margin: 10rpx auto;
	}
</style>
