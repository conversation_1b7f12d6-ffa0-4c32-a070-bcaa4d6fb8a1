<template>
	<view>
		<view class="" style="width: 100vw;overflow-x:hidden;padding: 30rpx;">
			<rich-text :nodes="strings"></rich-text>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				strings:''
			};
		},
		onLoad() {
			uni.showLoading({
				title:'加载中...'
			})
			this.$iBox.http('getMemberAgreementRichText', {})({
				method: 'post'
			}).then(res => {
				this.strings = this.$iBox.formatRichText(res.data.content);
				uni.hideLoading()
			})
		}
	}
</script>

<style lang="scss">

</style>
