<template>
	<view class="mainContent"
		:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.main_color+'A6,'+themeColor.com_color2+'A6)'}">
		<view class="box">
			<view class="title">
				<text style="font-size: 40rpx;">{{manager.shop_name}}</text>
				<view class="">
					<text style="font-size: 26rpx;">总间数:{{total_count}}间</text>
					<text style="padding-left: 30rpx;font-size: 26rpx;">入住中:{{staying_count}}间</text>
					<text style="padding-left: 30rpx;font-size: 26rpx;"
						:style="Number(stay_rate)<50?'color:red':'color:green'">入住率:{{stay_rate}}%</text>
				</view>

			</view>
			<view class="chooseBox">
				<text>房型筛选：</text>
				<view class="pickerBox" @click="pickRoomType" hover-class="getFocs" hover-stay-time="1000">
					<text>{{changeName}}</text>
					<view class="icon-down" :class="pop?'arrow_ac':'arrow'"
						style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
					</view>
				</view>
			</view>
			<view class="chooseBox">
				<text>房间号搜索：</text>
				<view class="" style="position: relative;">
					<input type="text" @input="roomNumSearch" v-model="roomNumber"
						style="border: 1px solid #eee;padding:0 10rpx;width: 280rpx;border-radius: 16rpx;height: 60rpx;margin-top: 20rpx;"
						placeholder="房间号">
					</input>
					<view class="icon-search"
						style="position: absolute;right: 15rpx;top:35rpx;bottom: 0;margin: auto 0;font-size: 32rpx;">
					</view>
				</view>


			</view>

			<checkbox-group @change="checkboxChange">
				<view class="roomStatusList" :style="{background:themeColor.com_color1+'40'}">
					<view class="item" v-for="(item, index) in roomStatusList" :key="index">
						<label>
							<checkbox style="transform:scale(0.7)" :value="item.id" />
						</label>
						<view class="itemAround" :style="{background:item.color}">

						</view>
						<text style="font-size: 24rpx;padding-left: 8rpx;">{{item.status_name}}</text>
					</view>
				</view>
			</checkbox-group>


			<!-- <checkbox-group @change="checkboxChange1">
				<view class="roomWorkStatusList" :style="{background:themeColor.com_color1+'40'}">
					<view class="item" v-for="(item,index) in roomWorkStatusList" :key="index">
						<label>
							<checkbox style="transform:scale(0.7)" :value="item.id" />
						</label>
						<view class="itemAround" :style="{background:item.color}">

						</view>
						<text style="font-size: 24rpx;padding-left: 8rpx;">{{item.status_name}}</text>
					</view>
				</view>
			</checkbox-group> -->

			<view class="floorAndBulidBox">
				<view class="bulid">
					<view class="bulidBox" v-for="(item, index) in buildingList" :key="index"
						:style="bulidCurrent==item.id?'color:'+themeColor.com_color1+';border:1px solid '+themeColor.com_color1:''"
						@click="chooseBuilding(item)">
						{{item.name}}
					</view>
				</view>
				<view class="floor">
					<view class="floorBox" v-for="(item, index) in floorList" :key="index"
						:style="floorCurrent==item.id?'color:'+themeColor.com_color1+';border:1px solid '+themeColor.com_color1:''"
						@click="chooseFloor(item)">
						{{item.name}}
					</view>
				</view>
			</view>

			<view class="roomBox">
				<mThirdRoomStatus :roomList="list" @roomGet="roomClick"></mThirdRoomStatus>
			</view>

		</view>
		<!-- 房型筛选 -->
		<m-popup :show="pop" @closePop="closePop" :closeable="false">
			<view class="picker-view_box">
				<view class="" style="position: absolute;top: 20rpx;right: 30rpx;z-index: 99999999;">
					<text @click="sure">确定</text>
				</view>
				<picker-view :value="value" @change="bindChange" class="picker-view">
					<picker-view-column>
						<view class="item" v-for="(item,index) in roomStatusBox" :key="index">{{item.name}}</view>
					</picker-view-column>
				</picker-view>
			</view>

		</m-popup>


	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'

	import mThirdRoomStatus from '../../components/m-thirdRoomStatus.vue'
	import mRoomClearBox from '../../components/m-roomClearBox.vue'
	import mConnectRoom from '../../components/m-connectRoom.vue'
	export default {
		data() {
			return {
				stay_rate: '',
				total_count: 0,
				staying_count: 0,
				list: [], //房态图使用的房态数据
				listCom: [], //筛选组件使用的房态数据
				roomStatusList: [],
				roomWorkStatusList: [],
				roomStatusBox: [{
					id: 0,
					name: '全部'
				}],
				value: [0],
				roomNumber: '',
				changeName: '',
				changeIndex: 0,
				pop: false,
				pop1: false,
				
				focusClass: false,
				buildingList: [{
					id: 0,
					name: '全部'
				}], //楼栋列表
				floorList: [], //楼层列表
				bulidCurrent: 0,
				floorCurrent: 0,
				params: {
					floor_id: '',
					building_id: "",
					room_type_id: "",
					room_number: "",
					room_sale_type: "",
					third_status:[]
				},
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		components: {
			mThirdRoomStatus
		},
		watch: {
		
		},
		onLoad() {
			this.changeName = this.roomStatusBox[0].name
			this.$iBox
				.http('bossGetRoomType', {})({
					method: 'post'
				})
				.then(res => {
					res.data.forEach(item => {
						this.roomStatusBox.push(item)
					})

				})
				
				this.roomStatusList = [{id:1,color:'#55aa00',status_name:'空净'},{id:2,color:'#0055ff',status_name:'在住'},{id:3,color:'#b90000',status_name:'脏房'},
				{id:4,color:'#050b00',status_name:'维修'},{id:5,color:'#ff9900',status_name:'自用'},{id:6,color:'#555500',status_name:'停用'}]
				
			// this.$iBox
			// 	.http('getRoomClearStatus', {})({
			// 		method: 'post'
			// 	})
			// 	.then(res => {
			// 		let list = res.data
			// 		list.forEach(item => {
			// 			item.checked = false
			// 		})
			// 		this.roomStatusList = list
			// 	})
			// getRoomRecordStatus getRoomBuilding
			// this.$iBox.http('getRoomRecordStatus', {})({
			// 		method: 'post'
			// 	})
			// 	.then(res => {
			// 		this.roomWorkStatusList = res.data
			// 	})

			this.$iBox.http('getRoomBuilding', {
					page: 1,
					limit: 10000
				})({
					method: 'post'
				})
				.then(res => {
					res.data.list.forEach(item => {
						this.buildingList.push(item)
					})
				})
		},
		onShow() {

			console.log('show');
			this.getUseableRoom()

		},
		methods: {
			...mapActions('room', ['getRoomInfo']),
			role(e) {
				let a = this.roles_list.filter(item => {
					return item.permission == e
				}).length
				return a
			},
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			
			callPhone(e) {
				uni.makePhoneCall({
					phoneNumber: e
				})
			},

			
			pickRoomType() {
				this.pop = true
				console.log(this.roomStatusBox, 'fangxing');
			},
			arrRecode(e) {
				console.log(e, 'dfdd', this.roomInfo.room_status_record_id);
				return e.sign.includes(this.roomInfo.room_status_record_id)

			},
			arrClear(e) {
				return e.sign.includes(this.roomInfo.clean_status)

			},

		
			// 确定回调事件
			treeConfirm(e) {
				console.log(e)
			},
			// 取消回调事件
			treeCancel(e) {
				console.log(e)
			},
			rangeChoose() {
				this.showRc = true
			},
			leaveAndTime(e) {
				let lastTime = this.$moment(e * 1000).locale('zh-cn').fromNow()
				console.log(lastTime);
				if(lastTime=='1 小时'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "hours")+'小时';
				}else if(lastTime=='1 天'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}else if(lastTime.includes('月')){
					
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}
				console.log(lastTime, 'lastTime');
				if (lastTime.includes('前')) {
					if (lastTime.includes('秒')) {
						lastTime = '-' + lastTime.split('秒')[0].trim() + 's'
					} else if (lastTime.includes('分')) {
						lastTime = '-' + lastTime.split('分')[0].trim() + 'm'
					} else if (lastTime.includes('时')) {
						lastTime = '-' + lastTime.split('小时')[0].trim() + 'h'
					} else if (lastTime.includes('天')) {
						lastTime = '-' + lastTime.split('天')[0].trim() + 'd'
					} else if (lastTime.includes('月')) {
						lastTime = '-' + lastTime.split('个')[0].trim() + '月'
					}
				} else {
					if (lastTime.includes('秒')) {
						lastTime = lastTime.split('秒')[0].trim() + 's'
					} else if (lastTime.includes('分')) {
						lastTime = lastTime.split('分')[0].trim() + 'm'
					} else if (lastTime.includes('时')) {
						lastTime = lastTime.split('小时')[0].trim() + 'h'
					} else if (lastTime.includes('天')) {
						lastTime = lastTime.split('天')[0].trim() + 'd'
					} else if (lastTime.includes('月')) {
						lastTime = lastTime.split('个')[0].trim() + '月'
					}
				}
				return lastTime;
			},
			formatTime(e) {
				let lastTime = this.$moment(e * 1000).locale('zh-cn').fromNow(true)
				console.log(lastTime);
				if(lastTime=='1 小时'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "hours")+'小时';
				}else if(lastTime=='1 天'){
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}else if(lastTime.includes('月')){
					
					let start_date = this.$moment(e * 1000);
					let end_date = this.$moment();
					lastTime = start_date.diff(end_date, "days")+'天';
				}
				//秒
				
				return lastTime
			},

			getUseableRoom() {
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox
					.http('getThirdSystemRoomStatus', this.params)({
						method: 'post'
					})
					.then(res => {
						this.stay_rate = res.data.stay_rate.toFixed(2)
						this.total_count = res.data.total_count
						this.staying_count = res.data.staying_count
						this.list = res.data.list

						uni.hideLoading()
					})
			},
			getUseableRoomCom() {
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox
					.http('getThirdSystemRoomStatus', this.params)({
						method: 'post'
					})
					.then(res => {
						this.listCom = res.data.list
						uni.hideLoading()
					})
			},
			roomNumSearch() {
				this.params.room_number = this.roomNumber
				this.getUseableRoom()
			},
			sureconn() {
				this.getUseableRoom()
			},
			checkboxChange(e) {
				console.log(e);
				this.params.third_status = e.detail.value.map(res=>{return res*1-1})
				this.getUseableRoom()
			},
			checkboxChange1(e) {

				this.params.room_record_status = e.detail.value.map(Number)
				this.getUseableRoom()
			},
			chooseBuilding(e) {
				this.bulidCurrent = e.id
				if (e.id != 0) {
					this.$iBox.http('getRoomFloor', {
							page: 1,
							limit: 10000,
							building_id: e.id == 0 ? '' : e.id
						})({
							method: 'post'
						})
						.then(res => {
							this.floorList = [{
								id: 0,
								name: '全部'
							}]
							res.data.list.forEach(item => {
								this.floorList.push(item)
							})

						})
				} else {
					this.floorList = []
				}
				this.params.building_id = this.bulidCurrent == 0 ? '' : this.bulidCurrent
				this.params.floor_id = ''
				this.getUseableRoom()
			},
			chooseFloor(e) {
				this.floorCurrent = e.id
				this.params.building_id = this.bulidCurrent
				this.params.floor_id = this.floorCurrent == 0 ? '' : this.floorCurrent
				this.getUseableRoom()
			},
			bindChange(e) {
				console.log(e);
				this.changeIndex = e.detail.value[0]
			},
			sure() {
				this.pop = false
				this.changeName = this.roomStatusBox[this.changeIndex].name
				this.params.room_type_id = this.changeIndex == 0 ? '' : this.roomStatusBox[this.changeIndex].id
				this.getUseableRoom()
			},
			closePopBookToIn() {
				this.popBookToIn = false
				this.chooseConnBill = []
			},
			bindChange1(e) {
				this.changeIndex1 = e.detail.value[0]
			},
			bindChange2(e) {
				this.changeIndex2 = e.detail.value[0]
			},
			getGender(e) {
				this.chooseConn = e.item
				this.msgIndex = e.index1
			},
			changePrice(e) {
				this.itemPrice = e
				this.pricesBox = e.room_date_price
				this.popPrice = true
			},
			
			
		}
	}
</script>

<style scoped lang="scss">
	.mainContent {
		border: 1px solid transparent;

		.box {
			height: auto;
			width: 700rpx;
			border-radius: 20rpx;
			background: #ffffff;
			margin: 20rpx auto;
			padding: 30rpx;

			.title {
				height: 110rpx;
				width: 100%;
				border-bottom: 1px solid #eee;
			}

			.chooseBox {
				height: 170rpx;
				width: 100%;
				padding: 20rpx 0;
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				border-bottom: 1px solid #eee;
				position: relative;

				.getFocs {
					border: 1px solid #ee607a !important;
				}

				.pickerBox {
					margin-top: 20rpx;
					position: relative;
					height: 60rpx;
					width: 280rpx;
					border-radius: 14rpx;
					border: 1px solid #eee;
					display: flex;
					padding: 0 20rpx;
					font-size: 30rpx;
					align-items: center;

					.arrow {
						animation-name: to_bottom_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					.arrow_ac {
						animation-name: to_up_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					/* 箭头动画 */

					@keyframes to_up_show {
						0% {
							transform: rotate(0);
						}

						50% {
							transform: rotate(90deg);
						}

						100% {
							transform: rotate(180deg);
						}
					}

					@keyframes to_bottom_show {
						0% {
							transform: rotate(180deg);
							animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
						}

						50% {
							transform: rotate(90deg);
							animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
						}

						100% {
							transform: rotate(0deg);
						}
					}
				}
			}

			.roomStatusList {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				min-height: 80rpx;
				// background: #d8e6ee;
				border-radius: 10rpx;
				width: 100%;
				margin-top: 20rpx;

				.item {
					display: flex;
					align-items: center;
					width: 25%;
					justify-content: center;

					.itemAround {
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
					}
				}

			}

			.roomWorkStatusList {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				min-height: 140rpx;
				// background: #d8e6ee;
				border-radius: 10rpx;
				width: 100%;
				margin-top: 20rpx;

				.item {
					display: flex;
					align-items: center;
					width: 30%;
					justify-content: center;

					.itemAround {
						width: 20rpx;
						height: 20rpx;
						border-radius: 50%;
					}
				}

			}

			.roomBox {
				border-top: 1px solid #eee;
				margin-top: 20rpx;
			}

			.floorAndBulidBox {
				min-height: 120rpx;
				border-top: 1px solid #eee;
				width: 100%;
				margin-top: 20rpx;
				padding: 20rpx;

				.bulid {
					width: 100%;
					display: flex;
					flex-wrap: wrap;

					.bulidBox {
						width: fit-content;
						padding: 0rpx 8rpx;
						border-radius: 6rpx;
						color: #333;
						border: 1px solid #333;
						font-size: 30rpx;
						margin-right: 20rpx;
						margin-top: 14rpx;
					}
				}

				.floor {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					margin-top: 30rpx;

					.floorBox {
						width: fit-content;
						padding: 0rpx 8rpx;
						border-radius: 6rpx;
						color: #333;
						border: 1px solid #333;
						font-size: 30rpx;
						margin-right: 20rpx;
						margin-top: 14rpx;
					}
				}
			}
		}
	}

	.picker-view_box {
		position: relative;
		height: 650rpx;
		width: 100vw;

		.picker-view {
			height: 600rpx;
			width: 100vw;

			.item {
				width: 100%;
				align-items: center;
				justify-content: center;
				text-align: center;
			}
		}
	}

	.roomStatusContent {
		width: 100%;
		min-height: 700rpx;

		.title {
			padding: 20rpx;
			font-size: 40rpx;
			font-weight: 600;
			padding-right: 10rpx;
		}

		.content {
			border-top: 1px solid #e5e3e3;
			padding: 20rpx;

			.statusRoom {
				width: 94%;
				margin: 14rpx auto;
				background-color: #f7f7f7;
				padding: 10rpx;
				font-size: 38rpx;
				font-weight: 600;
				display: flex;
				align-items: center;

				.timeOver {
					border-radius: 50%;

					font-size: 24rpx;
					width: 48rpx;
					height: 48rpx;
					border-radius: 50%;

					color: #fff;
					font-weight: 600;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 18rpx;
				}
			}

			.btnBox1 {
				display: flex;
				flex-wrap: wrap;

				.btnBox {
					width: 25%;
					display: flex;
					align-items: center;
					justify-content: center;

					.btn {
						width: 96%;
						padding: 8rpx;
						border: 1px solid #b7b7b7;
						margin-top: 14rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						border-radius: 6rpx;
					}
				}

			}

			.billDetail {
				background-color: #f7f7f7;
				width: 94%;
				margin: 24rpx auto;
				background-color: #f7f7f7;
				padding: 20rpx;
				font-size: 26rpx;
				display: flex;
				flex-direction: column;
				line-height: 40rpx;

				.billDetail_title {
					display: flex;
					align-items: center;
					justify-content: space-between;
					font-size: 26rpx;
				}
			}


			.futureBox {
				.futureBill {
					width: 96%;
					padding: 10rpx;
					color: #fff;
					font-size: 24rpx;
					margin-top: 22rpx;
					margin-left: 12rpx;
				}
			}
		}
	}

	.zjBox {
		height: 80vh;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;

		.contenBox {
			display: flex;
			border: 1px solid #ccc;
			border-radius: 6rpx;
			width: 560rpx;
			padding: 10rpx;
			flex-wrap: wrap;
			min-height: 60rpx;

			.innerBox {
				width: fit-content;
				padding: 10rpx;
				border: 1px solid #ccc;
				background-color: #fafafa;
				margin-right: 10rpx;
				margin-top: 10rpx;
				border-radius: 4rpx;
				font-size: 24rpx;
			}
		}

		.sureBtn {
			width: 400rpx;
			padding: 20rpx;
			margin-top: 20rpx;
			background-color: #55aa7f;
			color: #fff;
			display: flex;
			align-items: center;
			justify-content: center;
			border-radius: 14rpx;
			position: absolute;
			bottom: 20rpx;
			left: 0;
			right: 0;
			margin: 0 auto;
		}
	}

	.bookIn {
		height: 80vh;
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 10rpx;

		.bookBill {
			display: flex;
			flex-direction: column;
			align-items: center;

			.item {
				margin: 20rpx 0;
				display: flex;
				// flex-direction: column;
				align-items: center;

				// border-bottom: 1px solid #eee;
				.item_roomBox {
					width: 640rpx;

					.item_roomBox_title {
						width: 100%;
						display: flex;
						align-items: center;

						.item_roomBox_title_text {
							width: 33%;
							font-size: 28rpx;

						}
					}
				}

				.item_msg {
					width: 640rpx;
					display: flex;
					flex-wrap: wrap;
					align-items: center;

					.item_msg_inpt {
						border: 1px solid #eee;
						padding: 0 10rpx;
						border-radius: 4rpx;
						height: 40rpx;
						background: #fff;
						margin: 4rpx;
					}

					.msgItem {
						display: flex;
						align-items: center;
						padding: 10rpx 0;

						.pickerBox {
							position: relative;
							height: 43rpx;
							width: 110rpx;
							border-radius: 4rpx;
							border: 1px solid #eee;
							display: flex;
							font-size: 30rpx;
							align-items: center;
							background-color: #fff;
							padding-left: 6rpx;
							font-size: 26rpx;

							.arrow {
								animation-name: to_bottom_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}

							.arrow_ac {
								animation-name: to_up_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}


						}
					}
				}
			}
		}
	}
</style>


