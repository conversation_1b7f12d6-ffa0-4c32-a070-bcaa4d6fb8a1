<template>
	<view>
		<view class="nameBox">
			<text style="padding-right: 110rpx;">别名</text>
			<view class="" style="width: 400rpx;">
				<input type="text" placeholder="请填写锁别名" v-model="name" />
			</view>
		</view>
		
		<view style="width: 400rpx;margin: 100rpx auto;" >
			<button type="primary" @click="bind_room">确定</button>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				name:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		methods: {
			bind_room(){
				this.$iBox.http('editLockInfo', {id:this.lockDetail.id,lock_alias:this.name})({
					method: 'post'
				}).then(res => { 
					uni.showToast({
						icon:'none',
						title:'修改成功'
					})
				})
			}
			
		}
	}
</script>

<style lang="scss" scoped>
	.nameBox {
		background-color: #fff;
		padding: 30rpx;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e4e7ed;
		// justify-content: space-between;
	}
</style>
