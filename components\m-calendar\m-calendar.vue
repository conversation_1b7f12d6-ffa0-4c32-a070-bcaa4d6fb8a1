<template>
	<div class="calendar">
		<div class="calendar-header">
			<div class="week-number">
				<span v-for="(item, index) in weekList"
					:style="{ color: (index == 0 || index == weekList.length - 1) && themeColor }"
					:key="index">{{ item }}</span>
			</div>
			<p class="calendar-title" v-if="title">{{ title }}</p>
		</div>
		<p :style="{ height: title ? '68px' : '40px' }"></p>
		<div class="calendar-wrapper" v-for="(item, index) in calendar" :key="index">
			<h3 class="titleH3" v-text="item.year + '年'+ item.month + '月'"></h3>
			<ul class="each-month">
				<li class="each-day" v-for="(day, idx) in item.dayList" :key="idx"
					:class="[addClassBg(day, item.month, item.year)]"
					:style="{ background: themeOpacityBg(day, item.month, item.year) }"
					@click="chooseDate(day, item.month, item.year)">
					<div class="each-day-div"
						:class="[addClassName(day, item.month, item.year), { 'trip-time': isCurrent(day, item.month, item.year) }]"
						:style="{ background: themeBg(day, item.month, item.year) }">
						{{ day ? day : '' }}
					</div>
					<span class="recent" :style="{ color: themeColor }"
						v-text="setTip(day, item.month, item.year)"></span>
				</li>
			</ul>
		</div>
		<slot></slot>
	</div>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';

	export default {
		props: {
			title: {
				type: [String, Object],
				default () {
					return '';
				}
			},
			mode: {
				type: [String, Number],
				default () {
					return 1;
				}
			},
			startDate: {
				//开始日期
				type: [String, Object, Date],
				default () {
					const year = new Date().getFullYear(),
						month = new Date().getMonth() + 1,
						day = new Date().getDate();
					return year + '/' + month + '/' + day;
				}
			},
			endDate: {
				//结束日期
				type: [String, Object, Date],
				default () {
					return '';
				}
			},
			betweenStart: {
				//日历可选范围开始
				type: [String, Object, Date],
				default () {
					return '';
				}
			},
			betweenEnd: {
				//日历可选结束日期
				type: [String, Object, Date],
				default () {
					return '';
				}
			},
			initMonth: {
				//初始化的月数
				type: [String, Number],
				default () {
					return 6;
				}
			},
			themeColor: {
				//主题色
				type: [String],
				default: '#415FFB'
			},
			bgColor: {
				//背景色
				type: [String],
				default: '#FFFFFF'
			},
			imme_days: {
				type: [String, Number],
				default () {
					return '';
				}
			}
		},
		data() {
			return {
				preDates: '',
				startDates: '',
				endDates: '',
				betweenStarts: '',
				betweenEnds: '',
				calendar: [],
				weekList: [],
				flagDay: ''
			};
		},
		mounted() {
			let week = ['日', '一', '二', '三', '四', '五', '六']
			this.weekList = week
			this.init();

		},
		computed: {
			//theme
			getBetweenColor() {
				var hex = this.themeColor;
				if (hex.length == 4) {
					hex = `#${hex[1]}${hex[1]}${hex[2]}${hex[2]}${hex[3]}${hex[3]}`;
				}
				var str = 'rgba(' + parseInt('0x' + hex.slice(1, 3)) + ',' + parseInt('0x' + hex.slice(3, 5)) + ',' +
					parseInt('0x' + hex.slice(5, 7)) + ',0.1)';
				return str;
			}
		},
		methods: {
			init() {
				this.year = new Date().getFullYear();
				// this.month = new Date().getMonth() + 1;
				this.day = new Date().getDate();
				this.today = new Date(this.year + '/' + this.month + '/' + this.day) * 1;
				// 判断如果现在是月份第一天并且时间在0-6点 ，则显示上一个月的月份
				if (this.$moment().format('YYYY/MM/DD') == this.$moment().startOf('month').format('YYYY/MM/DD') && 0 <=
					this.$moment().get('hours') && this.$moment().get('hours') <= 6) {
					this.month = new Date().getMonth();
					this.flagDay = this.$moment().subtract(1, 'day').startOf('day').format('YYYY/MM/DD');
				} else if (0 <= this.$moment().get('hours') && this.$moment().get('hours') <= 6) {
					this.month = new Date().getMonth() + 1;
					this.flagDay = this.$moment().subtract(1, 'day').startOf('day').format('YYYY/MM/DD');
				} else {
					this.month = new Date().getMonth() + 1;
					this.flagDay = this.$moment().format('YYYY/MM/DD');
				}
				
				this.startDates = this.resetTime(this.startDate);
				this.endDates = this.resetTime(this.endDate);
				this.betweenStarts = this.resetTime(this.betweenStart);
				this.betweenEnds = this.resetTime(this.betweenEnd);
				this.createClendar(); //创建日历数据
			},
			//创建每个月日历数据，传入月份1号前面用null填充
			createDayList(month, year) {
				const count = this.getDayNum(month, year),
					_week = new Date(year + '/' + month + '/1').getDay();
				let list = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26,
					27, 28
				];

				for (let i = 29; i <= count; i++) {
					list.push(i);
				}
				for (let i = 0; i < _week; i++) {
					list.unshift(null);
				}
				return list;
			},
			//计算传入月份有多少天
			getDayNum(month, year) {
				let dayNum = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];

				if ((year % 4 === 0 && year % 100 !== 0) || year % 400 === 0) {
					dayNum[1] = 29;
				}
				return dayNum[month - 1];
			},
			//根据当天和结束日期创建日历数据
			createClendar() {
				var yearTemp = this.year;
				var monthTemp = this.month;
				if (!!this.betweenStarts) {
					//如果有范围起始日期，可选范围从betweenStart开始
					yearTemp = new Date(this.betweenStarts).getFullYear();
					monthTemp = new Date(this.betweenStarts).getMonth() + 1;
				}
				for (let i = 0; i < this.initMonth; i++) {
					let year = yearTemp;
					let month = monthTemp + i;
					let _monthData = {
						dayList: [],
						month: '',
						year: ''
					};

					var m = Math.ceil(month / 12);
					if (m > 0) {
						year += m - 1;
					} else {
						year += m - 1;
					}
					if (month > 12) {
						month = month % 12 == 0 ? 12 : month % 12;
					}
					if (month <= 0) {
						month = 12 + (month % 12);
					}

					_monthData.year = year;
					_monthData.month = month;
					_monthData.dayList = this.createDayList(month, year);
					this.calendar.push(_monthData);
				}
			},
			//添加日历样式
			addClassName(day, month, year) {
				if (!day) return;
				const _date = new Date(year + '/' + month + '/' + day);
				let className = [];
				let timeStamp = this.$moment().get('hour');
				
				if (_date.getDay() == 0 || _date.getDay() == 6) { //周末或周六样式
					className.push('weekend')
				}
				if (_date * 1 == this.today) {
					className.push('today');
				}
				if (this.betweenStarts) {
					_date * 1 < this.betweenStarts && className.push('disabled');
				} else {
					_date * 1 < this.$moment(this.flagDay).format('x') * 1 && className.push('disabled'); //当天和结束日期之外不可选
					// console.log(_date*1,'_Date');
				}
				_date * 1 > this.betweenEnds && className.push('disabled');
				return className.join(' ');
			},
			//入住离开的区间背景色
			addClassBg(day, month, year) {
				if (!day) return;
				const _date = this.resetTime(year + '/' + month + '/' + day);
				let className = [];
				if (_date >= this.startDates && _date <= this.endDates && this.mode > 1) {
					className.push('between');
				}
				return className.join(' ');
			},
			//theme入住离开的区间背景色
			themeOpacityBg(day, month, year) {
				if (!day) return;
				const _date = this.resetTime(year + '/' + month + '/' + day);
				if (_date >= this.startDates && _date <= this.endDates && this.mode > 1) {
					return this.getBetweenColor;
				}
			},
			//theme获取普通日期选中样式背景
			themeBg(day, month, year) {
				const _date = this.resetTime(year + '/' + month + '/' + day);
				// console.log(this.mode, '当前日期模式！！！',_date);
				//正常模式
				if (this.mode == 1) {
					if (_date == this.startDates) {
						return this.themeColor;
					}
				} else {
					//酒店和往返模式
					if (_date == this.startDates || _date == this.endDates) {
						return this.themeColor;
					}
				}
			},
			//清除时间 时 分 秒 毫秒
			resetTime(dateStr) {
				var date = new Date(dateStr.replace(/-/g, '/'));

				date.setHours(0);
				date.setMinutes(0);
				date.setSeconds(0);
				date.setMilliseconds(0);
				return date * 1;
			},
			//设置今天，明天，后天
			setTip(day, month, year) {
				if (!day) {
					return;
				}
				const _date = this.resetTime(year + '/' + month + '/' + day);
				let tip;

				if (_date == this.today) {

					tip = '今天';


				} else if (_date - this.today == 24 * 3600 * 1000) {

					tip = '明天';


				} else if (_date - this.today == 2 * 24 * 3600 * 1000) {

					tip = '后天';


				}
				if (this.mode == 2 || this.mode == 3) {
					if (_date == this.endDates) {

						tip = '离开';


					} else if (_date == this.startDates) {

						tip = '入住';

					}
				}
				return tip;
			},
			isCurrent(day, month, year) {
				if (!day) {
					return false;
				}
				const _date = this.resetTime(year + '/' + month + '/' + day);

				//正常模式
				if (this.mode == 1) {
					if (_date == this.startDates) {
						return true;
					}
				} else {
					//酒店模式
					if (_date == this.startDates || _date == this.endDates) {
						return true;
					}
				}
			},
			dateFormat(times) {
				let date = new Date(times);
				let recent = '';
				if (times == this.today) {
					recent = '今天';
				} else if (times - this.today === 24 * 3600 * 1000) {
					recent = '明天';
				} else if (times - this.today === 2 * 24 * 3600 * 1000) {
					recent = '后天';
				}

				var year = date.getFullYear();
				var month = parseInt(date.getMonth() + 1) > 9 ? parseInt(date.getMonth() + 1) : '0' + parseInt(date
					.getMonth() + 1);
				var day = date.getDate() > 9 ? date.getDate() : '0' + date.getDate();
				return {
					dateStr: year + '/' + month + '/' + day,
					week: this.weekList[date.getDay()],
					recent
				};
			},
			chooseDate(day, month, year) {
				const _date = this.resetTime(year + '/' + month + '/' + day);
				const week = this.weekList[new Date(_date).getDay()];
	
				// console.log(this.calendar);
				//判断日期区域是否可点击
				if (!day) return;
				if (this.betweenStarts) {
					if (_date * 1 < this.betweenStarts) return;
				} else {
					if (_date * 1 < this.$moment(this.flagDay).format('x') * 1) return; //
				}
				if (_date > this.betweenEnds) return;

				//判断酒店或者往返模式的选择逻辑

				if (this.startDates && this.endDates && _date > this.endDates) {

					this.startDates = _date;
					this.endDates = '';
				} else if (this.endDates && _date > this.endDates) {

					this.endDates = _date;
				} else if (_date >= this.startDates && _date <= this.endDates) {

					this.startDates = _date;
					this.endDates = '';
				} else if (_date < this.startDates) {

					this.startDates = _date;
					this.endDates = '';
				} else if (_date > this.startDates) {

					if (this.mode == 1) {
						this.startDates = _date;
					} else if (this.mode == 2) {
						this.endDates = _date;
					} else {
						let end_time_unix = this.resetTime(this.$moment(this.startDates).add(this.imme_days, 'day').format(
							'YYYY/MM/DD'));
						
						let s = this.$moment(this.startDates)
						let e = this.$moment(_date)
						let c = e.diff(s,'days')
						
						if(c < Number(this.imme_days)){
							uni.showToast({
								icon:'none',
								title:`长租房最少需预订${this.imme_days}晚`,
								duration:1500
							})
						}else{
							this.endDates = _date
							
						}
						
						console.log('gud',c, end_time_unix,this.startDates);
					}
				}



				const choose = {
					startStr: this.dateFormat(this.startDates)
				};

				if (this.mode == 1) {
					this.$emit('callback', choose);
				} else if (this.mode == 2 && this.startDates && this.endDates) {
					choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;
					choose.endStr = this.dateFormat(this.endDates);
					this.$emit('callback', choose);
				} else if (this.mode == 3 && this.startDates && this.endDates) {
					choose.dayCount = (this.endDates - this.startDates) / 24 / 3600 / 1000;
					choose.endStr = this.dateFormat(this.endDates);
					this.$emit('callback', choose);
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	$color: #415ffb;

	.calendar {
		width: 100%;
		height: 100%;
		background: #fff;
		position: fixed;
		left: 0;
		top: 0;
		overflow-y: scroll;
		z-index: 999;

		.calendar-header {
			position: fixed;
			width: 100%;
			top: 0;
			left: 0;
			z-index: 9991;
			box-shadow: 0 2px 15px rgba(100, 100, 100, 0.1);

			.calendar-title {
				height: 28px;
				line-height: 28px;
				background: #fff7dc;
				font-size: 12px;
				padding-left: 15px;
				color: #9e8052;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.week-number {
				background: #fff;
				padding: 0 1%;

				span {
					display: inline-block;
					text-align: center;
					height: 40px;
					line-height: 40px;
					width: 14.2857143%;

					&:first-child,
					&:last-child {
						color: $color;
					}
				}
			}
		}

		.calendar-wrapper {
			position: relative;
			color: #000;
			padding-top: 15px;

			.titleH3 {
				position: sticky;
				position: -webkit-sticky;
				z-index: 999;
				width: 100%;
				left: 0;
				color: #000;
				text-align: center;
				font-size: 16px;
				font-weight: 400;
				line-height: 40px;
				height: 40px;
			}

			.each-month {
				display: inline-block;
				width: 98%;
				margin-left: 1%;
				padding-bottom: 10px;
				font-size: 0;
				border-bottom: 1px solid #f4f4f4;

				.each-day {
					position: relative;
					display: inline-block;
					text-align: center;
					vertical-align: middle;
					width: 14.28%;
					font-size: 16px;
					height: 52px;
					margin-top: 4px;
					padding-top: 4px;

					.each-day-div {
						display: inline-block;
						font-size: 14px;
						height: 30px;
						width: 30px;
						line-height: 30px;
					}

					&.between {
						background: rgba(75, 217, 173, 0.1);
					}

					.disabled {
						color: #ccc !important;

						&.trip-time {
							background: #e7e7e7;
						}

						&.today {
							color: #ccc !important;
							background: none;
						}
					}

					.disabled+.recent {
						color: #ccc;
					}

					.today {
						background: #e7e7e7;
						border-radius: 4px;
					}

					.trip-time {
						background: $color;
						color: #fff !important;
						border-radius: 4px;
					}

					.weekend {
						color: $color;
					}

					.recent {
						position: absolute;
						font-size: 10px;
						width: 100%;
						text-align: center;
						color: $color;
						bottom: 2px;
						left: 0;
					}
				}
			}
		}
	}
</style>
