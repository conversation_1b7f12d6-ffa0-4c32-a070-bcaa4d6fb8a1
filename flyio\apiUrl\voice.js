// 语音识别API配置
export default {
	// 百度语音识别API
	baiduVoiceRecognition: {
		url: 'https://vop.baidu.com/server_api',
		appKey: 'your_app_key', // 需要替换为实际的API Key
		secretKey: 'your_secret_key', // 需要替换为实际的Secret Key
		format: 'mp3',
		rate: 16000,
		channel: 1,
		cuid: 'hotel_miniprogram',
		token: '' // 动态获取
	},
	
	// 腾讯云语音识别API
	tencentVoiceRecognition: {
		url: 'https://asr.tencentcloudapi.com/',
		secretId: 'your_secret_id', // 需要替换为实际的Secret ID
		secretKey: 'your_secret_key', // 需要替换为实际的Secret Key
		region: 'ap-beijing',
		engineModelType: '16k_zh',
		voiceFormat: 'mp3'
	},
	
	// 微信小程序内置语音识别（推荐用于原型）
	wechatVoiceRecognition: {
		lang: 'zh_CN', // 语言
		serviceType: 'iat' // 语音识别服务类型
	}
};
