<template>
	<view>
		<view class="" :key="index" v-for="(item, index) in diyModel" style="padding: 10rpx;">
			<m-richBox v-if="item.sign =='rich_text'" :contents="item.property.content"></m-richBox>
		</view>
		<m-tabbar :list="tabbar"></m-tabbar>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				diyModel: [],
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor','pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		async onLoad() {
			await this.$onLaunched;
			this.$iBox.http('getHomePageUi', {path:'pages/moreTabs/moreTabs',shop_id:this.hotel.id})({
				method: 'post'
			}).then(res => { 
				this.diyModel = res.data
			})
		},
		async onShow() {
			await this.$onLaunched;
			// 设置标题
			let pages = getCurrentPages();
			this.pageUrl = pages[pages.length - 1].route;
			this.tabbar.forEach(item => {
				if (item.path == this.pageUrl) {
					uni.setNavigationBarTitle({
						title: item.name
					})
				}
			})
		}
	}
</script>

<style lang="scss">

</style>
