<template>
	<view class="box">
		<view class="title">
			<text>距离设备越近，信号值越高排序越靠前</text>
			<text style="color: #0081FF;" @click="refresh">刷新</text>
		</view>
		<view class="" style="height: 80rpx;">
		</view>

		<view class="box1" v-for="item in lockList" @click="conn(item)" hover-class="bind_lock">
			<view class="box2">
				<text class="icon-mimasuo" style="font-size: 80rpx;"></text>
				<view class="content">
					<view class="" style="font-size: 34rpx;font-weight: 600;">
						<text>{{item.lockName}}</text>
					</view>

					<view class="" style="font-size: 22rpx;margin-top: 10rpx;display: flex;align-items: center;">
						<!-- <image style="width: 30rpx;height: 30rpx;" src="/static/images/ruo.png" v-if="item.rssi <= -70">
						</image>
						<image style="width: 30rpx;height: 30rpx;" src="/static/images/zhong.png"
							v-if="item.rssi > -70&&item.rssi <= -50"></image>
						<image style="width: 30rpx;height: 30rpx;" src="/static/images/qiang.png" v-if="item.rssi > 50">
						</image> -->
						<text style="padding-left: 6rpx;" v-if="item.rssi <= -70">信号弱</text>
						<text style="padding-left: 6rpx;" v-if="item.rssi > -70&&item.rssi <= -50">信号一般</text>
						<text style="padding-left: 6rpx;" v-if="item.rssi > 50">信号强</text>
					</view>
					<view class="" style="display: flex;align-items: center;">
						<view class="icon-iconset0252">
						</view>
						<text style="font-size: 22rpx;padding-left: 6rpx;">{{item.electricQuantity}}%</text>
					</view>

				</view>
			</view>
			<view class="icon-tianjia" style="font-size: 40rpx;">

			</view>

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	const plugin = requirePlugin("myPlugin");
	export default {
		data() {
			return {
				lockList: [],
				roomNumber: '',
				lockData:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		async onLoad(option) {
			await this.$onLaunched;
			console.log(option, 'dd')
			this.roomNumber = option.roomNumber
			this.startScan()
		},
		methods: {
			...mapActions('room', ['getLockData']),
			refresh() {
				this.startScan()
			},
			// 开始扫描附近的智能锁设备// 再判断是哪个平台的锁 ==========================================通通锁====================================
			startScan() {
				/**
				 * 调用蓝牙扫描接口，返回lockDevice 和 lockList对象
				 * 
				 */
				uni.showLoading({
					title: '正在搜索蓝牙设备'
				})
				plugin.startScanBleDevice((lockDevice, lockList) => {
					console.log(lockList,'lockList');
					this.lockList = lockList.filter(item => {
						return item.isSettingMode == true
					})

					uni.hideLoading()
				}, err => {
					console.log(err)
					uni.hideLoading()
					uni.showToast({
						icon: 'none',
						title: err
					})

				})
			},


			// 校准锁时间
			toCheckLockTime(e) {
				uni.showLoading({
					title: "正在校准锁时间",
				})
				let deviceId = ''
				const start = Date.now();
				// 调用设置锁时间接口，（！！为安全考虑，开锁时间请传入服务器时间）
				// 2.7.0版本开始，开锁接口成功后自动校准本地锁时间
				plugin.setLockTime(Date.now(), e, res => {
					if (res.errorCode === 10003) {
						console.log("获取版本信息时设备连接已断开", res)
					}
				}, deviceId).then(res => {
					wx.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'none',
							title: `锁时间已校准--操作时间:${Date.now() - start}ms`
						})
						uni.hideLoading()
					} else {
						uni.showToast({
							icon: 'none',
							title: `校准锁时间失败:${res.errorMsg}`
						})
						uni.hideLoading()
					}
				}, deviceId)
			},

			eleUpdate(res) {
				this.$iBox.http('updateElectricQuantity', res)({
					method: 'post'
				}).then(res => {

				})
			},
			conn(e) {
				let deviceId = ''
				uni.showLoading({
					title: '正在初始化蓝牙智能锁'
				})

				// 判断是否初始化，有则先删除再初始化,isSettingMode=true代表没有初始化，需要初始化。为false代表已经初始化，不能再绑定
				if (!e.isSettingMode) {
					uni.showToast({
						icon: 'none',
						title: '智能锁不可添加'
					})
					return;
				}
				console.log(e,'dsd');
				plugin.getLockVersion(e.MAC, res => {
					if (res.errorCode === 10003) {
						console.log("获取版本信息时设备连接已断开", res)
					}
				}).then(res => {
					console.log(res,'1');
					if (res.errorCode === 0) {

						// 调用添加锁接口
						plugin.initLock(e, res => {
							if (res.errorCode === 10003) {
								console.log("初始化设备时连接断开", res)
							}
						}).then(res => {
							console.log(res,'2');
							if (res.errorCode === 0) {
								// 添加锁时校准锁时间
								this.lockData = res.lockData
								this.toCheckLockTime(res.lockData)
								// 添加锁
								this.$iBox.http('addTtLock', {
									lock_data: res.lockData,
									room_number: this.roomNumber
								})({
									method: 'post'
								}).then(lock => {
									if (lock.data) {
										console.log(lock.data, '添加成功', lock.id);
										this.getLockData(lock.data)
										// 添加成功关闭重置按钮

										uni.showToast({
											icon: 'none',
											title: '智能锁添加成功'
										})

										uni.reLaunch({
											url: '/packageA/manager/lockList/TTLock/successLock'
										})
										// 更新电量
										let params = {
											id: lock.data.id,
											quantity: e.electricQuantity
										}
										this.eleUpdate(params)

									} else {
										uni.showToast({
											icon: 'none',
											title: `初始化智能锁失败，${res.errorMsg}`
										})
									}
								}).catch(err => {
									console.log('抓取', this.lockData,this.lockDetail);
									plugin.stopAllOperations().then(res => {
										console.log(res);
									})
									uni.showLoading({
										icon:'none',
										title:'门锁添加失败正在重置门锁'
									})
									let a = setTimeout(res=>{
										let that = this
										let deviceId = ""
										/**
										 * 调用重置接口 
										 * 请传入钥匙lockData, 初始化返回的lockData不做任何限制，直接使用调用接口仅适用于本地测试
										 */
										plugin.resetLock(this.lockData, res => {
											if (res.errorCode === 10003) {
												console.log("监控到设备连接已断开", res)
											}
										}, deviceId).then(res => {
											wx.hideLoading({});
											if (!!res.deviceId) deviceId = res.deviceId;
											console.log(res)
											if (res.errorCode === 0) {
												// 同步到服务器
												uni.showModal({
													title:'添加失败',
													content:'请检查锁配置或检查参数是否正确后,重新添加!',
													success() {
														
													}
												})
											} else {
												uni.showToast({
													icon: 'error',
													title: `重置失败: ${res.errorMsg}`
												})
											}
										})
									},2000)
									
									
									
								})
							} else {
								uni.showToast({
									icon: 'none',
									title: `初始化智能锁失败，${res.errorMsg}`
								})
							}
						})
					} else {
						uni.showToast({
							icon: 'none',
							title: `初始化智能锁失败，${res.errorMsg}`
						})
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		position: relative;

		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			position: fixed;
			top: 0;
			width: 100vw;
			background-color: #FFFFFF;
			height: 80rpx;
			border-bottom: 1px solid #eee;
		}

	}

	.box1 {
		height: 160rpx;
		width: 100%;
		background-color: #FFFFFF;
		border: 1px solid #eee;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 30rpx;

	}

	.box2 {
		height: 160rpx;
		width: 100%;
		display: flex;
		align-items: center;
		padding: 0 30rpx;

		.content {
			display: flex;
			flex-direction: column;

			justify-content: center;
			padding-left: 20rpx;
		}
	}
</style>