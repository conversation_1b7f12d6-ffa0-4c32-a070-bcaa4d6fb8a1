<template>
	<view>
		<m-popup :show="poprc" mode="center" @closePop="closePopPay">
			<scroll-view scroll-y="true" style="height: 90vh;">
				<view class="payBox">
					<p style="width: 100%;display: flex;justify-content: center;">冲调</p>
					<scroll-view scroll-y="true" style="max-height: 60vh;min-height: 20vh;" @scrolltolower="onRefresh">
						<view class="itemBox">
							<p style="font-size: 28rpx;">冲调项</p>
							<view class="itemBoxPay" v-for="item in groupList" :key="item.id" @click="choosePay(item)"
								:class="choosePayId==item.id?'chooseItem':''">
								<view class="" style="width: 80rpx;">
									<text class="" style="font-size: 24rpx;color: blueviolet;">选择</text>
								</view>
								<view class="" style="display: flex;flex-wrap: wrap;width: 600rpx;font-size: 24rpx;">
									<view class="itemText">流水ID：{{item.id}}</view>
									<view class="itemText">房间：{{item.room_number}}</view>
									<view class="itemText">动账账户：{{item.account_name}}</view>
									<view class="itemText">账务项目：{{item.detail_type_name}}</view>
									<view class="itemText">我方收款：</view>
									<view class="itemText">客户消费：{{item.amount}}</view>
									<view class="itemText">已退金额：{{item.refund_amount}}</view>
									<view class="itemText">已冲调金额：{{item.edit_amount}}</view>
									<view class="itemText">可冲调：{{item.amount - item.edit_amount - item.refund_amount}}
									</view>
								</view>


							</view>
						</view>
					</scroll-view>
					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">冲调方式</p>
						<uni-data-checkbox mode="button" @change="changePay" class="radioPad" v-model="payName"
							:localdata="payList"></uni-data-checkbox>
					</view>
					<view class="itemBox" style="background-color: #ffffff;" v-if="payName==2">
						<p style="font-size: 28rpx;">冲调金额</p>
						<uni-easyinput v-model="billMoney" placeholder="0" type="digit"></uni-easyinput>
						<!-- <uni-number-box v-model="billMoney" step="0.01" :max="choosePayItem.amount"/> -->
					</view>
					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">冲调备注</p>
						<uni-easyinput type="textarea" v-model="remark" placeholder="请输入内容"></uni-easyinput>
					</view>
					<view class="btnClass" @click="getPayTh">
						冲调
					</view>
					<view class="" style="height: 40rpx;">

					</view>
				</view>
			</scroll-view>


		</m-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				payList: [{
					value: 1,
					text: '全部冲调'
				}, {
					value: 2,
					text: '部分冲调'
				}],
				payName: 1,
				groupList: [],
				changeIndex: 0,
				remark: '',
				sureMoney: 0,
				choosePayId: 0,
				choosePayItem: null,
				billMoney: 0,
				params: {
					page: 1,
					limit: 10,
					pid: 0,
					type: '',
					bill_id:''
				},
				bool: true
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		props: {
			poprc: {
				type: Boolean,
				default: false
			},
			bill: {
				type: Object
			},
			money: {
				type: Number
			}
		},
		watch: {
			poprc: {
				handler(newVal, oldVal) {
					console.log(this.poprc);
					if (this.poprc) {
						this.remark = ''
						this.params.bill_id = this.bill.id
						this.$iBox
							.http('getRoomBillFund', this.params)({
								method: 'post'
							})
							.then(res => {
								this.groupList = res.data.list.filter(item => {
									return (item.amount - item.edit_amount - item.refund_amount) > 0
								})
								console.log(this.groupList, 'this.billMoney');
								if (this.groupList.length > 0) {
									this.choosePayId = this.groupList[0].id
									this.choosePayItem = this.groupList[0]
									this.billMoney = Math.abs(this.choosePayItem.amount - this.choosePayItem
										.edit_amount - this.choosePayItem.refund_amount)

								}

								uni.hideLoading()
							})

					}
				},
				immediate: true,
				deep: true
			},
			refundType: {
				handler(newVal, oldVal) {
					console.log(this.poprc);

				},
				immediate: true,
				deep: true
			},
			money: {
				handler(newVal, oldVal) {
					this.billMoney = this.money
				},
				immediate: true,
				deep: true
			}

		},
		mounted() {

		},

		methods: {
			closePopPay() {
				this.$emit('closeRefund', '')
			},
			changePay(e) {
				console.log(e);
				this.payName = e.detail.value
			},

			bindChange(e) {
				this.changeIndex = e.detail.value[0]
			},

			choosePay(e) {

				this.choosePayItem = e
				this.choosePayId = e.id
				this.billMoney = Math.abs(this.choosePayItem.amount - this.choosePayItem.edit_amount - this.choosePayItem
					.refund_amount)

			},
			getPayTh() {
				this.$iBox.throttle(() => {
					this.getPay()
				}, 2000);
			},
			getPay() {
				console.log(this.payName, this.choosePayItem);
				if (!this.remark) {
					uni.showToast({
						icon: 'none',
						title: '请填写退款原因!'
					})
					return
				}
				let params = {
					detail_id: this.choosePayItem.id,
					modify_amount: this.billMoney,
					memo: this.remark,
				}
				console.log(params);
				this.$iBox.http('modifyRoomBillDetail', params)({
					method: 'post'
				}).then(res => {
					this.$emit('closeRefund', '')
					this.$emit('upBillDetail', '')
				})


			},
			onRefresh() {
				console.log('shuaxin');
				if (this.bool) {
					++this.params.page
					uni.showLoading({
						title: '加载中...'
					})
					this.params.bill_id = this.bill.id
					this.$iBox.http('getRoomBillFund', this.params)({
						method: 'post'
					}).then(res => {
						let new_list = this.groupList.concat(res.data.list)
					
						if (this.groupList.length == res.data.count) {
							this.bool = false
						}
						this.groupList = new_list.filter(item => {
							return (item.amount - item.edit_amount - item.refund_amount) > 0
						})
						
						if (this.groupList.length > 0) {
							this.choosePayId = this.groupList[0].id
							this.choosePayItem = this.groupList[0]
							this.billMoney = Math.abs(this.choosePayItem.amount - this.choosePayItem
								.edit_amount - this.choosePayItem.refund_amount)
						
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.payBox {
		height: 90vh;
		width: 680rpx;
		padding: 10rpx;

		.itemBox {
			width: 98%;
			background: #f6f6f6;
			margin: 0 auto;
			padding: 20rpx 10rpx;

			.pickerBox {
				position: relative;
				height: 60rpx;
				width: 380rpx;
				border-radius: 14rpx;
				border: 1px solid #eee;
				display: flex;
				margin-top: 14rpx;
				font-size: 30rpx;
				align-items: center;
				padding: 0 10rpx;
			}

			.itemBoxPay {
				margin: 20rpx 0;
				display: flex;
				align-items: center;
				border: 1px solid #878787;
				border-radius: 6rpx;
				padding: 20rpx 10rpx;

				.itemText {
					display: flex;
					flex-wrap: wrap;
					padding: 0 20rpx;
				}
			}

			.chooseItem {
				background: #00aa0033;
			}
		}
	}

	.btnClass {
		width: fit-content;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		// border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		background-color: cornflowerblue;
		margin: 10rpx auto;
	}
</style>
