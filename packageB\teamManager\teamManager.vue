<template>
	<view class="container">
		<!-- 搜索框 -->
		<view class="top-search-section" v-if="showActionButtons">
			<view class="search-input-wrapper">
				<view class="search-icon">🔍</view>
				<input
					class="search-input"
					type="text"
					placeholder="搜索姓名或手机号"
					v-model="searchKeyword"
					@input="onSearchInput"
					maxlength="20"
				/>
				<view class="clear-btn" v-if="searchKeyword" @click="clearSearch">
					<text class="clear-icon">×</text>
				</view>
			</view>
		</view>

		<!-- 团队信息卡片 -->
		<view class="refresh-section">
			<view class="refresh-btn" @click="refresh" :class="{'refreshing': isRefreshing}">
				<text class="refresh-text" :style="{'color': isRefreshing ? '#999' : '#55aa7f'}">
					{{ isRefreshing ? '刷新中...' : '刷新数据' }}
				</text>
				<uni-icons
					type="loop"
					size="22"
					:color="isRefreshing ? '#999' : '#55aa7f'"
					:class="{'rotating': isRefreshing}"
				></uni-icons>
			</view>
		</view>
		<view class="info-card">
			<view class="card-header">
				<view class="header-main">
					<text class="team-name">{{ teamInfo.team_name }}</text>
					<text class="time">入住时间：{{ teamInfo.enter_time_plan| moment1 }}</text>
				</view>
				<view class="header-hint" v-if="showActionButtons">
					<text class="filter-hint" @click="showFilterTip">💡 点击统计数字可筛选</text>
				</view>
			</view>

			<view class="stats-container">
				<!-- 第一行：总人数和总房间 -->
				<view class="stats-row">
					<view class="stats-column">
						<view class="stat-item clickable" @click="setStatusFilter('all')" :class="{'active': statusFilter === 'all'}">
							<text class="label">总人数</text>
							<text class="value">{{ teamInfo.user_count }}</text>
						</view>
					</view>
					<view class="stats-column">
						<view class="stat-item non-clickable">
							<text class="label">总房间</text>
							<text class="value">{{ teamInfo.room_count?teamInfo.room_count:'暂无详细数据'  }}</text>
						</view>
					</view>
				</view>

				<!-- 第二行：男女性别 -->
				<view class="stats-row">
					<view class="stats-column">
						<view class="stat-item clickable" @click="setGenderFilter('male')" :class="{'active': genderFilter === 'male'}">
							<text class="label">男性</text>
							<text class="value male">{{ teamInfo.man_count}}</text>
						</view>
					</view>
					<view class="stats-column">
						<view class="stat-item clickable" @click="setGenderFilter('female')" :class="{'active': genderFilter === 'female'}">
							<text class="label">女性</text>
							<text class="value female">{{ teamInfo.woman_count }}</text>
						</view>
					</view>
				</view>

				<!-- 第三行：单间标间 -->
				<view class="stats-row">
					<view class="stats-column">
						<view class="stat-item clickable" @click="setRoomTypeFilter('single')" :class="{'active': roomTypeFilter === 'single'}">
							<text class="label">单间</text>
							<text class="value">{{ teamInfo.single_count}}</text>
						</view>
					</view>
					<view class="stats-column">
						<view class="stat-item clickable" @click="setRoomTypeFilter('standard')" :class="{'active': roomTypeFilter === 'standard'}">
							<text class="label">标间</text>
							<text class="value">{{ teamInfo.room_count - teamInfo.single_count}}</text>
						</view>
					</view>
				</view>

				<!-- 第四行：入住状态 -->
				<view class="stats-row">
					<view class="stats-column">
						<view class="stat-item clickable" @click="setStatusFilter('registered')" :class="{'active': statusFilter === 'registered'}">
							<text class="label">已入住</text>
							<text class="value" style="color: #55aa7f;">{{ getRegisteredCount() }}</text>
						</view>
					</view>
					<view class="stats-column">
						<view class="stat-item clickable" @click="setStatusFilter('unregistered')" :class="{'active': statusFilter === 'unregistered'}">
							<text class="label">未入住</text>
							<text class="value" style="color: #aa0000;">{{ getUnregisteredCount() }}</text>
						</view>
					</view>
				</view>
			</view>
			<!-- 操作按钮区域 -->
			<view class="action-buttons" v-if="showActionButtons">
				<!-- 第一行按钮 -->
				<view class="button-row">
					<view class="action-btn download-btn" @click="downLoadImg(teamInfo.qr_url)">
						<text class="btn-text">下载入住码</text>
					</view>
					<view class="action-btn share-btn">
						<text class="btn-text">分享团队链接</text>
						<button id="shareBtn" class="share-overlay-btn" open-type="share" type="primary"></button>
					</view>
				</view>

				<!-- 第二行按钮 -->
				<view class="button-row">
					<view class="action-btn single-room-btn" @click="generateRoomCode(1)">
						<text class="btn-text">生成单间码</text>
					</view>
					<view class="action-btn standard-room-btn" @click="generateRoomCode(0)">
						<text class="btn-text">生成标间码</text>
					</view>
				</view>

				<!-- 第三行按钮 - 会务组端二维码 -->
				<view class="button-row">
					<view class="action-btn manager-qr-btn" @click="generateManagerQr">
						<text class="btn-text">生成会务组端码</text>
					</view>
					<view class="action-btn view-manager-list-btn" @click="showManagerQrList">
						<text class="btn-text">查看管理员码列表</text>
					</view>
				</view>
			</view>

			<!-- 加载提示 -->
			<view class="loading-section" v-if="!showActionButtons">
				<view class="loading-content">
					<view class="loading-icon">⏳</view>
					<text class="loading-text">正在获取团队信息...</text>
					<text class="loading-tip">请稍候，正在解析扫码参数...</text>
				</view>
			</view>
		</view>



		<!-- 筛选状态指示器 -->
		<view class="filter-indicator" v-if="showActionButtons && (searchKeyword || statusFilter !== 'all' || genderFilter !== 'all' || roomTypeFilter !== 'all')">
			<view class="indicator-content">
				<text class="indicator-text">{{ getFilterInfo() }}</text>
				<view class="clear-filter" @click="clearAllFilters">
					<text class="clear-text">清除</text>
				</view>
			</view>
		</view>

		<!-- 住客信息列表 -->
		<view class="guest-list" v-if="showActionButtons && guests.length > 0">
			<view class="list-header" style="display: flex;align-items: center;justify-content: space-between;">
				<view class="" style="display: flex;align-items: center;">
					<text class="title">住客信息列表</text>
					<text class="count" style="margin-left: 8rpx;">共{{ filteredGuests.length }}人</text>
				</view>
				<view class="">
					<view class="" style="width: fit-content;padding: 8rpx 20rpx;border-radius: 4rpx;display: flex;align-items: center;justify-content: center;background-color: #55aa7f;color: #FFFFFF;">
						<text style="font-size: 26rpx;" @click="downLoadExcel">下载Excel名单</text>
					</view>
				</view>
			</view>

			<view class="guest-item" v-for="(guest, index) in filteredGuests" :key="index">
				<view class="info-row">
					<text class="name">{{ guest.name }}</text>
					<view :class="['gender-tag', guest.gender]">
						{{ guest.gender === 1 ? '男' : guest.gender === 2?'女':'未知' }}
					</view>
					<view class="auth-tag" >
						{{ getStuts(guest)}}
					</view>
				</view>

				<view class="detail-row">
					<view class="detail-item">
						<text class="label">手机号：</text>
						<text class="value">{{ guest.phone?guest.phone:'暂无' }}</text>
					</view>
					<view class="detail-item">
						<text class="label">证件号：</text>
						<text class="value"
							v-if="guest.identification_number">{{ maskIdRegex(guest.identification_number) }}</text>
					</view>
				</view>

				<view class="room-info">
					<text class="room-number">房号：{{ guest.room_number }}</text>
					<text class="room-type">房型：{{ guest.room_type_name }}</text>
				</view>
			</view>

			<!-- 搜索无结果提示 -->
			<view class="no-search-result" v-if="filteredGuests.length === 0 && (searchKeyword || statusFilter !== 'all' || genderFilter !== 'all' || roomTypeFilter !== 'all')">
				<view class="result-content">
					<view class="result-icon">🔍</view>
					<text class="result-title">未找到匹配的住客</text>
					<text class="result-desc">{{ getNoResultMessage() }}</text>
					<view class="result-actions">
						<view class="action-btn clear-filter-btn" @click="clearAllFilters">
							<text class="btn-text">清除筛选条件</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 无住客信息提示 -->
		<view class="no-guests-tip" v-if="showActionButtons && guests.length === 0">
			<view class="tip-content">
				<view class="tip-icon">👥</view>
				<text class="tip-title">暂无住客信息</text>
				<text class="tip-desc">当前团队还没有住客入住信息</text>
			</view>
		</view>

		<!-- 会务组端二维码列表弹窗 -->
		<view class="manager-list-modal" :class="{'modal-show': listModalVisible}" v-if="showManagerList" @click="hideManagerQrList">
			<view class="modal-content manager-list-content" :class="{'content-show': listModalVisible}" @click.stop>
				<view class="modal-header">
					<text class="modal-title">会务组端二维码列表</text>
					<view class="close-btn" @click="hideManagerQrList">
						<text class="close-icon">×</text>
					</view>
				</view>

				<view class="filter-section">
					<view class="filter-tabs">
						<view class="tab-item" :class="{'active': currentFilter === 'all'}" @click="filterManagerList('all')">
							<text class="tab-text">全部</text>
						</view>
						<view class="tab-item" :class="{'active': currentFilter === 0}" @click="filterManagerList(0)">
							<text class="tab-text">未使用</text>
						</view>
						<view class="tab-item" :class="{'active': currentFilter === 1}" @click="filterManagerList(1)">
							<text class="tab-text">已使用</text>
						</view>
					</view>
				</view>

				<scroll-view class="list-scroll" scroll-y="true">
					<view class="manager-qr-item" v-for="(item, index) in filteredManagerList" :key="index">
						<view class="qr-info">
							<view class="qr-header">
								<text class="qr-id">ID: {{ item.id }}</text>
								<view class="status-badge" :class="{'used': item.status === 1, 'unused': item.status === 0}">
									{{ item.status === 1 ? '已使用' : '未使用' }}
								</view>
							</view>
							<view class="qr-details">
								<text class="create-time">创建时间: {{ item.create_time | formatTime }}</text>
								<text class="use-time" v-if="item.status === 1 && item.used_at">使用时间: {{ item.used_at | formatTime }}</text>
							</view>
						</view>
						<view class="qr-actions">
							<view class="action-btn view-qr-btn" @click="viewManagerQr(item)">
								<text class="btn-text">查看</text>
							</view>
							<view class="action-btn share-qr-btn" @click="shareManagerQr(item)">
								<text class="btn-text">分享</text>
							</view>
							<view class="action-btn download-qr-btn" @click="downloadManagerQr(item)">
								<text class="btn-text">下载</text>
							</view>
							<view class="action-btn delete-qr-btn" :class="{'deleting': deletingIds.includes(item.id)}" @click="deleteManagerQr(item)">
								<text class="btn-text">{{ deletingIds.includes(item.id) ? '删除中...' : '删除' }}</text>
							</view>
						</view>
					</view>

					<view class="empty-state" v-if="filteredManagerList.length === 0">
						<view class="empty-icon">📋</view>
						<text class="empty-text">暂无会务组端二维码</text>
						<text class="empty-tip">点击"生成会务组端码"创建新的二维码</text>
					</view>
				</scroll-view>
			</view>
		</view>

		<!-- 房间码显示弹窗 -->
		<view class="code-modal" v-if="showCodeModal" @click="closeCodeModal">
			<view class="modal-content" @click.stop>
				<view class="modal-header">
					<text class="modal-title">{{currentCodeType}}</text>
					<view class="close-btn" @click="closeCodeModal">
						<text class="close-icon">×</text>
					</view>
				</view>

				<view class="code-display">
					<image :src="currentCodeImage" mode="aspectFit" class="code-image"></image>
					<view class="code-tip" v-if="currentCodeType.includes('会务组端')">
						<text class="tip-text">此二维码可用于会务组端管理系统登录</text>
					</view>
					<view class="code-tip" v-else>
						<text class="tip-text">入住不用排队、闪住、闪退。</text>
					</view>
				</view>

				<view class="modal-actions">
					<view class="action-btn download-code-btn" @click="downloadRoomCode">
						<text class="btn-text">保存图片</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState
	} from 'vuex';
	export default {
		data() {
			return {
				teamInfo: {},
				guests: [],
				team_id: '',
				code: '',
				// 控制按钮显示
				showActionButtons: false, // 是否显示操作按钮
				isRefreshing: false, // 是否正在刷新
				// 搜索和筛选相关数据
				searchKeyword: '', // 搜索关键词
				statusFilter: 'all', // 入住状态筛选：'all', 'registered', 'unregistered'
				genderFilter: 'all', // 性别筛选：'all', 'male', 'female'
				roomTypeFilter: 'all', // 房间类型筛选：'all', 'single', 'standard'
				filteredGuests: [], // 筛选后的住客列表
				searchTimer: null, // 搜索防抖定时器
				// 房间码相关数据
				singleRoomCode: '', // 单间码
				standardRoomCode: '', // 标间码
				showCodeModal: false, // 显示码的弹窗
				currentCodeType: '', // 当前显示的码类型
				currentCodeImage: '', // 当前显示的码图片
				// 会务组端二维码相关数据
				showManagerList: false, // 显示管理员码列表
				managerQrList: [], // 管理员码列表
				currentFilter: 'all', // 当前筛选条件：'all', 0, 1
				filteredManagerList: [], // 筛选后的管理员码列表
				listModalVisible: false, // 控制弹窗动画
				deletingIds: [] // 正在删除的ID列表
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting', 'setting'])
		},
		filters: {
			formatTime(value) {
				if (!value) return '';
				// 如果是时间戳，转换为日期字符串
				if (typeof value === 'number') {
					return new Date(value * 1000).toLocaleString();
				}
				// 如果是日期字符串，直接返回
				return value;
			}
		},
		async onShow() {
			await this.$onLaunched;

			this.refresh()


		},
		methods: {
			maskIdRegex(id) {
				return id.replace(/(\d{3})\d{6}(\d+)/, '$1******$2');
			},

			// ========== 搜索和筛选相关方法 ==========

			// 搜索输入处理（防抖）
			onSearchInput() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}

				// 设置新的定时器，300ms后执行搜索
				this.searchTimer = setTimeout(() => {
					this.filterGuests();
				}, 300);
			},

			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
				this.filterGuests();
			},

			// 设置状态筛选
			setStatusFilter(status) {
				this.statusFilter = status;
				// 清除其他筛选，避免冲突
				this.genderFilter = 'all';
				this.roomTypeFilter = 'all';
				this.filterGuests();
			},

			// 设置性别筛选
			setGenderFilter(gender) {
				this.genderFilter = gender;
				// 清除其他筛选，避免冲突
				this.statusFilter = 'all';
				this.roomTypeFilter = 'all';
				this.filterGuests();
			},

			// 设置房间类型筛选
			setRoomTypeFilter(roomType) {
				this.roomTypeFilter = roomType;
				// 清除其他筛选，避免冲突
				this.statusFilter = 'all';
				this.genderFilter = 'all';
				this.filterGuests();
			},

			// 筛选住客列表
			filterGuests() {
				let filtered = [...this.guests];

				// 根据搜索关键词筛选
				if (this.searchKeyword.trim()) {
					const keyword = this.searchKeyword.trim().toLowerCase();
					filtered = filtered.filter(guest => {
						const name = (guest.name || '').toLowerCase();
						const phone = (guest.phone || '').toLowerCase();
						return name.includes(keyword) || phone.includes(keyword);
					});
				}

				// 根据入住状态筛选
				if (this.statusFilter === 'registered') {
					// 已入住：只包含入住中的人
					filtered = filtered.filter(guest => guest.user_status === 1);
				} else if (this.statusFilter === 'unregistered') {
					// 未入住：包含除入住中以外的所有状态（包括已认证）
					filtered = filtered.filter(guest => guest.user_status !== 1);
				}

				// 根据性别筛选
				if (this.genderFilter === 'male') {
					// 男性：gender === 1
					filtered = filtered.filter(guest => guest.gender === 1);
				} else if (this.genderFilter === 'female') {
					// 女性：gender === 2
					filtered = filtered.filter(guest => guest.gender === 2);
				}

				// 根据房间类型筛选
				if (this.roomTypeFilter === 'single') {
					// 单间：需要根据实际的房间类型字段筛选
					filtered = filtered.filter(guest => this.isSingleRoom(guest));
				} else if (this.roomTypeFilter === 'standard') {
					// 标间：需要根据实际的房间类型字段筛选
					filtered = filtered.filter(guest => this.isStandardRoom(guest));
				}

				this.filteredGuests = filtered;
				console.log('筛选结果:', {
					原始数量: this.guests.length,
					筛选后数量: this.filteredGuests.length,
					搜索关键词: this.searchKeyword,
					状态筛选: this.statusFilter,
					性别筛选: this.genderFilter,
					房型筛选: this.roomTypeFilter
				});
			},

			// 判断是否已入住（根据user_status字段判断）
			isRegistered(guest) {
				// 根据新的业务逻辑：
				// 已入住：只包含入住中的人（user_status === 1）
				return guest.user_status === 1;
			},

			// 获取已入住数量（只包含入住中的人）
			getRegisteredCount() {
				return this.guests.filter(guest => guest.user_status === 1).length;
			},

			// 获取未入住数量（包含除入住中以外的所有状态）
			getUnregisteredCount() {
				return this.guests.filter(guest => guest.user_status !== 1).length;
			},

			// 获取标间数量
			getStandardRoomCount() {
				if (this.teamInfo.room_count && this.teamInfo.single_count) {
					return this.teamInfo.room_count - this.teamInfo.single_count;
				}
				return '暂无详细数据';
			},

			// 判断是否为单间
			isSingleRoom(guest) {
				// 根据房间类型名称判断，可能需要根据实际数据调整
				const roomTypeName = (guest.room_type_name || '').toLowerCase();
				return roomTypeName.includes('单') || roomTypeName.includes('single');
			},

			// 判断是否为标间
			isStandardRoom(guest) {
				// 根据房间类型名称判断，可能需要根据实际数据调整
				const roomTypeName = (guest.room_type_name || '').toLowerCase();
				return roomTypeName.includes('标') || roomTypeName.includes('双') || roomTypeName.includes('standard') || roomTypeName.includes('twin');
			},

			// 获取筛选信息描述
			getFilterInfo() {
				const parts = [];
				if (this.searchKeyword) {
					parts.push(`"${this.searchKeyword}"`);
				}
				if (this.statusFilter === 'registered') {
					parts.push('已入住');
				} else if (this.statusFilter === 'unregistered') {
					parts.push('未入住');
				}
				if (this.genderFilter === 'male') {
					parts.push('男性');
				} else if (this.genderFilter === 'female') {
					parts.push('女性');
				}
				if (this.roomTypeFilter === 'single') {
					parts.push('单间');
				} else if (this.roomTypeFilter === 'standard') {
					parts.push('标间');
				}

				if (parts.length === 0) return '';

				const filterText = parts.join(' + ');
				const resultCount = this.filteredGuests.length;
				return `筛选 ${filterText}，找到 ${resultCount} 人`;
			},

			// 获取无结果提示信息
			getNoResultMessage() {
				const conditions = [];
				if (this.searchKeyword) {
					conditions.push(`包含"${this.searchKeyword}"`);
				}
				if (this.statusFilter === 'registered') {
					conditions.push('已入住');
				} else if (this.statusFilter === 'unregistered') {
					conditions.push('未入住');
				}
				if (this.genderFilter === 'male') {
					conditions.push('男性');
				} else if (this.genderFilter === 'female') {
					conditions.push('女性');
				}
				if (this.roomTypeFilter === 'single') {
					conditions.push('单间');
				} else if (this.roomTypeFilter === 'standard') {
					conditions.push('标间');
				}

				if (conditions.length > 0) {
					return `没有找到${conditions.join('且')}的住客`;
				}
				return '没有找到匹配的住客';
			},

			// 清除所有筛选条件
			clearAllFilters() {
				this.searchKeyword = '';
				this.statusFilter = 'all';
				this.genderFilter = 'all';
				this.roomTypeFilter = 'all';
				this.filterGuests();
			},

			// 显示筛选功能提示
			showFilterTip() {
				uni.showModal({
					title: '筛选功能说明',
					content: '• 点击"总人数"查看所有住客\n• 点击"男性"/"女性"按性别筛选\n• 点击"单间"/"标间"按房型筛选\n• 点击"已入住"/"未入住"按状态筛选\n• 使用顶部搜索框可按姓名或手机号搜索\n• 筛选条件互斥，点击新条件会清除其他筛选',
					showCancel: false,
					confirmText: '知道了'
				});
			},


			refresh(){
				// 如果正在刷新，防止重复点击
				if (this.isRefreshing) {
					return;
				}

				// 设置刷新状态
				this.isRefreshing = true;

				let scene = wx.getEnterOptionsSync()
				console.log(scene, 'scene', this.$moment().unix());

				// 延迟检查，如果5秒后还没有获取到team_id，显示错误提示
				setTimeout(() => {
					if (!this.team_id) {
						console.error('未能获取到team_id参数');
						this.showActionButtons = false;
						uni.showModal({
							title: '参数错误',
							content: '未能获取到团队ID参数，请重新扫码进入',
							showCancel: false,
							confirmText: '返回',
							success: () => {
								uni.navigateBack();
							}
						});
					}
				}, 5000);

				if (scene.query.scene) {
					let query = decodeURIComponent(scene.query.scene)
					console.log(query, 'query');
					//解析参数
					if (query.includes("t=")) {
						console.log(query, 'scene12');
						this.team_id = this.$iBox.linkFormat(query, "t")
						this.code = this.$iBox.linkFormat(query, "mtc")

						// 获取到team_id后显示操作按钮
						if (this.team_id) {
							this.showActionButtons = true;
							console.log('team_id获取成功，显示操作按钮:', this.team_id);
						}

						this.$iBox.http('getTeamUserList', {
								team_id: this.team_id,
								code: this.code
							})({
								method: 'post'
							})
							.then(res => {
								this.guests = res.data.user_list || [];
								this.teamInfo = res.data || {};
								// 初始化筛选列表
								this.filterGuests();
								console.log('团队信息获取成功:', this.guests);

								// 显示刷新成功提示
								uni.showToast({
									title: '刷新成功',
									icon: 'success',
									duration: 1500
								});
							})
							.catch(err => {
								console.error('获取团队用户列表失败:', err);
								uni.showToast({
									title: '获取团队信息失败',
									icon: 'none'
								});
							})
							.finally(() => {
								// 重置刷新状态
								this.isRefreshing = false;
							})
					} else {
						console.error('扫码参数格式错误，未包含t=参数');
						// 重置刷新状态
						this.isRefreshing = false;
						uni.showToast({
							title: '参数格式错误',
							icon: 'none'
						});
					}

				} else if (scene.query) {
					this.team_id = scene.query.t
					this.code = scene.query.mtc

					// 获取到team_id后显示操作按钮
					if (this.team_id) {
						this.showActionButtons = true;
						console.log('team_id获取成功，显示操作按钮:', this.team_id);
					}

					this.$iBox.http('getTeamUserList', {
							team_id: this.team_id,
							code: this.code
						})({
							method: 'post'
						})
						.then(res => {
							this.guests = res.data.user_list || [];
							this.teamInfo = res.data || {};
							// 初始化筛选列表
							this.filterGuests();
							console.log('团队信息获取成功:', this.guests);

							// 显示刷新成功提示
							uni.showToast({
								title: '刷新成功',
								icon: 'success',
								duration: 1500
							});
						})
						.catch(err => {
							console.error('获取团队用户列表失败:', err);
							uni.showToast({
								title: '获取团队信息失败',
								icon: 'none'
							});
						})
						.finally(() => {
							// 重置刷新状态
							this.isRefreshing = false;
						})
				} else {
					console.error('未能获取到扫码参数');
					// 重置刷新状态
					this.isRefreshing = false;
					uni.showToast({
						title: '获取参数失败',
						icon: 'none'
					});
				}
			},
			downLoadImg(e) {
				uni.previewImage({
					urls: [e],
					longPressActions: {
						itemList: ['发送给朋友', '保存图片', '收藏'],
						success: function(data) {
							console.log('选中了第' + (data.tapIndex + 1) + '个按钮,第' + (data.index + 1) + '张图片');
						},
						fail: function(err) {
							console.log(err.errMsg);
						}
					}
				});
			},
			getStuts(e) {
				if (e.user_status==-2) {
					return '未报名'
				} else if (e.user_status==-1) {
					return '已采集'
				} else if (e.user_status==0) {
					return '已认证'
				} else if (e.user_status==1) {
					return '入住中'
				} else {
					return '已退房'
				}
			},

			// 获取状态样式
			getStatusStyle(guest) {
				switch(guest.user_status) {
					case -2: // 未入住
						return 'background: #f5f5f5; color: #9e9e9e;';
					case -1: // 已采集
						return 'background: #fff3cd; color: #856404;';
					case 0: // 已认证 - 改为蓝色
						return 'background: #e3f2fd; color: #1976d2;';
					case 1: // 入住中 - 改为绿色
						return 'background: #e8f5e9; color: #4caf50;';
					default: // 已退房
						return 'background: #f3e5f5; color: #7b1fa2;';
				}
			},
			downLoadExcel(){
				this.$iBox.http('downLoadUserExcel', {
						team_id: this.team_id
					})({
						method: 'post'
					})
					.then(res => {
						let fileUrl = res.data
						wx.downloadFile({
							// 示例 url，并非真实存在
							url: fileUrl,
							success: (resDown) => {
								if (resDown.statusCode === 200) {
									uni.saveFile({ //文件保存到本地
										tempFilePath: resDown.tempFilePath, //临时路径
										success: (res) => {
											console.log("下载成功" + res.savedFilePath)
											console.log(JSON.stringify(res))
											uni.showToast({
												icon: 'none',
												mask: true,
												title: '文件已保存!',
												duration: 3000,
											});
											uni.openDocument({
												//fileType: 'docx',
												showMenu: true, //关键点,可以转发到微信
												filePath: res.savedFilePath,
												success: () => {
													console.log('打开文档成功');
												}
											});
										}
									});
								}
							}
						})
					})
			},

			// 生成房间码
			generateRoomCode(single) {
				// 显示加载提示
				uni.showLoading({
					title: '生成中...'
				});

				// 调用API生成房间码
				this.$iBox.http('getTeamQr', {
					single: single, // 0标间，1单间
					team_id: this.team_id
				})({
					method: 'post'
				})
				.then(res => {
					uni.hideLoading();

					if (res.code === 0) {
						// 保存生成的码
						if (single === 1) {
							this.singleRoomCode = res.data;
							this.currentCodeType = '单间码';
						} else {
							this.standardRoomCode = res.data;
							this.currentCodeType = '标间码';
						}

						this.currentCodeImage = res.data;
						this.showCodeModal = true;

						uni.showToast({
							title: '生成成功',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.msg || '生成失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error('生成房间码失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},

			// 关闭码显示弹窗
			closeCodeModal() {
				this.showCodeModal = false;
				this.currentCodeImage = '';
				this.currentCodeType = '';
			},

			// 下载房间码图片
			downloadRoomCode() {
				if (this.currentCodeImage) {
					this.downLoadImg(this.currentCodeImage);
				}
			},

			// ========== 会务组端二维码相关方法 ==========

			// 生成会务组端二维码
			generateManagerQr() {
				uni.showLoading({
					title: '生成中...'
				});

				this.$iBox.http('getTeamManagerQr', {
					team_id: this.team_id
				})({
					method: 'post'
				})
				.then(res => {
					uni.hideLoading();
					console.log('生成会务组端二维码响应:', res);

					if (res.code === 0 && res.data) {
						const qrImage = res.data.qr || res.data.qr_url; // 优先使用qr字段，兼容qr_url

						if (qrImage) {
							// 立即弹窗展示二维码
							this.currentCodeType = '会务组端二维码 - 刚刚生成';
							this.currentCodeImage = qrImage;
							this.showCodeModal = true;

							console.log('生成成功，立即显示二维码:', qrImage);

							// 延迟显示成功提示，避免与弹窗冲突
							setTimeout(() => {
								uni.showToast({
									title: '二维码已生成',
									icon: 'success',
									duration: 2000
								});
							}, 300);

							// 刷新管理员码列表
							if (this.showManagerList) {
								setTimeout(() => {
									this.loadManagerQrList();
								}, 2000);
							}
						} else {
							console.error('接口返回成功但二维码数据为空:', res.data);
							uni.showToast({
								title: '二维码生成失败',
								icon: 'none'
							});
						}
					} else {
						console.error('生成失败，响应数据:', res);
						uni.showToast({
							title: res.msg || '生成失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					uni.hideLoading();
					console.error('生成会务组端二维码失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},

			// 显示管理员码列表
			showManagerQrList() {
				this.showManagerList = true;
				this.listModalVisible = true;
				this.loadManagerQrList();
			},

			// 隐藏管理员码列表
			hideManagerQrList() {
				this.listModalVisible = false;
				// 延迟隐藏，等待动画完成
				setTimeout(() => {
					this.showManagerList = false;
				}, 300);
			},

			// 加载管理员码列表
			loadManagerQrList(showLoading = true) {
				if (showLoading) {
					uni.showLoading({
						title: '加载中...'
					});
				}

				this.$iBox.http('getTeamRoomManagerList', {
					team_id: this.team_id,
					status: this.currentFilter === 'all' ? '' : this.currentFilter
				})({
					method: 'post'
				})
				.then(res => {
					if (showLoading) {
						uni.hideLoading();
					}
					console.log('加载管理员码列表响应:', res);

					if (res.code === 0) {
						this.managerQrList = res.data || [];
						this.filterManagerList(this.currentFilter);
						console.log('管理员码列表已更新，总数:', this.managerQrList.length, '筛选后:', this.filteredManagerList.length);
					} else {
						console.error('加载失败，响应数据:', res);
						uni.showToast({
							title: res.msg || '加载失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					if (showLoading) {
						uni.hideLoading();
					}
					console.error('加载管理员码列表失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				});
			},

			// 筛选管理员码列表
			filterManagerList(filter) {
				this.currentFilter = filter;

				if (filter === 'all') {
					this.filteredManagerList = this.managerQrList;
				} else {
					this.filteredManagerList = this.managerQrList.filter(item => item.status === filter);
				}
			},

			// 查看管理员二维码
			viewManagerQr(item) {
				const qrImage = item.qr || item.qr_url; // 优先使用qr字段，兼容qr_url
				if (qrImage) {
					// 先关闭列表弹窗
					this.hideManagerQrList();

					// 延迟显示二维码弹窗，等待列表弹窗关闭动画完成
					setTimeout(() => {
						this.currentCodeType = `会务组端二维码 (ID: ${item.id})`;
						this.currentCodeImage = qrImage;
						this.showCodeModal = true;
						console.log('查看二维码:', qrImage);
					}, 350);
				} else {
					console.error('二维码不存在，item数据:', item);
					uni.showToast({
						title: '二维码不存在',
						icon: 'none'
					});
				}
			},

			// 分享管理员二维码
			shareManagerQr(item) {
				const qrImage = item.qr || item.qr_url; // 优先使用qr字段，兼容qr_url
				if (qrImage) {
					uni.previewImage({
						urls: [qrImage],
						current: qrImage
					});
					console.log('分享二维码:', qrImage);
				} else {
					console.error('二维码不存在，item数据:', item);
					uni.showToast({
						title: '二维码不存在',
						icon: 'none'
					});
				}
			},

			// 下载管理员二维码
			downloadManagerQr(item) {
				const qrImage = item.qr || item.qr_url; // 优先使用qr字段，兼容qr_url
				if (qrImage) {
					this.downLoadImg(qrImage);
					console.log('下载二维码:', qrImage);
				} else {
					console.error('二维码不存在，item数据:', item);
					uni.showToast({
						title: '二维码不存在',
						icon: 'none'
					});
				}
			},

			// 删除管理员二维码
			deleteManagerQr(item) {
				// 如果正在删除，不允许重复操作
				if (this.deletingIds.includes(item.id)) {
					return;
				}

				const statusText = item.status === 1 ? '已使用' : '未使用';
				uni.showModal({
					title: '确认删除',
					content: `确定要删除ID为 ${item.id} 的管理员码吗？\n状态：${statusText}`,
					confirmText: '删除',
					confirmColor: '#f44336',
					cancelText: '取消',
					success: (res) => {
						if (res.confirm) {
							this.confirmDeleteManagerQr(item.id);
						}
					}
				});
			},

			// 确认删除管理员二维码
			confirmDeleteManagerQr(id) {
				// 添加到删除中列表
				this.deletingIds.push(id);

				this.$iBox.http('deleteTeamRoomManager', {
					id: id
				})({
					method: 'post'
				})
				.then(res => {
					console.log('删除管理员码响应:', res);

					if (res.code === 0) {
						uni.showToast({
							title: '删除成功',
							icon: 'success',
							duration: 1500
						});

						// 立即从本地列表中移除该项，提供即时反馈
						this.removeItemFromLocalList(id);

						// 延迟重新加载列表，确保服务器数据同步
						setTimeout(() => {
							this.loadManagerQrList(false); // 不显示加载提示
						}, 800);
					} else {
						console.error('删除失败，响应数据:', res);
						uni.showToast({
							title: res.msg || '删除失败',
							icon: 'none'
						});
					}
				})
				.catch(err => {
					console.error('删除管理员码失败:', err);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				})
				.finally(() => {
					// 无论成功失败，都要从删除中列表移除
					this.deletingIds = this.deletingIds.filter(deletingId => deletingId !== id);
				});
			},

			// 从本地列表中移除指定项
			removeItemFromLocalList(id) {
				// 从原始列表中移除
				this.managerQrList = this.managerQrList.filter(item => item.id !== id);
				// 重新应用当前筛选条件
				this.filterManagerList(this.currentFilter);
				console.log('已从本地列表移除ID:', id, '当前列表长度:', this.filteredManagerList.length);
			}
		},
		 onShareAppMessage() {
			// 调用异步函数获取分享数据
			try {
				console.log(this.hotel.pic_list[0],'this.hotel.pic_list[0]');
				return {
					title: `入住不用排队、闪住、闪退。`,
					path: '/packageB/teamCheckIn/teamCheckIn' + '?teamId=' + this.teamInfo.id,
					
					imageUrl: this.hotel.pic_list[0]
				};
		
			} catch (error) {
				// 处理异步函数中可能出现的错误
				console.error('获取分享数据失败:', error);
				// 返回默认的转发信息
				return {
					title: '默认分享标题',
					path: '/pages/index/index'
				};
			}
		
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		padding: 20rpx;
		background-color: #f5f5f5;
		position: relative;
	}

	/* 刷新按钮样式 */
	.refresh-section {
		height: 80rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: flex-end;
		padding: 0 30rpx;
	}

	.refresh-btn {
		display: flex;
		align-items: center;
		gap: 8rpx;
		padding: 8rpx 16rpx;
		border-radius: 8rpx;
		transition: all 0.3s ease;
		cursor: pointer;

		&:active {
			transform: scale(0.95);
			background: rgba(85, 170, 127, 0.1);
		}

		&.refreshing {
			pointer-events: none;
			opacity: 0.7;
		}
	}

	.refresh-text {
		font-size: 30rpx;
		text-decoration: underline;
		transition: color 0.3s ease;
	}

	.rotating {
		animation: rotate 1s linear infinite;
	}

	@keyframes rotate {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}

	/* 操作按钮区域样式 */
	.action-buttons {
		width: 100%;
		margin-top: 20rpx;
		padding: 0 30rpx;
	}

	.button-row {
		display: flex;
		justify-content: space-between;
		margin-bottom: 20rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.action-btn {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 250rpx;
		height: 80rpx;
		border-radius: 10rpx;
		color: #FFFFFF;
		font-weight: 500;
		position: relative;
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
		}

		.btn-text {
			font-size: 28rpx;
		}
	}

	.download-btn {
		background-color: #55aa7f;
	}

	.share-btn {
		background-color: #1743d2;
	}

	.single-room-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.standard-room-btn {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
	}

	.download-code-btn {
		background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
		width: 200rpx;
		height: 70rpx;
		border-radius: 35rpx;
	}

	.share-overlay-btn {
		position: absolute;
		bottom: 0;
		right: 0;
		width: 100%;
		height: 100%;
		opacity: 0;
		z-index: 999099;
	}
	/* 信息卡片样式 */
	.info-card {
		background: #fff;
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 32rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.08);
		/* 参考网页3的阴影效果 */

		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: flex-start;
			margin-bottom: 24rpx;
			gap: 16rpx;
			min-height: 80rpx;

			.header-main {
				flex: 1;
				display: flex;
				flex-direction: column;
				gap: 8rpx;

				.team-name {
					font-size: 36rpx;
					font-weight: bold;
					color: #333;
					line-height: 1.2;
					max-width: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}

				.time {
					font-size: 24rpx;
					color: #666;
					line-height: 1.3;
					max-width: 100%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
				}
			}

			.header-hint {
				flex-shrink: 0;
				display: flex;
				align-items: center;

				.filter-hint {
					font-size: 20rpx;
					color: #667eea;
					background: rgba(102, 126, 234, 0.08);
					padding: 8rpx 12rpx;
					border-radius: 16rpx;
					border: 1rpx solid rgba(102, 126, 234, 0.15);
					white-space: nowrap;
					font-weight: 500;
					transition: all 0.3s ease;

					&:active {
						background: rgba(102, 126, 234, 0.15);
						transform: scale(0.95);
					}
				}
			}
		}

		.stats-container {
			width: 100%;
		}

		.stats-row {
			display: flex;
			justify-content: space-between;
			margin-bottom: 8rpx;

			&:last-child {
				margin-bottom: 0;
			}

			.stats-column {
				width: 48%;

				.stat-item {
					display: flex;
					justify-content: space-between;
					margin-bottom: 16rpx;
					padding: 12rpx 16rpx;
					border-bottom: 1rpx solid #eee;
					border-radius: 12rpx;
					margin: 8rpx 0;
					min-height: 44rpx;
					align-items: center;

					&.clickable {
						cursor: pointer;
						transition: all 0.3s ease;
						position: relative;
						border: 1rpx solid transparent;

						&:active {
							transform: scale(0.98);
							background: rgba(102, 126, 234, 0.1);
						}

						&.active {
							background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
							box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
							border-color: #667eea;

							.label,
							.value {
								color: #fff !important;
							}
						}

						&::after {
							content: '👆';
							position: absolute;
							top: 50%;
							right: 6rpx;
							transform: translateY(-50%);
							font-size: 14rpx;
							opacity: 0.5;
						}

						&.active::after {
							opacity: 1;
						}
					}

					&.non-clickable {
						border: 1rpx solid transparent;
						background: transparent;
					}

					.label {
						color: #666;
						font-size: 28rpx;
					}

					.value {
						font-weight: bold;
						color: #333;

						&.male {
							color: #409EFF;
						}

						&.female {
							color: #E91E63;
						}
					}
				}
			}
		}
	}

	/* 住客信息列表样式 */
	.guest-list {
		background: #fff;
		border-radius: 16rpx;
		padding: 24rpx;

		.list-header {
			display: flex;
			justify-content: space-between;
			margin-bottom: 24rpx;

			.title {
				font-size: 32rpx;
				font-weight: bold;
			}

			.count {
				color: #666;
				font-size: 24rpx;
			}
		}

		.guest-item {
			padding: 24rpx 0;
			border-bottom: 1rpx solid #eee;

			&:last-child {
				border: none;
			}

			.info-row {
				display: flex;
				align-items: center;
				margin-bottom: 16rpx;

				.name {
					font-size: 30rpx;
					font-weight: 500;
					margin-right: 20rpx;
				}

				.gender-tag {
					padding: 4rpx 12rpx;
					border-radius: 6rpx;
					font-size: 22rpx;

					&.male {
						background: #e6f7ff;
						color: #1890ff;
					}

					&.female {
						background: #fff0f6;
						color: #eb2f96;
					}
				}

				.auth-tag {
					margin-left: auto;
					padding: 4rpx 16rpx;
					border-radius: 20rpx;
					font-size: 24rpx;

					&.authed {
						background: #e8f5e9;
						color: #4caf50;
					}

					&:not(.authed) {
						background: #f5f5f5;
						color: #9e9e9e;
					}
				}
			}

			.detail-item {
				display: flex;
				margin-bottom: 12rpx;
				font-size: 26rpx;

				.label {
					color: #666;
					min-width: 120rpx;
				}

				.value {
					color: #333;
				}
			}

			.room-info {
				margin-top: 16rpx;
				display: flex;
				gap: 40rpx;

				.room-number,
				.room-type {
					font-size: 26rpx;
					color: #666;
				}
			}
		}
	}

	/* 房间码弹窗样式 */
	.code-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		backdrop-filter: blur(10rpx);
	}

	.modal-content {
		background: #fff;
		border-radius: 20rpx;
		width: 600rpx;
		max-height: 80vh;
		overflow: hidden;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
		animation: modalSlideIn 0.3s ease-out;
	}

	@keyframes modalSlideIn {
		from {
			opacity: 0;
			transform: scale(0.8) translateY(50rpx);
		}
		to {
			opacity: 1;
			transform: scale(1) translateY(0);
		}
	}

	.modal-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		border-bottom: 1rpx solid #eee;
		background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
		flex-shrink: 0; /* 防止头部被压缩 */
	}

	.modal-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.close-btn {
		width: 60rpx;
		height: 60rpx;
		border-radius: 30rpx;
		background: rgba(0, 0, 0, 0.1);
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;

		&:active {
			background: rgba(0, 0, 0, 0.2);
			transform: scale(0.9);
		}
	}

	.close-icon {
		font-size: 40rpx;
		color: #666;
		line-height: 1;
	}

	.code-display {
		padding: 40rpx;
		text-align: center;
		background: #fafafa;
	}

	.code-image {
		width: 400rpx;
		height: 400rpx;
		border-radius: 16rpx;
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
		background: #fff;
	}

	.code-tip {
		margin-top: 20rpx;
		padding: 16rpx 24rpx;
		background: linear-gradient(135deg, #e8f5e9 0%, #f3e5f5 100%);
		border-radius: 12rpx;
		border: 1rpx solid rgba(76, 175, 80, 0.2);

		.tip-text {
			font-size: 24rpx;
			color: #4caf50;
			font-weight: 500;
			line-height: 1.4;
		}
	}

	.modal-actions {
		padding: 30rpx;
		text-align: center;
		border-top: 1rpx solid #eee;
	}

	/* 加载提示样式 */
	.loading-section {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 300rpx;
		padding: 60rpx 30rpx;
	}

	.loading-content {
		text-align: center;
		background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
		padding: 40rpx;
		border-radius: 20rpx;
		border: 1rpx solid rgba(102, 126, 234, 0.1);
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.08);
	}

	.loading-icon {
		font-size: 48rpx;
		margin-bottom: 20rpx;
		animation: loadingRotate 2s linear infinite;
	}

	@keyframes loadingRotate {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.loading-text {
		display: block;
		font-size: 28rpx;
		color: #667eea;
		font-weight: 600;
		margin-bottom: 12rpx;
		letter-spacing: 1rpx;
	}

	.loading-tip {
		display: block;
		font-size: 24rpx;
		color: #999;
		line-height: 1.4;
	}

	/* 无住客信息提示样式 */
	.no-guests-tip {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 200rpx;
		padding: 40rpx 30rpx;
	}

	.tip-content {
		text-align: center;
		background: linear-gradient(135deg, #fff9f0 0%, #fef3e2 100%);
		padding: 40rpx;
		border-radius: 20rpx;
		border: 1rpx solid rgba(245, 158, 11, 0.2);
		box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.1);
	}

	.tip-icon {
		font-size: 48rpx;
		margin-bottom: 16rpx;
		opacity: 0.8;
	}

	.tip-title {
		display: block;
		font-size: 28rpx;
		color: #92400e;
		font-weight: 600;
		margin-bottom: 8rpx;
	}

	.tip-desc {
		display: block;
		font-size: 24rpx;
		color: #a16207;
		line-height: 1.4;
	}

	/* 顶部搜索区域样式 */
	.top-search-section {
		margin: 15rpx 30rpx 10rpx;

		.search-input-wrapper {
			position: relative;
			display: flex;
			align-items: center;
			background: #f8f9fa;
			border-radius: 8rpx;
			border: 1rpx solid #e9ecef;
			padding: 0 12rpx;
			height: 70rpx;
			transition: all 0.3s ease;

			&:focus-within {
				border-color: #667eea;
				background: #fff;
				box-shadow: 0 0 0 3rpx rgba(102, 126, 234, 0.1);
			}
		}

		.search-icon {
			font-size: 24rpx;
			color: #6c757d;
			margin-right: 8rpx;
			flex-shrink: 0;
		}

		.search-input {
			flex: 1;
			height: 100%;
			font-size: 26rpx;
			color: #333;
			background: transparent;
			border: none;
			outline: none;

			&::placeholder {
				color: #adb5bd;
			}
		}

		.clear-btn {
			width: 32rpx;
			height: 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			background: #dee2e6;
			border-radius: 50%;
			margin-left: 8rpx;
			flex-shrink: 0;
			transition: all 0.3s ease;

			&:active {
				background: #ced4da;
				transform: scale(0.9);
			}
		}

		.clear-icon {
			font-size: 20rpx;
			color: #6c757d;
			font-weight: bold;
		}
	}





	/* 筛选状态指示器样式 */
	.filter-indicator {
		margin: 10rpx 30rpx;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 12rpx;
		padding: 16rpx 20rpx;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
	}

	.indicator-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.indicator-text {
		color: #fff;
		font-size: 26rpx;
		font-weight: 500;
	}

	.clear-filter {
		background: rgba(255, 255, 255, 0.2);
		border-radius: 8rpx;
		padding: 8rpx 16rpx;
		transition: all 0.3s ease;

		&:active {
			background: rgba(255, 255, 255, 0.3);
			transform: scale(0.95);
		}
	}

	.clear-text {
		color: #fff;
		font-size: 22rpx;
		font-weight: 500;
	}



	/* 搜索无结果提示样式 */
	.no-search-result {
		display: flex;
		justify-content: center;
		align-items: center;
		min-height: 300rpx;
		padding: 60rpx 30rpx;
	}

	.result-content {
		text-align: center;
		background: linear-gradient(135deg, #f8f9ff 0%, #f0f2ff 100%);
		padding: 40rpx;
		border-radius: 20rpx;
		border: 1rpx solid rgba(102, 126, 234, 0.1);
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.08);
		max-width: 500rpx;
	}

	.result-icon {
		font-size: 60rpx;
		margin-bottom: 20rpx;
		opacity: 0.6;
	}

	.result-title {
		display: block;
		font-size: 32rpx;
		color: #667eea;
		font-weight: 600;
		margin-bottom: 12rpx;
	}

	.result-desc {
		display: block;
		font-size: 24rpx;
		color: #999;
		line-height: 1.5;
		margin-bottom: 30rpx;
	}

	.result-actions {
		display: flex;
		justify-content: center;
	}

	.clear-filter-btn {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: #fff;
		padding: 16rpx 32rpx;
		border-radius: 12rpx;
		font-size: 26rpx;
		font-weight: 500;
		box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
		transition: all 0.3s ease;

		&:active {
			transform: scale(0.95);
			box-shadow: 0 2rpx 8rpx rgba(102, 126, 234, 0.4);
		}
	}

	/* 会务组端二维码按钮样式 */
	.manager-qr-btn {
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
	}

	.view-manager-list-btn {
		background: linear-gradient(135deg, #848bed 0%, #8465fe 100%);
	}

	/* 会务组端二维码列表弹窗样式 */
	.manager-list-modal {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0);
		display: flex;
		align-items: center;
		justify-content: center;
		z-index: 9999;
		padding: 40rpx;
		transition: all 0.3s ease;
		opacity: 0;

		&.modal-show {
			background: rgba(0, 0, 0, 0.5);
			opacity: 1;
		}

		.manager-list-content {
			width: 100%;
			width: 700rpx;
			min-height: 400rpx;
			height: 80vh;
			max-height: 70vh;
			background: #fff;
			border-radius: 24rpx;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
			transform: scale(0.8) translateY(100rpx);
			transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
			opacity: 0;

			&.content-show {
				transform: scale(1) translateY(0);
				opacity: 1;
			}
		}

		.filter-section {
			padding: 20rpx 30rpx;
			border-bottom: 1rpx solid #f0f0f0;
			background: #fafafa;
			flex-shrink: 0; /* 防止筛选区域被压缩 */

			.filter-tabs {
				display: flex;
				background: #fff;
				border-radius: 20rpx;
				padding: 4rpx;
				box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

				.tab-item {
					flex: 1;
					padding: 12rpx 16rpx;
					border-radius: 16rpx;
					text-align: center;
					transition: all 0.3s ease;

					.tab-text {
						font-size: 26rpx;
						color: #666;
						font-weight: 500;
					}

					&.active {
						background: linear-gradient(135deg, #55aa7f 0%, #4a9b6e 100%);
						box-shadow: 0 4rpx 12rpx rgba(85, 170, 127, 0.3);

						.tab-text {
							color: #fff;
						}
					}
				}
			}
		}

		.list-scroll {
			flex: 1;
			height: 0; /* 重要：让flex子元素能正确计算高度 */
			padding: 20rpx 30rpx 30rpx;
			overflow-y: auto;

			/* 滚动条样式 */
			&::-webkit-scrollbar {
				width: 6rpx;
			}

			&::-webkit-scrollbar-track {
				background: #f1f1f1;
				border-radius: 3rpx;
			}

			&::-webkit-scrollbar-thumb {
				background: #c1c1c1;
				border-radius: 3rpx;
			}

			&::-webkit-scrollbar-thumb:hover {
				background: #a8a8a8;
			}
		}

		.manager-qr-item {
			margin-bottom: 20rpx;
			padding: 20rpx;
			background: #fafafa;
			border-radius: 12rpx;
			border: 1rpx solid #e0e0e0;
			transition: all 0.3s ease;
			width: 625rpx;
			&:last-child {
				margin-bottom: 0;
			}

			&:active {
				background: #f0f0f0;
				transform: scale(0.98);
			}

			.qr-info {
				margin-bottom: 16rpx;

				.qr-header {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 12rpx;

					.qr-id {
						font-size: 28rpx;
						font-weight: 600;
						color: #667eea;
					}

					.status-badge {
						padding: 4rpx 12rpx;
						border-radius: 12rpx;
						font-size: 20rpx;
						font-weight: 500;

						&.used {
							background: #e8f5e9;
							color: #4caf50;
						}

						&.unused {
							background: #fff3e0;
							color: #ff9800;
						}
					}
				}

				.qr-details {
					.create-time,
					.use-time {
						display: block;
						font-size: 22rpx;
						color: #666;
						margin-bottom: 4rpx;
						line-height: 1.3;
					}
				}
			}

			.qr-actions {
				display: flex;
				gap: 8rpx;

				.action-btn {
					flex: 1;
					height: 56rpx;
					font-size: 22rpx;
					border-radius: 8rpx;
					font-weight: 500;
					transition: all 0.3s ease;

					&:active {
						transform: scale(0.95);
					}

					&.view-qr-btn {
						background: #2196f3;
					}

					&.share-qr-btn {
						background: #4caf50;
					}

					&.download-qr-btn {
						background: #ff9800;
					}

					&.delete-qr-btn {
						background: #f44336;

						&.deleting {
							background: #ccc;
							opacity: 0.6;
							pointer-events: none;
						}
					}
				}
			}
		}

		.empty-state {
			padding: 60rpx 30rpx;
			text-align: center;
			background: #f8f9fa;
			border-radius: 12rpx;
			margin: 20rpx 0;

			.empty-icon {
				font-size: 60rpx;
				margin-bottom: 16rpx;
				opacity: 0.6;
			}

			.empty-text {
				font-size: 28rpx;
				color: #666;
				font-weight: 500;
				margin-bottom: 8rpx;
			}

			.empty-tip {
				font-size: 22rpx;
				color: #999;
				line-height: 1.4;
			}
		}
	}
</style>