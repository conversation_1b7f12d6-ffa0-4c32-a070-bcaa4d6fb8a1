<template>
	<view style="background: #fff;width: 100%;height: 100vh;">
		<view class="item1" >
			<!-- <image :src="menu.theme.logo" style="width: 100rpx;height: 100rpx;"></image> -->
			<text style="font-size: 34rpx;font-weight: 600;margin-top: 30rpx;">为确保正常使用请授权以下权限</text>
			<text style="font-size: 24rpx;color: #909399;margin-top: 30rpx;">将获得你的手机号</text>
		</view>
		<view class="item2">
			<button type="primary" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"><text class="cuIcon-weixin text-white padding-right"></text>一键获取手机号</button>
		</view>
	
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				modal: false,
				old_msg:[],
				data_time:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['hotel','setting'])
		},
		onLoad(option) {
			
			if(this.userInfo.phone){
				uni.showModal({
					title:'提示',
					content:`您在本酒店已经预留手机号${this.userInfo.phone},将自动注册为会员！`,
					showCancel:false,
					success:res=>{
						// 注册会员
						this.$iBox.http('memberRegister', {
							phone:this.userInfo.phone,
							shop_id:this.hotel.id?this.hotel.id:''
						})({
							method: 'post'
						}).then(res => { 
							
							this.$iBox.http('getUserInfo', {
								simple:false
							})({
								method: 'post'
							}).then(res => {
								let userInfo = res.data
								userInfo.session_key =  this.userInfo.session_key
								this.updateUserInfo(userInfo)
								uni.navigateBack({
									delta:1
								})
								uni.hideLoading()
							})
						})
					}
				})
			}
			
			
		},
		onShow() {
		
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),

			// hide_mymodal() {
			// 	this.modal = false
			// },
			handleAgreePrivacyAuthorization(){
				
			},
			// 获取手机号
			getPhoneNumber(e) {
				let that = this;
				if (e.detail.errMsg === "getPhoneNumber:ok") {
					this.$iBox.http('getUserPhone', {
						iv: e.detail.iv,
						encrypted_data: e.detail.encryptedData,
						session_key:this.userInfo.session_key
						
					})({
						method: 'post'
					}).then(res2 => {
						// 注册会员
						this.$iBox.http('registerUserPhone', {
							phone:res2.data,
							shop_id:this.hotel.id?this.hotel.id:''
						})({
							method: 'post'
						}).then(resReg => { 
							
							this.$iBox.http('getUserInfo', {
								simple:false
							})({
								method: 'post'
							}).then(resUser => {
								let userInfo = resUser.data
								userInfo.session_key =  this.userInfo.session_key
								// 再判断是否是线下会员
								if(userInfo.grade_info && userInfo.grade_info.upgrade_growth_value > -1){
									this.updateUserInfo(userInfo)
									uni.showToast({
										title: '授权手机成功！'
									})
									uni.navigateBack()
								}else {
									// 如果不是线下会员在组测会员
									// 注册会员
									this.$iBox.http('memberRegister', {
										phone: res2.data,
										shop_id: this.hotel.id ? this.hotel.id : ''
									})({
										method: 'post'
									}).then(res => {
										let params = {
											phone: res2.data
										}
										this.$iBox.http('updateUserInfo', params)({
											method: 'post'
										}).then(resUp => {
											// 更新用户信息
											this.$iBox.http('getUserInfo', {
												simple: false
											})({
												method: 'post'
											}).then(res1 => {
												let userInfo = res1.data
												userInfo.session_key = this.userInfo.session_key
												this.updateUserInfo(userInfo)
												uni.showToast({
													title: '注册会员成功！'
												})
												uni.navigateBack()
											})
										}).catch(function(error) {
											console.log('网络错误', error)
										})
									})
									
								}
								
							})
						})
						
						
					})
				}
			},
		},
		
	}
</script>

<style lang="scss" scoped>
	page {
		height: 100vh;
		background: #fff;
	}

	.item1 {
		width: 100%;
		height: 500rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding-top: 30rpx;
		/* background: #007AFF; */
	}

	.item1 image {
		width: 500rpx;
		height: 500rpx;
		/* border-radius: 50%; */
		background: #FFFFFF;
	}

	.item2 {
		width: 80%;
		margin: 40rpx auto;
		/* margin-top: 60rpx; */
		/* background: #007AFF; */
	}

	.item2 button {
		width: 500rpx;
		border-radius: 50rpx;
	}

	/* 老客户绑定提示框 */
	.old_custom {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		height: 600rpx;
		width: 560rpx;
		margin: auto;
		background: #FFFFFF;
		border-radius: 20rpx;
		z-index: 9999997;
	}

	.my_modal_mask {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: rgba(0, 0, 0, 0.6);
		z-index: 9999996;
		/* transition: all 0.3s ease-in-out;
		opacity: 0;
		visibility: hidden; */
	}
</style>
