<template>
	<view>
		<m-popup :show="poprc" :closeable="false">
			<view class="chooseBox">
				<view class="btnBox">
					<!-- <text @click="closePoprc">取消</text> -->
					<text>已选择房间 {{chooseRoom.length}}</text>
					<text :style="{color:themeColor.com_color1}" @click="sure">确认</text>
				</view>
				<view class="" style="display: flex;padding: 20rpx;align-items: center;height: 120rpx;">
					<p><text style="font-size: 30rpx;">房型:</text></p>
					<picker @change="bindChangeType" :value="changeTypeIndex" range-key="name" :range="roomTypeList">
						<view class="pickerBox">
							{{roomTypeList[changeTypeIndex].name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>
				<view class="roomBox" style="min-height: 200rpx;width: 100%;">
					<view class="roomContent" :style="{background:item.room_status_color}" v-for="(item, index) in chooseRoom" :key="index" >
						{{item.room_number}}({{item.room_status_name}}-{{item.clear_status_name}})
						<image src="../../static/images/close.png" class="close" mode="" @click="deleteRoom(item)">
						</image>
					</view>
				</view>
				<scroll-view scroll-y="true" style="height: 60vh;">
					<view class="roomBoxContent" v-for="(item, index) in room_list" :key="index" >
						<p class="title" :style="{color:themeColor.main_color}">{{item.building}}({{item.room_count}})
						</p>
						<view class="roomBoxContentBox" v-for="(item1, index1) in item.floor_list" :key="index1">
							<m-divider>{{item1.floor}}({{item1.room_count}})</m-divider>
							<view class="statusBox">
								<view class="room" :style="{background:item2.room_status_color}"
									v-for="item2 in item1.room_list" :key="item2.id" @click="roomStatusGet(item2)">
									<p
										style="overflow: hidden;white-space: nowrap;text-overflow: ellipsis;font-size: 24rpx;">
										{{item2.room_number}}({{item2.room_status_name}}-{{item2.clear_status_name}})
									</p>
								</view>

							</view>
						</view>
					</view>
					<view class="" style="height: 60rpx;">
						
					</view>
				</scroll-view>

			</view>
		</m-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				chooseId: [],
				chooseRoom: [],
				room_list: [],
				roomTypeList:[{id:'',name:'全部'}],
				changeTypeIndex:0,
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		props: {
			roomList: {
				type: Array,
				default: []
			},
			poprc: {
				type: Boolean,
				default: false
			},
			ids: {
				type: Array,
				default: []
			},
			rooms: {
				type: Array,
				default: []
			},
			num: {
				type: Object
			}
		},
		watch: {
			poprc: {
				handler(oldValue, newValue) {
					this.chooseId = []
					this.chooseRoom = []
					console.log(this.poprc, 'this.poprc', this.chooseId, this.ids);
				},
				immediate: true,
				deep: true
			},
			roomList: {
				handler(oldValue, newValue) {
					console.log(this.roomList, 'changeList');
					this.room_list = JSON.parse(JSON.stringify(this.roomList))
				},
				immediate: true
			}
		},
		mounted() {
			this.$iBox
				.http('bossGetRoomType', {})({
					method: 'post'
				})
				.then(res => {
			
					this.roomTypeList = [...this.roomTypeList, ...res.data]
				})

		},

		methods: {
			// closePoprc() {
			// 	this.$emit('closeZj', '')
			// 	this.chooseId = []
			// 	this.chooseRoom = []
			// }, 
			roomStatusGet(e) {
				this.chooseId.push(e.id)
				this.chooseRoom.push(e)
				let a = {
					ids: this.chooseId,
					rooms: this.chooseRoom
				}
				
				this.$emit('sureRc', a)
				this.$emit('closeZj', '')
				
			},
			bindChangeType(e){
				
				this.changeTypeIndex = e.detail.value[0]
				this.room_list = JSON.parse(JSON.stringify(this.roomList )) 
				if(e.detail.value[0] != 0){
					this.room_list.forEach(item=>{
						item.floor_list.forEach(item1=>{
							item1.room_list = item1.room_list.filter(item2=>{
								return item2.room_type_id == this.roomTypeList[this.changeTypeIndex].id
							})
						})
					})
				}else {
					this.room_list = JSON.parse(JSON.stringify(this.roomList )) 
				}
				
				
				
			},
		
			sure() {
				this.$emit('closeZj', '')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.chooseBox {
		height: 80vh;
		width: 100%;
		position: relative;

		.btnBox {
			height: 80rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
		}
		
		.pickerBox {
			margin-top: 20rpx;
			position: relative;
			height: 60rpx;
			width: 280rpx;
			border-radius: 14rpx;
			border: 1px solid #eee;
			display: flex;
			padding: 0 20rpx;
			font-size: 30rpx;
			align-items: center;
		
			.arrow {
				animation-name: to_bottom_show;
				animation-duration: 0.2s;
				animation-timing-function: linear;
				/* animation-delay: 1s; */
				/* animation-iteration-count: infinite; */
				animation-direction: normal;
				animation-play-state: running;
				animation-fill-mode: forwards;
			}
		
			.arrow_ac {
				animation-name: to_up_show;
				animation-duration: 0.2s;
				animation-timing-function: linear;
				/* animation-delay: 1s; */
				/* animation-iteration-count: infinite; */
				animation-direction: normal;
				animation-play-state: running;
				animation-fill-mode: forwards;
			}
		
			/* 箭头动画 */
		
			@keyframes to_up_show {
				0% {
					transform: rotate(0);
				}
		
				50% {
					transform: rotate(90deg);
				}
		
				100% {
					transform: rotate(180deg);
				}
			}
		
			@keyframes to_bottom_show {
				0% {
					transform: rotate(180deg);
					animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
				}
		
				50% {
					transform: rotate(90deg);
					animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
				}
		
				100% {
					transform: rotate(0deg);
				}
			}
		}

		.roomBox {
			padding: 10rpx;
			position: relative;
			display: flex;
			flex-wrap: wrap;

			.roomContent {
				width: fit-content;
				border-radius: 4rpx;
				padding: 6rpx;
				color: #fff;
				position: relative;
				margin-right: 20rpx;
				font-size: 22rpx;
				height: 40rpx;
			}

			.close {
				position: absolute;
				top: -16rpx;
				right: -16rpx;
				font-size: 30rpx;
				color: #ff0000;
				width: 32rpx;
				height: 32rpx;
			}

		}


		.roomBoxContent {
			width: 100%;
			padding: 20rpx;

			.title {
				// padding: 30rpx 0;
				font-size: 36rpx;
				font-weight: 600;
			}

			display: flex;
			flex-direction: column;

			.roomBoxContentBox {
				width: 100%;

				.statusBox {
					width: 100%;
					display: flex;
					flex-wrap: wrap;
					align-items: center;


					.room {
						width: fit-content;
						border-radius: 4rpx;
						padding: 12rpx;
						color: #fff;
						position: relative;
						margin-right: 12rpx;
						margin-top: 16rpx;

						.choosed {
							position: absolute;
							width: 100%;
							height: 100%;
							background: rgba(36, 42, 47, 0.8);
							color: #ffffff;
							border: 2px solid #ff0000;
							font-size: 24rpx;
							font-weight: 600;
							bottom: 0;
							top: 0;
							right: 0;
							left: 0;
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}

				}
			}
		}
	}
</style>
