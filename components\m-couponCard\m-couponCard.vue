<template>
	<view>
		<view class="couponBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="title">
				<text>使用优惠</text>
	
			</view>
			<view class="room" @click="chooseCoup">
				<text style="padding-right: 80rpx;">优惠券</text>
				<view class="" style="padding: 0;display: flex;align-items: center;">
					<text style="color: red;font-size: 28rpx;">{{choosePrice==0?(coupNum>0?coupNum +'张优惠券可用':'无可用优惠券'):'已优惠'+choosePrice+'元'}}</text>
					<view class="icon-jiantou" :style="{color:themeColor.text_title_color}">
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-couponCard",
		data() {
			return {
				coupons:[],
				coupNum:0,
				limit_num:0,
			};
		},
		props:{
			coupType:{
				type:Number
			},
			limitNum: {
				type:Number
			},
			choosePrice: {
				type:Number
			},
			limitTime:{
				type:Number
			}
		},
		watch:{
			
			choosePrice:{
				handler(oldData,newData){
					console.log(this.choosePrice,'kkkkk');
					this.choosePrice = this.choosePrice
				},
				immediate:true
			},
			limitNum:{
				handler(oldData,newData){
					console.log(this.limitNum,'kkkkkwwww');
				
					this.$iBox.http('getUserCoupon', {
						type_id: this.coupType,
						use_status:0
					})({
						method: 'post'
					}).then(res => {
						let a = 0
						res.data.forEach(item=>{
							if((item.coupon_info.use_condition <= this.limitNum)&&(item.limit_time>this.$moment().unix())){
								a += 1
							}
						})
						this.coupNum = a
						console.log('this.limitNum',a);
					})
				},
				immediate:true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		mounted() {
			
		},
		
		methods:{
			chooseCoup(){
				this.$emit('chooseCoup','1',)
			}
		}
	}
</script>

<style lang="scss">
	.couponBox {
		width: 720rpx;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;
		padding:0rpx 30rpx;
		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-top-right-radius: 20rpx;
			border-top-left-radius: 20rpx;
			padding:30rpx 0;
			border-bottom: 1px solid #e4e7ed;
		}
		.room {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid #e4e7ed;

		}
	}
</style>
