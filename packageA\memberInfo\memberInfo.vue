<template>
	<view>
		<view class="avator_box">
			<image :src="memberImg" style="width: 600rpx;height: 300rpx;" mode=""></image>
		</view>

		<view class="InfoBox">

			<view class="nameBox">
				<view class="" style="width: 170rpx;">
					<text style="color: brown;">*</text>
					<text style="">手机号:</text>
				</view>
				<view class="" style="width: 360rpx;">
					<input type="number" placeholder="用于预订房间(必填)" v-model="phone" />
				</view>
				<button size="mini" style="width: 150rpx;padding: 0rpx;" open-type="getPhoneNumber" @getphonenumber="getPhoneNumber"
					:style="{background:themeColor.com_color1,color:themeColor.bg_color}">一键获取</button>
			</view>
			<view class="nameBox">
				<text style="color: red;">*</text>
				<text style="width: 170rpx;">姓名:</text>
				<view class="" style="width: 400rpx;">
					<input type="text" placeholder="真实姓名(必填)" v-model="name" />
				</view>
			</view>
			<view class="nameBox">

				<text style="width: 170rpx;">身份证:</text>
				<view class="" style="width: 400rpx;">
					<input type="idcard" placeholder="用于自助入住(非必填)" v-model="idcard" />
				</view>
			</view>
			<view class="nameBox">
				<text style="width: 170rpx;">生日:</text>
				<view class="" style="width: 400rpx;">
					<picker mode="date" :value="date" @change="bindDateChange">
						<view class="uni-input">{{date}}</view>
					</picker>
				</view>
			</view>
			<view class="nameBox">
				<text style="width: 170rpx;">性别:</text>
				<view class="" style="width: 400rpx;">
					<picker mode="selector" :value="sex" :range="list" @change="sexChange">
						<view class="uni-input">{{sex}}</view>
					</picker>
				</view>
			</view>
			<view class="check_contant">
				<checkbox-group @change="checkboxChange">
					<label class="radio" style="font-size: 22rpx;">
						<checkbox :value="cb" :checked="false" color="#0031d0" style="transform:scale(0.7)" />
						<text>购买即视为同意</text><text style="text-decoration:underline;"
							@click="agreement">{{hotel.shop_name}}会员用户协议</text>
					</label>
				</checkbox-group>
			</view>

			<view class="" style="width: 500rpx;margin: 80rpx auto;" >
				<view class="btn_register"
					:style="{background:themeColor.com_color1,color:themeColor.bg_color}" @click="register">
					成为会员(享受多种折扣)</view>
					
					<view class="btn_register" v-if="!userInfo.phone"
						style="background: #909399;color:#fff;border: 1px solid #ccc;"
						@click="registerPhone">暂不注册(按照散客价格订房)</view>
			</view>

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				phone: '',
				name: '',
				idcard: '',
				date: '',
				sex: '保密',
				list: ['保密', '男', '女'],
				memberImg: '',
				cb: '0',
				if_check: true
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		async onLoad() {
			await this.$onLaunched;
			
			this.$iBox.http('getMemberGrade', {})({
				method: 'post'
			}).then(res => {
				this.memberImg = res.data[0].card_image
			})
			
			this.phone = this.userInfo.phone?this.userInfo.phone:''
			this.name = this.userInfo.name?this.userInfo.name:''
			this.idcard = this.userInfo.nameidentification_number?this.userInfo.nameidentification_number:''
			this.date = this.userInfo.birthday?this.userInfo.birthday:this.$moment().format('YYYY-MM-DD')
			this.sex = this.userInfo.gender == 0 ? '保密' : (this.userInfo.gender == 1 ? '男' : '女')
			
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			checkboxChange(e) {
				console.log(e, 'll');
				if (e.detail.value.length == 0) {
					this.if_check = false
				} else {
					this.if_check = true
				}
			},
			bindDateChange(e) {
				this.date = e.detail.value
			},
			sexChange(e) {
				this.sex = e.detail.value == 0 ? '保密' : (e.detail.value == 1 ? '男' : '女')
			},
			getPhoneNumber(e) {
				let that = this;
				console.log(e)
				if (e.detail.errMsg === "getPhoneNumber:ok") {
					this.$iBox.http('getUserPhone', {
						iv: e.detail.iv,
						encrypted_data: e.detail.encryptedData,
						session_key: this.userInfo.session_key

					})({
						method: 'post'
					}).then(res2 => {
						// 更新用户信息
						this.phone = res2.data
					})
				}
			},
			agreement() {
				uni.navigateTo({
					url: '/pages/agreement/agreement'
				})
			},
			registerPhone() {
				if (!this.phone) {
					uni.showToast({
						icon: 'none',
						title: '为了保障您得会员服务，请授权手机号！'
					})
					return;
				}
				
				if (!this.name) {
					uni.showToast({
						icon: 'none',
						title: '为了保障您得会员服务，请填写您的姓名！'
					})
					return;
				}
				
				if (!this.if_check) {
					uni.showToast({
						icon: 'none',
						title: '请勾选用户协议！'
					})
					return;
				}

				this.$iBox.http('registerUserPhone', {
					phone: this.phone,
					shop_id: this.hotel.id ? this.hotel.id : '',
					birthday:this.date,
					gender:this.sex == '保密' ? 0 : (this.sex == '男' ? 1 : 2),
					identification_number: this.idcard,
					identification_type: 1,
					name: this.name,
				})({
					method: 'post'
				}).then(res => {
					uni.navigateBack()
				})
			},
			register() {
				if (!this.phone) {
					uni.showToast({
						icon: 'none',
						title: '为了保障您得会员服务，请授权手机号！'
					})
					return;
				}
				if (!this.if_check) {
					uni.showToast({
						icon: 'none',
						title: '请勾选用户协议！'
					})
					return;
				}
				
				if(this.userInfo.phone){
					// 注册会员
					this.$iBox.http('memberRegister', {
						phone: this.phone,
						shop_id: this.hotel.id ? this.hotel.id : '',
						birthday:this.date,
						gender:this.sex == '保密' ? 0 : (this.sex == '男' ? 1 : 2),
						identification_number: this.idcard,
						identification_type: 1,
						name: this.name,
					})({
						method: 'post'
					}).then(res => {
					
						let params = {
							gender: this.sex == '保密' ? 0 : (this.sex == '男' ? 1 : 2),
							identification_number: this.idcard,
							identification_type: 1,
							name: this.name,
							phone: this.phone
						}
					
						this.$iBox.http('updateUserInfo', params)({
							method: 'post'
						}).then(res => {
							// 每次进入个人中心要更新用户信息
							this.$iBox.http('getUserInfo', {
								simple: false
							})({
								method: 'post'
							}).then(res1 => {
								let userInfo = res1.data
								userInfo.session_key = this.userInfo.session_key
								this.updateUserInfo(userInfo)
								uni.showToast({
									title: '注册会员成功！'
								})
								uni.switchTab({
									url: '/pages/myCenter/myCenter'
								})
							})
						}).catch(function(error) {
							console.log('网络错误', error)
						})
					
					
					})
				}else {
					// 注册手机号
					this.$iBox.http('registerUserPhone', {
						phone: this.phone,
						shop_id: this.hotel.id ? this.hotel.id : ''
					})({
						method: 'post'
					}).then(res => {
						
						this.$iBox.http('getUserInfo', {
							simple:false
						})({
							method: 'post'
						}).then(resUser => { 
							let userInfo = resUser.data
							userInfo.session_key =  this.userInfo.session_key
							// 再判断是否是线下会员
							if(userInfo.grade_info && userInfo.grade_info.upgrade_growth_value > -1){
								this.updateUserInfo(userInfo)
								uni.showToast({
									title: '注册会员成功！'
								})
								uni.switchTab({
									url: '/pages/myCenter/myCenter'
								})
							}else {  
								// 注册会员
								this.$iBox.http('memberRegister', {
									phone: this.phone,
									shop_id: this.hotel.id ? this.hotel.id : '',
									birthday:this.date,
									gender:this.sex == '保密' ? 0 : (this.sex == '男' ? 1 : 2),
									identification_number: this.idcard,
									identification_type: 1,
									name: this.name,
								})({
									method: 'post'
								}).then(res => {
								
									let params = {
										gender: this.sex == '保密' ? 0 : (this.sex == '男' ? 1 : 2),
										identification_number: this.idcard,
										identification_type: 1,
										name: this.name,
										phone: this.phone
									}
								
									this.$iBox.http('updateUserInfo', params)({
										method: 'post'
									}).then(res => {
										// 每次进入个人中心要更新用户信息
										this.$iBox.http('getUserInfo', {
											simple: false
										})({
											method: 'post'
										}).then(res1 => {
											let userInfo = res1.data
											userInfo.session_key = this.userInfo.session_key
											this.updateUserInfo(userInfo)
											uni.showToast({
												title: '注册会员成功！'
											})
											uni.switchTab({
												url: '/pages/myCenter/myCenter'
											})
										})
									}).catch(function(error) {
										console.log('网络错误', error)
									})
								
								})
								
							}
						})
						
					})
				}
				
			}
		},
		onShareAppMessage: function(res) {},
	}
</script>

<style lang="scss" scoped>
	.avator_box {
		display: flex;
		padding: 20rpx;
		align-items: center;
		height: 360rpx;
		width: 100%;
		justify-content: center;
		flex-direction: column;
		position: relative;
	}

	.InfoBox {
		// padding: 40rpx;

		.nameBox {
			padding: 30rpx;
			display: flex;
			align-items: center;
			border-bottom: 1px solid #e4e7ed;
			// justify-content: space-between;
		}

		.check_contant {
			padding: 30rpx;
		}

		.btn_register {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 500rpx;
			height: 90rpx;
			border-radius: 20rpx;
			margin-top: 20rpx;
		}
	}
</style>
