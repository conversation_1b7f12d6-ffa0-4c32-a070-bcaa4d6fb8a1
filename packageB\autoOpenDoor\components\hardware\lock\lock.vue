<template>
	<view class="lockBox">
		<view class="">
			<ttLock v-if="ifTT()"  :billDetail="bill_detail" :styleModel="styleMode"></ttLock>
			<yyLock v-if="ifYY()" :billDetail="bill_detail"   :styleModel="styleMode"></yyLock>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	import ttLock from './ttLock.vue'
	import yyLock from './yyLock.vue'
	export default {
		data() {
			return {
				
			};
		},
		props:{
			styleMode:{
				type:[Number,String]
			},
			bill_detail: {
				type: Object
			},
			
		},
		components: {
			ttLock,
			yyLock
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('room', ['hardWareList'])
		},
		mounted() {
			console.log(this.hardWareList,'kll');
		},
		methods:{
			openLock(){
				uni.showToast({
					title:'本房间暂未配置智能门锁!'
				})
			},
			ifTT(){
				let a = ''
				a = this.hardWareList.lock_list.filter(item=>{
					return item.lock_type_sign == 'tongtong'
				})
				
				return a&&a.length>0
			},
			ifYY(){
				console.log('ddj');
				let a = ''
				a = this.hardWareList.lock_list.filter(item=>{
					return item.lock_type_sign == 'yaya'
				})
				console.log(a,'kl3');
				return a&&a.length>0
			},
		}
		
	}
</script>

<style lang="scss" scoped>
	.lockBox {
		display: flex;
		align-items: center;
		justify-content: center;
		flex-wrap: wrap;
		width: 100%;
	}
	
	.item1 {
		height: 180rpx;
		width: 330rpx;
		margin-top: 30rpx;
		border-radius: 20rpx;
		box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
	
		&_title {
			padding: 20rpx;
			font-size: 28rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
	
			.open {
				width: fit-content;
				padding: 10rpx 16rpx;
				border-radius: 30rpx;
				color: #ffffff;
				// background-color: rgba(0, 0, 0, 0.6);
				font-size: 22rpx;
			}
		}
	
		&_content {
			padding: 0rpx 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
	
			.img_lock {
				height: 80rpx;
				width: 80rpx;
			}
	
			&_text {
				display: flex;
				flex-direction: column;
			}
		}
	}
</style>
