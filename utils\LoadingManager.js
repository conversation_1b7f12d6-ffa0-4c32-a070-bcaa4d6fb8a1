/**
 * 加载状态管理器
 * 统一管理应用中的加载状态，避免loading重复显示和隐藏问题
 */
class LoadingManager {
	constructor() {
		this.loadingCount = 0 // 当前loading计数
		this.loadingTimer = null // loading延迟显示定时器
		this.isLoadingVisible = false // loading是否可见
		this.defaultDelay = 300 // 默认延迟显示时间(ms)
		this.minShowTime = 500 // 最小显示时间(ms)
		this.loadingStartTime = 0 // loading开始显示时间
	}

	/**
	 * 显示loading
	 * @param {Object} options - loading配置选项
	 * @param {string} options.title - loading标题
	 * @param {boolean} options.mask - 是否显示遮罩
	 * @param {number} options.delay - 延迟显示时间
	 */
	show(options = {}) {
		this.loadingCount++
		
		const config = {
			title: '加载中...',
			mask: true,
			delay: this.defaultDelay,
			...options
		}

		// 如果已经在显示，直接返回
		if (this.isLoadingVisible) {
			return
		}

		// 清除之前的定时器
		if (this.loadingTimer) {
			clearTimeout(this.loadingTimer)
		}

		// 延迟显示loading，避免闪烁
		this.loadingTimer = setTimeout(() => {
			if (this.loadingCount > 0) {
				uni.showLoading({
					title: config.title,
					mask: config.mask
				})
				this.isLoadingVisible = true
				this.loadingStartTime = Date.now()
				console.log('Loading显示:', config.title)
			}
		}, config.delay)
	}

	/**
	 * 隐藏loading
	 * @param {boolean} force - 是否强制隐藏
	 */
	hide(force = false) {
		if (force) {
			this.loadingCount = 0
		} else {
			this.loadingCount = Math.max(0, this.loadingCount - 1)
		}

		// 清除延迟显示定时器
		if (this.loadingTimer) {
			clearTimeout(this.loadingTimer)
			this.loadingTimer = null
		}

		// 如果还有loading任务，不隐藏
		if (this.loadingCount > 0 && !force) {
			return
		}

		// 确保最小显示时间
		if (this.isLoadingVisible) {
			const showTime = Date.now() - this.loadingStartTime
			const remainTime = Math.max(0, this.minShowTime - showTime)

			setTimeout(() => {
				uni.hideLoading()
				this.isLoadingVisible = false
				console.log('Loading隐藏')
			}, remainTime)
		}
	}

	/**
	 * 重置loading状态
	 */
	reset() {
		this.loadingCount = 0
		if (this.loadingTimer) {
			clearTimeout(this.loadingTimer)
			this.loadingTimer = null
		}
		if (this.isLoadingVisible) {
			uni.hideLoading()
			this.isLoadingVisible = false
		}
	}

	/**
	 * 获取当前loading状态
	 */
	getStatus() {
		return {
			count: this.loadingCount,
			visible: this.isLoadingVisible
		}
	}

	/**
	 * 包装异步函数，自动管理loading状态
	 * @param {Function} asyncFn - 异步函数
	 * @param {Object} loadingOptions - loading配置
	 */
	wrap(asyncFn, loadingOptions = {}) {
		return async (...args) => {
			this.show(loadingOptions)
			try {
				const result = await asyncFn(...args)
				return result
			} finally {
				this.hide()
			}
		}
	}

	/**
	 * 批量执行异步任务，统一管理loading
	 * @param {Array} tasks - 任务数组
	 * @param {Object} loadingOptions - loading配置
	 */
	async batchExecute(tasks, loadingOptions = {}) {
		this.show(loadingOptions)
		try {
			const results = await Promise.allSettled(tasks)
			return results
		} finally {
			this.hide()
		}
	}
}

// 创建全局实例
const loadingManager = new LoadingManager()

// 导出实例和类
export default loadingManager
export { LoadingManager }
