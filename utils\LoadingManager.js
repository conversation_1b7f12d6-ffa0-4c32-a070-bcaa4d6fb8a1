/**
 * 加载状态管理器
 * 统一管理应用中的加载状态，支持精致的初始化动画
 */
class LoadingManager {
	constructor() {
		this.loadingCount = 0 // 当前loading计数
		this.loadingTimer = null // loading延迟显示定时器
		this.isLoadingVisible = false // loading是否可见
		this.defaultDelay = 300 // 默认延迟显示时间(ms)
		this.minShowTime = 500 // 最小显示时间(ms)
		this.loadingStartTime = 0 // loading开始显示时间

		// 初始化动画相关
		this.initAnimationVisible = false
		this.initProgress = 0
		this.initCallbacks = []
	}

	/**
	 * 显示初始化动画
	 * @param {Object} options - 动画配置选项
	 */
	showInitAnimation(options = {}) {
		this.initAnimationVisible = true
		this.initProgress = 0
		console.log('🎬 显示初始化动画')

		// 触发回调
		this.initCallbacks.forEach(callback => {
			if (typeof callback.onShow === 'function') {
				callback.onShow()
			}
		})

		return this
	}

	/**
	 * 更新初始化进度
	 * @param {number} progress - 进度值 (0-100)
	 * @param {string} message - 进度消息
	 */
	updateInitProgress(progress, message = '') {
		this.initProgress = Math.min(100, Math.max(0, progress))
		console.log(`📊 初始化进度: ${this.initProgress}%`, message)

		// 触发进度更新回调
		this.initCallbacks.forEach(callback => {
			if (typeof callback.onProgress === 'function') {
				callback.onProgress(this.initProgress, message)
			}
		})

		return this
	}

	/**
	 * 隐藏初始化动画
	 */
	hideInitAnimation() {
		// 确保进度达到100%
		this.updateInitProgress(100, '初始化完成')

		// 延迟隐藏，让用户看到完成状态
		setTimeout(() => {
			this.initAnimationVisible = false
			console.log('🎬 隐藏初始化动画')

			// 触发隐藏回调
			this.initCallbacks.forEach(callback => {
				if (typeof callback.onHide === 'function') {
					callback.onHide()
				}
			})
		}, 500)

		return this
	}

	/**
	 * 注册初始化动画回调
	 * @param {Object} callbacks - 回调函数对象
	 */
	onInitAnimation(callbacks) {
		this.initCallbacks.push(callbacks)
		return this
	}

	/**
	 * 显示简单loading（用于非初始化场景）
	 * @param {Object} options - loading配置选项
	 */
	show(options = {}) {
		// 如果正在显示初始化动画，不显示普通loading
		if (this.initAnimationVisible) {
			return this
		}

		this.loadingCount++

		const config = {
			title: '加载中...',
			mask: true,
			delay: this.defaultDelay,
			...options
		}

		// 如果已经在显示，直接返回
		if (this.isLoadingVisible) {
			return this
		}

		// 清除之前的定时器
		if (this.loadingTimer) {
			clearTimeout(this.loadingTimer)
		}

		// 延迟显示loading，避免闪烁
		this.loadingTimer = setTimeout(() => {
			if (this.loadingCount > 0 && !this.initAnimationVisible) {
				uni.showLoading({
					title: config.title,
					mask: config.mask
				})
				this.isLoadingVisible = true
				this.loadingStartTime = Date.now()
				console.log('Loading显示:', config.title)
			}
		}, config.delay)

		return this
	}

	/**
	 * 隐藏loading
	 * @param {boolean} force - 是否强制隐藏
	 */
	hide(force = false) {
		if (force) {
			this.loadingCount = 0
		} else {
			this.loadingCount = Math.max(0, this.loadingCount - 1)
		}

		// 清除延迟显示定时器
		if (this.loadingTimer) {
			clearTimeout(this.loadingTimer)
			this.loadingTimer = null
		}

		// 如果还有loading任务，不隐藏
		if (this.loadingCount > 0 && !force) {
			return
		}

		// 确保最小显示时间
		if (this.isLoadingVisible) {
			const showTime = Date.now() - this.loadingStartTime
			const remainTime = Math.max(0, this.minShowTime - showTime)

			setTimeout(() => {
				uni.hideLoading()
				this.isLoadingVisible = false
				console.log('Loading隐藏')
			}, remainTime)
		}
	}

	/**
	 * 重置loading状态
	 */
	reset() {
		this.loadingCount = 0
		if (this.loadingTimer) {
			clearTimeout(this.loadingTimer)
			this.loadingTimer = null
		}
		if (this.isLoadingVisible) {
			uni.hideLoading()
			this.isLoadingVisible = false
		}
	}

	/**
	 * 获取当前loading状态
	 */
	getStatus() {
		return {
			count: this.loadingCount,
			visible: this.isLoadingVisible,
			initAnimationVisible: this.initAnimationVisible,
			initProgress: this.initProgress
		}
	}

	/**
	 * 获取初始化动画状态
	 */
	getInitStatus() {
		return {
			visible: this.initAnimationVisible,
			progress: this.initProgress
		}
	}

	/**
	 * 包装异步函数，自动管理loading状态
	 * @param {Function} asyncFn - 异步函数
	 * @param {Object} loadingOptions - loading配置
	 */
	wrap(asyncFn, loadingOptions = {}) {
		return async (...args) => {
			this.show(loadingOptions)
			try {
				const result = await asyncFn(...args)
				return result
			} finally {
				this.hide()
			}
		}
	}

	/**
	 * 批量执行异步任务，统一管理loading
	 * @param {Array} tasks - 任务数组
	 * @param {Object} loadingOptions - loading配置
	 */
	async batchExecute(tasks, loadingOptions = {}) {
		this.show(loadingOptions)
		try {
			const results = await Promise.allSettled(tasks)
			return results
		} finally {
			this.hide()
		}
	}
}

// 创建全局实例
const loadingManager = new LoadingManager()

// 导出实例和类
export default loadingManager
export { LoadingManager }
