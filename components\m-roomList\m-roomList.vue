<template>
	<view style="position: relative;" v-if="show">
		<m-roomType @chooseType="getType"></m-roomType>
		<m-dateCard :show="true" :mode="4"></m-dateCard>
		<!-- 模式一 -->
		<view class="m-roomList sk" v-if="mode==1&&!skShow" :style="{background:themeColor.bg_color}">
			<view class="m-roomList_box" v-for="item in room_list" :style="{color:themeColor.text_main_color}"
				:key="item.price_id">
				<view class="m-roomList_box_header sk-radius">
					<view class="m-roomList_box_header_item" @click="showPop1(item)">
						<image class="m-roomList_box_header_item_img" :src="item.image_list[0]" mode="aspectFill">
						</image>
					</view>
					<view class="m-roomList_box_header_item1" @click="showPop1(item)">
						<view class="m-roomList_box_header_item1_title">
							<text>{{item.name}}</text>
						</view>
						<view class="m-roomList_box_header_item1_sec" style="margin-top: 16rpx;">
							<text v-for="item2 in item.specialty">{{item2+' ' }}</text>
						</view>
						<!-- <view class="m-roomList_box_header_item1_tags" @click="showPop(item.priceList[0])">
							<m-tags mode="plain" :name="item1.name" v-for="item1 in item.favourable" :key="item.id"
								:type="item1.isHot?'hot':'primary'"></m-tags>
						</view> -->
						<view class="m-roomList_box_header_item1_sec" v-if="unit!='standard'">
							<text>入住类型:{{item.room_sale_type_name}}</text>
						</view>
						<view class="m-roomList_box_header_item1_sec" v-if="unit=='hour'||unit=='conference_room'">
							<text>入住时间段:{{item.start_time_limit +'-'+ item.end_time_limit}}</text>
						</view>

					</view>
					<view class="m-roomList_box_header_item2" >
						<text :style="{color:themeColor.text_title_color}" v-if="item.peak_price > item.lowest_price"
							style="font-size: 26rpx;text-decoration:line-through">￥{{item.peak_price}}</text>
						<view class="m-roomList_box_header_item2_price" style="display: flex;align-items: center;">
							<text style="font-size: 30rpx;font-weight: 500;color: #FF2B2B;">￥</text>
							<text class="m-roomList_box_header_item2_price_price">{{item.lowest_price}}</text>
							<text class="m-roomList_box_header_item2_price_price1"
								:style="{color:themeColor.text_title_color}">起</text>
						</view>
						<view class="m-roomList_box_header_item2_box" @click="lookPrice(item)"
							:style="!(priceIndex.includes(item.price_id))&&item.usable_count>0?'background:'+themeColor.main_color+';color:'+themeColor.bg_color:'background:'+themeColor.bg1_color+';color:'+themeColor.text_second_color">
							<text>{{item.usable_count==0?'已满房':(priceIndex.includes(item.price_id)?'收起价格':'查看价格')}}</text>
							<text class="icon-down" :class="priceIndex.includes(item.price_id)?'arrow_ac':'arrow'"
								style="font-size: 34rpx;"></text>
						</view>
					</view>
				</view>
				<!-- 收起的部分 -->
				<view class="m-roomList_box_content" v-if="priceIndex.includes(item.price_id)&&item.usable_count>0"
					:style="{background:themeColor.bg_color}"
					v-for="item1 in item.services" :key="item1.id">
					<view class="m-roomList_box_content_name">
						<text style="font-size: 36rpx;">{{item1.service_name}}</text>
						<view class="" style="font-size: 24rpx;width: fit-content;" @click="showPop(item)">
							<text :style="{color:themeColor.text_second_color}">{{item1.cancelable?'可取消':'不可取消'}}</text>
							<text class="icon-jinggao"></text>
						</view>

						<view class="m-roomList_box_content_name_fa">
							<m-tags mode="plain" :name="item2.coupon_name+'x'+item2.count"
								v-for="item2 in item1.breakfast_coupon" :key="item2.id"
								:type="item2.isHot?'hot':'primary'"></m-tags>
						</view>
					</view>

					<view class="m-roomList_box_content_price" v-if="item.usable_count > 0">
						<text :style="{color:themeColor.text_title_color}" v-if="item.peak_price > item.price"
							style="font-size: 26rpx;text-decoration:line-through">￥{{item.peak_price}}</text>
						<view class="m-roomList_box_content_price_main">
							<text style="font-size: 30rpx;font-weight: 500;color: #FF2B2B;">￥</text>
							<text class="m-roomList_box_content_price_main_price">{{item1.price}}</text>
						</view>
					</view>

					<view class="m-roomList_box_content_end">
						<view class="m-roomList_box_content_end_box" @click="toForm({'item':item,'service':item1})">
							<view class="m-roomList_box_content_end_box_t1"
								:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.com_color1+','+themeColor.com_color2+')',color:themeColor.bg_color}">
								<text>预定</text>
							</view>
							<view class="m-roomList_box_content_end_box_t2"
								:style="{background:themeColor.main_color,color:themeColor.bg_color}">
								<text style="font-weight: 300;font-size: 26rpx;">在线订</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 优惠说明 -->
			<m-popup :show="pop1" @closePop="closePop">
				<view class="couponStyle" style="height: 600rpx;"
					:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
					<view class="couponStyle_title">
						<text>取消规则</text>
					</view>
					<view class="couponStyle_cancel" v-if="if_cancel">
						<text>每天预定{{cancelTime}}之前可免费取消</text>
					</view>
					<view class="couponStyle_cancel" v-else>
						<text>本套餐不可取消</text>
					</view>
					<!-- <view class="couponStyle_priceList">
						<view class="couponStyle_priceList_item">
							<text>原价</text>
							<text style="font-weight: 600;color: #e42d15;">￥{{hxPrice}}</text>
						</view>
						<view class="couponStyle_priceList_item">
							<text>活动</text>
							<text style="font-weight: 600;color: #e42d15;">￥{{servicePrice-hxPrice}}</text>
						</view>
						<view class="couponStyle_priceList_item1">
							优惠价:
							<text style="font-weight: 600;color: #e42d15;">
								￥{{servicePrice}}
							</text>
						</view>
					</view> -->
				</view>
			</m-popup>

			<!-- 酒店详情说明 -->
			<m-popup :show="pop2" @closePop="closePop1">
				<scroll-view scroll-y="true" style="height: 80vh;">
					<view class="roomDetail" style="padding:50rpx 30rpx"
						:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
						<m-swiper :swiper_list="roomDetail.image_list" :round="20"></m-swiper>
						<text class="roomDetail_title">{{roomDetail.name}}</text>
						<view class="roomDetail_basic">
							<view class="roomDetail_basic_item" v-for="item in roomDetail.basic_msg" :key="item.price_id">
								<text :style="{color:themeColor.text_title_color}"
									style="padding-right: 20rpx;text-overflow: clip;width: 30%;">{{item.label}}</text>
								<text style="width: 60%;">{{item.des}}</text>
							</view>
						</view>
						<view class="roomDetail_facility">
							<m-amenities :mode="item.property.style" :list="roomDetail.facility"></m-amenities>
						</view>
						<view @click="closePop1" class="icon-close"
							style="position: absolute;top: 10rpx;right:10rpx;font-size: 40rpx;color: #383838;">
						</view>
					</view>
				</scroll-view>
			</m-popup>

		</view>

		<!-- 模式二 -->
		<view class="m-roomList1 sk" v-if="mode==2&&!skShow" :style="{background:themeColor.bg_color}">
			<view class="m-roomList1_box sk-radius" v-for="item in room_list"
				:style="{color:themeColor.text_main_color}" :key="item.id">
				<view class="m-roomList1_box_header">
					<view class="m-roomList1_box_header_item" @click="showPop1(item)">
						<image class="m-roomList1_box_header_item_img" :src="item.image_list[0]" mode="aspectFill">
						</image>
					</view>
					<view class="m-roomList1_box_header_item1" @click="showPop1(item)">
						<view class="m-roomList1_box_header_item1_title">
							<text>{{item.name}}</text>
						</view>
						<view class="m-roomList1_box_header_item1_sec">
							<text v-for="item2 in item.specialty">{{item2+' ' }}</text>
						</view>
						<view class="m-roomList1_box_header_item1_tags"
							:style="{background:themeColor.main_color+'40',color:themeColor.text_second_color}"
							@click="showPop(item.priceList[0])">
							<text class="icon-loswest" style="font-size: 36rpx;"
								:style="{color:themeColor.main_color}"></text>
							<text>为您优选最低价</text>
						</view>
						<view class="m-roomList_box_header_item1_sec" v-if="unit!='standard'">
							<text>入住类型:{{item.room_sale_type_name}}</text>
						</view>
						<view class="m-roomList1_box_header_item1_sec" v-if="unit=='hour'||unit=='conference_room'">
							<text>入住时间段:{{item.start_time_limit +'-'+ item.end_time_limit}}</text>
						</view>
					</view>
					<view class="m-roomList1_box_header_item2">
						<text :style="{color:themeColor.text_title_color}"
							style="font-size: 26rpx;text-decoration:line-through"
							v-if="item.services.length>0&&item.peak_price-item.services[0].price>0&&item.usable_count > 0">￥{{item.peak_price}}</text>
						<view class="m-roomList1_box_header_item2_price" v-if="item.usable_count > 0">
							<text style="font-size: 30rpx;font-weight: 500;color: #FF2B2B;"
								:style="item.services.length>0?'':'font-size:26rpx'">￥</text>
							<text class="m-roomList1_box_header_item2_price_price"
								:style="item.services.length>0?'':'font-size:26rpx'">{{item.price}}</text>
						</view>
						<view class="m-roomList1_box_header_item2_price1"
							v-if="item.peak_price-item.services[0].price>0&&item.services.length>0&&item.usable_count > 0">
							<text :style="{color:themeColor.text_title_color}"
								style="font-size: 18rpx;">已减{{(item.peak_price-item.services[0].price).toFixed(2)}}元</text>
							<text class="icon-jinggao" style="font-size: 22rpx;"></text>
						</view>
					</view>
					<view class="m-roomList1_box_header_item3">
						<view class="m-roomList1_box_header_item3_box"
							@click="toForm({'item':item,'service':item.services[0]})" v-if="item.usable_count > 0">
							<view class="m-roomList1_box_header_item3_box_t1"
								:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.com_color1+','+themeColor.com_color2+')',color:themeColor.bg_color}">
								<text>预定</text>
							</view>
							<view class="m-roomList1_box_header_item3_box_t2"
								:style="{background:themeColor.main_color,color:themeColor.bg_color}">
								<text style="font-weight: 300;font-size: 26rpx;">在线订</text>
							</view>
						</view>
						<view class="m-roomList1_box_header_item3_box" @click="fullRoom" v-else>
							<view class="m-roomList1_box_header_item3_box_t1"
								style="background-color: #8a8a8a;color: aliceblue;">
								<text style="font-size: 22rpx;">不可预定</text>
							</view>
							<view class="m-roomList1_box_header_item3_box_t2"
								:style="{background:themeColor.bg1_color,color:themeColor.text_title_color}">
								<text style="font-weight: 300;font-size: 26rpx;">已满房</text>
							</view>
						</view>
					</view>

					<view class="m-roomList1_box_header_fixed" @click="lookPrice(item)" v-if="item.services.length>0"
						:style="!(priceIndex.includes(item.price_id))?'color:'+themeColor.main_color:'color:'+themeColor.text_title_color">
						<text>{{priceIndex.includes(item.price_id)?'收起价格':'更多价格'}}</text>
						<view class="icon-iconfonti2-copy-copy-copy-copy"
							:class="priceIndex.includes(item.price_id)?'arrow_ac':'arrow'" style="font-size: 34rpx;">
						</view>
					</view>
				</view>
				<!-- 收起的部分 -->
				<view class="m-roomList1_box_content" v-if="priceIndex.includes(item.price_id)"
					:style="{background:themeColor.bg_color,'border-bottom':'1px solid '+themeColor.border_color}"
					v-for="item1 in item.services" :key="item1.id">
					<view class="m-roomList1_box_content_name">
						<text style="font-size: 38rpx;">{{item1.service_name}}</text>
						<view class="" style="font-size: 24rpx;width: fit-content;" @click="showPop(item1)">
							<text :style="{color:themeColor.text_second_color}">{{item1.cancelable?'可取消':'不可取消'}}</text>
							<text class="icon-jinggao"></text>
						</view>

						<view class="m-roomList_box_content_name_fa">
							<m-tags mode="plain" :name="item2.coupon_name+'x'+item2.count"
								v-for="item2 in item1.breakfast_coupon" :key="item2.id"
								:type="item2.isHot?'hot':'primary'"></m-tags>
						</view>
					</view>

					<view class="m-roomList1_box_content_price">
						<text :style="{color:themeColor.text_title_color}"
							style="font-size: 26rpx;text-decoration:line-through"
							v-if="Number(item.peak_price) - Number(item1.price) > 0&&item.usable_count > 0">￥{{item.peak_price}}</text>
						<view class="m-roomList1_box_content_price_main" v-if="item.usable_count > 0">
							<text style="font-size: 30rpx;font-weight: 500;color: #FF2B2B;">￥</text>
							<text class="m-roomList1_box_content_price_main_price">{{item1.price}}</text>
						</view>
						<view class="m-roomList1_box_content_price_plus" @click="showPop(item1)"
							v-if="Number(item.peak_price) - Number(item1.price) > 0&&item.usable_count > 0">
							<text :style="{color:themeColor.text_title_color}"
								style="font-size: 18rpx;">已减{{(item.peak_price-item1.price).toFixed(2)}}元</text>
							<text class="icon-jinggao" style="font-size: 22rpx;"></text>
						</view>
					</view>

					<view class="m-roomList1_box_content_end">
						<view class="m-roomList1_box_content_end_box" @click="toForm({'item':item,'service':item1})"
							v-if="item.usable_count>0">
							<view class="m-roomList1_box_content_end_box_t1"
								:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.com_color1+','+themeColor.com_color2+')',color:themeColor.bg_color}">
								<text>预定</text>
							</view>
							<view class="m-roomList1_box_content_end_box_t2"
								:style="{background:themeColor.main_color,color:themeColor.bg_color}">
								<text style="font-weight: 300;font-size: 26rpx;">在线订</text>
							</view>
						</view>

						<view class="m-roomList1_box_content_end_box" @click="fullForm()" v-if="item.usable_count==0">
							<view class="m-roomList1_box_content_end_box_t1"
								style="background-color: #8a8a8a;color: aliceblue;">
								<text style="font-size: 22rpx;">不可预定</text>
							</view>
							<view class="m-roomList1_box_content_end_box_t2"
								:style="{background:themeColor.main_color,color:themeColor.text_main_color}">
								<text style="font-weight: 300;font-size: 26rpx;">已满房</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<!-- 优惠弹窗 -->
			<m-popup :show="pop1" @closePop="closePop">
				<view class="couponStyle" style="height: 600rpx;"
					:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
					<view class="couponStyle_title">
						<text>取消规则</text>
					</view>
					<view class="couponStyle_cancel" v-if="if_cancel">
						<text>每天预定{{cancelTime}}之前可免费取消</text>
					</view>
					<view class="couponStyle_cancel" v-else>
						<text>本套餐不可取消</text>
					</view>
					<!-- <view class="couponStyle_priceList">
						<view class="couponStyle_priceList_item">
							<text>原价</text>
							<text style="font-weight: 600;color: #e42d15;">￥{{hxPrice}}</text>
						</view>
						<view class="couponStyle_priceList_item">
							<text>活动</text>
							<text style="font-weight: 600;color: #e42d15;">￥{{servicePrice-hxPrice}}</text>
						</view>
						<view class="couponStyle_priceList_item1">
							优惠价:
							<text style="font-weight: 600;color: #e42d15;">
								￥{{servicePrice}}
							</text>
						</view>
					</view> -->
				</view>
			</m-popup>
			
			<m-popup :show="pop2" @closePop="closePop1">
				<scroll-view scroll-y="true" style="height: 80vh;border-radius: 20rpx;">
					<view class="roomDetail" style="padding:50rpx 30rpx;"
						:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
						
						<m-swiper :swiper_list="roomDetail.image_list" :round="20"></m-swiper>
						<text class="roomDetail_title">{{roomDetail.name}}</text>
						<view class="roomDetail_basic">
							<view class="roomDetail_basic_item" v-for="item in roomDetail.basic_msg" :key="item.id">
								<text :style="{color:themeColor.text_title_color}"
									style="padding-right: 20rpx;text-overflow: clip;width: 30%;">{{item.label}}</text>
								<text style="width: 60%;">{{item.des}}</text>
							</view>
						</view>
						<view class="roomDetail_facility">
							<m-amenities :mode="item.property.style" :list="roomDetail.facility"></m-amenities>
						</view>
						<!-- 关闭 -->
						<view @click="closePop1" class="icon-close"
							style="position: absolute;top: 10rpx;right:10rpx;font-size: 40rpx;color: #383838;">
						</view>
					</view>
				</scroll-view>
			</m-popup>
		</view>


		<!-- 骨架屏 -->
		<m-skeleton :selector="selectorNum" :show="skShow"></m-skeleton>
	</view>
</template>

<script>
	// import * as Mock from '@/mock/index.js'
	import {
		duration
	} from 'moment';
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		props: {
			show: {
				type: Boolean,
				default: true
			},
			mode: {
				type: [String, Number],
				default: 1
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'startDate', 'endDate', 'unit', 'setting']),
			selectorNum() {
				return 3
			}
		},
		watch: {

		},
		async mounted() {
			await this.$onLaunched;

			this.$iBox.http('getSaleType', {
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {
				let type = res.data.filter(item => {
					return item.sign == this.unit
				})[0]
				this.$iBox.http('getRoomType', {
					shop_id: this.hotel.id,
					sell_type: type?type.id:res.data[0].id,
					start_time: this.startDate,
					end_time: this.endDate
				})({
					method: 'post'
				}).then(res => {
					uni.hideLoading()
					this.room_list = res.data
					this.skShow = false
				})
			})

		},
		watch: {

		},
		data() {
			return {
				skShow: true, //骨架屏
				room_list: [],
				priceIndex: [],
				pop1: false,
				pop2: false,
				cancelTime: '',
				if_cancel: null,
				serviceName: null,
				servicePrice: 0,
				hxPrice: 0,
				// 暂时演示数据，正式删掉
				sw: [],
				roomDetail: null,
				itemCurrentID: '',
				sellTypeId: ''
			};
		},
		methods: {
			...mapActions('hotel', ['getRoomInfo']),
			fullRoom() {
				uni.showToast({
					icon: 'none',
					title: '此房型已被预订完，请选择其他房型'
				})
			},
			getType(e) {
				this.skShow = true
				this.$iBox.http('getRoomType', {
					shop_id: this.hotel.id,
					sell_type: e,
					start_time: this.startDate,
					end_time: this.endDate
				})({
					method: 'post'
				}).then(res => {
					this.room_list = res.data
					this.skShow = false

				})
			},
			lookPrice(e) {
				console.log(e);
				if (this.priceIndex.includes(e.price_id)) {
					this.priceIndex = this.priceIndex.filter(item => {
						return item != e.price_id
					})

				} else {
					this.priceIndex.push(e.price_id)
				}
				
				console.log(this.priceIndex,'this.priceIndex', e);
			},
			showPop(e) {
				this.cancelTime = e.before_end_time
				this.if_cancel = e.cancelable
				this.pop1 = true
			},
			closePop(e) {

				this.pop1 = false
			},
			showPop1(e) {

				this.roomDetail = e
				this.pop2 = true
				console.log(e, 'ddd');
			},
			closePop1(e) {
				console.log(e);
				this.pop2 = false
			},
			toForm(item) {
				//是否是会员
				if (this.userInfo.phone&& this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -1) {
					// 时租房判断是否受限制时间预订
					if (item.usable_count == 0) {
						uni.showToast({
							icon: 'none',
							title: `暂无可预定房间，请联系酒店咨询！`,
							duration: 3000
						})
						return;
					}
					
					if (item.item.advance_time != -1 && (this.unit == 'hour' || this.unit == 'conference_room')) {
						if (this.$moment().get('hour') <= Number(item.item.start_time_limit.split(':')[0]) - item.item
							.advance_time) {
							uni.showToast({
								icon: 'none',
								title: `当前时间不符合预订时间段，${item.item.start_time_limit + '-' + item.item.end_time_limit}可预定!`,
								duration: 3000
							})
							return;
						}
					}
					this.getRoomInfo(item)
					uni.navigateTo({
						url: '/packageA/roomForm/roomForm'
					})
				} else {
					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					let a = set[0].property.value
					if (a == 2) {
						uni.navigateTo({
							url: '/pages/login/login'
						})
				
					} else if (a == 1) {
						// this.pop = true
						uni.navigateTo({
							url: '/packageA/memberInfo/memberInfo'
						})
					}
				}
				
				

			}
		}
	}
</script>

<style lang="scss" scoped>
	.m-roomList {
		display: flex;
		flex-direction: column;
		width: 750rpx;
		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		// margin: 0 auto;
		// border-radius: 20rpx;

		&_box {
			display: flex;
			flex-direction: column;
			width: 100%;
			padding: 0 30rpx;

			&_header {
				padding: 30rpx 0rpx;
				display: flex;
				width: 100%;

				// border-bottom: 1px solid #e4e7ed;
				&_item {
					height: 256rpx;
					width: 196rpx;

					&_img {
						width: 100%;
						height: 100%;
						border-radius: 8rpx;
					}
				}

				&_item1 {
					height: 220rpx;
					width: 330rpx;
					padding: 0 20rpx;

					&_title {
						font-size: 30rpx;
						font-weight: 500;
						word-break: break-all;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						/* 这里是超出几行省略 */
						overflow: hidden;
					}

					&_sec {
						width: 100%;
						line-height: 40rpx;
						font-size: 24rpx;
						overflow: hidden;
						text-overflow: ellipsis;
						white-space: nowrap;
					}

					&_tags {
						width: 100%;
						display: flex;
						overflow: scroll;
						white-space: nowrap;
					}
				}

				&_item2 {
					height: 220rpx;
					width: 180rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: flex-end;

					&_price {

						// align-items: center;
						&_price {
							font-size: 44rpx;
							font-weight: 600;
							color: #FF2B2B;
						}

						&_price1 {
							font-size: 34rpx;
							font-weight: 600;
						}
					}

					&_box {
						display: flex;
						align-items: center;
						white-space: nowrap;
						padding: 16rpx 20rpx;
						width: fit-content;
						border-radius: 30rpx;
						font-size: 24rpx;
						margin-top: 30rpx;

						.arrow {
							animation-name: to_bottom_show;
							animation-duration: 0.2s;
							animation-timing-function: linear;
							/* animation-delay: 1s; */
							/* animation-iteration-count: infinite; */
							animation-direction: normal;
							animation-play-state: running;
							animation-fill-mode: forwards;
						}

						.arrow_ac {
							animation-name: to_up_show;
							animation-duration: 0.2s;
							animation-timing-function: linear;
							/* animation-delay: 1s; */
							/* animation-iteration-count: infinite; */
							animation-direction: normal;
							animation-play-state: running;
							animation-fill-mode: forwards;
						}

						/* 箭头动画 */

						@keyframes to_up_show {
							0% {
								transform: rotate(0);
							}

							50% {
								transform: rotate(90deg);
							}

							100% {
								transform: rotate(180deg);
							}
						}

						@keyframes to_bottom_show {
							0% {
								transform: rotate(180deg);
								animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
							}

							50% {
								transform: rotate(90deg);
								animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
							}

							100% {
								transform: rotate(0deg);
							}
						}

					}
				}
			}

			&_content {
				width: 100%;
				margin: 0 auto;
				height: 180rpx;
				padding: 10rpx 30rpx;
				display: flex;

				&_name {
					display: flex;
					flex-direction: column;
					justify-content: space-around;
					width: 55%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					&_fa {
						display: flex;
						margin-left: -10rpx;
						overflow: scroll;
						white-space: nowrap;
						width: 100%;
					}
				}

				&_price {
					width: 25%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: flex-end;
					padding-right: 20rpx;

					&_main {

						&_price {
							font-size: 44rpx;
							font-weight: 600;
							color: #FF2B2B;
						}
					}
				}


				&_end {
					width: 20%;
					display: flex;
					align-items: center;
					justify-content: center;

					&_box {
						width: 120rpx;
						height: 100rpx;
						border-radius: 18rpx;
						box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;

						&_t1 {
							border-radius: 18rpx 18rpx 0 0;
							display: flex;
							align-items: center;
							justify-content: center;
							width: 100%;
							height: 50%;
						}

						&_t2 {
							border-radius: 0 0 18rpx 18rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							width: 100%;
							height: 50%;
						}
					}
				}
			}

		}

		.couponStyle {
			display: flex;
			flex-direction: column;
			align-items: center;

			&_title {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100rpx;
				width: 100%;
				border-bottom: 1px solid #eee;
			}

			&_cancel {
				height: 120rpx;
				width: 100%;
				border-bottom: 1px solid #eee;
				display: flex;
				align-items: center;
				font-size: 28rpx;
				padding: 30rpx;
			}

			&_priceList {
				padding: 30rpx;
				width: 100%;
				display: flex;
				flex-direction: column;

				&_item {
					width: 100%;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}

				&_item1 {
					width: 100%;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: flex-end;
				}
			}
		}

		.roomDetail {
			display: flex;
			flex-direction: column;
			width: 100%;
			border-radius: 20rpx;

			&_title {
				padding: 20rpx;
				// text-align: center;
				font-size: 40rpx;
				font-weight: 600;
			}

			&_basic {
				width: 100%;
				padding: 0 30rpx;
				display: flex;
				flex-wrap: wrap;

				&_item {
					width: 50%;
					display: flex;
					justify-content: flex-start;
					line-height: 60rpx;
				}
			}

			&_facility {
				display: flex;
				flex-direction: column;
				padding: 30rpx;
				width: 100%;

				&_title {
					padding: 30rpx 0;
					font-size: 44rpx;
					font-weight: 500;
				}

				&_item {
					display: flex;
					flex-wrap: wrap;
					width: 100%;
				}
			}
		}
	}

	.m-roomList1 {
		display: flex;
		flex-direction: column;
		width: 750rpx;

		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		// margin: 0 auto;
		// border-radius: 20rpx;
		&_box {
			display: flex;
			flex-direction: column;
			width: 100%;
			padding: 0 30rpx;

			&_header {
				padding: 30rpx 0rpx;
				display: flex;
				width: 100%;
				position: relative;

				// border-bottom: 1px solid #e4e7ed;
				&_item {
					min-height: 220rpx;
					width: 192rpx;

					&_img {
						width: 100%;
						height: 100%;
						border-radius: 8rpx;
					}
				}

				&_item1 {
					min-height: 180rpx;
					width: 300rpx;
					padding: 0 10rpx;
					text-overflow: ellipsis;
					overflow: hidden;
					word-break: break-all;

					&_title {
						font-size: 30rpx;
						font-weight: 500;
						word-break: break-all;
						text-overflow: ellipsis;
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						/* 这里是超出几行省略 */
						overflow: hidden;
					}

					&_sec {
						line-height: 40rpx;
						font-size: 22rpx;
						text-overflow: ellipsis;
						overflow: hidden;
						word-break: break-all;
					}

					&_tags {
						width: fit-content;
						display: flex;
						align-items: center;
						word-wrap: break-all;
						word-break: normal;
						overflow: hidden;
						font-size: 20rpx;
						padding: 4rpx 8rpx;
						border-radius: 6rpx;
						margin-top: 10rpx;
					}
				}

				&_item2 {
					height: 180rpx;
					width: 170rpx;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					padding: 0 10rpx;
					text-overflow: ellipsis;
					overflow: hidden;
					word-break: break-all;
					white-space: nowrap;

					&_price {

						// align-items: center;
						&_price {
							font-size: 34rpx;
							font-weight: 400;
							color: #FF2B2B;
						}
					}

					&_price1 {
						width: 100%;
						display: flex;
						align-items: center;
						word-wrap: break-all;
						word-break: normal;
						overflow: hidden;
					}

					&_box {
						display: flex;
						align-items: center;
						white-space: nowrap;
						padding: 16rpx 20rpx;
						width: fit-content;
						border-radius: 30rpx;
						font-size: 24rpx;
						margin-top: 30rpx;
					}
				}

				&_item3 {
					height: 180rpx;
					width: 100rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					&_box {
						width: 100rpx;
						height: 100rpx;
						border-radius: 12rpx;
						box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;

						&_t1 {
							border-radius: 12rpx 12rpx 0 0;
							display: flex;
							align-items: center;
							justify-content: center;
							width: 100%;
							height: 50%;
							font-size: 30rpx;
						}

						&_t2 {
							border-radius: 0 0 18rpx 18rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							width: 100%;
							height: 50%;
						}
					}

				}

				&_fixed {
					position: absolute;
					bottom: 10rpx;
					right: 20rpx;
					font-size: 28rpx;
					display: flex;

					/* 箭头动画 */
					.arrow {
						animation-name: to_bottom_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					.arrow_ac {
						animation-name: to_up_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					@keyframes to_up_show {
						0% {
							transform: rotate(0);
						}

						50% {
							transform: rotate(90deg);
						}

						100% {
							transform: rotate(180deg);
						}
					}

					@keyframes to_bottom_show {
						0% {
							transform: rotate(180deg);
							animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
						}

						50% {
							transform: rotate(90deg);
							animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
						}

						100% {
							transform: rotate(0deg);
						}
					}


				}


			}

			&_content {
				width: 100%;
				margin: 0 auto;
				height: 180rpx;
				padding: 10rpx 30rpx;
				display: flex;

				&_name {
					display: flex;
					flex-direction: column;
					justify-content: space-around;
					width: 55%;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;

					&_fa {
						display: flex;
						margin-left: -10rpx;
						overflow: scroll;
						white-space: nowrap;
						width: 100%;
					}
				}

				&_price {
					width: 25%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: flex-end;
					padding-right: 20rpx;

					&_main {

						&_price {
							font-size: 44rpx;
							font-weight: 600;
							color: #FF2B2B;
						}
					}

					&_plus {
						width: 100%;
						display: flex;
						justify-content: flex-end;
					}
				}


				&_end {
					width: 20%;
					display: flex;
					align-items: center;
					justify-content: center;

					&_box {
						width: 100rpx;
						height: 100rpx;
						border-radius: 18rpx;
						box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
						font-size: 30rpx;

						&_t1 {
							border-radius: 18rpx 18rpx 0 0;
							display: flex;
							align-items: center;
							justify-content: center;
							width: 100%;
							height: 50%;
						}

						&_t2 {
							border-radius: 0 0 18rpx 18rpx;
							display: flex;
							align-items: center;
							justify-content: center;
							width: 100%;
							height: 50%;
						}
					}
				}
			}


		}

		.couponStyle {
			display: flex;
			flex-direction: column;
			align-items: center;

			&_title {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 100rpx;
				width: 100%;
				border-bottom: 1px solid #eee;
			}

			&_cancel {
				height: 120rpx;
				width: 100%;
				padding: 30rpx;
				border-bottom: 1px solid #eee;
				display: flex;
				align-items: center;
				font-size: 28rpx;
			}

			&_priceList {
				padding: 30rpx;
				width: 100%;
				display: flex;
				flex-direction: column;

				&_item {
					width: 100%;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: space-between;
				}

				&_item1 {
					width: 100%;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: flex-end;
				}
			}
		}

		.roomDetail {
			display: flex;
			flex-direction: column;
			width: 100%;
			border-radius: 20rpx;

			&_title {
				padding: 20rpx;
				// text-align: center;
				font-size: 40rpx;
				font-weight: 600;
			}

			&_basic {
				width: 100%;
				padding: 0 30rpx;
				display: flex;
				flex-wrap: wrap;

				&_item {
					width: 50%;
					display: flex;
					justify-content: flex-start;
					line-height: 60rpx;
				}
			}

			&_facility {
				display: flex;
				flex-direction: column;
				padding: 30rpx;
				width: 100%;

				&_title {
					padding: 30rpx 0;
					font-size: 44rpx;
					font-weight: 500;
				}

				&_item {
					display: flex;
					flex-wrap: wrap;
					width: 100%;
				}
			}
		}
	}
</style>
