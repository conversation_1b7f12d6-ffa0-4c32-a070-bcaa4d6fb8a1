<template>
	<view>
		<view class="titleBox"
			:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.main_color+','+themeColor.com_color1+')',color:themeColor.bg_color}">
			<image :src="userInfo.avatar_url?userInfo.avatar_url:'/static/images/customs.png'" mode=""
				style="width: 150rpx;height: 150rpx;border-radius: 50%;"></image>
			<view class="info">
				<p class="infoTxt">{{distributionInfo.name}} <text
						style="font-size: 24rpx;">{{distributionInfo.status==1&&distributionInfo.group_name?'-'+distributionInfo.group_name:''}}</text>
				</p>
				<text class="infoTxt">{{distributionInfo.phone}}</text>
				<text class="infoTxt"
					style="font-size: 24rpx;">状态:{{distributionInfo.status==0?'申请中':(distributionInfo.status==1?'已通过':'未通过')}}</text>
			</view>
			<view class="qrcode" @click="showQr">
				<text class="icon-erweima" style="font-size: 50rpx;"></text>
				<text style="font-size: 24rpx;">分销码</text>
			</view>
		</view>
		<!-- 数据卡片 -->
		<view class="infoBox">
			<view class="dataInfo">
				<view class="itemBox">
					<text class="itemTitle"
						:style="{color:themeColor.main_color}">￥{{distributionInfo.total_distribution_amount}}</text>
					<text style="font-size: 28rpx;color:#606266">累计佣金</text>
				</view>
				<view class="itemBox" @click="dsRecord">
					<text class="itemTitle"
						:style="{color:themeColor.main_color}">{{distributionInfo.child_count}}人</text>
					<text style="font-size: 28rpx;color:#606266">推广人数</text>
				</view>
				<view class="itemBox" @click="dsBill">
					<text class="itemTitle"
						:style="{color:themeColor.main_color}">{{distributionInfo.distribution_bill_count}}</text>
					<text style="font-size: 28rpx;color:#606266">分销订单</text>
				</view>
			</view>
			<view class="moneyInfo">
				<view class="" style="font-size: 30rpx;">
					<text>可提现佣金</text>
					<text :style="{color:themeColor.main_color}"
						style="margin-left: 10rpx;">￥{{distributionInfo.distribution_amount}}</text>
				</view>
				<view class="">
					<view class='txBtn' :style="{background:themeColor.main_color,color:themeColor.bg_color}" @click="cashWith">
						立即提现
					</view>
				</view>
			</view>
		</view>

		<!-- 列表 -->
		<view class="list">
			<view class="listItem" @click="cashRecord">
				<view class="" style="display: flex;align-items: center;">
					<view class="icon-shouye" :style="{color:themeColor.com_color1}" style="font-size: 40rpx;">
					</view>
					<text style="margin-left: 14rpx;">提现记录</text>
				</view>
				<text class="icon-jiantou"></text>
			</view>
			<view class="listItem" @click="dsRecord">
				<view class="" style="display: flex;align-items: center;">
					<view class="icon-xingming" :style="{color:themeColor.com_color1}" style="font-size: 40rpx;">
					</view>
					<text style="margin-left: 14rpx;">我的客户</text>
				</view>
				<text class="icon-jiantou"></text>	
			</view>
		</view>
		
		<!-- 提现规则展示 -->
		<view class="rules" style="padding: 30rpx;">
			<p>分销规则:</p>
			<view class="" v-for="(item, index) in distributionInfo.rules" :key="item.id">
				<p style="color:#606266;padding: 10rpx 0;font-size: 28rpx;">{{index+1}}.<text>{{item.name}}订单提成百分比:</text> <text>{{item.proportion*100}}%</text> <text style="padding-left: 20rpx;">即:每100元提成金额为{{item.proportion*100}}元</text> </p>
			</view>
		</view>
		<!-- 分销码列表弹窗 -->
		<m-popup :show="qrListShow" mode="center" @closePop="closeQr">
			<view class="qrList">
				<p style="width: 100%;display: flex;justify-content: center;margin-bottom: 20rpx;">分销码列表</p>
				<view class="qrListBox">
					<view class="qr" v-for="item in qrList" :key="item.id">
						<view class="" style="width: 120rpx;height: 120rpx;position: relative;" @click="chooseQr(item)">
							<image :src="item.url" mode="" style="width: 120rpx;height: 120rpx;"></image>
							<view class="" style="position: absolute;width: 100%;height: 100%;background-color: rgba(0, 0, 0, 0.5);top:0;right: 0;display: flex;align-items: center;justify-content: center;">
								<text style="color: #FFFFFF;font-size: 24rpx;font-weight: 600;">点击展示</text>
							</view>
						</view>
						
						<text style="margin-top: 8rpx;font-size: 24rpx;">{{item.qr_name}}</text>
					</view>
				</view>
			</view>
		</m-popup>
		
		<!-- 分销码弹窗 -->
		<m-popup :show="qrShow" mode="center" @closePop="close">
			<view class="qr">
				<text class="icon-close" style="font-size: 44rpx;position: absolute;right: 20rpx;top: 20rpx;" @click="close"></text>
				<p style="width: 100%;display: flex;justify-content: center;margin-bottom: 20rpx;font-size: 40rpx;font-weight: 600;" :style="{color:themeColor.main_color}">{{qr.qr_name}}</p>
				<view class="qrBox" @click="showBig(qr.url)">
					<image :src="qr.url" mode="" style="width:440rpx;height: 440rpx;"></image>
				</view>
				<p style="width: 100%;display: flex;justify-content: center;margin-bottom: 20rpx;font-size: 30rpx;font-weight: 600;color: brown;">点击图片预览大图，长按大图可保存图片</p>
			</view>
		</m-popup>

		<m-login v-if="hackReset&&if_login"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				if_login: false,
				hackReset: true,
				distributionInfo: null,
				qrListShow:false,
				qrList:[],
				qrShow:false,
				qr:null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		async onShow() {
			await this.$onLaunched;
			// 判断是否登录
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			//是否是会员
			if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
				1) {
				this.if_login = false
			} else {
				this.if_login = true
			}

			// 查询分销信息
			this.$iBox.http('getDistributionInfo', {})({
				method: 'post'
			}).then(res => {
				if (res.data) {
					this.distributionInfo = res.data
					this.qrList = res.data.qr_list
				} else {
					uni.navigateTo({
						url: './apply/apply'
					})
				}
				uni.hideLoading()
			})
		},
		methods: {
			closeQr(){
				this.qrListShow = false
			},
			showQr(){
				this.qrListShow = true
			},
			close(){
				this.qrShow = false
			},
			chooseQr(e){
				this.qrShow = true
				this.qr = e
			},
			dsBill(){
				uni.navigateTo({
					url:'./distributionBill/distributionBill'
				})
			},
			cashWith(){
				uni.navigateTo({
					url:'./withdrawal/withdrawal'
				})
			},
			cashRecord(){
				uni.navigateTo({
					url:'./withdrawalRecord/withdrawalRecord'
				})
			},
			dsRecord(){
				uni.navigateTo({
					url:'./commissionBill/commissionBill'
				})
			},
			showBig(e){
				let urls = []
				this.qrList.forEach(item=>{
					urls.push(item.url)
				})
				
				wx.previewImage({
				  current: e, // 当前显示图片的http链接
				  urls: urls // 需要预览的图片http链接列表
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.titleBox {
		height: 300rpx;
		width: 100%;
		padding: 30rpx;
		display: flex;
		align-items: flex-start;
		position: relative;

		.info {
			display: flex;
			flex-direction: column;
			margin-left: 14rpx;
			height: 200rpx;

			.infoTxt {
				line-height: 50rpx;
			}
		}

		.qrcode {
			position: absolute;
			top: 20rpx;
			right: 20rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
		}
	}

	.infoBox {
		margin: -60rpx auto;
		margin-bottom: 0;
		height: 260rpx;
		background: #FFFFFF;
		border-radius: 24rpx;
		width: 690rpx;
		z-index: 6;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		position: relative;

		.dataInfo {
			width: 100%;
			height: 60%;
			border-bottom: 1px solid #e4e7ed;
			display: flex;
			align-items: center;

			.itemBox {
				height: 100%;
				width: 33%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				.itemTitle {
					font-size: 44rpx;

				}
			}
		}

		.moneyInfo {
			width: 100%;
			height: 40%;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			.txBtn {
				height: 60rpx;
				width: fit-content;
				padding: 10rpx 20rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 30rpx;
				font-size: 26rpx;
			}
		}
	}

	.list {
		margin: 0 auto;
		margin-top: 30rpx;
		width: 690rpx;

		.listItem {
			height: 120rpx;
			width: 100%;
			border-radius: 14rpx;
			background-color: #FFFFFF;
			border-bottom: 1px solid #e4e7ed;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 40rpx;
		}
	}
	
	.qrList {
		height: 600rpx;
		width: 700rpx;
		border-radius: 30rpx;
		padding: 30rpx;
		.qrListBox{
			display: flex;
			.qr{
				width: 200rpx;
				height: 200rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
			}
		}
	}
	
	.qr {
		height: 100vh;
		width: 750rpx;
		// border-radius: 30rpx;
		padding: 30rpx;
		position: relative;
		.qrBox{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 500rpx;
		}
	}
</style>