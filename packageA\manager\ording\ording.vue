<template>
	<view>
		<scroll-view scroll-y="true" style="height: 100vh;" :scroll-into-view="idIndex">
			<view class="ordingBox">
				<p class="title">
					<text>预订信息</text>
					<text style="font-size: 24rpx;color: darkgreen;" @click="quick">快速定位房型</text>
				</p>
				<view class="msgBox">
					<view class="msgItem">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">销售类型</text></p>
						<picker @change="bindChange" :value="changeIndex" range-key="name" :range="roomStatusBox">
							<view class="pickerBox">
								{{roomStatusBox[changeIndex].name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
			
					<view class="msgItem">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">销售规则</text></p>
						<picker @change="bindChange1" :value="changeIndex1" range-key="name" :range="roomSaleList">
							<view class="pickerBox">
								{{roomSale.name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
			
					<view class="msgItem">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">入住类型</text></p>
						<picker @change="bindChange2" :value="changeIndex2" range-key="name" :range="stayTypeList">
							<view class="pickerBox">
								{{stayTypeList[changeIndex2].name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
			
					<view class="msgItem">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">联系人</text></p>
						<view class=""
							style="height: 38rpx;width: 400rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<uni-easyinput v-model="linkman" trim="all" placeholder="请输入联系人" @input="searchInfo">
							</uni-easyinput>
						</view>
					</view>
			
			
					<view class="msgItem">
						<p><text style="font-size: 34rpx;">手机号</text></p>
						<view class=""
							style="height: 38rpx;width: 400rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<uni-easyinput type="number" @blur="chooseInfo" v-model="linkphone" trim="all"
								placeholder="请输入联系电话" @input="searchInfo1"></uni-easyinput>
						</view>
					</view>
			
					<!-- 会员展示 -->
					<view class="manListBox" v-if="manList.length > 0&&ifShow">
						<scroll-view scroll-y="true" style="max-height: 320rpx;">
							<view class="infoBox" style="" v-for="(item,index) in manList" :key="index"
								@click="chooseName(item)">
								<text>{{item.name?item.name:'暂无'}}</text>
								<text>({{item.grade_name?item.grade_name:'散客'}})</text>
								<text>{{item.phone?item.phone:'暂无'}}</text>
								<text style="font-size: 30rpx;color: #5b900b;">选择</text>
							</view>
						</scroll-view>
					</view>
			
					<view class="msgItem">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">价格方案</text></p>
						<picker @change="bindChange3" :value="changeIndex3" range-key="name" :range="priceTypeList">
							<view class="pickerBox">
								{{priceTypeList[changeIndex3].name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
			
					<view class="msgItem" style="border: 2px dashed #2c7f08;" v-if="manInfo&&ifShow1">
						<view class="" style="width: 100%;display: flex;flex-wrap: wrap;font-size: 26rpx;padding:0 20rpx;">
							<p style="width: 50%;">姓名:{{manInfo.name}}</p>
							<p style="width: 50%;">会员等级:{{manInfo.grade_name}}</p>
							<p style="width: 50%;">电话:{{manInfo.phone}}</p>
							<p style="width: 50%;">会员余额:{{manInfo.balance}}</p>
						</view>
					</view>
			
					<view class="msgItem" v-if="unitList.length > 0">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">单位</text></p>
						<picker @change="bindChange4" :value="changeIndex4" range-key="intermediary_name" :range="unitList">
							<view class="pickerBox">
								{{unitList[changeIndex4].intermediary_name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
			
					<view class="msgItem" v-if="mediatorList.length > 0">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">中介</text></p>
						<picker @change="bindChange5" :value="changeIndex5" range-key="intermediary_name"
							:range="mediatorList">
							<view class="pickerBox">
								{{mediatorList[changeIndex5].intermediary_name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
					<!-- 预抵时间 -->
					<view class="msgItem">
						<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">预抵时间</text></p>
						<view class="" style="width: 320rpx;">
							<uni-datetime-picker :clear-icon="false" :start="startTime" type="date" v-model="datetimesingle"
								@change="changeLog" />
						</view>
						<view class="" style="">
							<picker mode="time" :value="time" :start="time" @change="bindTimeChange">
								<view class=""
									style="margin-left: 20rpx;padding: 12rpx 20rpx;border: 1px solid #e6e6e6;border-radius: 4rpx;">
									{{time}}
								</view>
							</picker>
						</view>
					</view>
			
					<!-- 入住时长 -->
					<view class="">
						<!-- 小时 -->
						<view class="msgItem" v-if="roomSale.sign=='hour'||roomSale.sign=='conference_room'">
							<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
									style="font-size: 34rpx;">入住时长</text></p>
							<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
								<text>{{roomSale.stay_time}}小时</text>
							</view>
						</view>
						<!-- 天数 -->
						<view class="msgItem" v-if="roomSale.sign=='standard'">
							<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
									style="font-size: 34rpx;">入住时长</text></p>
							<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
								<uni-number-box :min="1" v-model="dayCounts" @change="dayChange"></uni-number-box>天
							</view>
						</view>
						<!-- 月租 -->
						<view class="msgItem" v-if="roomSale.sign=='long_standard'">
							<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
									style="font-size: 34rpx;">入住时长</text></p>
							<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
								<uni-number-box :min="1" v-model="monthCounts" @change="monthChange"></uni-number-box>月
							</view>
						</view>
					</view>
			
					<!-- 离店时间 -->
					<view class="msgItem">
						<p style="margin-right: 8rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">离店时间</text></p>
						<view class="" v-if="roomSale.sign=='hour'||roomSale.sign=='conference_room'"
							style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<text>{{datetimesingle1}}</text>
						</view>
						<view class="" style="width: 480rpx;" v-if="roomSale.sign=='standard'">
							<uni-datetime-picker :start="endTime" type="datetime"  v-model="datetimesingle1"
								@change="changeLog1" />
						</view>
						<view class="" v-if="roomSale.sign=='long_standard'"
							style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<text>{{datetimesingle1}}</text>
						</view>
					</view>
			
					<!-- 订单来源 -->
					<view class="msgItem">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">订单来源</text></p>
						<picker @change="bindChange6" :value="changeIndex6" range-key="source_name" :range="billSource">
							<view class="pickerBox">
								{{billSource[changeIndex6].source_name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
					
					<!-- 付款方式 -->
					<view class="msgItem">
						<p><text style="color: red;">*</text><text style="font-size: 34rpx;">付款方式</text></p>
						<picker @change="bindChangePayType" :value="changeIndexPayType" range-key="name" :range="payType">
							<view class="pickerBox">
								{{payType[changeIndexPayType].name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
			
					<!-- 外部订单号 -->
					<view class="msgItem">
						<p><text style="font-size: 34rpx;">外部订单号</text></p>
						<input type="text" placeholder="请输入外部订单号" class="msgInput" v-model="outOrderNum" style="">
					</view>
					<!-- 备注 -->
					<view class="msgItem">
						<p><text style="font-size: 34rpx;">备注</text></p>
						<input type="text" placeholder="请输入备注" class="msgInput" v-model="memo" style="">
					</view>
				</view>
			</view>
			
			<view class="roomBox">
				<p class="title">房间信息</p>
				<view class="room" v-for="(item, index) in roomList" :key="index" :id="'room'+item.id" v-if="item.usable_room_count > 0">
					<view class="room_content">
						<view class="item" style="width: fit-content;">
							<text>{{item.name}}</text>
						</view>
						<view class="item" style="width: fit-content;">
							<text>{{priceTypeList[changeIndex3].name=='门市价'?'散客':''}}</text>
						</view>
						<view class="item" style="width: fit-content;">
							<text>可订:{{item.usable_room_count}}间</text>
						</view>
			
						<view class="item" style="width: fit-content;">
							<text>未排房:{{item.roomNumber - (item.room_list?item.room_list.length:0)}}间</text>
						</view>
						<view class="item1">
							<text>价格:</text>
							<view class="" style="width: 260rpx;">
								<uni-easyinput type="digit" v-model="item.room_prices.room_price[0].room_price" trim="all" @blur="changePrice(item)" :clearable="false"></uni-easyinput>
							</view>
							<text style="font-size: 22rpx;color: blue;padding-left: 6rpx;"
								@click="changePrice(item)">多日房价</text>
						</view>
						<view class="item1">
							<text>押金:</text>
							<view class="" style="width: 260rpx;">
								<uni-easyinput type="digit" v-model="item.room_prices.cash_pledge" trim="all" :clearable="false"></uni-easyinput>
							</view>
						</view>
						<view class="item1">
							<text>预订间数:</text>
							<uni-number-box v-model="item.roomNumber" @change="roomNumChoose(item)" :max='item.usable_room_count'
								:step="1" />
						</view>
						<view class="item3" style="" v-if="item.roomNumber>0">
							<view class="serviceBox"
								:style="item1.checked?'background:#5c79e3;border:1px solid #5c79e3;color:#cad9f4':''"
								v-for="(item1, index1) in item.room_prices.room_service" :key="index1"
								@click="chooseService({'item':item,'item1':item1})">
								<text>{{item1.service_name}}</text>
								<text style="padding-left: 6rpx;">￥{{item1.price}}</text>
							</view>
						</view>
						<view class="item3">
							<view class="roomSelcet" v-for="(item2, index2) in item.room_list" :key="index2"
								style="width: 120rpx;height: 100rpx;color: #fff;margin: 10rpx;">
								<view class="" :style="{background:item2.room_status_color}"
									style="height: 60rpx;width: 100%;display: flex;;align-items: center;justify-content: center;flex-direction: column;">
									<text>{{item2.room_number}}</text>
									<view class="" style="width: 80rpx;height: 8rpx;border-radius: 16rpx;"
										:style="{background:item2.clear_color}"></view>
								</view>
								<view class="" style="width: 120rpx;height: 40rpx;">
									<input type="text" v-model="item2.userInfo[0].name"
										style="width: 100%;height: 40rpx;padding: 6rpx 0rpx;background-color: #fff;color:#000;text-align:center;border: 1px solid #eee;"
										placeholder="输入名字" placeholder-style="font-size:22rpx">
								</view>
								<image src="../../../static/images/close.png" class="close" mode=""
									@click="deleteRoom({'item':item,'item1':item2})"></image>
							</view>
						</view>
						<view class="item2" style="background: #eee;"
							:style="item.roomNumber>0?'color:#2074f1':'color:#888888'" @click="selectRoom(item)">
							<text>排房</text>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 价格 -->
			<view class="priceBox">
				<p class="title">订单价格</p>
				<view class="priceContent">
					<view style="display: flex;align-items: center;">
						<p style="width: 120rpx;">房费:</p> <text>{{roomPrice}}</text>
					</view>
					<view style="display: flex;align-items: center;">
						<p style="width: 120rpx;">套餐费:</p> <text>{{servicePrice}}</text>
					</view>
					<view style="display: flex;align-items: center;">
						<p style="width: 120rpx;">押金:</p> <text>{{cashPrice}}</text>
					</view>
					<view style="display: flex;align-items: center;margin-top: 30rpx;">
						<p style="width: 120rpx;">优惠券:</p><text v-if="chooseCouponse"
							style="margin-right: 30rpx;">-{{chooseCouponsePrice}}</text> <text
							style="font-size: 22rpx;color: #2c7f08;">{{couponsList.length?couponsList.length:0}}张可用</text>
						<text style="padding-left: 40rpx;color: blue;" @click="chooseCoupon">选择</text>
					</view>
				</view>
			</view>
			
			<view class="" style="height: 140rpx;width: 100%;"></view>
			
			<!-- 应收款 -->
			<view class="submitContent" style="">
				<view class="" :style="{color: themeColor.main_color}" style="font-size: 40rpx;font-weight: 600;">
					<p>应收款:￥{{(roomPrice*1 + servicePrice*1 + cashPrice*1 - chooseCouponsePrice*1).toFixed(2)}}</p>
				</view>
				<view class=""
					style="width: 120rpx;height: 60rpx;border-radius: 10rpx;padding: 10rpx;color: #fff;text-align: center;"
					:style="{background: themeColor.main_color}" @click="surePay">
					提交
				</view>
			</view>
		</scroll-view>
		
		<!-- 选择优惠券弹窗 -->
		<m-popup :show="popCoupon" @closePop="closePopCoupon">
			<mBossChooseCoupons :coupType="1" :list="couponsList" :limitNum="couponLimit" @getCouponIfo="getInfo">
			</mBossChooseCoupons>
		</m-popup>

		<!-- 价格弹窗 -->
		<m-popup :show="popPrice" @closePop="closepopPrice">
			<view class="" style="height: 80vh;width: 100%;padding: 30rpx;">
				<scroll-view scroll-y="true" style="height:100%;position: relative;">
					<view class="" v-for="(item3, index3) in pricesBox.room_price" style="margin-top: 30rpx;"
						:key="index3">
						<view class=""
							style="display: flex;align-items: center;height: 50rpx;justify-content: space-between;">
							<text>{{item3.date}}:</text>
							<view class="" style="width: 400rpx;height: 44rpx;display: flex;align-items: center;">
								<view class="" style="width: 260rpx;">
									<uni-easyinput type="digit" v-model="item3.room_price" trim="all" @change="changeDatePrice" :clearable="false"></uni-easyinput>
								</view>
								<text style="font-size: 28rpx;color: #2c7f08;padding-left: 20rpx;"
									@click="dayPrice(item3.room_price)" v-if="index3==0">同步房价</text>
							</view>

						</view>

					</view>
					<view class=""
						style="width: 100%;height: 60rpx;display: flex;align-items: center;justify-content: center;margin-top: 80rpx;">
						<view class=""
							style="width: 480rpx;height: 70rpx;border-radius: 30rpx;background-color: #2c7f08;color: #fff;display: flex;align-items: center;justify-content: center;"
							@click="closepopPrice">
							<text>确认</text>
						</view>

					</view>
				</scroll-view>

			</view>
		</m-popup>

		<!-- 选房弹窗 -->
		<mSelectRooms :roomList="selectRoomList" :ids="chooseIds" :num="itemPrice" :rooms="chooseRooms" :poprc="showRc"
			@closeZj="closeRc" @sureRc="getRcIds"></mSelectRooms>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	import mSelectRooms from '../../components/m-selectRooms.vue'
	import mBossChooseCoupons from '../../components/m-chooseBossCoupons.vue'
	export default {
		data() {
			return {
				idIndex:'',
				//销售类型
				changeIndex: 0,
				//销售规则
				changeIndex1: 0,
				//入住类型
				changeIndex2: 0,
				// 联系人
				linkman: '',
				params: {
					search_word: '',
					page: 1,
					limit: 10000
				},
				linkphone: '',
				params1: {
					search_word: '',
					page: 1,
					limit: 10000
				},
				ifShow: true,
				ifShow1: true,
				manList: [],
				manInfo: null,
				// 价格方案
				changeIndex3: 0,
				ifGrade: '',
				roomStatusBox: [], //销售类型
				roomSaleList: [], //销售规则
				roomSale: null,
				//入住类型
				stayTypeList: [{
					id: 1,
					name: '正常'
				}, {
					id: 2,
					name: '免费'
				}, {
					id: 3,
					name: '自用'
				}],
				priceTypeList: [{
					id: 1,
					name: '门市价',
					status: 1
				}, {
					id: 2,
					name: '会员',
					status: 1
				}, {
					id: 3,
					name: '单位',
					status: 1
				}, {
					id: 4,
					name: '中介',
					status: 1
				}],
				//单位
				changeIndex4: 0,
				unitList: [],
				// 中介
				changeIndex5: 0,
				mediatorList: [],
				// 预抵时间
				dayCounts: 1,
				monthCounts: 1,
				datetimesingle: '',
				time: '', //具体时间
				startTime: '',
				datetimesingle1: '',
				endTime: '',
				// 订单来源
				billSource: [],
				changeIndex6: 0,
				// 付款方式
				payType:[{id:1,name:'预付'},{id:2,name:'到付'},{id:3,name:'先住后付'}],
				changeIndexPayType:0,
				// 外部单号
				outOrderNum: '',
				//备注
				memo: '',
				params: {
					end_time: '',
					room_clear_status: [],
					room_record_status: [],
					room_sale_type: '',
					start_time: '',
					times: ''
				},

				// 房型列表
				roomList: [],
				roomNumber: 0, //选择间数
				service: '', //选择的服务

				//价格弹窗
				popPrice: false,
				pricesBox: [],
				itemPrice: null, //暂时存储点击的item

				// 优惠券弹窗
				popCoupon: false,

				// 房态
				params1: {
					room_clear_status: [],
					room_record_status: [],
					floor_id: '',
					building_id: "",
					room_type_id: "",
					room_number: "",
					room_sale_type: "",
					grade_id: "",
					intermediaries_id: "",
					start_time: "",
					end_time: ""
				},
				selectRoomList: [],
				showRc: false,
				chooseIds: [],
				chooseRooms: [],

				// 价格
				roomPrice: 0,
				servicePrice: 0,
				cashPrice: 0,
				couponsList: [],
				chooseCouponse: null,
				chooseCouponsePrice: 0,
				couponLimit: 0,
				leave_time: ''
			};
		},
		components: {
			mSelectRooms,
			mBossChooseCoupons
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager', 'shopSetting', 'gradeList']),
			...mapState('room', ['roomInfo']),
		},
		watch: {
			manInfo: {
				handler(newVal, oldVal) {
					if (this.manInfo && this.manInfo.grade != -1&&this.manInfo.grade_name!='散客') {
						let a = [{
							id: 1,
							name: '门市价',
							status: 1
						}, {
							id: 2,
							name: '会员',
							status: 1
						}, {
							id: 3,
							name: '单位',
							status: 1
						}, {
							id: 4,
							name: '中介',
							status: 1
						}]

						this.priceTypeList = a
						this.priceTypeList[1].status = 1
						this.changeIndex3 = 1

						// 查询优惠券
						this.$iBox
							.http('bossGetUserCoupon1', {
								common_code: this.manInfo.common_code,
								page: 1,
								limit: 1000
							})({
								method: 'post'
							})
							.then(res => {
								this.couponsList = res.data.list
							})

						let leave_time = ''
						this.gradeList.filter(item => {
							if (this.manInfo.grade == item.id) {
								item.right_itererest.forEach(item1 => {
									if (item1.sign == 'yctf') {
										leave_time = item1.value
									}
								})
							}
						})

						if (this.roomSale.sign == 'standard' || this.roomSale.sign == 'long_standard') {
							this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle1, 'YYYY-MM-DD HH:mm:ss'))
								.format('YYYY-MM-DD') + ` ${leave_time}:00`
							console.log(leave_time, 'leave_time',this.datetimesingle1);
						}

					} else {

						let a = [{
							id: 1,
							name: '门市价',
							status: 1
						}, {
							id: 2,
							name: '单位',
							status: 1
						}, {
							id: 3,
							name: '中介',
							status: 1
						}]
					
						this.priceTypeList = a
						this.changeIndex3 = 0
					}
				},
				immediate: true,
				deep: true
			},
			linkphone() {
				if (this.linkphone.length != 11) {
					this.manInfo = null
				}
			},

			changeIndex3() {
				if (this.priceTypeList[this.changeIndex3].name != '会员') {
					this.ifShow = false
					this.ifShow1 = false
					this.couponsList = []
				} else if (this.priceTypeList[this.changeIndex3].name == '会员') {
					this.ifShow = true
					this.ifShow1 = true
					this.params.grade_id = this.manInfo.grade
					this.getRoomType()


				} else {
					this.ifShow = true
					this.ifShow1 = true
					this.couponsList = []
				}
			},
			roomList: {
				handler(newVal, oldVal) {
					console.log(newVal, 'room');
					let roomPrice = 0
					let servicePrice = 0
					let cashPrice = 0
					newVal.forEach(item => {
						item.room_prices.room_price.forEach(price => {
							roomPrice += price.room_price * item.roomNumber
							console.log(roomPrice, 'room1');
						})

						item.room_prices.room_service.forEach(item1 => {
							if (item1.checked) {
								servicePrice += item1.price * item.roomNumber * (this.roomSale.sign ==
									'hour' || this.roomSale.sign == 'conference_room' ?
									1 : (this.roomSale
										.sign == 'standard' ? this.dayCounts : this.monthCounts))
							}
						})
						cashPrice += item.room_prices.cash_pledge * item.roomNumber * (this.roomSale.sign ==
							'hour' || this.roomSale.sign == 'conference_room' ?
							1 : (this.roomSale
								.sign == 'standard' ? this.dayCounts : this.monthCounts))
					})
					this.roomPrice = roomPrice.toFixed(2)
					this.servicePrice = servicePrice.toFixed(2)
					this.cashPrice = cashPrice.toFixed(2)
				},
				deep: true,
				immediate: true

			},

			roomPrice: {
				handler(newVal, oldVal) {

					this.couponLimit = newVal * 1 + this.servicePrice * 1
					console.log('价格变化', this.couponLimit);
				},
				deep: true,
				immediate: true
			},
			servicePrice: {
				handler(newVal, oldVal) {
					this.couponLimit = newVal * 1 + this.roomPrice * 1
					console.log('价格变化', this.couponLimit);
				},
				deep: true,
				immediate: true
			},
			roomSale: {
				handler(oldVal, newVal) {
					console.log(this.roomSale, 'grade', this.datetimesingle, this.time);
					if (this.datetimesingle) {


						if (this.manInfo) {
							let leave_time = ''
							this.gradeList.filter(item => {
								if (this.manInfo.grade == item.id) {
									item.right_itererest.forEach(item1 => {
										if (item1.sign == 'yctf') {
											leave_time = item1.value
										}
									})
								}
							})


							if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle + ' ' + this.time,
									'YYYY-MM-DD HH:mm').add(
									this.roomSale.stay_time, "hours")).format('YYYY-MM-DD HH:mm:ss')
									if (this.datetimesingle == this.$moment().format('YYYY-MM-DD')) {
										this.time = this.$moment().get('hour')+':' + this.$moment().get('minute')*1<10?'0'+this.$moment().get('minute'):this.$moment().get('minute');
									} else {
										this.time = this.roomSale.start_time_limit
									}
								console.log('时租房', this.time);
							} else if (this.roomSale.sign == 'standard') {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle + ' ' + this.time,
										'YYYY-MM-DD HH:mm').add(this.roomSale.stay_time, "days")).format('YYYY-MM-DD') +
									` ${leave_time}:00`
							} else {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle + ' ' + this.time,
										'YYYY-MM-DD HH:mm').add(this.roomSale.stay_time, "months")).format('YYYY-MM-DD') +
									` ${leave_time}:00`
							}

						} else {
							if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle + ' ' + this.time,
									'YYYY-MM-DD HH:mm').add(this.roomSale.stay_time, "hours")).format(
									'YYYY-MM-DD HH:mm:ss')
									if (this.datetimesingle == this.$moment().format('YYYY-MM-DD')) {
										this.time = this.$moment().get('hour')+':' + this.$moment().get('minute');
									} else {
										this.time = this.roomSale.start_time_limit
									}
									console.log('时租房', this.time);
							} else if (this.roomSale.sign == 'standard') {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle + ' ' + this.time,
										'YYYY-MM-DD HH:mm').add(this.roomSale.stay_time, "days")).format('YYYY-MM-DD') +
									` ${this.leave_time}:00`
							} else {
								this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle + ' ' + this.time,
										'YYYY-MM-DD HH:mm').add(this.roomSale.stay_time, "months")).format('YYYY-MM-DD') +
									` ${this.leave_time}:00`
							}

						}
					}

				},
				immediate: true,
				deep: true

			}
		},
		onLoad() {
			
			
			console.log(this.idIndex);
			// 获取离店时间设置
			this.leave_time = this.shopSetting.filter(item => {
				return item.sign == 'leave_time'
			})[0].property.value

			this.$iBox
				.http('getRoomSellType', {})({
					method: 'post'
				})
				.then(res => {
					// res.data.forEach(item => {
					// 	this.roomStatusBox.push(item)
					// })
					this.roomStatusBox = res.data
					this.$iBox
						.http('getRoomSaleType', {
							sell_type: res.data[0].id,
							status: 1
						})({
							method: 'post'
						})
						.then(res => {
							this.roomSaleList = res.data
							this.roomSale = res.data[0]


							this.$iBox
								.http('getDefaultEnterTime', {})({
									method: 'post'
								})
								.then(res => {
									
									if (this.$moment().unix() > this.$moment(this.$moment().format(
											'YYYY-MM-DD') + ' ' + res.data, 'YYYY-MM-DD HH:mm').unix()) {
										this.time = this.$moment().get('hour')+':' + this.$moment().get('minute');
									} else {
										this.time = res.data
									}


									// 订单来源
									this.$iBox
										.http('getBillSource', {})({
											method: 'post'
										})
										.then(res => {
											this.billSource = res.data
										})
									this.datetimesingle = this.$moment(this.$moment().unix() * 1000).format(
										'YYYY-MM-DD')
									this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle,
											'YYYY-MM-DD').add(1, "days")).format('YYYY-MM-DD') +
										` ${this.leave_time}:00`
										
									this.startTime = this.$moment().format('YYYY-MM-DD')
									if (this.roomSale.sign == 'hour' || this.roomSale.sign ==
										'conference_room') {
										this.endTime = this.$moment(this.$moment(this.startTime,
												'YYYY-MM-DD HH:mm:ss')
											.add(roomSale.stay_time,
												"hours")).format('YYYY-MM-DD HH:mm:ss')
									} else if (this.roomSale.sign == 'standard') {
										this.endTime = this.$moment(this.$moment(this.startTime,
												'YYYY-MM-DD HH:mm:ss')
											.add(this.dayCounts,
												"days")).format('YYYY-MM-DD HH:mm:ss')
									} else {
										this.endTime = this.$moment(this.$moment(this.startTime,
												'YYYY-MM-DD HH:mm:ss')
											.add(this.monthCounts,
												"months")).format('YYYY-MM-DD HH:mm:ss')
									}
									console.log(this.endTime,'this.datetimesingle',this.datetimesingle1,);
									this.params = {
										end_time: this.$moment(this.datetimesingle1).unix(),
										room_clear_status: [1, 2, 3],
										room_record_status: [3, 5],
										room_sale_type: this.roomSale.id,
										start_time: this.$moment(this.datetimesingle + ' ' + this.time,
											'YYYY-MM-DD HH:mm').unix(),
										times: this.dayCounts
									}
									this.$iBox
										.http('getUsableRoomType', this.params)({
											method: 'post'
										})
										.then(res => {
											let a = []
											res.data.forEach(item => {
												item.roomNumber = 0
												a.push(item)
												item.room_prices.room_service.forEach((item1,
													index1) => {
													if (index1 == 0) {
														item1.checked = true
													} else {
														item1.checked = false
													}

												})
											})

											this.roomList = a
											
											if (this.roomInfo) {
												this.roomList.forEach(item => {
													if (item.id == this.roomInfo.room_type_id) {
														item.room_list = []
														item.roomNumber = 1
														item.room_list.push(this.roomInfo)
														item.room_list.forEach(item2 => {
															item2.userInfo = []
															item2.userInfo.push({
																name: ''
															})
														})


													}
												})
											}

											// console.log(this.roomList, 'this.roomList');
										})


								})


						})
				})


		},
		methods: {
			getRoomType() {
				this.$iBox
					.http('getUsableRoomType', this.params)({
						method: 'post'
					})
					.then(res => {
						let a = []
						res.data.forEach(item => {
							item.roomNumber = 0

							item.room_prices.room_service.forEach((item1, index1) => {
								if (index1 == 0) {
									item1.checked = true
								} else {
									item1.checked = false
								}

							})
							// 查询得时候不删除已选房间
							this.roomList.forEach(room => {
								if (item.id == room.id) {
									item.room_list = room.room_list
									item.roomNumber = room.roomNumber
								}
							})
							a.push(item)
						})


						this.roomList = a
						
					})
			},
			roomNumChoose(e) {

			},
			quick(){
				this.idIndex ='room' +  this.roomInfo.room_type_id
				console.log(this.idIndex);
			},
			changePrice(e) {
				this.itemPrice = e
				this.pricesBox = e.room_prices
				this.roomList.forEach(item => {
					if (item.id == e.id) {
						item.room_prices = e.room_prices
					}
				})
				// console.log(this.roomList, 'ee');
				this.popPrice = true
			},
			closepopPrice() {
				this.popPrice = false
			},
			chooseService(e) {
				this.roomList.forEach(item => {
					if (item.id == e.item.id) {
						item.room_prices.room_service.forEach(item1 => {
							if (item1.id == e.item1.id) {
								item1.checked = true
							} else {
								item1.checked = false
							}
						})
					}
				})
			},
			chooseInfo() {
				// console.log(this.manList, 'this.manList');
				if (this.manList.length == 1) {
					this.manInfo = this.manList[0]
					this.linkman = this.manInfo.name
				}

			},

			searchInfo(e) {
				this.params.search_word = this.linkman
				this.$iBox
					.http('searchUser', this.params)({
						method: 'post'
					})
					.then(res => {
						this.manList = res.data
						this.ifShow = true
					})
			},
			searchInfo1(e) {
				this.params.search_word = this.linkphone
				this.$iBox
					.http('searchUser', this.params)({
						method: 'post'
					})
					.then(res => {
						this.manList = res.data
						this.ifShow = true
					})
			},
			bindChange(e) {
				// console.log(e);
				this.roomSaleList = []
				this.changeIndex = e.detail.value
				this.changeIndex1 = 0
				this.$iBox
					.http('getRoomSaleType', {
						sell_type: this.roomStatusBox[this.changeIndex].id,
						status: 1
					})({
						method: 'post'
					})
					.then(res => {
						this.roomSaleList = res.data
						this.roomSale = this.roomSaleList[this.changeIndex1]
						this.params.room_sale_type = this.roomSale.id
						this.getRoomType()
					})


			},
			bindChange1(e) {
				this.changeIndex1 = e.detail.value
				this.roomSale = this.roomSaleList[this.changeIndex1]
				this.params.room_sale_type = this.roomSale.id
				this.getRoomType()
			},
			bindChange2(e) {

				this.changeIndex2 = e.detail.value
			},
			bindChange3(e) {
				this.changeIndex3 = e.detail.value
				if (this.priceTypeList[e.detail.value].name == '单位') {
					this.$iBox
						.http('getIntermediaryList', {
							type: 1
						})({
							method: 'post'
						})
						.then(res => {
							this.mediatorList = []
							res.data.forEach(item => {
								this.unitList.push(item)
							})

							this.params.intermediaries_id = this.unitList[0].id
							this.getRoomType()

						})
				} else if (this.priceTypeList[e.detail.value].name == '中介') {
					this.$iBox
						.http('getIntermediaryList', {
							type: 2
						})({
							method: 'post'
						})
						.then(res => {
							this.unitList = []
							res.data.forEach(item => {
								this.mediatorList.push(item)
							})
							this.params.intermediaries_id = this.mediatorList[0].id
							this.getRoomType()
						})
				} else {
					this.unitList = []
					this.mediatorList = []
				}
			},
			bindChange4(e) {
				this.changeIndex4 = e.detail.value
				this.params.intermediaries_id = this.unitList[this.changeIndex4].id
				this.getRoomType()
			},
			bindChange5(e) {
				this.changeIndex5 = e.detail.value
				this.params.intermediaries_id = this.mediatorList[this.changeIndex5].id
				this.getRoomType()
			},
			bindChange6(e) {
				this.changeIndex6 = e.detail.value
			},
			bindChangePayType(e){
				console.log(e);
				this.changeIndexPayType = e.detail.value
			},
			chooseName(e) {
				this.manList = []
				this.linkman = e.name ? e.name : this.linkman
				this.linkphone = e.phone
				this.manInfo = e
				

			},
			bindTimeChange(e) {
				this.time = e.detail.value
			},
			changeLog(e) {
				this.datetimesingle = e
				this.$iBox
					.http('getDefaultEnterTime', {})({
						method: 'post'
					})
					.then(res => {
						
						if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
					
								if (this.datetimesingle == this.$moment().format('YYYY-MM-DD')) {
									this.time = this.$moment().get('hour')+':' + this.$moment().get('minute');
								} else {
									this.time = this.roomSale.start_time_limit
								}
							console.log('时租房', this.time);
						}else{
							if (this.datetimesingle == this.$moment().format('YYYY-MM-DD')) {
								if (this.$moment().unix() > this.$moment(this.datetimesingle + ' ' + this.time,
										'YYYY-MM-DD HH:mm').unix()) {
									this.time = this.$moment().get('hour') +':' + this.$moment().get('minute');
								} else {
									this.time = res.data
								}
							
							} else {
								this.time = res.data
							}
						}
						
					

						if (this.manInfo) {
							let leave_time = ''
							this.gradeList.filter(item => {
								if (this.manInfo.grade == item.id) {
									item.right_itererest.forEach(item1 => {
										if (item1.sign == 'yctf') {
											leave_time = item1.value
										}
									})
								}
							})

							if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
								this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this
									.dayCounts,
									"hours")).format('YYYY-MM-DD HH:mm:ss')
							} else if (this.roomSale.sign == 'standard') {
								this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this
										.dayCounts,
										"days"))
									.format('YYYY-MM-DD') + ` ${leave_time}:00`
							} else {
								this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this
									.dayCounts,
									"months")).format('YYYY-MM-DD') + ` ${leave_time}:00`
							}

						} else {

							if (this.roomSale.sign == 'hour' || this.roomSale.sign == 'conference_room') {
								this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this
									.dayCounts,
									"hours")).format('YYYY-MM-DD HH:mm:ss')
							} else if (this.roomSale.sign == 'standard') {
								this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this
										.dayCounts,
										"days"))
									.format('YYYY-MM-DD') + ` ${this.leave_time}:00`
							} else {
								this.datetimesingle1 = this.$moment(this.$moment(e, 'YYYY-MM-DD HH:mm:ss').add(this
									.dayCounts,
									"months")).format('YYYY-MM-DD') + ` ${this.leave_time}:00`
							}
						}


						this.params.start_time = this.$moment(this.datetimesingle + ' ' + this.time,
								'YYYY-MM-DD HH:mm').unix(),
							this.params.end_time = this.$moment(this.datetimesingle1).unix()
						this.params.times = this.dayCounts
						this.getRoomType()
					})


			},
			changeLog1(e) {
				this.datetimesingle1 = e
				if (this.roomSale.sign == 'standard') {
					let s = this.$moment(this.datetimesingle.split(' ')[0])
					let e = this.$moment(this.datetimesingle1.split(' ')[0])
					let c = e.diff(s, 'days')
					this.dayCounts = c
				}

				this.params.end_time = this.$moment(this.datetimesingle1).unix()
				this.params.times = this.dayCounts
				this.getRoomType()
			},
			dayChange(e) {
				this.dayCounts = e
				if (this.manInfo) {
					let leave_time = ''
					this.gradeList.filter(item => {
						if (this.manInfo.grade == item.id) {
							item.right_itererest.forEach(item1 => {
								if (item1.sign == 'yctf') {
									leave_time = item1.value
								}
							})
						}
					})
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"days")).format('YYYY-MM-DD') + ` ${leave_time}:00`
				} else {
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"days")).format('YYYY-MM-DD') + ` ${this.leave_time}:00`
				}

				this.params.start_time = this.$moment(this.datetimesingle + ' ' + this.time, 'YYYY-MM-DD HH:mm').unix(),
				this.params.end_time = this.$moment(this.datetimesingle1).unix()
				this.params.times = this.dayCounts
				this.getRoomType()
			},
			monthChange(e) {
				if (this.manInfo) {
					let leave_time = ''
					this.gradeList.filter(item => {
						if (this.manInfo.grade == item.id) {
							item.right_itererest.forEach(item1 => {
								if (item1.sign == 'yctf') {
									leave_time = item1.value
								}
							})
						}
					})
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"months")).format('YYYY-MM-DD') + ` ${leave_time}:00`

				} else {
					this.datetimesingle1 = this.$moment(this.$moment(this.datetimesingle, 'YYYY-MM-DD HH:mm:ss').add(e,
						"months")).format('YYYY-MM-DD') + ` ${this.leave_time}:00`

				}

				this.params.start_time = tthis.$moment(this.datetimesingle + ' ' + this.time, 'YYYY-MM-DD HH:mm').unix(),
					this.params.end_time = this.$moment(this.datetimesingle1).unix()
				this.params.times = this.monthCounts
				this.getRoomType()
			},
			changeDatePrice() {

				this.roomList.forEach(item => {
					if (item.id == this.itemPrice.id) {
						item.room_prices = this.pricesBox
					}
				})
				// console.log(this.roomList);
			},
			dayPrice(e) {
				console.log(e, this.pricesBox);
				this.pricesBox.room_price.forEach(item => {
					item.room_price = e
				})
			},
			selectRoom(e) {
				// console.log(e);
				if (e.roomNumber == 0) {
					return
				}

				this.itemPrice = e
				this.params1.start_time = this.$moment(this.datetimesingle + ' ' + this.time, 'YYYY-MM-DD HH:mm').unix(),
					this.params1.end_time = this.$moment(this.datetimesingle1).unix()
				this.params1.room_sale_type = this.roomSale.id
				this.params1.room_type_id = e.id
				this.params1.intermediaries_id = this.unitList.length > 0 && this.mediatorList.length == 0 ? this.unitList[
					this.changeIndex4].id : (this.unitList.length == 0 && this.mediatorList.length > 0 ? this
					.mediatorList[this.changeIndex5].id : '')
				if (this.priceTypeList[this.changeIndex3].name == '会员') {
					this.params1.grade_id = this.manInfo ? this.manInfo.grade : ''
				}
				this.$iBox.http('selectRoom', this.params1)({
					method: 'post'
				}).then(res => {

					let ids = []
					this.roomList.forEach(item => {
						if (item.id == e.id) {
							if (item.room_list) {
								if (item.room_list.length > 0) {
									item.room_list.forEach(item2 => {

										ids.push(item2.id)
									})
									this.chooseIds = []
									this.chooseRooms = []
									this.selectRoomList = res.data
									let selectRoomList = JSON.parse(JSON.stringify(this.selectRoomList))
									item.room_list.forEach(item3 => {
										console.log(item3, 'item3');
										selectRoomList.forEach(item4 => {
											console.log(item4, 'item4');
											if (item4.building == item3.building_id) {
												console.log(item4, 'item41');
												item4.floor_list.forEach(item1 => {
													if (item1.floor == item3.floor_name) {
														item1.room_list = item1.room_list.filter(
																item2 => {
																	return item2.id !=item3.id
																})
													}
												})
											}
										})
									})

									this.selectRoomList = selectRoomList
									console.log('选择了房间', ids, this.selectRoomList, item.room_list);
								} else {
									this.chooseIds = []
									this.chooseRooms = []
									this.selectRoomList = res.data
								}
							} else {
								item.room_list = []
								this.chooseIds = []
								this.chooseRooms = []
								this.selectRoomList = res.data
							}
						}
					})
					console.log(this.roomList, 'this.roomList');
					this.showRc = true
				})
			},
			closeRc() {
				this.showRc = false
			},
			getRcIds(e) {
				console.log(e, 'sure1', this.roomList);
				this.roomList.forEach(item => {
					if (item.id == this.itemPrice.id) {
						if (item.room_list.length > 0) {
							item.room_list = [...item.room_list, ...e.rooms]
						} else {
							item.room_list = e.rooms
						}
						item.room_list.forEach(item2 => {
							item2.userInfo = []
							item2.userInfo.push({
								name: ''
							})
						})


					}
				})
				console.log(e, 'sure', this.roomList);
			},
			deleteRoom(e) {
				let roomList = JSON.parse(JSON.stringify(this.roomList))
				roomList.forEach(item => {
					if (item.id == e.item.id) {
						item.room_list = item.room_list.filter(item1 => {
							return item1.id != e.item1.id
						})
					}
				})
				this.roomList = roomList
				console.log(this.roomList, 'dd', e);
			},
			chooseCoupon() {
				this.popCoupon = true
			},
			closePopCoupon() {
				this.popCoupon = false
			},
			getInfo(e) {
				console.log(e);
				this.chooseCouponse = e
				this.chooseCouponsePrice = e.discounts
				this.popCoupon = false
			},
			surePay() {
				this.$iBox.throttle1(() => {
					this.booking()
				}, 2000);
			},
			booking() {
				if (!this.linkman) {
					uni.showToast({
						icon: 'none',
						title: '请完善预定人信息'
					})
					return
				}

				let params = {
					memo: this.memo,
					bill_source: this.billSource[this.changeIndex6].id,
					enter_time_plan: this.$moment(this.datetimesingle + ' ' + this.time, 'YYYY-MM-DD HH:mm').unix(),
					price_project: this.priceTypeList[this.changeIndex3].id,
					room_list: [],
					room_sale_type: this.roomSale.id,
					stay_type: this.stayTypeList[this.changeIndex2].id,
					times: this.roomSale.sign == 'conference_room' || this.roomSale.sign == 'hour' ? this.roomSale
						.stay_time : (this.roomSale.sign == 'standard' ? this
							.dayCounts : this.monthCounts),
					user_info: {
						link_man: this.linkman,
						link_phone: this.linkphone,
						common_code: this.priceTypeList[this.changeIndex3].name == '会员' ? this.manInfo.common_code : ''
					},
					intermediary_id: this.unitList.length > 0 && this.mediatorList.length == 0 ? this.unitList[
						this.changeIndex4].id : (this.unitList.length == 0 && this.mediatorList.length > 0 ? this
						.mediatorList[this.changeIndex5].id : ''),
					user_coupon_id: this.chooseCouponse ? this.chooseCouponse.id : '',
					other_bill_code: this.outOrderNum,
					booking_type:this.payType[this.changeIndexPayType].id
					
				}
				let room_list = []
				this.roomList.forEach(item => {
					if (item.roomNumber > 0) {
						let custom_room_price = []
						item.room_prices.room_price.forEach(item1 => {
							let price = {
								date: item1.date,
								price: item1.room_price
							}
							custom_room_price.push(price)
						})
						if (item.room_list && item.room_list.length > 0) {
							item.room_list.forEach(item2 => {
								console.log(item2,'入住人');
								let room = {
									custom_price: {
										custom_cash_pledge: item.room_prices.cash_pledge,
										custom_room_price: custom_room_price,
									},
									room_id: item2.id,
									room_service_selected: item.room_prices.room_service.filter(
										service => {
											return service.checked == true
										})[0].id,
									room_type_id: item.id,
									user_info: item2.userInfo[0].name?item2.userInfo:null
								}
								room_list.push(room)
							})
						} else {
							for (var i = 0; i < item.roomNumber; i++) {
								let room = {
									custom_price: {
										custom_cash_pledge: item.room_prices.cash_pledge,
										custom_room_price: custom_room_price,
									},
									room_id: '',
									room_service_selected: item.room_prices.room_service.filter(service => {
										return service.checked == true
									})[0].id,
									room_type_id: item.id,
									user_info: []
								}
								room_list.push(room)
							}
						}

					}
				})
				params.room_list = room_list

				if (this.roomSale.sign == 'hour') {
					this.$iBox.http('HourbookRoom', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title:'提示',
								content:'预订成功!',
								cancelText:'返回',
								confirmText:'查看订单',
								success:res=>{
									if(res.confirm){
										uni.navigateTo({
											url:'../bill/billList/billList'
										})
									}else{
										uni.navigateBack({})
									}
								}
							})
							
						})
				} else if (this.roomSale.sign == 'standard') {
					this.$iBox.http('bookRoom', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title:'提示',
								content:'预订成功!',
								cancelText:'返回',
								confirmText:'查看订单',
								success:res=>{
									if(res.confirm){
										uni.navigateTo({
											url:'../bill/billList/billList'
										})
									}else{
										uni.navigateBack({})
									}
								}
							})
						})
				} else if (this.roomSale.sign == 'long_standard') {
					this.$iBox.http('LongbookRoom', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title:'提示',
								content:'预订成功!',
								cancelText:'返回',
								confirmText:'查看订单',
								success:res=>{
									if(res.confirm){
										uni.navigateTo({
											url:'../bill/billList/billList'
										})
									}else{
										uni.navigateBack({})
									}
								}
							})
						})
				} else {
					this.$iBox.http('ConferencebookRoom', params)({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title:'提示',
								content:'预订成功!',
								cancelText:'返回',
								confirmText:'查看订单',
								success:res=>{
									if(res.confirm){
										uni.navigateTo({
											url:'../bill/billList/billList'
										})
									}else{
										uni.navigateBack({})
									}
								}
							})
						})
				}

			}

		}
	}
</script>

<style lang="scss" scoped>
	.ordingBox {
		width: 96%;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin: 20rpx auto;

		.title {
			font-size: 36rpx;
			font-weight: 600;
			padding: 20rpx 0;
			display: flex;
			justify-content: space-between;
		}

		.msgBox {
			border-top: 1px solid #c9c9c9;

			.msgItem {
				display: flex;
				align-items: center;
				padding: 20rpx 0;

				.msgInput {
					border: 1px solid #ece8e8;
					height: 36rpx;
					width: 340rpx;
					border-radius: 4px;
					padding: 4px 10px;
					font-size: 30rpx;
					margin-left: 36rpx;
				}

				.pickerBox {
					margin-left: 16rpx;
					position: relative;
					height: 60rpx;
					width: 380rpx;
					border-radius: 14rpx;
					border: 1px solid #eee;
					display: flex;
					padding: 0 20rpx;
					font-size: 30rpx;
					align-items: center;

					.arrow {
						animation-name: to_bottom_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					.arrow_ac {
						animation-name: to_up_show;
						animation-duration: 0.2s;
						animation-timing-function: linear;
						/* animation-delay: 1s; */
						/* animation-iteration-count: infinite; */
						animation-direction: normal;
						animation-play-state: running;
						animation-fill-mode: forwards;
					}

					/* 箭头动画 */

					@keyframes to_up_show {
						0% {
							transform: rotate(0);
						}

						50% {
							transform: rotate(90deg);
						}

						100% {
							transform: rotate(180deg);
						}
					}

					@keyframes to_bottom_show {
						0% {
							transform: rotate(180deg);
							animation-timing-function: cubic-bezier(0.55, 0.055, 0.675, 0.19);
						}

						50% {
							transform: rotate(90deg);
							animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
						}

						100% {
							transform: rotate(0deg);
						}
					}
				}
			}


			.manListBox {
				min-height: 80rpx;
				max-height: 500rpx;
				background: #eee;
				padding: 0 20rpx;
				font-size: 26rpx;

				.infoBox {
					display: flex;
					width: 100%;
					justify-content: space-between;
					align-items: center;
					padding: 20rpx 0;
					border-bottom: 1px solid #c9c9c9;
				}
			}
		}

	}

	.roomBox {
		width: 96%;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin: 20rpx auto;

		.title {
			font-size: 36rpx;
			font-weight: 600;
			padding: 20rpx 0;
		}

		.room {
			width: 100%;
			padding: 20rpx 0;
			border: 1px solid #c9c9c9;
			margin: 10rpx 0;
			border-radius: 10rpx;

			.room_content {
				display: flex;
				align-items: center;
				flex-wrap: wrap;

				.item {
					font-size: 28rpx;
					// width: 33%;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx 0;
					padding: 0 20rpx;
				}

				.item1 {
					font-size: 28rpx;
					width: 100%;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					// justify-content: center;
					padding: 0 20rpx;
					margin: 10rpx 0;
				}

				.item2 {
					font-size: 28rpx;
					width: 500rpx;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin: 10rpx auto;
					padding: 14rpx 0;
					border: 1px solid #eee;
					border-radius: 10rpx;
					font-size: 34rpx;
					font-weight: 600;
				}

				.item3 {
					font-size: 28rpx;
					width: 90%;
					line-height: 40rpx;
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					margin: 10rpx auto;
					padding: 14rpx;
					border: 1px solid #eee;
					border-radius: 10rpx;

					.serviceBox {
						width: fit-content;
						padding: 4rpx;
						border: 1px solid #d7d7d7;
						color: #c9c9c9;
						border-radius: 6rpx;
						font-size: 24rpx;
						margin-right: 10rpx;
					}

					.roomSelcet {
						display: flex;
						flex-direction: column;
						align-items: center;
						position: relative;

						.close {
							position: absolute;
							top: -16rpx;
							right: -16rpx;
							font-size: 30rpx;
							color: #ff0000;
							width: 32rpx;
							height: 32rpx;
						}
					}
				}
			}
		}
	}

	.priceBox {
		width: 96%;
		background-color: #ffffff;
		border-radius: 20rpx;
		padding: 20rpx;
		margin: 20rpx auto;

		.title {
			font-size: 36rpx;
			font-weight: 600;
			padding: 20rpx 0;
		}
	}

	.picker-view_box {
		position: relative;
		height: 650rpx;
		width: 100vw;

		.picker-view {
			height: 600rpx;
			width: 100vw;

			.item {
				width: 100%;
				align-items: center;
				justify-content: center;
				text-align: center;
			}
		}
	}

	.linkBox {
		height: 500rpx;
		width: 100%;
		padding: 30rpx;

		.linkSearch {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: 20rpx;
		}
	}

	.submitContent {
		box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
		position: fixed;
		bottom: 0;
		height: 120rpx;
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 24rpx;
		background: #fff;
		z-index: 3;
	}
</style>
