<template>
	<view class="deviceItemBox">
		<view class=""
			style="height: 100%;width: 690rpx;display: flex;align-items: center;justify-content: center;margin: 0 auto;">
			<image src="http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/kaisuo_1208.png" mode="aspectFill"
				style="height: 260rpx;width: 260rpx;" @click="openLock">
			</image>
		</view>

		<!-- 锁的弹窗 -->
		<m-popup :show="show" @closePop="closePop">
			<view class="popBox">
				<text style="font-size: 36rpx;font-weight: 600;">通行区域:{{lockDetail.lock_alias}}房门锁</text>
				<view class="box_out">
					<view :class="isOpen==1?'box':'box1'">
						<view class="inner_leange">
							<view class="ball">
							</view>
						</view>
					</view>
					<!-- 正在开门 -->
					<text class="icon-kaisuo overImg" style="font-size: 100rpx;" v-if="isOpen==1"></text>

					<!-- 开门完成 -->
					<image src="/static/images/smile.png" mode="aspectFill" class="overImg1" v-if="isOpen==2">
					</image>

					<!-- 开门失败 -->
					<image src="/static/images/nosmile.png" mode="aspectFill" class="overImg1" v-if="isOpen==3">
					</image>
				</view>

				<text v-if="isOpen==1">授权验证中...</text>


				<text v-if="isOpen==2">已为您门锁授权</text>


				<view class="">
					<u-button type="error" v-if="isOpen==3" style="color:#CD1225">请再试一次！</u-button>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				lockDetail: null,
				show: false,
				isOpen: 0, //1代表正在开门2，开门成功 3,error
				isShow: 0, //离线
				authInfo: null
			};
		},
		props: {
			lockInfo: {
				type: Object
			},
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('room', ['hardWareList']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting'])
		},
		mounted() {
			this.startScan()
			this.lockDetail = this.lockInfo
		},
		methods: {
			closePop() {
				this.show = false
			},
			openLock() {
				this.toOpenDoor()
				this.show = true
				this.isOpen = 1
			},
			// 开始扫描附近的智能锁设备// 再判断是哪个平台的锁 ==========================================通通锁====================================
			startScan() {
				/**
				 * 调用蓝牙扫描接口，返回lockDevice 和 lockList对象
				 * 
				 */
				uni.showLoading({
					title: '正在搜索蓝牙设备'
				})
				plugin.startScanBleDevice((lockDevice, lockList) => {

					let a = this.lockDetail
					if (lockList.length > 0) {
						for (let item of lockList) {
							console.log(item, a, 'lock');
							if (item.lockMac == a.lock_mac) {
								this.isShow = 1
								this.toCheckLockTime()
							} else {
								this.isShow = 0
							}

						}
					}
					console.log(a, 'aaa');
					this.lockDetail = a

					uni.hideLoading()
				}, err => {
					console.log(err)
					uni.hideLoading()
					uni.showToast({
						icon: 'none',
						title: err
					})

				})
			},
			// 校准锁时间
			toCheckLockTime(e) {
				const start = Date.now();
				// 建议使用服务器时间
				plugin.setLockTime.setLockTime({
					lockData: this.lockDetail.lock_data,
					serverTime: Date.now(),
				}).then(res => {
					if (res.errorCode === 0) {
						wx.showToast({
							icon: "success",
							title: "锁时间已校准"
						});
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'none',
							title: `校准锁时间失败:${res.errorMsg}`
						})
					}
				});
			},
			// 读取操作记录
			toReadRecord() {
				uni.showLoading({
					title: `正在读取锁内操作记录`,
				})
				// this.lockDetail.lock_data
				const start = Date.now();
				// 获取操作记录
				plugin.getOperationLog({
					/* 读取操作记录方式 1 -全部, 2 -最信 */
					logType: 2,
					lockData: this.lockDetail.lock_data
				}).then(res => {
					uni.hideLoading({});
					console.log(res, '读取最新操作记录')
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'success',
							title: `操作记录已获取--操作时间::${Date.now() - start}`
						})
						this.$iBox.http('uploadOpenRecord', {
								id: this.lockDetail.id,
								records: res.log
							})({
								method: 'post'
							})
							.then(res => {

							})
					} else {
						uni.showToast({
							icon: 'error',
							title: "读取操作记录失败:" + res.errorMsg
						})

					}
				})
			},
			// 点击开锁
			toOpenDoor() {
				this.getWxAuthorizeLocation()
				this.getWxAuthorizeBle()
				uni.showLoading({
					title: '正在开启智能锁,请等待！'
				})
				const start = Date.now();

				plugin.controlLock({
					/* 控制智能锁方式 3 -开锁, 6 -闭锁 */
					controlAction: 3,
					lockData: this.lockDetail.lock_data,
					serverTime: Date.now(),
				}).then(res => {
					if (res.errorCode == 0) {
						wx.showToast({
							icon: "success",
							title: "已开锁"
						});
						wx.hideLoading()
						this.isOpen = 2
						this.toReadRecord();
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'none',
							title: `开锁失败: ${res.errorMsg}`
						})
						this.isOpen = 3
					}
				})
			},

			//===========================================权限验证=================================================

			getWxAuthorizeLocation: function() {
				wx.getSetting({
					success(res) {
						console.log(res);
						// 如果从未申请定位权限，则申请定位权限
						if (res.authSetting['scope.userLocation'] == null) {
							wx.authorize({
								scope: 'scope.userLocation',
								success() {
									// 用户同意
									// 相关操作
								},
								fail() {
									wx.showToast({
										title: '无法申请定位权限,请确认是否已经授权定位权限',
										icon: "none",
										duration: 2000
									})
								}
							})
						}
						// 如果已经有权限，就查询
						else if (res.authSetting['scope.userLocation'] == true) {
							// 相关操作
						}
						// 被拒绝过授权，重新申请
						else {
							wx.showModal({
								title: '位置信息授权',
								content: '位置授权暂未开启，将导致无法正常手机开门',
								cancelText: '仍然拒绝',
								confirmText: '开启授权',
								success: function(res) {
									if (res.confirm) {
										wx.openSetting({
											fail: function() {}
										})
									} else {

									}
								}
							})
						}
					}
				});
			},

			getWxAuthorizeBle: function() {
				uni.getSystemInfo({
					success(res) {
						console.log(res, '蓝牙');
						if (!res.bluetoothEnabled) {
							uni.showModal({
								title: '提示!',
								content: '系统蓝牙未打开，请打开后重试！',
								showCancel: false,
								success: res => {

								}
							})
						}

						if (!res.locationEnabled) {
							uni.showModal({
								title: '提示!',
								content: '手机定位未打开！',
								showCancel: false,
								success: res => {

								}
							})
						}

						if (!res.locationAuthorized) {
							uni.showModal({
								title: '提示!',
								content: '请授权微信使用定位功能!',
								showCancel: false,
								success: res => {

								}
							})
						}

					}
				})
			},
		}

	}
</script>

<style lang="scss" scoped>
	.deviceItemBox {

		// display: flex;
		// align-items: center;
		// justify-content: space-between;
		// flex-wrap: wrap;
		// padding: 0 30rpx;
		// width: 100%;
		.item1 {
			height: 180rpx;
			width: 330rpx;
			margin-top: 30rpx;
			border-radius: 20rpx;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

			&_title {
				padding: 20rpx;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.open {
					width: fit-content;
					padding: 10rpx 16rpx;
					border-radius: 30rpx;
					color: #ffffff;
					// background-color: rgba(0, 0, 0, 0.6);
					font-size: 22rpx;
				}
			}

			&_content {
				padding: 0rpx 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.img_lock {
					height: 80rpx;
					width: 80rpx;
				}

				&_text {
					display: flex;
					flex-direction: column;
				}
			}
		}

		.popBox {
			height: 880rpx;
			padding: 30rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;

			@keyframes spin {

				from {
					transform: rotate(0deg);
				}

				to {
					transform: rotate(360deg);
				}

			}

			@keyframes scbig {

				from {
					transform: scale(0, 0);
				}

				to {
					transform: scale(1, 1);
				}

			}

			.overImg {
				height: 120rpx;
				width: 120rpx;
				position: absolute;
				margin: 0 auto;
				animation: scbig 1s 1;
				// animation-name: scbig;
				// animation-duration: 1s;
				// animation-timing-function: ease;
				// animation-iteration-count: 1;
			}

			.overImg1 {
				height: 100rpx;
				width: 120rpx;
				position: absolute;
				margin: 0 auto;
				animation: scbig 1s 1;
				// animation-name: scbig;
				// animation-duration: 1s;
				// animation-timing-function: ease;
				// animation-iteration-count: 1;
			}

			.box_out {
				height: 320rpx;
				width: 320rpx;
				border-radius: 50%;
				background: #dddddd;
				display: flex;
				align-items: center;
				justify-content: center;

				.box {
					height: 280rpx;
					width: 280rpx;
					border-radius: 50%;
					background-image: radial-gradient(circle, #f4c97f, #ffffc4);
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.inner_leange {
						width: 240rpx;
						height: 240rpx;
						border-radius: 50%;
						border: 2px solid #43413b;
						position: relative;
						animation: spin 3s infinite linear;

						.ball {
							position: absolute;
							left: 64rpx;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background-image: radial-gradient(circle, #5b4f4a, #bec8b1);
						}
					}

				}

				.box1 {
					height: 280rpx;
					width: 280rpx;
					border-radius: 50%;
					background-image: radial-gradient(circle, #f4c97f, #ffffc4);
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.inner_leange {
						width: 240rpx;
						height: 240rpx;
						border-radius: 50%;
						border: 2px solid #43413b;
						position: relative;


						.ball {
							position: absolute;
							left: 64rpx;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background-image: radial-gradient(circle, #5b4f4a, #bec8b1);
						}
					}

				}
			}

		}
	}
</style>