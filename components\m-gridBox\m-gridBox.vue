<template>
	<view v-if="show">
		<view class="m-gride_box" v-if="mode==1">
			<view class="m-gride_box_item" :style="list.length<4?'width:'+100/list.length+'%;height:'+height+'rpx':''" v-for="item in list" :key="item.id" @click="()=>tapGrid(item)">
			<view class="" style="display: flex;flex-direction: column;justify-content:center;height: 100%;position: relative;padding: 20rpx;" :style="list.length==1?'width: 100%':'width:98%'">
				<image class="m-gride_box_item_img" :src="item.icon" mode="aspectFill" style="width: 100%;"></image>
				<text class="m-gride_box_item_title" v-if="list.length>1">{{item.name}}</text>
				<text class="m-gride_box_item_title1" v-if="list.length>1">{{item.content}}</text>
			</view>
				
			</view>
		</view>
		<view class="m-gride_box1" v-if="mode==2">
			<view class="m-gride_box1_item" v-for="item in list" :key="item.id" @click="()=>tapGrid(item)">
				<image class="m-gride_box1_item_img" :style="item.content?'':''" :src="item.icon" ></image>
				<!-- <text class="m-gride_box1_item_title">{{item.name}}</text> -->
				<text class="m-gride_box1_item_title" v-if="item.content">{{item.content}}</text>
			</view>
		</view>
		<view class="m-gride_box2" v-if="mode==3">
			<view class="m-gride_box2_item" >
				<view class="" style="width:345rpx;height:100%;padding-right:5rpx">
					<image class="m-gride_box2_item_img" style="width:100%;height:100%" @click="()=>tapGrid(list[0])" :src="list[0].icon"></image>
				</view>
				
				<view class="" style="width:345rpx;padding-right:5rpx;height:100%;display: flex;flex-direction: column;justify-content: space-between;">
					<view class="" style="width:100%;height:49.5%">
						<image class="m-gride_box2_item_img" style="width:100%;height:100%" @click="()=>tapGrid(list[1])" :src="list[1].icon"></image>
					</view>
					<view class="" style="width:100%;height:49.5%">
						<image class="m-gride_box2_item_img" style="width:100%;height:100%" @click="()=>tapGrid(list[2])" :src="list[2].icon"></image>
					</view>
					
				</view>
				
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-gridBox",
		props: {
			show: {
				type: Boolean,
				default: true
			},
			mode: {
				type: [String, Number],
				default: 1
			},
			height: {
				type: [String, Number],
				default: 300
			},
			grid_list: {
				type: Array,
				default: []
			}
		},
		watch:{
			grid_list : {
				handler(newVal, oldVal){
					this.list = newVal;
				},
				immediate:true,
				deep:true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'startDate', 'endDate', 'unit', 'setting']),
		},
		data() {
			return {
				list:[]
			};
		},
		mounted() {
			
		},
		methods: {
			tapGrid(e) {
				// if (this.userInfo.phone&& this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
				// 		1) {
					console.log(e,'d');
					if (e.type == 'xcx_page') {
						if(e.url == 'pages/myCenter/myCenter'||e.url == 'pages/myRoom/myRoom'||e.url == 'pages/moreTabs/moreTabs'){
							uni.switchTab({
								url:'/' + e.url
							})
						}else {
							uni.navigateTo({
								url: '/' + e.url
							})
						}
					} else if (e.type == 'h5') {
						uni.navigateTo({
							url: '/pages/webView/webView?url=' + e.url
						})
					} else {
						let appid = '';
						let path = '';
						let data = {}
						appid = e.url.split('#')[0]
						if (e.url.split('#').length > 1) {
							let a = e.url.indexOf('#')
							path = e.url.slice(a + 1)
						}
						// #ifdef MP-WEIXIN
						wx.navigateToMiniProgram({
							appId: appid,
							path: path,
							extraData:data,
							success(res) {
								// 打开成功
								console.log(res);
							}
						});
						// #endif
					}
				// } else {
				// 	let set = this.setting.filter(item => {
				// 		return item.sign == 'auto_register_member'
				// 	})
				// 	let a = set[0].property.value
				// 	if (a == 2) {
				// 		uni.navigateTo({
				// 			url: '/pages/login/login'
				// 		})
				
				// 	} else if (a == 1) {
				// 		// this.pop = true
				// 		uni.navigateTo({
				// 			url: '/packageA/memberInfo/memberInfo'
				// 		})
				// 	}
				// }
				
			}
		}
	}
</script>

<style lang="scss" scoped>
	.m-gride_box {
		display: flex;
		justify-content: flex-start;
		padding: 0rpx 20rpx;
		flex-wrap: wrap;
		width: 100%;
		margin: 20rpx 0;
		&_item {
			// margin: 0 20rpx 20rpx 0;
			// width: calc(100%-60rpx)/3;
			width: 33%;
			// 去除第3n个的margin-right
			&:nth-child(3n) {
				margin-right: 0;
			}

		
			border-radius: 20rpx;
			margin-top: 5rpx;
			// background-color: aliceblue;
			// background-image: url('https://s3.bmp.ovh/imgs/2022/02/b4d78758de98d9b4.png');
			box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			color: #FFFFFF;
			// padding:10rpx 30rpx;
			position: relative;

			&_title {
				z-index: 3;
				font-size: 28rpx;
			}

			&_title1 {
				font-size: 22rpx;
				z-index: 3;
			}

			&_img {
				position: absolute;
				top: 0;
				right: 0;
				width: 220rpx;

				height: 100%;
				border-radius: 20rpx;
				// background-color: rgba($color: #000000, $alpha: .4);
			}
		}
	}

	.m-gride_box1 {
		display: flex;
		flex-direction: column;
		padding: 30rpx;
		flex-wrap: wrap;

		&_item {
			width: 700rpx;
			height: 440rpx;
			// height: 120rpx;
			border-radius: 20rpx;
			margin-top: 20rpx;
			// background-image: url('https://s3.bmp.ovh/imgs/2022/02/b4d78758de98d9b4.png');
			// box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
			display: flex;
			flex-direction: column;
			// align-items: center;
			justify-content: center;
			// color: #FFFFFF;
			position: relative;

			&_title {
				z-index: 3;
				font-size: 28rpx;
				left: 10rpx;
				width:100%;
				height: 80rpx;
				padding-top: 10rpx;
				font-weight: 600;
			}
			
			&_img {
				border-radius: 10rpx;
				width: 700rpx;
				height: 360rpx;
				// background-color: rgba($color: #000000, $alpha: .4);
			}
		}
	}
	
	.m-gride_box2 {
		display: flex;
		// flex-direction: column;
		padding: 30rpx;
		justify-content: space-between;
		&_item {
			width: 700rpx;
			height: 440rpx;
			// height: 120rpx;
			border-radius: 20rpx;
			margin-top: 20rpx;
			// background-image: url('https://s3.bmp.ovh/imgs/2022/02/b4d78758de98d9b4.png');
			// box-shadow: rgba(0, 0, 0, 0.05) 0px 1px 2px 0px;
			display: flex;
			// flex-direction: column;
			// align-items: center;
			justify-content: center;
			// color: #FFFFFF;
			position: relative;
			
			&_img {
				border-radius: 10rpx;
				// background-color: rgba($color: #000000, $alpha: .4);
			}
		}
	}
</style>
