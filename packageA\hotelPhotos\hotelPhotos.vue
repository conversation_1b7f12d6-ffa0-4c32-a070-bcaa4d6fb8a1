<template>
	<view>
		<m-tabs :list="titleList" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="change"
			:activeIndex="current_tab" :config="{color:themeColor.text_main_color,
							  fontSize:30,
							  activeColor:themeColor.text_main_color,
							  underLineColor:themeColor.com_color1,
							  underLineWidth:80,
							  underLineHeight:10}">
		</m-tabs>

		<!-- 图片展示 -->
		<view class=""
			style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
			v-if="pic_list.length==0">
			<view class="icon-tupian" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无酒店照片</p>
		</view>
		<view class="flex flex-wrap" style="width: 100vw;min-height: 260rpx;padding: 30rpx;" v-else>
			<view class="" v-if="current_tab==0">
				<view class="" v-for="(item, index) in pic_list" :key="index" style="margin-top: 40rpx;">
					<p style="font-size: 40rpx;font-weight: 600;">{{item.name}}</p>
					<view class="" style="display: flex;flex-wrap: wrap;">
						<view class="flex justify-center align-center" style="width: 50%;height:220rpx;margin-top: 20rpx;"
							v-for="(item1, index1) in item.pics" :key="index1"
							@click="lookImage({item:item.pics,index:index1})">
							<image :src="item1.url" style="width: 96%;height: 100%;border-radius: 20rpx;"
								mode="aspectFill" show-menu-by-longpress></image>
						</view>
					</view>
					
				</view>

			</view>
			<view class="" v-else>
					<view class="" style="display: flex;flex-wrap: wrap;" >
						<view class="flex justify-center align-center" style="width: 50%;height:220rpx;margin-top: 20rpx;" v-for="(item, index) in item_list" :key="index"
							@click="lookImage({item:item_list,index:index})">
							<image :src="item.url" style="width: 96%;height: 100%;border-radius: 20rpx;"
								mode="aspectFill" show-menu-by-longpress></image>
						</view>
					</view>

			</view>

		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				titleList: [{
					id: 1,
					name: '全部'
				}],
				pic_list: [],
				item_list:[],
				current_tab: 0,
				status: 'nomore',
				params: {
					shop_id: 0,
				},
				bool: true,
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting'])
		},
		async onLoad() {
			await this.$onLaunched
			this.pic_list = this.hotel.hotel_pics
			if(this.hotel.hotel_pics.length>0){
				this.hotel.hotel_pics.forEach(item => {
					let pic = {
						id:item.id,
						name:item.name
					}
					this.titleList.push(pic)
				})
				
			}
		
			

		},
		onShow() {

		},
		methods: {
			lookImage(e) {
				console.log(e);
				let urls = []
				e.item.forEach(item=>{
					urls.push(item.url)
				})
				wx.previewImage({
					current: urls[e.index], // 当前显示图片的http链接
					urls: urls // 需要预览的图片http链接列表
				});
			},
			change(index) {
				this.current_tab = index
				console.log(this.pic_list,'this.pic_list');
				if(index!=0){
					this.item_list = this.pic_list[index-1].pics
				}
			}
		},
		/**
		 * 用户分享自定义
		 */
		onShareAppMessage: function(res) {
			// 1.返回节点对象
			let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
			let currentPage = pages[pages.length - 1]; //获取当前页面的对象
			let url = currentPage.route //当前页面url
			//当前页面url
			// menus: ['shareAppMessage', 'shareTimeline']
			if (this.userInfo.distribution_user_id) {
				if (this.hotel.share_setting.image_url) {
					return {
						title: this.hotel.share_setting.title,
						desc: this.hotel.share_setting.desc,
						imageUrl: this.hotel.share_setting.image_url,
						path: url + '?du_id=' + this.userInfo.distribution_user_id + '&type=wxxcx'
					};
				} else {
					return {
						title: this.hotel.share_setting.title,
						desc: this.hotel.share_setting.desc,
						path: url + '?du_id=' + this.userInfo.distribution_user_id + '&type=wxxcx'
					}
				}
			} else {
				if (this.hotel.share_setting.image_url) {
					return {
						title: this.hotel.share_setting.title,
						desc: this.hotel.share_setting.desc,
						imageUrl: this.hotel.share_setting.image_url,
						path: url
					};
				} else {
					return {
						title: this.hotel.share_setting.title,
						desc: this.hotel.share_setting.desc,
						path: url
					}
				}
			}
		}
		// // // 上拉加载
		// onReachBottom() {
		// 	if (this.current_tab == 0) {
		// 		this.params.type = 1

		// 	} else if (this.current_tab == 1) {
		// 		this.params.type = 2

		// 	}
		// 	if (this.bool) {
		// 		++this.params.page
		// 		this.status = 'loadmore'
		// 		this.$iBox.http('getHotelPicList', this.params)({
		// 			method: 'get'
		// 		}).then(res => {
		// 			console.log('我是返回', res.data)
		// 			let new_list = this.item_list.concat(res.data.list)
		// 			this.item_list = new_list
		// 			if (this.item_list.length == res.data.count) {
		// 				this.bool = false
		// 				this.status = 'nomore'
		// 			}
		// 			uni.hideLoading()
		// 		}).catch(function(error) {
		// 			console.log('网络错误', error)
		// 		})
		// 	}

		// }
	}
</script>

<style lang="scss">


</style>