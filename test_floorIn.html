<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>floorIn.vue 功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .section h3 {
            margin-top: 0;
            color: #2979ff;
        }
        .floor-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 10px;
            margin: 20px 0;
        }
        .floor-button {
            height: 40px;
            border: none;
            border-radius: 8px;
            background: #2979ff;
            color: white;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .floor-button:hover {
            background: #1976d2;
            transform: scale(1.05);
        }
        .floor-button.loading {
            background: #ccc;
            cursor: not-allowed;
        }
        .floor-button.success {
            background: #4CAF50;
        }
        .floor-button.error {
            background: #F44336;
        }
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status.info {
            background: #e3f2fd;
            color: #1976d2;
        }
        .status.success {
            background: #e8f5e8;
            color: #4CAF50;
        }
        .status.error {
            background: #ffebee;
            color: #F44336;
        }
        .mode-switch {
            display: flex;
            justify-content: center;
            margin-bottom: 20px;
        }
        .switch-btn {
            padding: 10px 20px;
            margin: 0 5px;
            border: 2px solid #2979ff;
            background: white;
            color: #2979ff;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .switch-btn.active {
            background: #2979ff;
            color: white;
        }
        .qr-section {
            text-align: center;
            padding: 40px;
        }
        .qr-placeholder {
            width: 200px;
            height: 200px;
            border: 2px dashed #ccc;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🏢 电梯楼层控制功能测试</h1>
        
        <div class="section">
            <h3>📱 界面预览</h3>
            <p>这是 floorIn.vue 页面的功能演示</p>
            
            <!-- 模式切换 -->
            <div class="mode-switch">
                <div class="switch-btn active" onclick="switchMode('floor')">🏠 楼层选择</div>
                <div class="switch-btn" onclick="switchMode('qr')">📱 二维码</div>
            </div>
            
            <!-- 楼层选择模式 -->
            <div id="floor-mode">
                <div class="status info">点击楼层按钮，蓝牙自动连接电梯</div>
                
                <div class="floor-grid" id="floor-grid">
                    <!-- 楼层按钮将通过 JavaScript 生成 -->
                </div>
                
                <div id="bluetooth-status" class="status" style="display: none;"></div>
            </div>
            
            <!-- 二维码模式 -->
            <div id="qr-mode" style="display: none;">
                <div class="qr-section">
                    <div class="qr-placeholder">二维码区域</div>
                    <p>*若二维码没生效请点击下方刷新按钮</p>
                    <button onclick="refreshQR()">🔄 刷新</button>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>🔧 功能特性</h3>
            <ul>
                <li>✅ 1-33层楼层按钮网格布局</li>
                <li>✅ 双模式切换（楼层选择 ↔ 二维码）</li>
                <li>✅ 楼层按钮状态管理（正常/加载/成功/失败）</li>
                <li>✅ 蓝牙自动连接和控制</li>
                <li>✅ 实时状态反馈</li>
                <li>✅ 错误处理和用户提示</li>
                <li>✅ 现代化UI设计和动画效果</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>📋 测试说明</h3>
            <ol>
                <li><strong>界面测试</strong>：检查楼层按钮是否正确显示（1-33层）</li>
                <li><strong>模式切换</strong>：测试楼层选择和二维码模式的切换</li>
                <li><strong>按钮交互</strong>：点击楼层按钮查看状态变化</li>
                <li><strong>状态反馈</strong>：观察加载、成功、失败状态的显示</li>
                <li><strong>蓝牙功能</strong>：在真实环境中测试蓝牙连接</li>
            </ol>
        </div>
    </div>

    <script>
        // 生成楼层按钮
        function generateFloorButtons() {
            const grid = document.getElementById('floor-grid');
            grid.innerHTML = '';
            
            for (let i = 1; i <= 33; i++) {
                const button = document.createElement('button');
                button.className = 'floor-button';
                button.textContent = i;
                button.onclick = () => clickFloor(i);
                grid.appendChild(button);
            }
        }
        
        // 模式切换
        function switchMode(mode) {
            const floorMode = document.getElementById('floor-mode');
            const qrMode = document.getElementById('qr-mode');
            const buttons = document.querySelectorAll('.switch-btn');
            
            buttons.forEach(btn => btn.classList.remove('active'));
            
            if (mode === 'floor') {
                floorMode.style.display = 'block';
                qrMode.style.display = 'none';
                buttons[0].classList.add('active');
            } else {
                floorMode.style.display = 'none';
                qrMode.style.display = 'block';
                buttons[1].classList.add('active');
            }
        }
        
        // 楼层点击处理
        function clickFloor(floor) {
            const button = document.querySelector(`button:nth-child(${floor})`);
            const status = document.getElementById('bluetooth-status');
            
            // 重置所有按钮状态
            document.querySelectorAll('.floor-button').forEach(btn => {
                btn.className = 'floor-button';
            });
            
            // 设置当前按钮为加载状态
            button.classList.add('loading');
            button.textContent = '...';
            
            // 显示状态信息
            status.style.display = 'block';
            status.className = 'status info';
            status.textContent = `正在连接电梯，目标楼层：${floor}层...`;
            
            // 模拟蓝牙连接过程
            setTimeout(() => {
                const success = Math.random() > 0.3; // 70% 成功率
                
                if (success) {
                    button.classList.remove('loading');
                    button.classList.add('success');
                    button.textContent = '✓';
                    status.className = 'status success';
                    status.textContent = `电梯已启动，前往 ${floor} 层！`;
                } else {
                    button.classList.remove('loading');
                    button.classList.add('error');
                    button.textContent = '✗';
                    status.className = 'status error';
                    status.textContent = `连接失败，请重试或使用二维码模式`;
                }
                
                // 2秒后重置状态
                setTimeout(() => {
                    button.className = 'floor-button';
                    button.textContent = floor;
                    status.style.display = 'none';
                }, 2000);
            }, 2000);
        }
        
        // 刷新二维码
        function refreshQR() {
            alert('二维码已刷新！');
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateFloorButtons();
        });
    </script>
</body>
</html>
