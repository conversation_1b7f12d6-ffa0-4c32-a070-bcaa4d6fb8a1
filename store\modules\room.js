import Vue from 'vue'

const state = {
	lockDetail:null,
	type:null,
	hardWareList:[],
	roomInfo:null,
	billDetail:null,
	roomBillUser:[],
	autoCode:''
}

const getters = {
	
}

const mutations = {
	// 自定义tabbar栏
	PUSHLOCKDATA: (state, lockDetail) => {
		state.lockDetail = lockDetail
		console.log(lockDetail,'lockDetail');
	},
	PUSHTYPE: (state, type) => {
		state.type = type
		
	},
	PUSHHARDWARE:  (state, hardWareList) => {
		state.hardWareList = hardWareList
	},
	PUSHROOMINFO:  (state, roomInfo) => {
		console.log(roomInfo,'type_roomInfo');
		state.roomInfo = roomInfo
	},
	PUSHBILLDETAIL:  (state, billDetail) => {
		state.billDetail = billDetail
	},
	PUSHROOMBILLUSER:  (state, roomBillUser) => {
		state.roomBillUser = roomBillUser
	},
	PUSHAUTOCODE:  (state, autoCode) => {
		state.autoCode = autoCode
	},
}

const actions = {
	getLockData({
		commit
	}, params) {
		commit('PUSHLOCKDATA', params)
	},
	getType({
		commit
	}, params) {
		commit('PUSHTYPE', params)
	},
	getHardWareList({
		commit
	}, params) {
		commit('PUSHHARDWARE', params)
	},
	
	getRoomInfo({
		commit
	}, params) {
		commit('PUSHROOMINFO', params)
	},
	
	getBillDetail({
		commit
	}, params) {
		commit('PUSHBILLDETAIL', params)
	},
	
	getRoomBillUser({
		commit
	}, params) {
		commit('PUSHROOMBILLUSER', params)
	},
	
	getAutoCode({
		commit
	}, params) {
		commit('PUSHAUTOCODE', params)
	},
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}
