<template>
	<view>
		<view class="elseItem">
			<view class="head"  @click="recharge" v-if="styleMode.show_user_qr">
				<view class="t1">
					<text class="icon-qianbao" style="font-size: 40rpx;color: #9B9DA3;"></text>
					<text style="font-size: 32rpx;margin-left: 12rpx;">我的钱包</text>
				</view>
				<view class="t2" :style="{color:themeColor.text_main_color}">
					<text style="font-size: 32rpx;">充值</text>
					<view class="icon-jiantou" style="font-size: 32rpx;"></view>
					
				</view>
			</view>
			<view class="body">
				<view class="bodyItem" @click="recharge">
					<text style="font-size: 32rpx;" :style="{color:themeColor.text_main_color}">￥{{userInfo.balance?userInfo.balance:0}}</text>
					<text style="font-size: 28rpx;" :style="{color:themeColor.text_main_color}">会员余额</text>
				</view>
				<view class="bodyItem" @click="point">
					<text style="font-size: 32rpx;" :style="{color:themeColor.text_main_color}">{{userInfo.point?userInfo.point:0}}</text>
					<text style="font-size: 28rpx;" :style="{color:themeColor.text_main_color}">积分</text>
				</view>
				<view class="bodyItem" @click="myCoupon">
					<text style="font-size: 32rpx;"
						:style="{color:themeColor.text_main_color}">{{userInfo.coupons&&userInfo.coupons.length>0?userInfo.coupons.length:0}}张</text>
					<text style="font-size: 28rpx;" :style="{color:themeColor.text_main_color}">券包</text>
				</view>
				<view class="" style="height: 100%;background-color: #EBEBEB;">
					
				</view>
				<view class="" @click="toPayCode" style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
					<view class="icon-erweima" style="font-size: 86rpx;color: #7C86B2;">
						
					</view>
					<text style="color: #7C86B2;font-size: 30rpx;">会员码</text>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {

			};
		},
		props:{
			styleMode:{
				type:Object
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		methods: {
			toPayCode() {
				//是否是会员

				if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
					1) {
					uni.navigateTo({
						url: '/pages/payCode/payCode'
					})
				} else {
					uni.showToast({
						icon: 'none',
						title: '请先注册会员！'
					})
					return;
				}

			},
			recharge() {
				//是否是会员

				if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
					1) {
					uni.navigateTo({
						url: '/pages/recharge/recharge'
					})

				} else {
					uni.showToast({
						icon: 'none',
						title: '请先注册会员！'
					})
					return;
				}
			},
			point() {
				uni.navigateTo({
					url: '/pages/pointList/pointList'
				})
			},
			myCoupon() {
				uni.navigateTo({
					url: '/packageA/myCoupon/myCoupon'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.elseItem {
		width: 700rpx;
		min-height: 268rpx;
		border-radius: 20rpx;
		margin: 60rpx auto;
		background-color: #FFFFFF;

		.head {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			display: flex;
			align-items: center;

			.t1 {
				padding: 32rpx;
				display: flex;
				align-items: center;
			}

			.t2 {
				padding: 0 30rpx;
				display: flex;
				align-items: center;
			}
		}

		.body {
			display: flex;
			align-items: center;
			padding: 0 30rpx;
			border-bottom: 1px solid #EBEDF0;
			
			.bodyItem {
				padding: 30rpx 0;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-between;
				height: 160rpx;
				flex: 1;
				font-size: 24rpx;
			}
		}
	}
</style>
