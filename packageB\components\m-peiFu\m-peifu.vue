<template>
	<view>
		<view class="" style="position: fixed;top: 0;height: 14vhx;background-color: #FFFFFF;width: 100%;">
			<view class="" style="width: 720rpx;margin: 10rpx auto;display: flex;align-items: center;">
				<uni-easyinput prefixIcon="search" v-model="value" placeholder="模糊搜索" @input="search">
				</uni-easyinput>
				<!-- <view class="" style="width: fit-content;">
					更多筛选
				</view> -->
				<view class="" @click="addMore"
					style="margin: 0 34rpx;width: 170rpx;height: 60rpx;display: flex;align-items: center;justify-content: center;background-color: #00aa7f;color: #FFFFFF;border-radius: 8rpx;">
					添加赔付
				</view>
			</view>
			<m-tabs :list="list" style="position: sticky;top: 0;width: 100%;z-index: 99;" @tabClick="tab_click"
				:activeIndex="index" :config="{color:themeColor.main_color,
							  fontSize:30,
							  activeColor:themeColor.main_color,
							  underLineColor:themeColor.main_color,
							  underLineWidth:80,
							  underLineHeight:10}">

			</m-tabs>
		</view>
		<view class="" style="height: 14vh;">

		</view>
		<scroll-view scroll-y="true" style="height: 86vh;width: 100%;" @scrolltolower="refresh">
			<view class="" v-for="item in billList"
				:style="{background:themeColor.main_color+'22',border: '1px solid '+themeColor.main_color}"
				style="margin: 30rpx auto;border-radius: 32rpx;min-height: 556rpx;width: 710rpx;padding:12rpx 30rpx;">
				<p class="textStyle">id：{{item.id}}</p>
				<p class="textStyle">物品：{{item.goods_name?item.goods_name:''}}</p>
				<p class="textStyle">客房订单ID：{{item.room_bill_id?item.room_bill_id:''}}</p>
				<p class="textStyle">房间号：{{item.room_number?item.room_number:''}}</p>
				<p class="textStyle">分类：{{item.goods_type_name?item.goods_type_name:''}}</p>
				<view class="" style="display: flex;align-items: center;justify-content: space-between;">
					<p class="textStyle">单价：{{item.goods_price?item.goods_price:0}}元</p>
					<p class="textStyle">数量：{{item.goods_count?item.goods_count:0}}</p>
				</view>
				<view class="" style="display: flex;align-items: center;justify-content: space-between;">
					<p class="textStyle">联系人：{{item.user_name?item.user_name:''}}</p>
					<p class="textStyle">联系电话：{{item.user_phone?item.user_phone:''}}</p>
				</view>
			
				<p class="textStyle">赔偿金额：{{item.bill_amount?item.bill_amount:0}}元</p>
				<p class="textStyle">备注：{{item.remark?item.remark:''}}</p>
				<p class="textStyle">状态：{{item.status==0?'待赔偿':(item.status==1?'已赔偿':'已取消')}}</p>
				<p class="textStyle">创建时间：{{item.create_time?item.create_time:'' | moment1}}</p>
				<p class="textStyle">更新时间：{{item.update_time?item.update_time:'' | moment1}}</p>
				<view class=""
					style="margin-top: 10rpx;display: flex;align-items: center;width: 100%;justify-content: flex-end;">
				<!-- 	<view class="" @click="showPay"
						style="width: 200rpx;height: 60rpx;border-radius: 8rpx;display: flex;align-items: center;justify-content: center;color: #FFFFFF;"
						:style="{background:themeColor.main_color}">
						收银
					</view> -->
					
					<view class="" @click="cancleGood(item)"
						style="width: 200rpx;height: 60rpx;border-radius: 8rpx;display: flex;align-items: center;justify-content: center;background-color: #ff0000;color: #FFFFFF;">
						取消
					</view>
				</view>
			</view>
			<view class="" style="height: 100rpx;">
			
			</view>
		</scroll-view>
		
		
		<m-popup :show="pop" mode="center" @closePop="closePop">
			<view class="" style="height: 60vh;width: 710rpx;border-radius: 32rpx;padding: 30rpx;position: relative;">
				<view class="" style="display: flex;align-items: center;width: 100%;">
					<text>物品分类：</text>
					<view class="" style="width: 500rpx;">
						<uni-data-select v-model="goodType" :localdata="range" @change="change"></uni-data-select>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;width: 100%;margin-top: 24rpx;">
					<text style="color: #ff0000;">*</text>
					<text>物品：</text>
					<view class="" style="width: 500rpx;">
						<uni-data-select v-model="good" :localdata="range1" @change="change1"></uni-data-select>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;width: 100%;margin-top: 24rpx;">
					<text style="color: #ff0000;">*</text>
					<text>数量：</text>
					<view class="" style="width: 500rpx;">
						<uni-easyinput v-model="number" placeholder="请输入物品数量" type="digit"></uni-easyinput>
					</view>
				</view>
				<!-- <view class="" style="display: flex;align-items: center;width: 100%;margin-top: 24rpx;">
					<text style="color: #ff0000;">*</text>
					<text>联系人：</text>
					<view class="" style="width: 500rpx;">
						<uni-easyinput v-model="link_name" placeholder="请输入联系人" type="text"></uni-easyinput>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;width: 100%;margin-top: 24rpx;">
					<text style="color: #ff0000;">*</text>
					<text>联系人电话：</text>
					<view class="" style="width: 400rpx;">
						<uni-easyinput v-model="link_phone" placeholder="请输入联系人电话" type="number"></uni-easyinput>
					</view>
				</view> -->
				<view class="" style="display: flex;align-items: center;width: 100%;margin-top: 24rpx;">
					<text>备注：</text>
					<view class="" style="width: 500rpx;">
						<uni-easyinput v-model="remark" placeholder="请输入备注" type="text"></uni-easyinput>
					</view>
				</view>
				<view class=""
					style="position: absolute;bottom: 10rpx;height: 120rpx;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="toSure"
						style="width: 600rpx;height: 80rpx;background-color: #00aa7f;color: #FFFFFF;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;">
						提交
					</view>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list: [{
					name: '全部',
					status: ''
				}, {
					name: '待赔付',
					status: 0
				}, {
					name: '已赔付',
					status: 1
				}, {
					name: '已取消',
					status: 2
				}],
				params: {
					goods_id: "",
					goods_type_id: "",
					limit: 10,
					page: 1,
					room_bill_id: null,
					room_number: null,
					search_word: null,
					status: ""
				},
				index: 0,
				value: '',
				billList: [],
				pop: false,
				goodType: '',
				range: [],
				good: '',
				range1: [],
				number: '',
				link_name: '',
				link_phone: '',
				remark: '',
				payList: [],
				selfList: [],
				payName: 0,
				paySelfName: 0,
				vipCard: null,
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager'])
		},
		props: {
			billId: {
				type: [String, Number],
				default: ''
			}
		},
		watch: {
			

		},
		async mounted() {
			await this.$onLaunched;
			this.params.page = 1
			this.params.room_bill_id = this.billId
			this.bool = true
			this.getList()
			this.$iBox.http('getLoseGoodsTypeList', {
				page: 1,
				limit: 1000
			})({
				method: 'post'
			}).then(res => {
				let list = []
				res.data.list.forEach(item => {
					let a = {
						value: '',
						text: ''
					}
					a.text = item.lose_type_name
					a.value = item.id
					list.push(a)
				})

				this.range = list

			})


		},
		methods: {
			search() {
				this.params.search_word = this.vale
				this.$iBox.debounce(() => {
					this.getList()
				}, 2000)
			},
			addMore() {
				this.pop = true
			},
			toSure() {
				
				if(!this.goodType){
					uni.showToast({
						icon:'none',
						title:'请选择类别'
					})
					return
				}
				
				if(!this.good){
					uni.showToast({
						icon:'none',
						title:'请选择物品'
					})
					return
				}
				
				if(!this.number){
					uni.showToast({
						icon:'none',
						title:'请填写数量'
					})
					return
				}
				
				this.$iBox.http('addLostBill', {
					goods_count:Number(this.number),
					goods_id: this.good,
					goods_type_id: this.goodType,
					user_name: this.link_name,
					user_phone: this.link_phone,
					room_bill_id: this.billId,
					remark: this.remark
				})({
					method: 'post'
				}).then(res => {
					this.pop = false
					this.params.page = 1
					this.bool = true
					this.getList()
				})
			},
			cancleGood(){
				this.$iBox.http('cancelLoseBill', {
					bill_id: this.billId
				})({
					method: 'post'
				}).then(res => {
					this.pop = false
					this.params.page = 1
					this.bool = true
					this.getList()
				})
			},
			change() {
				console.log(this.goodType);
				this.$iBox.http('getLoseGoodsList', {
					page: 1,
					limit: 1000,
					type_id: this.goodType
				})({
					method: 'post'
				}).then(res1 => {
					let list = []
					res1.data.list.forEach(item => {
						let a = {
							value: '',
							text: ''
						}
						a.text = item.goods_name
						a.value = item.id
						list.push(a)
					})

					this.range1 = list

				})
			},
			change1() {
				console.log(this.good);
			},
			closePop() {
				this.pop = false
			},
			getList() {
				this.$iBox.http('getLoseBillList', this.params)({
					method: 'post'
				}).then(res => {
					this.list[0].tip = res.data.tips.tip_all
					this.list[1].tip = res.data.tips.tip_0
					this.list[2].tip = res.data.tips.tip_1
					this.list[3].tip = res.data.tips.tip_2

					this.billList = res.data.list

				})
			},
			tab_click(e) {
				this.params.page = 1
				this.bool = true

				this.params.status = this.list[e].status
				console.log(this.list[e].status, this.params.status);
				this.getList()
			},
			closePopPay(){
				this.poprc = false
			},
			refresh(){
				if (this.bool) {
					++this.params.page
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getLoseBillList', this.params)({
						method: 'post'
					}).then(res => {
						this.list[0].tip = res.data.tips.tip_all
						this.list[1].tip = res.data.tips.tip_0
						this.list[2].tip = res.data.tips.tip_1
						this.list[3].tip = res.data.tips.tip_2
						let new_list = this.billList.concat(res.data.list)
						this.billList = new_list
						if (this.billList.length == res.data.count) {
							this.bool = false
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}
			}
		},
		
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}

	page {
		background-color: #FFFFFF;
	}
</style>
<style scoped lang="scss">
	.textStyle {
		padding: 7rpx 0;
		color: #222222;
	}
	
	.payBox {
		height: 94vh;
		width: 680rpx;
		padding: 10rpx;
	
		.itemBox {
			width: 98%;
			background: #f6f6f6;
			margin: 0 auto;
			padding: 20rpx 10rpx;
	
			.pickerBox {
				position: relative;
				height: 60rpx;
				width: 380rpx;
				border-radius: 14rpx;
				border: 1px solid #eee;
				display: flex;
				margin-top: 14rpx;
				font-size: 30rpx;
				align-items: center;
				padding: 0 10rpx;
			}
		}
	}
	
	.btnClass {
		width: 400rpx;
		height: 80rpx;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		// border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		background-color: #00aa7f;
		color: #FFFFFF;
		margin: 10rpx auto;
		display: flex;
		align-items: center;
		justify-content: center;
		position: absolute;
		bottom: 20rpx;
		left: 0;
		right: 0;
		margin: 0 auto;
	}
</style>