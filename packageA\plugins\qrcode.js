var Base64 = require('./Base64.js');
var crc16 = require('./crc.js').crc16;

var qrcode1 = {
    SETTING_PREFIX:"fc",
    /**
     * 低位在前 高位在后
     * @param value
     * @returns {Array}
     */
    intToBytes:function (value, n) {
        var src = [];
        src[3] = ((value >> 24) & 0xFF).toString(16);
        src[2] = ((value >> 16) & 0xFF).toString(16);
        src[1] = ((value >> 8) & 0xFF).toString(16);
        src[0] = (value & 0xFF).toString(16);
        return src;
    },

    // 高位在前低位在后
    intToBytes2:function(value) {
        var src = [];
        src[0] = ((value >> 24) & 0xFF).toString(16);
        src[1] = ((value >> 16) & 0xFF).toString(16);
        src[2] = ((value >> 8) & 0xFF).toString(16);
        src[3] = (value & 0xFF).toString(16);
        return src;
    },

    base64Encode:function(input) {
        var rv;
        rv = encodeURIComponent(input);
        rv = unescape(rv);
        rv = Base64.btoa(rv);
        return rv;
    },

    base64Decode:function(input) {
        var rv = Base64.atob(input);
        var rv = escape(rv);
        var rv = decodeURIComponent(rv);
        return rv;
    },

    isString:function(obj){ //判断对象是否是字符串
        return Object.prototype.toString.call(obj) === "[object String]";
    },

    isContains:function(sn,lifts){
        var result=false;
        if (lifts && lifts.floors) {
            var floors=lifts.floors;
            if(this.isString(floors)){
                floors = JSON.parse(floors);
            }
            for(var i=0;i<floors.length;i++){
                if(floors[i].sn==sn){
                    result=true;
                    break;
                }
            }
        }
        return result;
    },

    /**
     * 该方法用于产生动态二维码
     * @param userType 用户类型，普通用户为u,管理用户为a
     * @param startTime 开始时间，日期类型
     * @param validTime 有效时间，单位为分钟，整数类型
     * @param userId 用户ID,整数类型
     * @param lifts 梯控权限，没有梯控则传空数组
     * @param doors 门禁列表，门禁ID的数组，如 [32449,32450,34451]表示二维码门禁设备编号为32449,32450,34451可以开门
     * @returns {{cmds: Array, encrypt: *}} 返回值是一个JSON数据，其中cmds为二进制数据，encrypt为加密后的字符串，该字符串可以用于生成二维码
     */
    generateAccessCode: function (userType,startTime,validTime,userId,lifts,doors) {
        var _this = this;
        // 当前时间戳
		
        var timestamp = Math.floor(startTime.getTime() / 1000);
        // 高位在前 低位在后
        var timestampBytes = _this.intToBytes2(timestamp);
        // 有效时间
        var valid_time = parseInt(validTime).toString(16);
        // 用户编码
        var code = _this.intToBytes2(userId);
        // 是否直达
        var direct_arrival = "0";
        if (lifts && lifts.direct_arrival == 1) {
            direct_arrival = "1";
        }
        var all_lift="0";
		console.log(lifts,'liost')
        if (lifts && lifts.all_lift == 1) {
            all_lift = "1";
        }
        // 梯控权限 sn(3)+floor(5)
        var liftAccesses = [];
        if (doors && doors.length) {
            for(var i=0;i<doors.length;i++){
                var sn=doors[i];
                // 判断doors 与lifts sn是否有相同的，如有重复则去掉door中相同的sn权限
                if (!_this.isContains(sn,lifts)) {
                    // sn 转3位字节数字, 去掉高位的第一个字节 0
                    var snBytes = _this.intToBytes(sn);
                    snBytes.pop();
                    liftAccesses = liftAccesses.concat(snBytes);
                    if(userType!="a") {
                        for (var j = 0; j < 5; j++) {
                            var val = "0";
                            liftAccesses.push(val);
                        }
                    }
                }
            }
        }

        if (lifts && lifts.floors) {
            var floors=lifts.floors;
            if(_this.isString(floors)){
                floors = JSON.parse(floors);
            }
            for(var i=0;i<floors.length;i++){
                var lift=floors[i];
                var sn = lift.sn;
                var floorAccesses = lift.floors;
				
                if(floorAccesses){
                    if(_this.isString(floorAccesses)){
                        floorAccesses = JSON.parse(floorAccesses);
                    }
                }else{
                    floorAccesses=[];
                }
				console.log(lifts, 'floorAccesses')
                // sn 转3位字节数字, 去掉高位的第一个字节 0
                var snBytes = _this.intToBytes(sn);
                snBytes.pop();
                liftAccesses = liftAccesses.concat(snBytes);
				
                if(userType!="a") {
                    for (var j = 0; j < 5; j++) {
                        var val = "0";
                        if (floorAccesses.length > j) {
                            var floor = floorAccesses[j];
							
                            val = parseInt(floor.floor).toString(16);
							
                        }
                        liftAccesses.push(val);
                    }
					console.log(liftAccesses,'floor');
                }
            }
        }

        // 最多9个电梯门禁权限
        if(userType=="a") {
            if (liftAccesses.length > 8 * 9) {
                liftAccesses.length = 72
            }
        }else{
            if (liftAccesses.length > 3 * 24) {
                liftAccesses.length = 72
            }
        }

        if(all_lift=="1"&&userType=="a"){
            liftAccesses=[];
        }
        // 指令集合
        var cmds = [];
        // 开始指令 1个字节
        if(userType=="a"){
            cmds.push("fc"); //员工卡
        }else{
            cmds.push("fa"); //普通用户卡
        }
        // 数据长度 1个字节
        cmds.push("0");
        // 当前时间戳 4个字节
        cmds = cmds.concat(timestampBytes);
        // 有效时间(分钟) 1个字节
        cmds.push(valid_time);
        // 用户编码
        cmds = cmds.concat(code);
		
        if(userType=="a"){
            cmds.push(all_lift);
        }else{
            cmds.push(direct_arrival);
        }
		console.log(cmds, 'cms',liftAccesses);
        cmds = cmds.concat(liftAccesses);
        // 数据长度
        var len = cmds.length - 2;
        cmds[1] = len.toString(16);
        var ret = this.generateCode(cmds);;
        return {cmds: cmds, encrypt: ret};
    },
    
	getCurrentSecret:function(){
	    return ["1","2","3","4"];	
	},
	
    addVerifyBytes:function(cmds){
        var cmdsWithSecret=cmds.concat(this.getCurrentSecret());  //将设备的加密密码加进去，默认为["1","2","3","4"]
        var intBytes = [];
        for(var i=0;i<cmdsWithSecret.length;i++){
            intBytes.push(parseInt(cmdsWithSecret[i],16));
        } //将字符串数组转换成整数数组

        var vertify = crc16(intBytes);  //整数数组通过CRC16产生校验位
        vertify = parseInt(vertify, 16) & 0xffff; //校验位只取后面两个字节

        var vertifyBytes = [];
        vertifyBytes[0] = (vertify >> 8).toString(16); 
        vertifyBytes[1] = (vertify & 0xff).toString(16);  //获得校验位的两个字节字符串

        // console.log(vertifyBytes);
        // cmds 校验位
        cmds = cmds.concat(vertifyBytes); //将校验位合并到字符串数组中
        return cmds;
    },

    convertToCmds:function(code){
        var cmds = [];
        if(code&&code.length>0){
            var index=0;
            while(index<code.length){
                var thisValue=null;
                if(code.length-index>=2){
                    thisValue=code.substring(index,index+2);
                }else{
                    thisValue=code.substring(index);
                }
                cmds.push(thisValue);
                index=index+2;
            }
        }
        return cmds;
    },

    reviseToCmds:function(code){
        var cmds = [];
        if(code&&code.length>0){
            var index=0;
            while(index<code.length){
                var thisValue=null;
                if(code.length-index>=2){
                    thisValue=code.substring(index,index+2);
                }else{
                    thisValue=code.substring(index);
                }
                cmds.splice(0,0,thisValue);
                index=index+2;
            }
        }
        return cmds;
    },

    generateCode:function(cmds){
        cmds=this.addVerifyBytes(cmds);
        console.log(cmds);
        var code=this.base64Encode(cmds);
        console.log(code);
        return code;
    },
    generateCancelAllBlack:function(){
        var code="fd080155";
        var cmds=this.convertToCmds(code);
        return this.generateCode(cmds);
    },

    generateTimeCorrect:function(){
        var cmds=[];
        cmds.push("fd");
        cmds.push("07");
        cmds.push("04");
        var timestamp = Math.floor(Date.now() / 1000);
        // 高位在前 低位在后
        var timestampBytes = this.intToBytes2(timestamp);
        cmds=cmds.concat(timestampBytes);
        return this.generateCode(cmds);
    },

    generateBlackCode: function (flag,list,type) {
        var cmds=[];
        cmds.push("fd");
        cmds.push(flag);
        cmds.push(parseInt(5*list.length).toString(16));
        for(var i=0;i<list.length;i++){
            var cardNo=list[i];
            cmds.push(type);
            cmds=cmds.concat(this.reviseToCmds(cardNo));
            if(type=="01"){
                cmds.push("00");
                cmds.push("00");
            }
        }
        return this.generateCode(cmds);
    },

    generateCancelBlackCode: function (list,type) {
        return this.generateBlackCode("0A",list,type);
    },

    generateAddBlackCode: function (list,type) {
        return this.generateBlackCode("09",list,type);
    },

    generateIcSecretCode:function(password){
        var cmds=[];
        cmds.push("fd");
        cmds.push("02");
        cmds.push("04");
        cmds=cmds.concat(password.split(","));
        return this.generateCode(cmds);
    },

    generateSettingParameter:function(flag,value){
        var cmds=[];
        cmds.push(flag?"1":"0");
        cmds.push(parseInt(value).toString(16));
        return cmds;
    },

    generateSettingTime:function(time){
        var values=time.split(":");
        return values;
    },

    generateWeekLimit:function(week){
        var cmds = [];
        if(week&&week.length>0){
            for(var i=0;i<week.length;i++){
                var thisValue=week.substring(i,i+1);
                cmds.push(thisValue);
            }
        }
        return cmds;
    },

    generateLiftSettingCode:function(setting,flags){
        var cmds=[];
        cmds.push("fd");
        cmds.push("aa");
        cmds.push(parseInt(28).toString(16));
        cmds=cmds.concat(this.generateSettingParameter(flags.liftDelay,setting.floor_delay));
        cmds=cmds.concat(this.generateSettingParameter(flags.directDelay,setting.direct_delay));
        cmds=cmds.concat(this.generateSettingParameter(flags.liftCallFloor,setting.call_floor));
        cmds=cmds.concat(this.generateSettingParameter(flags.cardRepeat,setting.repeat_readcard_time));
        cmds=cmds.concat(this.generateSettingParameter(flags.callTime,setting.chainspeek_time));
        cmds=cmds.concat(this.generateSettingParameter(flags.guangouTime,setting.guangou_time));
        cmds.push(flags.startLiftControl?"1":"0");
        cmds.push(flags.stopLiftControl?"1":"0");
        cmds.push(flags.autoControl?"1":"0");
        cmds.push(setting.auto_ctl+""); //0 启动自动控制 1关闭自动控制
        cmds.push(setting.in_period+""); //0 时段内受控 1时段外受控

        cmds=cmds.concat(this.generateSettingTime(setting.begin_time));
        cmds=cmds.concat(this.generateSettingTime(setting.end_time));
        cmds=cmds.concat(this.generateWeekLimit(setting.weekLimit));
        return this.generateCode(cmds);
    },
};

export default qrcode1

