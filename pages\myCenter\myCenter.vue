<template>
	<view :style="{background:themeColor.bg1_color}">
		<view class="" :style="{background:themeColor.bg1_color}">
			<view class="" :key="index" v-for="(item, index) in diyModel">
				<vipInfo v-if="item.sign=='mine_member'" :modeStyle="item" :mode="item.property.style" :if_buy="ifBuy"></vipInfo>
				<balanceCard v-if="item.sign=='mine_user_info'" :mode="item.property.style" :styleMode="item.property"></balanceCard>
				<hotelOrder v-if="item.sign=='mine_room_bill'" :mode="item.property.style" :list="item"></hotelOrder>
				<otherOrder v-if="item.sign=='mine_other_bill'" :mode="item.property.style" :list="item"></otherOrder>
				<gridBox v-if="item.sign=='mine_other'" :mode="item.property.style" :list="item"></gridBox>
				<fanxian v-if="item.sign=='booking_share'"></fanxian>
			</view>
		<!-- 	<view class="" @click="toTeam">
				测试
			</view> -->
		
			<!-- 管理员身份卡片 -->
			<view class="admin-identity-card" v-if="userInfo.is_boss">
				<view class="card-background">
					<view class="card-pattern"></view>
					<view class="card-shine"></view>
				</view>

				<view class="card-content">
					<!-- 管理员标识 -->
					<view class="admin-badge">
						<view class="badge-icon">👑</view>
						<text class="badge-text">管理员</text>
					</view>

					<!-- 主要信息 -->
					<view class="admin-info">
						<view class="admin-title">酒店管理者</view>
						<view class="admin-subtitle">您拥有酒店管理权限</view>
					</view>

					<!-- 功能按钮组 -->
					<view class="admin-actions">
						<view class="action-btn primary" @click="goBoss">
							<view class="btn-icon">🏨</view>
							<text class="btn-text">管理端</text>
						</view>
						<view class="action-btn secondary" @click="showAdminFeatures">
							<view class="btn-icon">⚙️</view>
							<text class="btn-text">权限</text>
						</view>
					</view>

					<!-- 快捷功能 -->
					<view class="quick-features">
						<view class="feature-item" @click="goRoomStatus">
							<view class="feature-icon">🏠</view>
							<text class="feature-text">房态</text>
						</view>
						<view class="feature-item" @click="goOrderManage">
							<view class="feature-icon">📋</view>
							<text class="feature-text">订单</text>
						</view>
						<view class="feature-item" @click="goDataCenter">
							<view class="feature-icon">📊</view>
							<text class="feature-text">数据</text>
						</view>
					</view>
				</view>
			</view>

			<bossBox v-if="false"></bossBox>
		</view>
		<view style="margin-bottom: 120rpx;">
			<p class="right">小程序版本号:V3.3.89</p>
		</view>
		<!-- 安全底部 -->
		<view class="" style="height: 140rpx;" :style="{background:themeColor.bg1_color}"></view>
	
		<m-tabbar :list="tabbar"></m-tabbar>
	</view>
</template>

<script>
	import vipInfo from './components/vipInfo.vue'
	import balanceCard from './components/balanceCard.vue'
	import hotelOrder from './components/hotelOrder.vue'
	import otherOrder from './components/otherOrder.vue'
	import gridBox from './components/gridBox.vue'
	import bossBox from './components/bossBox.vue'
	import fanxian from './components/fanxian.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				diyModel:[],
				ifBuy:false,
				navBarHeight:''
			}
		},
		components:{
			vipInfo,
			balanceCard,
			hotelOrder,
			otherOrder,
			gridBox,
			bossBox,
			fanxian
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel','setting'])
		},
		async onShow() {
			await this.$onLaunched;
			// 每次进入个人中心要更新用户信息
			const systemInfo = wx.getSystemInfoSync();
			// 胶囊按钮位置信息
			const menuButtonInfo = wx.getMenuButtonBoundingClientRect();
			// 导航栏高度 = 状态栏高度 + 44
			this.navBarHeight = systemInfo.statusBarHeight + 44;
			uni.showLoading({
				title: '加载中...'
			})
			this.$iBox.http('getUserInfo', {
				simple:false
			})({
				method: 'post'
			}).then(res => {
				let userInfo = res.data
				userInfo.session_key =  this.userInfo.session_key
				// 移除自动弹窗逻辑，改为卡片展示
				// this.is_boss = this.userInfo.is_boss
				this.updateUserInfo(userInfo)
				uni.hideLoading()
			})
			
			// 购买等级
			this.$iBox.http('getMemberGradeBuySetting', {})({
				method: 'post'
			}).then(res => { 
				if(res.data.length > 0){
					this.ifBuy = true
				}else {
					this.ifBuy = false
				}
				
			})
			// 获取页面UI
			this.pageUI()
		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			pageUI(e){
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getHomePageUi', {
					path: 'pages/myCenter/myCenter',
					shop_id:this.hotel.id
				})({
					method: 'post'
				}).then(res => {
					this.diyModel = res.data
					uni.hideLoading()
					console.log(this.diyModel);
				})
			},
			goBoss(){
				uni.navigateTo({
					url:'/packageA/manager/manager'
				})
			},

			// 显示管理员功能详情
			showAdminFeatures(){
				uni.showModal({
					title: '管理员权限',
					content: '您拥有酒店完整管理权限，包括：\n• 房态管理\n• 订单处理\n• 数据统计\n• 用户管理\n• 系统设置',
					showCancel: false,
					confirmText: '我知道了'
				})
			},

			// 快捷跳转房态管理
			goRoomStatus(){
				uni.navigateTo({
					url: '/packageA/manager/roomStatus/roomStatus'
				})
			},
			toTeam(){
				uni.reLaunch({
					url:'/packageB/teamCheckIn/teamCheckIn?t=2309&&s=994'
				})
			},

			// 快捷跳转订单管理
			goOrderManage(){
				uni.navigateTo({
					url: '/packageA/manager/bill/billList/billList'
				})
			},

			// 快捷跳转数据中心
			goDataCenter(){
				uni.navigateTo({
					url: '/packageA/manager/hotelData/hotelData'
				})
			}
			
		},
	
	}
</script>

<style scoped lang="scss">
	.box{
		// width: 750rpx;
		// height: 260rpx;
		
		// background: #FFFFFF;
		// box-shadow: rgba(33, 35, 38, 0.1) 0px 10px 10px -10px;
		// margin: 30rpx auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 30rpx;
		
		.inner_box{
			display: flex;
			justify-content: center;
			align-items: center;
			height: 60rpx;
			width: 100%;
		}
		
		.inner_box1 {
			display: flex;
			justify-content: flex-start;
			align-items: center;
			height: 140rpx;
			width: 100%;
			.box1_bt{
				display: flex;
				flex-direction: column;
				align-items: flex-start;
				justify-content: space-around;
				color: #303133;
				font-size: 26rpx;
				line-height: 40rpx;
				padding-left: 20rpx;
			}
		}

	}
	
	
	.inner_box2 {
		min-height: 120rpx;
		// width: 100%;
		padding: 20rpx;
		display: flex;
		margin-top: 30rpx;
		flex-direction: column;
		justify-content: space-between;
		.box2_hz{
			display: flex;
			align-items: center;
			flex-wrap: wrap;
			margin-top: 20rpx;
			color: #606266;
			// min-height: 60rpx;
			.box2_tx {
				padding-right: 22rpx;
			}
			.tagClass{
				padding-right: 16rpx;
				margin-top: 8rpx;
				display: flex;
				flex-wrap: wrap;
			}
		}
	}
	
	.inner_box3 {
		min-height: 60rpx;
		// display: flex;
		align-items: center;
		flex-wrap: wrap;
		// width: 100%;
		padding: 20rpx;
		.tagSt{
			display: flex;
			align-items: center;
		}
	}
	
	.inner_box4{
		min-height: 260rpx;
		display: flex;
		flex-direction: column;
		padding: 30rpx;
		align-items: center;
		justify-content: space-around;
	}
	
	.right {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		font-size: 22rpx;
		color: #c0c4cc;
		width: 100vw;

		text-align: center;
		padding-left: 30rpx;
		padding-right: 30rpx;
	}

	/* 管理员身份卡片样式 */
	.admin-identity-card {
		margin: 30rpx;
		height: 280rpx;
		border-radius: 25rpx;
		position: relative;
		overflow: hidden;
		box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.15);
	}

	.card-background {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	.card-pattern {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background-image:
			radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx),
			radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 2rpx, transparent 2rpx);
		background-size: 50rpx 50rpx;
	}

	.card-shine {
		position: absolute;
		top: -50%;
		left: -50%;
		width: 200%;
		height: 200%;
		background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
		animation: shine 4s infinite;
	}

	@keyframes shine {
		0% {
			transform: translateX(-100%) translateY(-100%) rotate(45deg);
		}
		100% {
			transform: translateX(100%) translateY(100%) rotate(45deg);
		}
	}

	.card-content {
		position: relative;
		z-index: 2;
		padding: 25rpx 30rpx;
		height: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
	}

	.admin-badge {
		display: flex;
		align-items: center;
		width: fit-content;
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10rpx);
		border-radius: 25rpx;
		padding: 8rpx 16rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.3);
	}

	.badge-icon {
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.badge-text {
		font-size: 22rpx;
		color: #fff;
		font-weight: bold;
	}

	.admin-info {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
		margin: 20rpx 0;
	}

	.admin-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #fff;
		text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
		margin-bottom: 8rpx;
	}

	.admin-subtitle {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.admin-actions {
		display: flex;
		gap: 15rpx;
		margin-bottom: 20rpx;
	}

	.action-btn {
		flex: 1;
		height: 60rpx;
		border-radius: 30rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
	}

	.action-btn.primary {
		background: rgba(255, 255, 255, 0.9);
		backdrop-filter: blur(10rpx);
	}

	.action-btn.secondary {
		background: rgba(255, 255, 255, 0.2);
		backdrop-filter: blur(10rpx);
		border: 1rpx solid rgba(255, 255, 255, 0.3);
	}

	.action-btn:active {
		transform: scale(0.95);
	}

	.btn-icon {
		font-size: 24rpx;
		margin-right: 8rpx;
	}

	.btn-text {
		font-size: 26rpx;
		font-weight: 500;
	}

	.action-btn.primary .btn-text {
		color: #333;
	}

	.action-btn.secondary .btn-text {
		color: #fff;
	}

	.quick-features {
		display: flex;
		justify-content: space-around;
	}

	.feature-item {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 10rpx;
		border-radius: 15rpx;
		background: rgba(255, 255, 255, 0.1);
		backdrop-filter: blur(5rpx);
		min-width: 80rpx;
		transition: all 0.3s ease;
	}

	.feature-item:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.2);
	}

	.feature-icon {
		font-size: 28rpx;
		margin-bottom: 5rpx;
	}

	.feature-text {
		font-size: 20rpx;
		color: #fff;
		font-weight: 500;
	}
</style>
