<template>
	<view>
		<view class="" style="height: 160rpx;width:710rpx;border-radius: 20rpx;background: linear-gradient(296.97deg, #FDF8FF 23.9%, #DDB6FF 74.96%);
border: 1px solid #F2DEFF;margin: 20rpx auto;padding: 20rpx;position: relative;" @click="toShare">
			<view class="" style="display: flex;align-items: center;">
				<uni-icons type="gift" color="#aa557f" size="30"></uni-icons>
				<p>会员返现</p>
			</view>
			<p style="font-size: 30rpx;">分享好友入住，获得返现红包!</p>
			<view class=""
				style="display: flex;flex-direction: column;align-items: center;position: relative;margin-top: 40rpx;">
				<view class=""  style="position: absolute;right: 20rpx;bottom: 40rpx;height: 60rpx;width: fit-content;padding: 0 20rpx;border: 1px solid #aa557f;display: flex;align-items: center;justify-content: center;color: #aa557f;font-size: 30rpx;">
					立即邀请
				</view>
				<!-- <button class="shareBtn"
					style="position: absolute;bottom: 0;right: 0;width: 100%;height: 100%;z-index: 999099;opacity: 0;"
					id="shareBtn" open-type="share" type="primary">
			
				</button> -->
			</view>
			
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'setting']),
		},
		methods: {
			toShare(){
				if (this.userInfo.phone&& this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
						1) {
					
					uni.navigateTo({
						url:'/packageB/active/activeFanxian'
					})
				} else {
					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					let a = set[0].property.value
					if (a == 2) {
						uni.navigateTo({
							url: '/pages/login/login'
						})
				
					} else if (a == 1) {
						// this.pop = true
						uni.navigateTo({
							url: '/packageA/memberInfo/memberInfo'
						})
					}
				}
				
			}
		}
		
	}
</script>

<style scoped lang="scss">
 view {
	 box-sizing: border-box;
 }
 
 .sharebtn {
 	position: absolute;
 	bottom: 0;
 	right: 0;
 	width: 100%;
 	height: 40%;
 	opacity: 0;
 	z-index: 99999;
 }
</style>
