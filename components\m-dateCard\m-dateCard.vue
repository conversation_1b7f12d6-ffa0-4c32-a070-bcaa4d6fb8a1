<template>
	<view v-if="show">
		<!-- 模式1 -->
		<view class="m-dataCard" @click="chooseDate" v-if="mode==1"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="date_box" v-if="unit=='standard'">
				<view class="date_box_start">
					<text :style="{color:themeColor.text_second_color}"
						style="font-size: 24rpx;">{{startWeek_show}}</text>
					<text style="font-size: 40rpx;font-weight: 500;">{{startDate_show}}</text>
				</view>
				<view class="date_box_count" :style="{background:themeColor.bg_color}">
					<text>共{{countDay_show}}晚</text>
				</view>
				<view class="date_box_end">
					<text :style="{color:themeColor.text_second_color}"
						style="font-size: 24rpx;">{{endWeek_show}}</text>
					<text style="font-size: 40rpx;font-weight: 500;">{{endDate_show}}</text>
				</view>
			</view>
			<!-- unit==hour为时租房 conference_room会议室-->
			<view class="date_box_hour" v-if="unit=='hour'||unit=='conference_room'">
				<view class="date_box_hour_start">
					<text :style="{color:themeColor.text_second_color}"
						style="font-size: 24rpx;">{{startWeek_show}}</text>
					<text style="font-size: 40rpx;font-weight: 500;">{{startDate_show}}</text>
				</view>
			</view>
			<!-- unit=long_standard月租房 -->
			<view class="date_box_month" v-if="unit=='long_standard'">
				<view class="date_box_month_start">
					<text :style="{color:themeColor.text_second_color}"
						style="font-size: 24rpx;">{{startWeek_show}}</text>
					<text style="font-size: 40rpx;font-weight: 500;">{{startDate_show}}</text>
				</view>
		
			</view>

		</view>

		<!-- 模式2 -->
		<view class="m-dataCard1" @click="chooseDate" v-if="mode==2"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="date_box1" v-if="unit=='standard'">
				<view class="date_box1_content">
					<view class="date_box1_content_start">
						<text style="font-size: 32rpx;font-weight: 500;">{{startDate_show}}</text>
						<text style="font-size: 24rpx;"
							:style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
					<view class="date_box1_content_border">
						<text class="icon-hr" style="font-size: 60rpx;"
							:style="{color:themeColor.text_second_color}"></text>
					</view>
					<view class="date_box1_content_end">
						<text style="font-size: 32rpx;font-weight: 500;">{{endDate_show}}</text>
						<text style="font-size: 24rpx;"
							:style="{color:themeColor.text_title_color}">{{endWeek_show}}</text>
					</view>
				</view>
				<view class="date_box1_days">
					<text :style="{color:themeColor.text_second_color}">共{{countDay_show}}天</text>
					<text class="icon-jiantou" style="font-size: 30rpx;"></text>
				</view>
			</view>
			<!-- 时租 -->
			<view class="date_box1_hour" v-if="unit=='hour'||unit=='conference_room'">
				<view class="date_box1_hour_content">
					<view class="date_box1_hour_content_start">
						<text style="font-size: 36rpx;font-weight: 500;">{{startDate_show}}</text>
						<text style="font-size: 24rpx;"
							:style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
				</view>
			</view>
			<!-- 月租 -->
			<view class="date_box1_month" v-if="unit=='long_standard'">
				<view class="date_box1_month_content">
					<view class="date_box1_month_content_start">
						<text style="font-size: 36rpx;font-weight: 500;">{{startDate_show}}</text>
						<text style="font-size: 24rpx;"
							:style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
					<!-- <view class="date_box1_month_content_border">
						<text class="icon-hr" style="font-size: 60rpx;"
							:style="{color:themeColor.text_title2_color}"></text>
					</view>
					<view class="date_box1_month_content_end">
						<text style="font-size: 36rpx;font-weight: 500;">{{endDate_show}}</text>
						<text style="font-size: 24rpx;"
							:style="{color:themeColor.text_title_color}">{{endWeek_show}}</text>
					</view> -->
				</view>
				<!-- <view class="date_box1_month_days">
					<text :style="{color:themeColor.text_second_color}">共{{countDay_show}}晚</text>
					<text class="icon-jiantou" style="font-size: 30rpx;"></text>
				</view> -->
			</view>
		</view>

		<!-- 模式3 -->
		<view class="m-dataCard2" v-if="mode==3" @click="chooseDate"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="date_box2" v-if="unit=='standard'">
				<view class="date_box2_box">
					<view class="date_box2_box_start">
						<text class="start" style="font-size: 40rpx;" :style="{color:themeColor.text_main_color}">{{startDate_show}}</text>
						<text class="start1" :style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
					<view class="date_box2_box_border">
						<text class="icon-hr" style="font-size: 50rpx;"
							:style="{color:themeColor.text_main_color}"></text>
					</view>
					<view class="date_box2_box_end">
						<text class="end" style="font-size: 40rpx;" :style="{color:themeColor.text_main_color}">{{endDate_show}}</text>
						<text class="end1" :style="{color:themeColor.text_title_color}">{{endWeek_show}}</text>
					</view>
				</view>
				<view class="date_box2_count">
					<text :style="{color:themeColor.text_second_color}">共{{countDay_show}}天</text>
					<!-- <text class="icon-jiantou" style="font-size: 30rpx;"></text> -->
				</view>
			</view>

			<!-- 时租 -->
			<view class="date_box2_hour" v-if="unit=='hour'||unit=='conference_room'">
				<view class="date_box2_hour_box">
					<view class="date_box2_hour_box_start">
						<text class="start" :style="{color:themeColor.text_main_color}">{{startDate_show}}</text>
						<text class="start1" :style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
				</view>
			</view>

			<!-- 月租 -->
			<view class="date_box2_month" v-if="unit=='long_standard'">
				<view class="date_box2_month_box">
					<view class="date_box2_month_box_start">
						<text class="start" :style="{color:themeColor.text_main_color}">{{startDate_show}}</text>
						<text class="start1" :style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
					<!-- <view class="date_box2_month_box_border">
						<text class="icon-hr" style="font-size: 50rpx;"
							:style="{color:themeColor.text_title2_color}"></text>
					</view>
					<view class="date_box2_month_box_end">
						<text class="end" :style="{color:themeColor.text_main_color}">{{endDate_show}}</text>
						<text class="end1" :style="{color:themeColor.text_title_color}">{{endWeek_show}}</text>
					</view> -->
				</view>
				<!-- <view class="date_box2_month_count">
					<text :style="{color:themeColor.text_second_color}">共{{countDay_show}}晚</text>
					<text class="icon-jiantou" style="font-size: 30rpx;"></text>
				</view> -->

			</view>
		</view>


		<!-- 模式4 -->
		<view class="m-dataCard3" v-if="mode==4" @click="chooseDate"
			:style="{background:themeColor.bg_color,color:themeColor.text_main_color,border:'1px solid '+themeColor.border_color}">
			<view class="date_box3" v-if="unit=='standard'"
				:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
				<view class="date_box3_box">
					<view class="date_box3_box_start">
						<text class="start" :style="{color:themeColor.text_main_color}">{{startDate_show}}</text>
						<text class="start1" :style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
					<view class="date_box3_box_border">
						<text class="icon-hr" style="font-size: 50rpx;"
							:style="{color:themeColor.text_main_color}"></text>
					</view>
					<view class="date_box3_box_end">
						<text class="end" :style="{color:themeColor.text_main_color}">{{endDate_show}}</text>
						<text class="end1" :style="{color:themeColor.text_title_color}">{{endWeek_show}}</text>
					</view>
				</view>
				<view class="date_box3_count">
					<text :style="{color:themeColor.text_second_color}">共{{countDay_show}}天</text>
					<text class="icon-jiantou" style="font-size: 30rpx;"></text>
				</view>
			</view>

			<!-- 时租 -->
			<view class="date_box3_hour" v-if="unit=='hour'||unit=='conference_room'"
				:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
				<view class="date_box3_hour_box">
					<view class="date_box3_hour_box_start">
						<text class="start" :style="{color:themeColor.text_main_color}">{{startDate_show}}</text>
						<text class="start1" :style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
				</view>
			</view>

			<!-- 月租 -->
			<view class="date_box3_month" v-if="unit=='long_standard'"
				:style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
				<view class="date_box3_month_box">
					<view class="date_box3_month_box_start">
						<text class="start" :style="{color:themeColor.text_main_color}">{{startDate_show}}</text>
						<text class="start1" :style="{color:themeColor.text_title_color}">{{startWeek_show}}</text>
					</view>
					<!-- <view class="date_box3_month_box_border">
						<text class="icon-hr" style="font-size: 50rpx;"
							:style="{color:themeColor.text_title2_color}"></text>
					</view>
					<view class="date_box3_month_box_end">
						<text class="end" :style="{color:themeColor.text_main_color}">{{endDate_show}}</text>
						<text class="end1" :style="{color:themeColor.text_title_color}">{{endWeek_show}}</text>
					</view> -->
				</view>
				<!-- <view class="date_box3_month_count">
					<text :style="{color:themeColor.text_second_color}">共{{countDay_show}}晚</text>
					<text class="icon-jiantou" style="font-size: 30rpx;"></text>
				</view> -->
			</view>
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-dateCard",
		props: {
			show: {
				type: Boolean,
				default: true
			},
			mode: {
				type: [String, Number],
				default: 1
			},
			type: {
				type: [String, Number],
				default: 2
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['unit', 'startDate', 'endDate']),
			startDate_show() {
				//正常双日期情况：如果是凌晨显示时间按照昨天-今天的，其他月租房和钟点房则正常
				//有两种情况：1，默认进入如果是凌晨会赋予开始时间为昨天结束时间为今天，2，日历选择凌晨开始时间选择昨天，结束时间为今天
				//首先判断当前时间是否大于开始时间就代表的是选择的凌晨
				if (this.$moment().startOf('day').unix() > this.$moment(this.startDate).format('x') * 1) {
					return '凌晨入住'
				} else {
					return this.$moment.unix(this.startDate).format('MM月DD日')
				}
			},
			endDate_show() {
				return this.$moment.unix(this.endDate).format('MM月DD日')
			},
			countDay_show() {
				let s = this.$moment.unix(this.startDate)
				let e = this.$moment.unix(this.endDate)

				let c = 0
				if (this.startDate == this.endDate) {
					c = 1
				} else {
					c = e.diff(s, 'days')
				}
				return c
			},
			startWeek_show() {
				let sw = this.$moment.unix(this.startDate).weekday()
				let w = ''
				//首先判断当前时间是否大于选择时间是就代表选择的昨天就是凌晨算法
				if (this.$moment().startOf('day').unix() > parseInt(this.$moment(this.startDate).format('x'))) {
					return w = '今日'
				} else {
					let week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
					w = week[sw]
				}

				return w
			},
			endWeek_show() {
				let sw = this.$moment.unix(this.endDate).weekday()
				let w = ''
				//首先判断当前时间是否大于选择时间是就代表选择的昨天就是凌晨算法
				if (this.$moment().startOf('day').unix() > parseInt(this.$moment(this.startDate).format('x'))) {
					return w = '今日'
				} else {
					if (sw == this.$moment().add(1, 'day').weekday()) {
						w = '明日'
					} else {
						let week = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
						w = week[sw]
					}
				}

				return w
			},
		},
		data() {
			return {
				lastDay:7
			};
		},
		watch: {},
		created() {

		},
		methods: {
			...mapActions('hotel', ['getChooseDate']),
			chooseDate() {
				if (this.unit == 'standard') {
					uni.navigateTo({
						url: '/pages/chooseDate/chooseDate?startDate=' + this.$moment.unix(this.startDate).format(
							'YYYY/MM/DD') + '&endDate=' + this.$moment.unix(this.endDate).format(
							'YYYY/MM/DD') + '&type=2'
					})
				} else if (this.unit == 'hour' || this.unit == 'conference_room') {
					uni.navigateTo({
						url: '/pages/chooseDate/chooseDate?startDate=' + this.$moment.unix(this.startDate)
							.format('YYYY/MM/DD') + '&type=1'
					})

				} else { //月租房
					uni.navigateTo({
						url: '/pages/chooseDate/chooseDate?startDate=' + this.$moment.unix(this.startDate)
							.format('YYYY/MM/DD') + '&type=1'
					})
				}
				
				// else {
				// 	// 长租房有最短日数限制
				// 	uni.navigateTo({
				// 		url: '/pages/chooseDate/chooseDate?startDate=' + this.$moment.unix(this.startDate).format(
				// 			'YYYY/MM/DD') + '&type=3&countDays='+ this.lastDay
				// 	})
				// }

			}
		}
	}
</script>

<style scoped lang="scss">
	.m-dataCard {
		// 全日房的样式
		// width: 750rpx;
		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		// margin: 0 auto;
		// border-radius: 20rpx;
		padding: 20rpx 0;
		.date_box {
			min-height: 120rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			border-radius: 40rpx;

			&_start {
				display: flex;
				flex-direction: column;
			}

			&_count {
				padding: 20rpx;
				height: 50rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
			}

			&_end {
				display: flex;
				flex-direction: column;
			}
		}

		// 钟点房的样式
		.date_box_hour {
			height: 120rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			padding: 0 30rpx;
			border-radius: 40rpx;

			&_start {
				display: flex;
				flex-direction: column;
			}
		}

		// 月租房
		// 钟点房的样式
		.date_box_month {
			height: 120rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			border-radius: 40rpx;

			&_start {
				display: flex;
				flex-direction: column;
			}

			&_count {
				padding: 20rpx;
				height: 50rpx;
				border-radius: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 24rpx;
			}

			&_end {
				display: flex;
				flex-direction: column;
			}
		}
	}

	.m-dataCard1 {
		padding: 20rpx 0;
		// width: 750rpx;
		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		// margin: 0 auto;
		// border-radius: 20rpx;
		// 全日房
		.date_box1 {
			height: 120rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 40rpx;
			padding: 0 30rpx;

			&_content {
				width: fit-content;
				display: flex;
				align-items: center;
				padding: 30rpx;
				font-size: 40rpx;

				&_border {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					margin-top: -26rpx;
					padding: 0 16rpx;
					height: 100%;
				}

				&_start {
					display: flex;
					flex-direction: column;
					align-items: center;
				}

				&_end {
					display: flex;
					flex-direction: column;
					align-items: center;
				}
			}

			&_days {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				font-size: 28rpx;
				padding: 30rpx;
			}
		}

		// 时租房
		.date_box1_hour {
			height: 120rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			border-radius: 40rpx;
			padding: 0 30rpx;

			&_content {
				width: 70%;
				display: flex;
				align-items: center;
				padding: 30rpx;
				font-size: 40rpx;

				&_border {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					margin-top: -26rpx;
					padding: 0 16rpx;
					height: 100%;
				}

				&_start {
					display: flex;
					flex-direction: column;
					align-items: center;
				}

			}
		}

		// 月租房
		.date_box1_month {
			height: 120rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 40rpx;
			padding: 0 30rpx;

			&_content {
				width: 70%;
				display: flex;
				align-items: center;
				padding: 30rpx;
				font-size: 40rpx;

				&_border {
					display: flex;
					align-items: center;
					justify-content: flex-start;
					margin-top: -26rpx;
					padding: 0 16rpx;
					height: 100%;
				}

				&_start {
					display: flex;
					flex-direction: column;
					align-items: center;
				}

				&_end {
					display: flex;
					flex-direction: column;
					align-items: center;
				}

			}
		}
	}

	.m-dataCard2 {

		// width: 750rpx;
		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		// margin: 0 auto;
		// border-radius: 20rpx;
		width: 100%;
		padding: 20rpx 0;
		.date_box2 {
			height: 100rpx;
			width: 100%;
			margin: 0rpx auto;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 40rpx;

			&_box {
				display: flex;
				
				&_start {
					display: flex;
					align-items: center;
					width: fit-content;
					.start {
						font-size: 30rpx;
						font-weight: 500;
						width: fit-content;
					}

					.start1 {
						font-size: 24rpx;
						padding-left: 6rpx;
						width: fit-content;
					}
				}

				&_border {
					padding: 0 6rpx;
				}

				&_end {
					display: flex;
					align-items: center;
					width: fit-content;
					.end {
						font-size: 30rpx;
						font-weight: 500;
						width: fit-content;
					}

					.end1 {
						font-size: 24rpx;
						padding-left: 6rpx;
						width: fit-content;
					}
				}
			}

			&_count {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				font-size: 26rpx;
				width: fit-content;
			}
		}

		.date_box2_hour {
			height: 100rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			border-radius: 40rpx;
			padding: 0 30rpx;

			&_box {
				display: flex;

				&_start {
					.start {
						font-size: 30rpx;
						font-weight: 500;
					}

					.start1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}
			}
		}

		.date_box2_month {
			height: 100rpx;
			width: 700rpx;
			margin: 0rpx auto;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 40rpx;

			&_box {
				display: flex;

				&_start {
					display: flex;
					align-items: center;

					.start {
						font-size: 30rpx;
						font-weight: 500;
					}

					.start1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}

				&_border {
					padding: 0 6rpx;
				}

				&_end {
					display: flex;
					align-items: center;

					.end {
						font-size: 30rpx;
						font-weight: 500;
					}

					.end1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}
			}

			&_count {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				font-size: 26rpx;
			}
		}
	}

	.m-dataCard3 {

		// width: 750rpx;
		padding: 20rpx 0;
		.date_box3 {
			height: 100rpx;
			width: 700rpx;
			padding: 0 30rpx;
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 20rpx;
			// background-color: #f2f5fa;

			&_box {
				display: flex;
				align-items: center;

				&_start {
					display: flex;
					align-items: center;

					.start {
						font-size: 34rpx;
						font-weight: 500;
					}

					.start1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}

				&_border {
					padding: 0 6rpx;
				}

				&_end {
					display: flex;
					align-items: center;

					.end {
						font-size: 34rpx;
						font-weight: 500;
					}

					.end1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}
			}

			&_count {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				font-size: 26rpx;
			}
		}

		.date_box3_hour {
			height: 100rpx;
			width: 700rpx;
			margin: 0rpx auto;
			display: flex;
			align-items: center;
			// justify-content: space-between;
			border-radius: 20rpx;
			padding: 0 30rpx;
			// background-color: #f2f5fa;

			&_box {
				display: flex;

				&_start {
					.start {
						font-size: 30rpx;
						font-weight: 500;
					}

					.start1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}
			}
		}

		.date_box3_month {
			height: 100rpx;
			width: 700rpx;
			margin: 0rpx auto;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			border-radius: 20rpx;
			justify-content: space-between;
			// background-color: #f2f5fa;

			&_box {
				display: flex;
				align-items: center;

				&_start {
					display: flex;
					align-items: center;

					.start {
						font-size: 30rpx;
						font-weight: 500;
					}

					.start1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}

				&_border {
					padding: 0 6rpx;
				}

				&_end {
					display: flex;
					align-items: center;

					.end {
						font-size: 30rpx;
						font-weight: 500;
					}

					.end1 {
						font-size: 24rpx;
						padding-left: 6rpx;
					}
				}
			}

			&_count {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				font-size: 26rpx;
			}
		}
	}
</style>
