<template>
	<view class="coupon-container">
		<!-- 早餐券主体 -->
		<view class="coupon-card" v-for="(item,index) in couponList" :key="index">
			<!-- 左侧价格区 -->
			<view class="price-section">
				<text class="currency">¥</text>
				<text class="price">{{item.amount}}</text>
			</view>

			<!-- 右侧信息区 -->
			<view class="info-section">
				<text class="title">{{item.name}}</text>
				<text class="time">{{item.content}}</text>
				<text class="time">使用时间：{{item.start_time}} - {{item.end_time}}</text>
				<view class="tags">
					<text class="tag" v-if="item.remark">{{item.remark}}</text>
				</view>
			</view>

			<!-- 领取按钮 -->
			<view class="action-section" @click="getCoupon(item)">
				<text :class="item.isReceived ? 'received-btn' : 'get-btn'">
					{{item.isReceived ? '已购买' : '立即购买'}}
				</text>
			</view>

			<!-- 锯齿装饰线 -->
			<view class="sawtooth"></view>

			<view class=""
				style="position: fixed;bottom:20rpx;height: 100rpx;display: flex;align-items: center;justify-content: center;left: 0;right: 0;">
				<view class="" @click="toLook"
					style="width: 600rpx;height: 80rpx;border-radius: 32rpx;display: flex;align-items: center;justify-content: center;background: #55aa7f;">
					<text style="color: #FFFFFF;">查看已购买优惠券</text>
				</view>
			</view>
		</view>
		<m-popup :show="pop" @closePop="closePop">
			<m-payCard @toPay="payFor" :payType="payList" v-if="hackReset"></m-payCard>
		</m-popup>

		<m-popup :show="pop1" mode="center" @closePop="closePop1">
			<view class=""
				style="width: 700rpx;height: 70vh;display: flex;align-items: center;flex-direction: column;justify-content: space-around;padding: 30rpx 0;">
				<scroll-view scroll-y="true" style="height: 55vh;">
					<p style="margin-bottom: 30rpx;padding: 0 30rpx;" v-if="couponUserList.length>0">
						查询到{{couponUserList.length}}张早餐券<text v-if="ifShow">,点击使用自动核销</text></p>
					<view class="" style="width: 100%;padding: 0 30rpx;" v-if="couponUserList.length>0">
						<view class="" v-for="(item, index) in couponUserList" @click="choose(item)"
							style="height: 120rpx;border: 1px solid #e5e5e5;border-radius: 8rpx;margin-bottom: 20rpx;padding: 0 20rpx;display: flex;align-items: center;justify-content: space-between;">
							<view class="" style="display: flex;align-items: center;">
								<img src="http://doc.hanwuxi.cn/wp-content/uploads/2025/05/zaocan.png"
									style="height: 80rpx;width: 80rpx;" alt="" />
									<view class="" style="display: flex;flex-direction: column;margin-left: 20rpx;">
										<text style="">{{item.content}}</text>
										<text style="font-size: 26rpx;color: #00000099;">使用日期:{{item.date}}</text>
									</view>
								
							</view>

							<view class=""
								style="width: 50rpx;height: 50rpx;background-color: #e5e5e5;border-radius: 50%;display: flex;align-items: center;justify-content: center;">
								<view class=""
									:style="chooseListIds.includes(item.id)?'width:36rpx;height:36rpx;border-radius:50%;background:#55aa00;':''">

								</view>
							</view>
						</view>
					</view>
					<view class=""
						style="width: 100%;padding: 0 30rpx;display: flex;align-items: center;justify-content: center;"
						v-if="couponUserList.length==0">
						<p>未查到相关早餐券</p>
					</view>
				</scroll-view>

				<view class="" @click="toUse" v-if="chooseList.length>0&&ifShow"
					style="width: 440rpx;height: 80rpx;border-radius: 6rpx;display: flex;align-items: center;justify-content: center;"
					:style="{background:themeColor.main_color}">
					<text style="color: #FFFFFF;">点击使用</text>
				</view>
				<view class="" v-if="chooseList.length==0&&ifShow" @click="tips"
					style="width: 440rpx;height: 80rpx;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;background-color: #e5e5e5;">
					<text style="color: #FFFFFF;">点击使用</text>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				couponList: [],
				pop: false,
				payList: ['weixin', 'tongyong', 'duli'], //支付方式,
				hackReset: true,
				detail: null,
				couponUserList: [],
				hooseList: [],
				chooseListIds: [],
				chooseList: [],
				pop1: false,
				ifShow:false
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'hotelList', 'setting'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options);
			if (options.id) {
				this.hotel_id = options.id
				this.ifShow = true
			}else{
				this.hotel_id = this.hotel.id
				this.ifShow = false
			}
			let params = {
				shop_id: this.hotel_id,
				bill_id: null,
				page: 1,
				limit: 100
			}
			this.$iBox.http('getShopBreakfastCouponList', params)({
				method: 'post'
			}).then(res => {
				this.couponList = res.data.list
			});

		},
		methods: {
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			getCoupon(item) {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
				})
				this.pop = true
				this.detail = item
				if(item.point_status==1){
					this.payList.push('point')
				}else{
					if(this.payList.includes('point')){
						this.payList.pop()
					}
				}
			},
			payFor(e) {
				console.log(e,'l');
				this.$iBox.throttle(() => {
					this.fnPay(e)
				}, 2000);
			},
			toLook() {
				this.$iBox.http('getUserBreakfastCouponList', {
					shop_id: this.hotel_id,
					page: 1,
					limit: 100
				})({
					method: 'post'
				}).then(res1 => {
					this.couponUserList = res1.data.list.filter(item => {
						return item.date == this.$moment().format('YYYY-MM-DD')
					})
					this.pop1 = true
				});
			},
			choose(e) {
				console.log(e);
				if (!this.chooseListIds.includes(e.id)) {
					this.chooseList.push(e)
					this.chooseListIds.push(e.id)
				} else {
					this.chooseListIds = this.chooseListIds.filter(item => {
						return item != e.id
					})

					this.chooseList = this.chooseList.filter(item => {
						return item.id != e.id
					})
				}
			},
			tips() {
				uni.showToast({
					icon: 'none',
					title: '请选择早餐券!'
				})
			},
			toUse() {
				uni.showModal({
					title: '提示',
					content: '是否确认使用已选择早餐券',

					success: (res) => {
						if (res.confirm) {
							this.$iBox.http('wxVerificationBreakfastCoupon', {
								ids: this.chooseListIds
							})({
								method: 'post'
							}).then(res => {

								uni.navigateBack()
							});
						}
					}
				})

			},
			fnPay(e) {
	
				let params = {
					coupon_id: this.detail.id,
					count: 1,
					pay_type: e == 'weixin' ? 1 : (e == 'tongyong' ? 2:(e=='duli'?3:(e=='daodian'?4:5))),
					date: this.$moment().format('YYYY-MM-DD')
				}
				console.log(e,'kl');
				uni.showModal({
					title:'提示',
					content:`是否支付${e=='point'?this.detail.point+'积分购买':this.detail.amount+'元购买'}`,
					success:res=>{
						uni.showLoading({
							title: '等待支付...'
						})
						if(res.confirm){
							this.$iBox.http('buyBreakfastCoupon', params)({
								method: 'post'
							}).then(res => {
								// 如果有押金且押金设置显示则必定需要微信支付
								if (res.data) {
									if (res.data.bizCode == '0000') {
										// 随行付
										uni.requestPayment({
											provider: 'wxpay',
											AppId: res.data.payAppId,
											timeStamp: res.data.payTimeStamp,
											nonceStr: res.data.paynonceStr,
											package: res.data.payPackage,
											signType: res.data.paySignType,
											paySign: res.data.paySign,
											success: (res) => {
												this.$iBox.http('getUserBreakfastCouponList', {
													shop_id: this.hotel_id,
													page: 1,
													limit: 100
												})({
													method: 'post'
												}).then(res1 => {
													this.couponUserList = res1.data.list.filter(item => {
														return item.date == this.$moment().format('YYYY-MM-DD')
													})
													this.pop = false
													this.pop1 = true
												});
							
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
							
							
									} else {
										// 微信支付
										uni.requestPayment({
											provider: 'wxpay',
											timeStamp: res.data.timeStamp,
											nonceStr: res.data.nonceStr,
											package: res.data.package,
											signType: 'MD5',
											paySign: res.data.paySign,
											success: (res) => {
												this.$iBox.http('getUserBreakfastCouponList', {
													shop_id: this.hotel_id,
													page: 1,
													limit: 100
												})({
													method: 'post'
												}).then(res1 => {
													this.couponUserList = res1.data.list.filter(item => {
														return item.date == this.$moment().format('YYYY-MM-DD')
													})
													this.pop = false
													this.pop1 = true
												});
							
											},
											fail: function(err) {
												uni.hideLoading()
											}
										});
									}
								} else {
									uni.showModal({
										title:'提示',
										content:'购买成功!',
										showCancel:false,
										success:(res)=> {
											this.$iBox.http('getUserBreakfastCouponList', {
												shop_id: this.hotel_id
											})({
												method: 'post'
											}).then(res => {
												this.couponUserList = res.data.filter(item => {
													return item.date == this.$moment().format('YYYY-MM-DD')
												})
												this.pop = false
												this.pop1 = true
											});
										}
									})
								}
							}).catch(err=>{
								uni.showToast({
									icon:'none',
									title:err
								})
							})
						}else {
							uni.hideLoading()
						}
					}
				})
			}
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.coupon-container {
		padding: 20rpx;

		.coupon-card {
			position: relative;
			background: linear-gradient(135deg, #fff5e6, #ffe8cc);
			border-radius: 16rpx;
			margin-bottom: 30rpx;
			display: flex;
			padding: 30rpx;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);

			.price-section {
				width: 180rpx;
				display: flex;
				align-items: baseline;

				.currency {
					font-size: 36rpx;
					color: #ff6600;
					margin-right: 8rpx;
				}

				.price {
					font-size: 56rpx;
					font-weight: bold;
					color: #ff6600;
				}
			}

			.info-section {
				flex: 1;
				padding-left: 20rpx;

				.title {
					display: block;
					font-size: 36rpx;
					color: #333;
					margin-bottom: 16rpx;
				}

				.time {
					font-size: 24rpx;
					color: #666;
				}

				.tags {
					margin-top: 20rpx;

					.tag {
						display: inline-block;
						padding: 6rpx 16rpx;
						background: rgba(255, 102, 0, 0.1);
						border-radius: 8rpx;
						font-size: 22rpx;
						color: #ff6600;
						margin-right: 12rpx;
					}
				}
			}

			.action-section {
				width: 160rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.get-btn {
					background: #ff6600;
					color: white;
					padding: 12rpx 24rpx;
					border-radius: 40rpx;
					font-size: 28rpx;
				}

				.received-btn {
					color: #999;
					font-size: 28rpx;
				}
			}

			/* 锯齿装饰线 */
			.sawtooth {
				position: absolute;
				bottom: -10rpx;
				left: 0;
				right: 0;
				height: 20rpx;
				background: radial-gradient(circle at 10rpx 10rpx,
						transparent 10rpx, #ffe8cc 10rpx);
				background-size: 20rpx 20rpx;
			}
		}
	}
</style>