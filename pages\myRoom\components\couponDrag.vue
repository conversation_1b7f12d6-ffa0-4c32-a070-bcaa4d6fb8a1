<template>
	<view>
		<!-- 样式一 -->
		<view class="">
			<liu-drag-button @clickBtn="clickBtn" :bottomPx="180" :rightPx="5">
				<view class="" style="height: 100rpx;width: 100rpx;">
					<image :src="styleModel.bg_image" style="height: 100rpx;width: 100rpx;border-radius: 50%;"></image>
				</view>
				
			</liu-drag-button>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		props: {
			styleModel:{
				type:Object
			}
		},
		data() {
			return {

			};
		},
		components: {
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['hotel','cityModel']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		mounted() {
			console.log(this.modeDate,'diyModel');
		},
		methods: {
			clickBtn(){
				uni.navigateTo({
					url:'/packageA/customers/customers'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	
</style>
