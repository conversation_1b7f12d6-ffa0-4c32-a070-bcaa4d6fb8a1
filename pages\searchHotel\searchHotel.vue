<template>
	<view :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
		<m-searchBox v-model="keyword" :mode="1" @toSearch="toSearchHotel"></m-searchBox>
		<view class="hotelList" :style="{background:themeColor.bg_main1_color,'border-bottom':'1px solid '+themeColor.border_color}" v-for="(item, index) in hotelList"
			:key="item.id" @click="chooseHotel(item)">
			<view class="hotelList_t">
				<image src="../../static/images/hotel.png" style="height: 40rpx;width: 40rpx;"></image>
			</view>
			<view class="hotelList_t1">
				<text :style="{color:themeColor.text_second_color}">{{item.shop_name}}</text>
				<text style="font-size: 22rpx;" :style="{color:themeColor.text_second_color}">{{item.address?item.address:'' + item.detail_info?item.detail_info:''}}</text>
			</view>
			<view class="hotelList_t2" :style="{color:themeColor.text_second_color}">
				<text style="font-size: 26rpx;color: crimson;">￥{{item.price?item.price:'暂无'}}</text>
				<text style="font-size: 26rpx;" >起</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				hotelList: [],
				params: {
					page: 1,
					limit: 10
				},
				bool: true,
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'city'])
		},
		onShow() {
			this.params.page = 1
		},
		methods: {
			...mapActions('hotel', ['getHotelList', 'getHotel']),
			getList() {
				this.$iBox.http('getShopList', this.params)({
					method: 'post'
				}).then(res => {
					this.hotelList = res.data.list
					this.getHotelList(res.data.list)
				})
			},
			toSearchHotel(e) {
				console.log(e, 'd');
				if (e) {
					this.params.keyword = e
					this.getList()
				}
			},
			chooseHotel(e){
				this.getHotel(e)
				uni.navigateTo({
					url:'/pages/hotelDetail/hotelDetail'
				})
			}
		},
		// // 上拉加载
		onReachBottom() {
			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getShopList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.hotelList.concat(res.data.list)
					this.hotelList = new_list
					console.log(this.hotelList.length, res.data.count);
					if (this.hotelList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}

		}
	}
</script>

<style lang="scss" scoped>
	.hotelList {
		display: flex;
		min-height: 120rpx;
		width: 100%;
		
		&_t {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
			width: 80rpx;
		}

		&_t1 {
			display: flex;
			flex-direction: column;
			justify-content: center;
			height: 100%;
			width: 520rpx;
		}

		&_t2 {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 100%;
			width: 150rpx;
		}
	}
</style>
