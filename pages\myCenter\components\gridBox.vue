<template>
	<view>
		<view class="elseItem" :style="{color:themeColor.text_main_color}">
			<view class="head">
				
				<text style="padding-left: 10rpx;font-size: 40rpx;">{{list.property.title}}</text>
			</view>
			<view class="body" :style="{background:themeColor.bg_color}">
				<view class="bodyItem" @click="goPages(item.path)" :key="index" v-for="(item,index) in list.property.list">
					<image :src="item.icon" style="width: 56rpx;height: 56rpx;"></image>
					<text style="font-size: 30rpx;margin-top: 6rpx;">{{item.title}}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			};
		},
		props:{
			style:{
				type:Number
			},
			list:{
				type:Object,
				default:null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		methods:{
			goPages(e){
				uni.navigateTo({
					url:'/'+e
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.elseItem {
		width: 700rpx;
		min-height: 200rpx;
		border-radius: 20rpx;
		margin: 60rpx auto;
		// background-color: #FFFFFF;
		.head {
			padding: 20rpx;
			width: 100%;
			// border-bottom:1px solid #EBEDF0;
			display: flex;
			align-items: center;
		}
		
		.body{
			display: flex;
			align-items: center;
			padding: 0 48rpx;
			border-radius: 32rpx;
			width: 100%;
			flex-wrap: wrap;
			.bodyItem{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 200rpx;
				width: 33%;
			}
		}
	}
</style>
