<template>
	<view class="phoneBox">
		<p style="font-size: 40rpx;font-weight: 600;margin: 50rpx auto;">会员手机号修改</p>
		<view class="nameBox">
			<view class="" style="width: 360rpx;">
				<input type="number" placeholder="请输入手机号码" v-model="phone" />
			</view>
			<view class="codeBtn" @click="getPhoneCode"
				:style="phone&&isDisabled?'background:'+themeColor.com_color1+';color:'+themeColor.bg_color:'background:#eee'">
				获取验证码{{timeNum>0&&timeNum<60?'('+timeNum+'s)':''}}</view>
		</view>
		<view class="nameBox">
			<view class="" style="width: 500rpx;">
				<input type="text" placeholder="请填写验证码" v-model="code" />
			</view>
		</view>

		<view class="" style="width: 500rpx;margin: 80rpx auto;">
			<view class="btn_register" :style="{background:themeColor.com_color1,color:themeColor.bg_color}"
				@click="register">确定</view>
		</view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				phone: '',
				code: '',
				isDisabled: true,
				seconds: 60,
				timeNum: 0
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		async onLoad() {
			await this.$onLaunched;

		},
		watch: {
			seconds() {
				this.timeNum = this.seconds
				if (this.timeNum == 60) {
					this.isDisabled = true
				}
				if (this.seconds == 59) {
					console.log('zhuc');
					this.$iBox.http('sendCaptchaCode', {
						phone: this.phone
					})({
						method: 'post'
					}).then(res => {
						
					})
				}

			}
		},
		methods: {

			getPhoneCode(e) {
				// 点击倒计时60秒，60秒后可重新计时
				const reg = /^[1][3,4,5,6,7,8,9][0-9]{9}$/
				if (!reg.test(this.phone)) {
					uni.showToast({
						icon: 'none',
						title: '请填写正确的11位手机号码！'
					})
					return
				}
				if (!this.phone || !this.isDisabled) {
					return
				} else {
					this.isDisabled = false
					let t = setInterval(() => {
						this.seconds--
						console.log(this.seconds);
						if (this.seconds < 0) {
							this.isDisabled = false
							this.seconds = 60
							clearInterval(t)
						} else if (this.seconds == 60) {
							this.isDisabled = true

						} else {
							this.isDisabled = false
						}
					}, 1000)
				}

			},
			register() {
				this.$iBox.http('updateUserPhone', {
					phone: this.phone,
					captcha_code: this.code
				})({
					method: 'post'
				}).then(res => {
					uni.showModal({
						title:'提示',
						content:'修改手机号成功!',
						showCancel:false,
						success:res=>{
							if(res.confirm){
								uni.navigateBack()
							}
						}
					})
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #fff;
	}

	.phoneBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 80rpx;
		background: #fff;
		height: 100vh;

		.nameBox {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			border-bottom: 1px solid #e4e7ed;
			// justify-content: space-between;
		}

		.codeBtn {
			padding: 10rpx;
			border-radius: 6rpx;
			font-size: 22rpx;
		}

		.btn_register {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 500rpx;
			height: 90rpx;
			border-radius: 20rpx;
		}

	}
</style>