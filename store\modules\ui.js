import Vue from 'vue'

const state = {
	tabbar: [], // 自定义tabbar栏
	themeColor:{} ,//自定义主题
	copyRight:null,//版权
	pop:[],//全局弹窗控件
}

const getters = {
	
}

const mutations = {
	// 自定义tabbar栏
	PUSHTABBAR: (state, tabbar) => {
		state.tabbar = tabbar
	},
	//自定义主题
	PUSHTHEMECOLOR: (state, themeColor) =>{
		state.themeColor = themeColor
	},
	//自定义控件弹出窗
	PUSHPOP: (state, pop) =>{
		state.pop = pop
	},
	PUSHRIGHT: (state, copyRight) =>{
		state.copyRight = copyRight
	},
	
}

const actions = {
	
	toTabbar({
		commit
	}, params) {
		commit('PUSHTABBAR', params)
	},
	
	toThemeColor({
		commit
	}, params) {
		commit('PUSHTHEMECOLOR', params)
	},
	
	toPop({
		commit
	}, params) {
		commit('PUSHPOP', params)
	},
	
	toCopyRight({
		commit
	}, params) {
		commit('PUSHRIGHT', params)
	},
}

export default {
	namespaced: true,
	state,
	getters,
	mutations,
	actions
}
