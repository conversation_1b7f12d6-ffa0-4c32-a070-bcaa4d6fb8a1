<template>
	<view class="deviceItemBox">
		<view class="" style="display: flex;flex-wrap: wrap;width: 690rpx;">
			<view class="" style="width: 345rpx;display: flex;align-items: center;justify-content: center;"
				v-for="item in lock_list" :key="item.id">
				<view class="item1" :style="{background:themeColor.bg_main1_color}" @click="openLock(item)"
					v-if="styleModel==1">
					<view class="item1_title" :style="{color:themeColor.text_title_color}">
						<text>智能门锁</text>
						<view class="open"
							:style="isShow==1?'background-color:'+themeColor.main_color:'background-color: #b5b5b5;'">
							{{isShow==1?'点击开锁':'等待连接'}}
						</view>
					</view>
					<view class="item1_content">
						<text class="icon-mimasuo" style="font-size: 80rpx;"></text>
						<view class="item1_content_text">
							<text :style="{color:themeColor.text_main_color}"
								style="font-weight: 600;">{{lockDetail.lock_alias}}</text>
							<view class="" style="padding-top: 10rpx;display: flex;align-items: center;">
								<p style="width: 10rpx;height: 10rpx;border-radius: 50%;"
									:style="isShow==1?'background-color: #53C21D;':'background-color: #333;'"></p>
								<text style="font-size: 24rpx;padding-left: 6rpx;">{{isShow==1?'在线':'离线'}}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class=""
			style="height: 300rpx;width: 690rpx;display: flex;align-items: center;justify-content: center;margin: 30rpx auto;"
			v-if="styleModel==2">
			<swiper :autoplay="false" style="height: 100%;width: 100%;">
				<swiper-item v-for="(item,index) in lock_list" :key="item.id" style="height: 100%;width: 100%;display: flex;align-items: center;justify-content: center;">
					<image src="http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/kaisuo_1208.png"
						mode="aspectFill" style="height: 260rpx;width: 260rpx;" @click="openLock(item)">
					</image>
				</swiper-item>
			</swiper>
		</view>
		<view class="" v-if="styleModel==3" style="height: 800rpx;width: 718rpx; background: #FFFFFF;
		border-bottom-left-radius: 32rpx;border-bottom-right-radius: 32rpx;display: flex;align-items: center;">
			<swiper :autoplay="false" style="height: 100%;width: 100%;">
				<swiper-item v-for="(item,index) in lock_list" :key="item.id" style="height: 100%;width: 100%;">
					<view class=""
						style="height: 100%;width: 100%;display: flex;flex-direction: column;align-items: center;justify-content: center;position: relative;">
						<view class="" v-if="current==1"
							style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
							<text style="margin-bottom: 30rpx;">蓝牙开锁</text>
							<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/lockBg.png"
								style="height: 400rpx;width: 640rpx;" mode="" @click="openLock(item)"></image>
						</view>
						<view class="" v-if="current==2"
							style="display: flex;flex-direction: column;align-items: center;justify-content: center;">
							<text style="margin-bottom: 30rpx;">密码开锁</text>
							<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/lock.png"
								style="height: 256rpx;width: 176rpx;margin: 70rpx auto;" mode=""></image>
							<p style="color: #000000E0;font-size: 64rpx;letter-spacing:14rpx">{{item.password}}</p>
							<p style="color: #757575;font-size: 28rpx;"><text style="margin-right: 20rpx;">开始时间:</text>{{billDetail.enter_time| moment1}}</p>
							<p style="color: #757575;font-size: 28rpx;"><text style="margin-right: 20rpx;">失效时间:</text>{{billDetail.leave_time_plan | moment1}}</p>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
							<view class=""
								style="height: 80rpx;width: 368rpx;display: flex;align-items: center;background-color: #00000014;border-radius: 16rpx;padding: 0 16rpx;">
								<view class="" v-for="item1 in typeList"
									:style="current==item1.id?'height:60rpx;width:160rpx;border-radius:8rpx;background:#FFFFFF;color:'+themeColor.main_color:''"
									@click="chooseType({item1:item1,item:item})" style="width: 50%;height: 100%;display: flex;align-items: center;
								 justify-content: center;font-size: 30rpx;">
									{{item1.name}}
								</view>
							</view>
						</view>
						<view class="" v-if="lock_list.length>1" :style="{color:themeColor.main_color}"
							style="position: absolute;right: 20rpx;top: 20rpx;height: 80rpx;min-width: 100rpx;
						 display: flex;align-items: center;justify-content: center;background-color: #00000014;padding: 0 20rpx;border-radius: 8rpx;">
							门锁{{index+1}}/{{lock_list.length}}
						</view>
					</view>
				</swiper-item>
			</swiper>
		</view>

		<!-- 锁的弹窗 -->
		<m-popup :show="show" @closePop="closePop">
			<view class="popBox">
				<text style="font-size: 36rpx;font-weight: 600;">通行区域:{{lockDetail.lock_alias}}房门锁</text>
				<view class="box_out">
					<view :class="isOpen==1?'box':'box1'">
						<view class="inner_leange">
							<view class="ball">
							</view>
						</view>
					</view>
					<!-- 正在开门 -->
					<text class="icon-kaisuo overImg" style="font-size: 100rpx;" v-if="isOpen==1"></text>

					<!-- 开门完成 -->
					<image src="/static/images/smile.png" mode="aspectFill" class="overImg1" v-if="isOpen==2">
					</image>

					<!-- 开门失败 -->
					<image src="/static/images/nosmile.png" mode="aspectFill" class="overImg1" v-if="isOpen==3">
					</image>
				</view>

				<text v-if="isOpen==1">授权验证中...</text>


				<text v-if="isOpen==2">已为您门锁授权</text>


				<view class="">
					<u-button type="error" v-if="isOpen==3" style="color:#CD1225">请再试一次！</u-button>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	// if(true){
	// 	// #ifdef MP-WEIXIN
	// 	// 引入插件
	// 	uni.requireNativePlugin('myPlugin');
	// 	// #endif
	// }
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				lockDetail: null,
				show: false,
				isOpen: 0, //1代表正在开门2，开门成功 3,error
				isShow: 0, //离线
				authInfo: null,
				typeList: [{
					id: 1,
					name: '蓝牙开锁'
				}, {
					id: 2,
					name: '密码开锁'
				}],
				current: 1,
				lock_list: []
			};
		},
		props: {

			styleModel: {
				type: [Number, String]
			},
			billDetail: {
				type: Object
			},
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('room', ['hardWareList']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting'])
		},
		mounted() {
			this.lock_list = this.hardWareList.lock_list.filter(item => {
				return item.lock_type_sign == 'tongtong'
			})
			if (this.lock_list.length > 0) {
				this.lockDetail = this.lock_list[0]
			}
			console.log(this.lock_list, 'this.lockDetail');
			this.startScan()
		},
		methods: {
			closePop() {
				this.show = false
			},
			chooseType(e) {
				console.log(e, 'kkl');
				if (e.item1.id == 2 && !e.item.password) {
					uni.showToast({
						icon: 'none',
						title: '此门锁暂无密码',
						duration: 1500
					})
					return
				}
				this.current = e.item1.id
			},
			openLock(e) {
				console.log(e, 'dds');
				this.lockDetail = e
				this.startScan()
				this.$iBox.http('getRoomBillUser', {
					bill_id: this.billDetail.id
				})({
					method: 'post'
				}).then(res2 => {
					this.authInfo = res2.data.filter(item => {
						return item.common_code == this.userInfo.common_code
					})[0]

					let over_time = this.shopSetting.filter(item => {
						return item.sign == 'over_time_blue_tooth_open_door'
					})[0].property.status

					let arrears = this.shopSetting.filter(item => {
						return item.sign == 'arrears_blue_tooth_open_door'
					})[0].property.status
					console.log(arrears, 'this.$moment().unix()');
					// 关闭了超时可以开门设置
					if (!over_time) {
						if (this.billDetail.leave_time_plan < this.$moment().unix()) {
							uni.showToast({
								icon: 'error',
								title: '入住已超时！'
							})
							return
						}
					}
					// 关闭了欠费可以开门设置
					if (!arrears) {
						if (this.billDetail.already_pay - this.billDetail.bill_amount < 0) {
							uni.showToast({
								icon: 'error',
								title: '订单已欠费！'
							})
							return
						}

					}

					// 判断是否有身份证记录,有的话则直接展示
					if (this.authInfo && this.authInfo.identification_type == 1 && this.authInfo.user_status ==
						1) {

						this.toOpenDoor()
						this.show = true
						this.isOpen = 1
					} else {
						if (this.authInfo.user_status == 0) {
							uni.showToast({
								icon: 'error',
								title: '您还未到入住时间！无开门权限',
								duration: 1500
							})
						} else {
							uni.showToast({
								icon: 'error',
								title: '入住时间已经超时！请续房！',
								duration: 1500
							})
						}

					}

				})


			},
			// 开始扫描附近的智能锁设备// 再判断是哪个平台的锁 ==========================================通通锁====================================
			startScan() {
				/**
				 * 调用蓝牙扫描接口，返回lockDevice 和 lockList对象
				 * 
				 */
				uni.showLoading({
					title: '正在搜索蓝牙设备'
				})
				plugin.startScanBleDevice((lockDevice, lockList) => {

					let a = this.lockDetail
					if (lockList.length > 0) {
						for (let item of lockList) {
							console.log(item, a, 'lock');
							if (item.lockMac == a.lock_mac) {
								this.isShow = 1
								this.toCheckLockTime()
							} else {
								this.isShow = 0
							}

						}
					}
					console.log(a, 'aaa');
					this.lockDetail = a

					uni.hideLoading()
				}, err => {
					console.log(err)
					uni.hideLoading()
					uni.showToast({
						icon: 'none',
						title: err
					})

				})
			},
			// 校准锁时间
			toCheckLockTime(e) {
				const start = Date.now();
				// 建议使用服务器时间
				plugin.setLockTime.setLockTime({
					lockData: this.lockDetail.lock_data,
					serverTime: Date.now(),
				}).then(res => {
					if (res.errorCode === 0) {
						wx.showToast({
							icon: "success",
							title: "锁时间已校准"
						});
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'none',
							title: `校准锁时间失败:${res.errorMsg}`
						})
					}
				});
			},
			// 读取操作记录
			toReadRecord() {
				uni.showLoading({
					title: `正在读取锁内操作记录`,
				})
				// this.lockDetail.lock_data
				const start = Date.now();
				// 获取操作记录
				plugin.getOperationLog({
					/* 读取操作记录方式 1 -全部, 2 -最信 */
					logType: 2,
					lockData: this.lockDetail.lock_data
				}).then(res => {
					uni.hideLoading({});
					console.log(res, '读取最新操作记录')
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'success',
							title: `操作记录已获取--操作时间::${Date.now() - start}`
						})
						this.$iBox.http('cuploadOpenRecord', {
								id: this.lockDetail.id,
								records: res.log
							})({
								method: 'post'
							})
							.then(res => {
								console.log('上传日志成功');
							})
					} else {
						uni.showToast({
							icon: 'error',
							title: "读取操作记录失败:" + res.errorMsg
						})

					}
				})
			},
			// 点击开锁
			toOpenDoor() {
				
				uni.showLoading({
					title: '正在开启智能锁,请等待！'
				})
				const start = Date.now();
				console.log(this.lockDetail, 'mn');
				plugin.controlLock({
					/* 控制智能锁方式 3 -开锁, 6 -闭锁 */
					controlAction: 3,
					lockData: this.lockDetail.lock_data,
					serverTime: Date.now(),
				}).then(res => {
					if (res.errorCode == 0) {
						wx.showToast({
							icon: "success",
							title: "已开锁"
						});
						this.isOpen = 2
						wx.hideLoading()
						this.toReadRecord();
					} else {
						wx.hideLoading();
						uni.showToast({
							icon: 'none',
							title: `开锁失败: ${res.errorMsg}`
						})
						this.isOpen = 3
					}
				}).catch(err=>{
					console.log(err,'kl');
				})
			},

			//===========================================权限验证=================================================

			getWxAuthorizeLocation: function() {
				wx.getSetting({
					success(res) {
						console.log(res);
						// 如果从未申请定位权限，则申请定位权限
						if (res.authSetting['scope.userLocation'] == null) {
							wx.authorize({
								scope: 'scope.userLocation',
								success() {
									// 用户同意
									// 相关操作
								},
								fail() {
									wx.showToast({
										title: '无法申请定位权限,请确认是否已经授权定位权限',
										icon: "none",
										duration: 2000
									})
								}
							})
						}
						// 如果已经有权限，就查询
						else if (res.authSetting['scope.userLocation'] == true) {
							// 相关操作
						}
						// 被拒绝过授权，重新申请
						else {
							wx.showModal({
								title: '位置信息授权',
								content: '位置授权暂未开启，将导致无法正常手机开门',
								cancelText: '仍然拒绝',
								confirmText: '开启授权',
								success: function(res) {
									if (res.confirm) {
										wx.openSetting({
											fail: function() {}
										})
									} else {

									}
								}
							})
						}
					}
				});
			},

			getWxAuthorizeBle: function() {
				uni.getSystemInfo({
					success(res) {
						console.log(res, '蓝牙');
						if (!res.bluetoothEnabled) {
							uni.showModal({
								title: '提示!',
								content: '系统蓝牙未打开，请打开后重试！',
								showCancel: false,
								success: res => {

								}
							})
						}

						if (!res.locationEnabled) {
							uni.showModal({
								title: '提示!',
								content: '手机定位未打开！',
								showCancel: false,
								success: res => {

								}
							})
						}

						if (!res.locationAuthorized) {
							uni.showModal({
								title: '提示!',
								content: '请授权微信使用定位功能!',
								showCancel: false,
								success: res => {

								}
							})
						}

					}
				})
			},
		}

	}
</script>

<style lang="scss" scoped>
	.deviceItemBox {

		// display: flex;
		// align-items: center;
		// justify-content: space-between;
		// flex-wrap: wrap;
		// padding: 0 30rpx;
		// width: 100%;
		.item1 {
			height: 180rpx;
			width: 96%;
			margin-top: 30rpx;
			border-radius: 20rpx;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

			&_title {
				padding: 20rpx;
				font-size: 28rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.open {
					width: fit-content;
					padding: 10rpx 16rpx;
					border-radius: 30rpx;
					color: #ffffff;
					// background-color: rgba(0, 0, 0, 0.6);
					font-size: 22rpx;
				}
			}

			&_content {
				padding: 0rpx 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.img_lock {
					height: 80rpx;
					width: 80rpx;
				}

				&_text {
					display: flex;
					flex-direction: column;
				}
			}
		}

		.popBox {
			height: 880rpx;
			padding: 30rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-around;

			@keyframes spin {

				from {
					transform: rotate(0deg);
				}

				to {
					transform: rotate(360deg);
				}

			}

			@keyframes scbig {

				from {
					transform: scale(0, 0);
				}

				to {
					transform: scale(1, 1);
				}

			}

			.overImg {
				height: 120rpx;
				width: 120rpx;
				position: absolute;
				margin: 0 auto;
				animation: scbig 1s 1;
				// animation-name: scbig;
				// animation-duration: 1s;
				// animation-timing-function: ease;
				// animation-iteration-count: 1;
			}

			.overImg1 {
				height: 100rpx;
				width: 120rpx;
				position: absolute;
				margin: 0 auto;
				animation: scbig 1s 1;
				// animation-name: scbig;
				// animation-duration: 1s;
				// animation-timing-function: ease;
				// animation-iteration-count: 1;
			}

			.box_out {
				height: 320rpx;
				width: 320rpx;
				border-radius: 50%;
				background: #dddddd;
				display: flex;
				align-items: center;
				justify-content: center;

				.box {
					height: 280rpx;
					width: 280rpx;
					border-radius: 50%;
					background-image: radial-gradient(circle, #f4c97f, #ffffc4);
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.inner_leange {
						width: 240rpx;
						height: 240rpx;
						border-radius: 50%;
						border: 2px solid #43413b;
						position: relative;
						animation: spin 3s infinite linear;

						.ball {
							position: absolute;
							left: 64rpx;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background-image: radial-gradient(circle, #5b4f4a, #bec8b1);
						}
					}

				}

				.box1 {
					height: 280rpx;
					width: 280rpx;
					border-radius: 50%;
					background-image: radial-gradient(circle, #f4c97f, #ffffc4);
					display: flex;
					align-items: center;
					justify-content: center;
					position: relative;

					.inner_leange {
						width: 240rpx;
						height: 240rpx;
						border-radius: 50%;
						border: 2px solid #43413b;
						position: relative;


						.ball {
							position: absolute;
							left: 64rpx;
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;
							background-image: radial-gradient(circle, #5b4f4a, #bec8b1);
						}
					}

				}
			}

		}
	}
</style>