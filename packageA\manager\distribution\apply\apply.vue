<template>
	<view>
		<view class="box">
			<view class="title">
				<image src="/static/images/customs.png" style="width: 60rpx;height: 60rpx;" mode=""></image>
				<text style="font-size: 44rpx;font-weight: 600;margin-left: 10rpx;">分销申请</text>
			</view>
			<view class="content">
				<view class="" style="display: flex;height: 100rpx;align-items: center;">
					<p style="width: 120rpx;">姓名:</p>
					<view class="" style="width: 500rpx;">
						<uni-easyinput  v-model="name" placeholder="请输入申请人员姓名" />
					</view>
					
				</view>
				<view class="" style="display: flex;height: 100rpx;align-items: center;">
					<p style="width: 120rpx;">手机号:</p>
					<view class="" style="width: 500rpx;">
						<uni-easyinput type="number"  v-model="phone" placeholder="请输入申请人员手机号" />
					</view>
				</view>
				
				<p style="font-size: 24rpx;color: #909399;margin-top: 20rpx;">*默认填写会员手机，如不同请手动填写其他手机号</p>
			</view>
			<view class="btnBox">
				<view style="width: 600rpx;height: 80rpx;border-radius: 36rpx;display: flex;align-items: center;justify-content: center;" :style="{background:themeColor.main_color,color:themeColor.bg_color}" @click="toSure">申请成为分销人员</view>
			</view>
			
			<view class="btnBox">
				<view style="background: #a9acb3;width: 600rpx;height: 80rpx;border-radius: 36rpx;display: flex;align-items: center;justify-content: center;" @click="back">返回首页</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				name:'',
				phone:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		onLoad() {
			this.phone = this.userInfo.phone?this.userInfo.phone:''
		},
		methods: {
			apply() {
				if(!this.name){
					uni.showToast({
						icon:'error',
						title:'请填写姓名'
					})
					return
				}
				
				if(!this.name){
					uni.showToast({
						icon:'error',
						title:'请填写手机号'
					})
					return
				}
				
				this.$iBox.http('applyDistributionUser', {name:this.name,phone:this.phone})({
					method: 'post'
				}).then(res => {
					uni.navigateBack({})
				})
			},
			toSure(){
				this.$iBox.throttle(() => {
					this.apply()
				}, 2000);
			},
			back(){
				uni.navigateBack({
					delta:2
				})
			}
		}
	}
</script>

<style scoped lang="scss">
	.box {
		width: 700rpx;
		height: 560rpx;
		background: #FFFFFF;
		border-radius: 36rpx;
		margin: 40rpx auto;
		padding: 30rpx;
		.title{
			display: flex;
			align-items: center;
			justify-content: center;
			width: 100%;
			height: 100rpx;
		}
		
		.content {
			margin-top: 30rpx;
		}
		
		.btnBox{
			margin-top: 30rpx;
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>