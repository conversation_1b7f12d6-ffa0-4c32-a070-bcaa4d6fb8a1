<template>
	<view>
		<web-view :src='srcUrl'></web-view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				srcUrl:'',
			};
		},
		computed: {
			...mapState('login', ['userInfo'])
		},
		onLoad(options) {
			console.log(options,'web');
			this.srcUrl = options.url
		}
	}
</script>

<style lang="scss">

</style>
