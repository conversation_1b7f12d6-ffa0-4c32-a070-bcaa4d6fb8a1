<template>
	<view class="">
		<view class="hotel_detail" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="hotel_detail_title">
				<text>{{hotel.shop_name?hotel.shop_name:'暂无信息'}}</text>
			</view>
			<!-- <view class="hotel_detail_show" style="height: 50rpx;border-radius: 8rpx;font-size: 24rpx;width: 700rpx;margin: 20rpx auto;padding: 0 10rpx;display: flex;align-items: center;justify-content: space-between;" :style="{background:themeColor.com_color1+'66',color:themeColor.text_main_color}">
				<text>精选房间</text>
				<view class="" style="display: flex;align-items: center;">
					<text style="border: 1px solid #FFFFFF;border-radius: 4rpx;padding: 4rpx;padding-right: 6rpx;" v-for="item in 4">24H服务</text>
					<view class="icon-jiantou" :style="{color:themeColor.text_title_color}"
						style="font-size: 30rpx;padding-left: 6rpx;">
					</view>
				</view>
			</view> -->
			<view class="hotel_detail_desc">
				<view class="hotel_detail_desc_t">
					<m-tags mode="tag" :color="themeColor.bg1_color" :name="item1" v-for="item1 in hotel.tags" :key="item1"></m-tags>
				</view>
				<view class="hotel_detail_desc_t1" @click="toPhone">
					<text :style="{color:themeColor.text_title_color}">设备/设施</text>
					<view class="icon-jiantou" :style="{color:themeColor.text_title_color}"
						style="font-size: 30rpx;padding-left: 6rpx;">
					</view>
				</view>
			</view>
			<view class="hotel_detail_rate" @click="goRating">
				<view class="hotel_detail_desc_t" style="position: relative;">
					<text style="font-size: 48rpx;font-weight: 600;">{{hotel.socre==0?'5.0':hotel.socre}}</text>
					<!-- <text>.0</text> -->
					<text style="padding-left: 10rpx;padding-right: 4rpx;" v-if="ecount>0">{{hotel.socre*1 > 4|| hotel.socre*1==0?'很棒':(hotel.socre*1 <= 4&&hotel.socre*1 >3?'一般':'很差')}}</text>
					<m-rate :size="20" :value="hotel.socre" :readonly="true"></m-rate>
					<text style="overflow: hidden;text-overflow: ellipsis;
					white-space: nowrap; width: 200rpx;font-size: 22rpx;padding-left: 10rpx;">{{`"${evaluateList[0].evaluate?evaluateList[0].evaluate:'暂无评论'}"`}}</text>
					<view class="hotel_detail_rate_bg"
						:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.main_color+'40,'+themeColor.com_color2+'40)'}">
					</view>
				</view>
				<view class="hotel_detail_desc_t1">
					<text :style="{color:themeColor.text_title_color}">{{ecount?ecount:0}}条点评</text>
					<view class="icon-jiantou" :style="{color:themeColor.text_title_color}"
						style="font-size: 30rpx;padding-left: 6rpx;">
					</view>
				</view>
			</view>
			<view class="hotel_detail_map" @click="getAddress" style="position: relative;"
				:style="{background:themeColor.bg1_color,color:themeColor.text_main_color}" v-if="!mode.bg_image">
				<!-- 模拟地图街道 -->
				<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/Frame-147.png" style="height: 100%;width: 256rpx;position: absolute;right: 0;" mode=""></image>
				<!-- 模拟地图街道 -->
				<view class="hotel_detail_map_address">
					<text>{{hotel.address}}</text>
					<view class="" v-if="hotel.distance"
						style="display: flex;align-items:  center;padding-top: 10rpx;font-size: 26rpx;color: #414143;">
						<view class="icon-ditu-dibiao">
						</view>
						<text v-if="hotel.distance">距您{{hotel.distance*1<1000?hotel.distance+'m':(hotel.distance/1000).toFixed(1)+'km'}}</text>
						<text v-else>暂无距离</text>
					</view>
				</view>
				<view class="hotel_detail_map_icon">
					<view class="icon-Location" style="font-size: 50rpx;color: #ff8080;z-index: 99;">
					</view>
					<text>地图</text>
				</view>
			</view>
			
			<view class="hotel_detail_map" v-else>
				<image :src="mode.bg_image" mode="" style="position: absolute;width: 100%;height:100%;top:0;left: 0;"></image>
			</view>
		</view>
	</view>

</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-hotelDetailCard",
		data() {
			return {
				// favourable: [{
				// 	id: 0,
				// 	name: '网红酒店'
				// }, {
				// 	id: 1,
				// 	name: '网红酒店'
				// }, {
				// 	id: 2,
				// 	name: '网红酒店'
				// }],
				params: {
					shop_id: '',
					page: 1,
					limit: 10
				},
				evaluateList:[],
				ecount:0
			};
		},
		props:{
			mode:{
				type:Object
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		mounted() {
			console.log(this.hotel, 'd');
			this.params.shop_id = this.hotel.id
			this.params.page = 1
			this.params.limit = 10
			this.$iBox.http('getShopRoomBillEvaluate', this.params)({
				method: 'post'
			}).then(res => {
				this.evaluateList = res.data.list
				this.ecount = res.data.count
			})
		},
		methods: {
			getAddress() {
				uni.openLocation({
					name: this.hotel.shop_name,
					address: this.hotel.address ? this.hotel.address : '',
					latitude: parseFloat(this.hotel.latitude),
					longitude: parseFloat(this.hotel.longitude),
					scale: 18,
					success: function() {
						console.log('success');
					}
				})
			},
			goRating(){
				uni.navigateTo({
					url:'/pages/rating/rating'
				})
			},
			toPhone(){
				uni.navigateTo({
					url:'/packageA/amenities/amenities'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hotel_detail {
		width: 750rpx;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 0 auto;
		border-radius: 20rpx;
		// margin-top: -40rpx;
		// background-color: #FFFFFF;
		z-index: 2;
		position: relative;
		margin-bottom: 20rpx;
		padding-bottom: 20rpx;

		&_title {
			font-size: 40rpx;
			padding: 20rpx 0;
			padding: 30rpx 30rpx 10rpx 30rpx;
		}

		&_desc {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0rpx 30rpx 10rpx 30rpx;

			// border-bottom: 1px solid #e4e7ed;
			&_t {
				display: flex;
				align-items: center;
				width: 75%;
			}

			&_t1 {
				display: flex;
				align-items: center;
				width: 25%;
				font-size: 28rpx;

				justify-content: flex-end
			}
		}

		&_rate {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0rpx 30rpx 20rpx 30rpx;

			// border-bottom: 1px solid #e4e7ed;
			&_t {
				display: flex;
				align-items: center;
				width: 75%;
			}

			&_t1 {
				display: flex;
				align-items: center;
				width: 25%;
				font-size: 28rpx;
			}

			&_bg {
				position: absolute;
				width: 80rpx;
				height: 32rpx;
				border-radius: 20rpx;
				z-index: 1;
				bottom: -2rpx;
			}
		}

		&_map {
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
			width: 700rpx;
			min-height: 160rpx;
			margin: 0 auto;
			position: relative;
			border-radius: 20rpx;


			// 地图样式
			.stree {
				position: absolute;
				width: 164rpx;
				height: 8rpx;
				background: #f1f6f7;
				right: 24rpx;
				top: 17rpx;
				-webkit-transform: rotate(-45deg);
				transform: rotate(6deg);
			}

			.stree1 {
				position: absolute;
				width: 178rpx;
				height: 8rpx;
				background: #f1f6f7;
				right: 27rpx;
				top: 87rpx;
				-webkit-transform: rotate(-90deg);
				transform: rotate(5deg);
			}

			.stree2 {
				position: absolute;
				width: 158rpx;
				height: 8rpx;
				background: #f1f6f7;
				right: -10rpx;
				top: 84rpx;
				-webkit-transform: rotate(-45deg);
				transform: rotate(109deg);
			}

			.stree3 {

				position: absolute;
				width: 124rpx;
				height: 8rpx;
				background: #f1f6f7;
				right: -30rpx;
				top: 80rpx;
				-webkit-transform: rotate(-95deg);
				transform: rotate(-95deg);
			}

			.stree4 {
				position: absolute;
				width: 159rpx;
				height: 8rpx;
				background: #f1f6f7;
				right: 92rpx;
				top: 80rpx;
				-webkit-transform: rotate(-134deg);
				transform: rotate(-46deg);

			}

			.stree5 {
				position: absolute;
				width: 215rpx;
				height: 8rpx;
				background: #f1f6f7;
				right: 14rpx;
				top: 141rpx;
				/* -webkit-transform: rotate(-134deg); */
				transform: rotate(15deg);

			}

			&_address {
				display: flex;
				flex-direction: column;
				justify-content: center;
				height: 100%;
				width: 80%;
				z-index: 99;
			}

			&_icon {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				width: 20%;
				z-index: 99;
				font-size: 28rpx;
				color: #7e7e85;
			}
		}
	}
</style>
