<template>
	<view style="height: 100vh;background-color: #fff;border: 1px solid transparent;">
		<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="rechargeList.length==0">
			<view class="icon-quesheng<PERSON>_zanwu<PERSON>lu" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无记录</p>
		</view>
		<view class="listBox" v-else v-for="item in rechargeList">
			<view class="title">
				<text>{{item.change_reason}}</text> 
				<text style="color: #a2a2a2;">{{item.create_time | moment1}}</text>
			</view>
			<text :style="item.change_reason=='会员充值'?'color:green':'color:red'">{{item.change_reason=='会员充值'?'+'+item.change:item.change}}</text>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				rechargeList:[],
				params:{
					page:1,
					limit:10
				},
				bool :true
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel'])
		},
		async onShow() {
			await this.$onLaunched;
			this.params.page = 1
			this.rechargeInfo()
			
		},
		methods:{
			rechargeInfo(){
				this.$iBox.http('getUserBalanceRecord', this.params)({
					method: 'post'
				}).then(res => {
					this.rechargeList = res.data.list
					uni.hideLoading()
				})
			}
		},
		onReachBottom() {
		
			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getUserBalanceRecord', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.rechargeList.concat(res.data.list)
					this.rechargeList = new_list
					if (this.rechargeList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		
		}
	}
</script>

<style lang="scss" scoped>
	.listBox {
		height: 150rpx;
		padding: 30rpx;
		border-bottom:1px solid #eee;
		display: flex;
		align-items: center;
		justify-content: space-between;
		.title { 
			height: 100%;
			display: flex;
			flex-direction: column;
			justify-content: space-around;
		}
	}
</style>
