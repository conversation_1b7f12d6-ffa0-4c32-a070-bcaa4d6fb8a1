<template>
	<view>
		<!-- 记消费 -->
		<m-popup :show="poprc" @closePop="closeConsume" mode="center">
			<scroll-view scroll-y="true" class="ConsumeRoom">
				<p>记消费:</p>
				<view class="itemBox" style="background-color: #ffffff;">
					<p style="font-size: 28rpx;">账务项目分组</p>
					<picker @change="bindChange" :value="changeIndex" range-key="name" :range="groupList">
						<view class="pickerBox">
							{{groupList[changeIndex].name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<view class="itemBox" style="background-color: #ffffff;">
					<p style="font-size: 28rpx;">账务项目</p>
					<picker @change="bindChange1" :value="changeIndex1" range-key="name" :range="groupTypeList">
						<view class="pickerBox">
							{{groupTypeList[changeIndex1].name}}
							<view class="icon-down"
								style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
							</view>
						</view>
					</picker>
				</view>

				<view class="itemBox" style="background-color: #ffffff;">
					<p style="font-size: 28rpx;">收款金额</p>
					<uni-easyinput v-model="billMoney" placeholder="0" type="digit"></uni-easyinput>
				</view>

				<view class="itemBox" style="background-color: #ffffff;">
					<p style="font-size: 28rpx;">收款备注</p>
					<uni-easyinput type="textarea" v-model="remark" placeholder="请输入内容"></uni-easyinput>
				</view>
				<view class="btnClass" @click="getPayTh">
					提交
				</view>
			</scroll-view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				groupList: [],
				groupTypeList: [],
				changeIndex: 0,
				changeIndex1: 0,
				remark: '',
				billMoney: 0
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		props: {
			poprc: {
				type: Boolean,
				default: false
			},
			bill: {
				type: Object
			}
		},
		watch: {
			poprc: {
				handler(newVal, oldVal) {
					this.billMoney = 0
					if (this.poprc) {
						this.$iBox
							.http('getDetailTypeGroup', {
								type: 1
							})({
								method: 'post'
							})
							.then(res => {
								this.groupList = res.data
								this.$iBox
									.http('getDetailTypeByGroupId', {
										type: 1,
										group_id:this.groupList[0].id
									})({
										method: 'post'
									})
									.then(res => {
										this.groupTypeList = res.data
									})
							})

						
					}
				},
				immediate: true,
				deep: true
			}
		},
		mounted() {

		},
		methods: {
			closeConsume() {
				this.$emit('closePay', '')
			},
			bindChange(e) {
				this.changeIndex = e.detail.value[0]
			},
			bindChange1(e) {
				this.changeIndex1 = e.detail.value[0]
			},
			getPayTh() {
				this.$iBox.throttle(() => {
					this.getPay()
				}, 2000);
			},
			getPay() {
				if (this.billMoney == 0) {
					uni.showToast({
						icon: 'none',
						title: '金额不能等于0'
					})
					return
				}
				let params = {
					bill_id: this.bill.id,
					group_id: this.groupList[this.changeIndex].id,
					room_bill_type_id: this.groupTypeList[this.changeIndex1].id,
					receive_money: this.billMoney
				}

				this.$iBox
					.http('addRoomConsumeDetail', params)({
						method: 'post'
					})
					.then(res => {
						this.$emit('closePay', '')
						this.$emit('upBillDetail', '')
					})

			}
		}
	}
</script>

<style lang="scss" scoped>
	.ConsumeRoom {
		height: 70vh;
		width: 680rpx;
		padding: 30rpx;

		.itemBox {
			width: 98%;
			background: #f6f6f6;
			margin: 0 auto;
			padding: 20rpx 10rpx;

			.pickerBox {
				position: relative;
				height: 60rpx;
				width: 380rpx;
				border-radius: 14rpx;
				border: 1px solid #eee;
				display: flex;
				margin-top: 14rpx;
				font-size: 30rpx;
				align-items: center;
				padding: 0 10rpx;
			}
		}
	}

	.btnClass {
		width: fit-content;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		// border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		background-color: cornflowerblue;
		margin: 10rpx auto;
	}
</style>