<template>
	<view class="box">
		<view class="resBox">
			<view class="resTitle">
				<view class="icon-chenggong" style="font-size: 80rpx;" :style="{color:themeColor.main_color}"></view>
				<text style="font-size: 48rpx;font-weight: 600;padding-left: 20rpx;">付款成功</text>
			</view>
			<p style="margin-top: 30rpx;color: dimgray;font-size: 24rpx;">您已成功购买菜品，可以通过「订单详情」查看！</p>
		</view>
		<view class="btnClass">
			<view class="ording" @click="goOrding">
				查看订单
			</view>
			
			<view class="goDetail" @click="goDetail" :style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}">
				回到首页
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'unit', 'startDate', 'endDate', 'roomInfo', 'linkMan', 'shopSetting']),
			},
			methods:{
				goDetail(){
					uni.switchTab({
						url:'/pages/index/index'
					})
				},
				goOrding(){
					uni.navigateTo({
						url:'/packageB/food/order/order'
					})
				}
			}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #fff;
	}
	.box {
		height: 100vh;
		background: #fff;
	}
	
	.resBox{
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
		.resTitle{
			margin-top: 140rpx;
			display: flex;
			align-items: center;
		}
	}
	
	.btnClass{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 80rpx 50rpx;
		.ording{
			height: 100rpx;
			border: 1px solid #7d7d7d;
			color: #7d7d7d;
			width: 300rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.goDetail {
			height: 100rpx;
			width: 300rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>
