<template>
	<view class="content">
		<view class="tabHeads" style="z-index: 9999999;">
			<view class="tabLabel" @click="tabsClick(item)" v-if="index==0||index==2" v-for="(item,index) in tabsArray"
				:key="index">
				<view :class="[isNum === item.inx?'isTabActive':'default']"
					:style="isNum === item.inx?'border-bottom: 3px solid '+themeColor.main_color:'default'">
					{{item.name}}</view>
			</view>
		</view>
		<view class="headWrap" style="z-index: 9999999;">
			<view class="labelDiv" v-for="(item,index) in labels" :key="index" @click.stop="toggle(item)">
				<view class="labelDivItem"
					:style="isActive === item.id?'background:' + themeColor.main_color+';color:'+themeColor.bg_color:''">
					{{item.name}}
				</view>

			</view>
		</view>
		<scroll-view style="position: relative;" :style="'height:'+wHeight+'px'" :scroll-top="scrollTop"
			scroll-y="true">
			<view class="couponMain" v-if="isActive!=3">
				<view class=""
					style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;"
					v-if="coupons.length==0">
					<view class="icon-zanwuyouhuiquan1" style="font-size: 260rpx;color: #c1c1c1;">
					</view>
					<p style="color: #c1c1c1;">暂无优惠券</p>
				</view>
				<view class="coupon" v-for="(item,index) in coupons" v-else :key="index">
					<view class="couponTop"
						:class="[isNum ===1?item.coupon_info.type_id ===1?'bgColorTop1':item.coupon_info.type_id ===2?'bgColorTop2':'bgColorTop3':'bgColorTop4']">
						<view class="couponTopLeft">
							<view class="couponTypeDiv">
								<view class="tpyeNameSty"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'useBtnBgColor1':item.coupon_info.type_id ===2?'useBtnBgColor2':'useBtnBgColor3':'useBtnBgColor4']">
									{{item.coupon_info.type_id ===1?'订房券':item.coupon_info.type_id ===2?'超市券':(item.coupon_info.type_id ===3?'周边券':(item.coupon_info.type_id ===4?'点餐券':'商城券'))}}
								</view>
							</view>
							<view class="valueSty"
								:class="[isNum ===1?item.coupon_info.type_id ===1?'color1':item.coupon_info.type_id ===2?'color3':'color5':'color7']">
								<text v-if="item.coupon_info.type_id ===1" class="symbolMoney">￥</text>
								<text v-if="item.coupon_info.type_id ===1"
									class="moneyVal">{{item.coupon_info.discounts}}</text>
								<!-- <text v-if="item.type_id ===2" class="moneyVal">{{item.discount}}</text>
								<text v-if="item.type_id ===2" style="font-size: 14px;">折</text>
								<text v-if="item.type_id ===3" class="moneyVal">{{item.num}}</text>
								<text v-if="item.type_id ===3" style="font-size: 14px;">件</text> -->
							</view>
							<view v-if="item.coupon_info.use_condition ===0 " class="useCondition"
								:class="[isNum ===1?item.coupon_info.type_id ===1?'borderColor1 color1':item.coupon_info.type_id ===2?'borderColor2 color3':'borderColor3 color5':'borderColor4 color7']">
								无门槛使用
							</view>
							<view v-else class="useCondition"
								:class="[isNum ===1?item.coupon_info.type_id ===1?'borderColor1 color1':item.coupon_info.type_id ===2?'borderColor2 color3':'borderColor3 color5':'borderColor4 color7']">
								<text>满{{item.coupon_info.use_condition}}元可用</text>
								<!-- <text>{{item.type_id ===3?'兑':'用'}}</text> -->
							</view>
						</view>
						<view class="couponTopRight">
							<view class="ctr-left">
								<view class="couponName"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'color1':item.coupon_info.type_id ===2?'color3':'color5':'color7']">
									{{item.coupon_info.name}}
								</view>
								<view class="couponStore"
									:class="[isNum ===1?item.coupon_info.coupon_info.type_id ===1?'color2':item.coupon_info.coupon_info.type_id ===2?'color4':'color6':'color8']">
									{{item.coupon_info.desc}}
								</view>
								<view class="couponDate"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color4':'color6':'color8']">
									有效期:{{item.limit_time | moment}}
								</view>
							</view>
							<view class="ctr-right">
								<text class="useBtn" @click="toGet(item)"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'useBtnBgColor1':item.coupon_info.type_id ===2?'useBtnBgColor2':'useBtnBgColor3':'useBtnBgColor4']">{{isNum ===1?'去使用':(isNum ===2?'已使用':'已过期')}}</text>
							</view>
						</view>
					</view>
					<view class="couponBottom"
						:class="[isNum ===1?item.coupon_info.type_id ===1?'bgColorTBottom1':item.coupon_info.type_id ===2?'bgColorTBottom2':'bgColorTBottom3':'bgColorTBottom4']">
						<view class="ruleLabel">
							<view class="ruleLabel-left" @click.stop="viewRules(item)">
								<text class="limit" style="margin-right: 30rpx;"
									:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color4':'color6':'color8']">
									{{item.coupon_info.usable_date.length > 0||item.coupon_info.usable_time||item.coupon_info.usable_week.length > 0||item.usable_shop_List.length > 0?'有使用限制':'无限制条件'}}
								</text>
							</view>
							<view class="ruleBtn"
								:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color4':'color6':'color8']"
								@click.stop="viewRules(item)">
								<text style="margin-right: 6px;">使用规则</text>
								<view class="arrowIcon"
									:class="[item.coupon_info.isViewRule?isNum ===1?item.coupon_info.type_id ===1?'rotate arrowIcon1':item.coupon_info.type_id ===2?'rotate arrowIcon2':'rotate arrowIcon3':'rotate arrowIcon4':isNum ===1?item.coupon_info.type_id ===1?'backRotate arrowIcon1':item.coupon_info.type_id ===2?'backRotate arrowIcon2':'backRotate arrowIcon3':'backRotate arrowIcon4']">
								</view>
							</view>
						</view>
						<view v-if="priceIndex.includes(item.coupon_info.id)" class="ruleDetail"
							:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color4':'color6':'color8']">
							<view class="" style="display: flex;align-items: flex-start;"
								v-if="item.usable_shop_List.length > 0">
								<text
									style="font-size: 24rpx;margin-top: 16rpx;max-width: 258rpx;min-width: 116rpx;">可用酒店：</text>
								<view class="ruleList" style="flex-wrap: wrap;margin-top: 16rpx;padding-top: 0;">
									<view style="margin-left:20rpx;display: flex;align-items: center;flex-wrap: wrap;"
										v-for="item1 in item.usable_shop_List">
										<view class="">
											{{item1.shop_name}}可用
										</view>
										<view class="" style="margin-left: 40rpx;">
											适用商品:<text
												v-for="item2 in item1.usable_goods_list">{{item2.goods_name}}、</text>
										</view>
									</view>
								</view>
							</view>
							<view class="ruleList" v-if="item.coupon_info.usable_date.length > 0">
								使用日期：<text v-for="item1 in item.coupon_info.usable_date">{{item1}}可用</text>
							</view>
							<view class="ruleList" v-if="item.coupon_info.usable_time">
								使用时间:{{item.coupon_info.usable_time}}前可用
							</view>
							<view class="ruleList" v-if="item.coupon_info.usable_week.length > 0">
								使用星期: <text style="margin-right: 10rpx;"
									v-for="item1 in item.coupon_info.usable_week">星期{{item1==1?'一':(item1==2?'二':(item1==3?'三':(item1==4?'四':(item1==5?'五':(item1==6?'六':'日')))))}}可用</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			<view class="" style="height: 100%;" v-else>
				<view class="breakBox" style="position: relative;" v-for="(item, index) in breakList" :key="index" @click="showQr(item)">
					<view class="" v-if="isNum==3" style="width: 100%;height: 100%;position: absolute;top: 0;left: 0;background-color: rgba(0, 0, 0, .2);display: flex;align-items: center;justify-content: center;">
						<text style="color: #FFFFFF;font-size: 44rpx;">已过期</text>
					</view>
					<view class="titleBox">
						<p class="title">{{item.name}}</p>
						<p style="color: crimson;" class="title">￥{{item.amount}}</p>
					</view>

					<p class="content">{{item.content}}</p>
					<p class="content">{{item.room_type_name}}</p>
					<p class="content" style="color:darkgray">订单ID:{{item.bill_id}}</p>
					<p class="content" style="color:darkgray">使用日期:{{item.date}}</p>
					<view class="qrBox"
						style="position: absolute;bottom: 10rpx;right: 10rpx;width: 120rpx;height: 140rpx;">
						<text class="icon-erweima" style="font-size: 100rpx;"></text>
						<text style="font-size: 22rpx;">点击展示</text>
					</view>
				</view>
				<view class="" @click="toBuy"
					style="position: fixed;width: 440rpx;height: 80rpx;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;background-color: #55aa7f;bottom: 20rpx;left: 0;right: 0;margin: auto;">
					<text style="color: #FFFFFF;">购买早餐券</text>
				</view>
			</view>
		</scroll-view>

		<!-- 酒店详情说明 -->
		<m-popup :show="pop" @closePop="closePop" mode="center">
			<view class=""
				style="height: 500rpx;width: 600rpx;display: flex;align-items: center;justify-content: center;flex-direction: column;">
				<image :src="qr" style="height: 400rpx;width: 400rpx;" mode="" show-menu-by-longpress></image>
				<p>提示:长按二维码可以分享早餐券给朋友</p>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				wHeight: 0,
				scrollTop: 0,
				isNum: 1,
				tabsArray: [{
					inx: 1,
					name: '未使用'
				}, {
					inx: 2,
					name: '已使用'
				}, {
					inx: 3,
					name: '已过期'
				}],
				priceIndex: [],
				isActive: 1,
				labels: [{
					id: 1,
					name: '订房券'
				}, {
					id: 2,
					name: '超市券'
				}, {
					id: 3,
					name: '早餐券'
				}],
				coupons: [],
				breakList: [],
				pop: false,
				qr: ''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		async onLoad() {
			await this.$onLaunched;
			uni.hideTabBar()
			const res = uni.getSystemInfoSync()
			this.wHeight = res.windowHeight - 86
		},
		async onShow() {
			await this.$onLaunched;
			this.isActive = 1
			uni.showLoading({
				title: '加载中...'
			})
			this.$iBox.http('getUserCoupon', {
				type_id: this.isActive,
				use_status: 0
			})({
				method: 'post'
			}).then(res => {
				this.coupons = res.data
				uni.hideLoading()
			})
		},
		methods: {
			tabsClick(item) {
				console.log(item);
				this.isNum = item.inx
				uni.showLoading({
					title: '加载中...'
				})
				if (this.isActive != 3) {
					this.$iBox.http('getUserCoupon', {
						type_id: this.isActive,
						use_status: this.isNum - 1
					})({
						method: 'post'
					}).then(res => {
						this.coupons = res.data
						uni.hideLoading()
					})
				} else {
					if(this.isNum==1){
						uni.showLoading({
							title: '加载中...'
						})
						let params = {}
						params.page = 1
						params.shop_id = this.hotel.id
						this.$iBox.http('getUserBreakfastCouponList', params)({
							method: 'post'
						}).then(res => {
							uni.hideLoading()
							this.breakList = res.data.filter(item => {
									return item.date == this.$moment().format('YYYY-MM-DD')
							})
						})
					}else{
						uni.showLoading({
							title: '加载中...'
						})
						let params = {}
						params.page = 1
						params.shop_id = this.hotel.id
						this.$iBox.http('getUserBreakfastCouponList', params)({
							method: 'post'
						}).then(res => {
							uni.hideLoading()
							this.breakList = res.data.filter(item => {
								console.log(this.$moment(item.date,'YYYY-MM-DD').unix(),this.$moment(this.$moment().format('YYYY-MM-DD'),'YYYY-MM-DD').unix());
								return this.$moment(item.date,'YYYY-MM-DD').unix()   <  this.$moment(this.$moment().format('YYYY-MM-DD'),'YYYY-MM-DD').unix();
							})
							console.log(this.breakList);
						})
					}
					
				}

			},
			toggle(item) {
				this.isActive = item.id
				uni.showLoading({
					title: '加载中...'
				})
				
				if (this.isActive == 1) {
					this.$iBox.http('getUserCoupon', {
						type_id: this.isActive,
						use_status: this.isNum - 1
					})({
						method: 'post'
					}).then(res => {
						this.coupons = res.data
						uni.hideLoading()
					})
				} else if (this.isActive == 2) {
					this.$iBox.http('getUserMarketCoupon', {
						type_id: this.isActive,
						use_status: this.isNum - 1
					})({
						method: 'post'
					}).then(res => {
						this.coupons = res.data
						uni.hideLoading()
					})
				} else {
					if(this.isNum==1){
						uni.showLoading({
							title: '加载中...'
						})
						let params = {}
						params.page = 1
						params.shop_id = this.hotel.id
						this.$iBox.http('getUserBreakfastCouponList', params)({
							method: 'post'
						}).then(res => {
							uni.hideLoading()
							this.breakList = res.data.filter(item => {
								return item.date == this.$moment().format('YYYY-MM-DD')
							})
						})
					}else{
						uni.showLoading({
							title: '加载中...'
						})
						let params = {}
						params.page = 1
						params.shop_id = this.hotel.id
						this.$iBox.http('getUserBreakfastCouponList', params)({
							method: 'post'
						}).then(res => {
							uni.hideLoading()
							this.breakList = res.data.filter(item => {
								return this.$moment(item.date,'YYYY-MM-DD').unix()   <  this.$moment(this.$moment().format('YYYY-MM-DD'),'YYYY-MM-DD').unix();
							})
						})
					}
					
				}
				
				


			},
			toBuy() {
				uni.navigateTo({
					url: '/packageB/buyFood/buyFood'
				})
			},
			showQr(e) {
				if(this.isNum==3){
					return
				}
				this.pop = true
			
				this.qr = e.qr
			},
			closePop() {
				this.pop = false
			},
			viewRules(e) {
				if (this.priceIndex.includes(e.coupon_info.id)) {
					this.priceIndex = this.priceIndex.filter(item => {
						return item != e.coupon_info.id
					})
				} else {
					this.priceIndex.push(e.coupon_info.id)
				}
			},
			toGet(e) {
				if (this.isNum == 3) {
					uni.showToast({
						icon: 'none',
						title: '此优惠券已经过期'
					})
				} else {
					uni.switchTab({
						url: '/pages/index/index'
					})
				}

			}
		}
	}
</script>

<style scoped lang="scss">
	.breakBox {
		width: 700rpx;
		margin: 30rpx auto;
		height: 300rpx;
		border-radius: 20rpx;
		background-color: #fff;
		padding: 30rpx;
		position: relative;
		z-index: 0;

		.titleBox {
			display: flex;
			height: 80rpx;
			width: 100%;
			align-items: center;
			justify-content: space-between;

			.title {
				font-size: 40rpx;
				font-weight: 700;
			}
		}



		.content {
			font-size: 30rpx;
			color: #454545;
		}

		.qrBox {
			display: flex;
			flex-direction: column;
			align-items: center;
		}
	}

	.content {
		padding: 0;
		overflow: hidden;
	}

	.tabHeads {
		display: flex;
		background-color: #fff;
		margin-bottom: 1px;
	}

	.tabLabel {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
	}

	.default {
		color: #999999;
		padding: 10px 0;
		border-bottom: 1px solid #ffffff;
	}

	.isTabActive {
		font-weight: 600;
		padding: 10px 0;

	}

	.headWrap {
		padding: 10px;
		background-color: #fff;
		display: flex;
		align-items: center;
		/* justify-content: space-between; */
		flex-wrap: wrap;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
	}

	.labelDiv {
		width: 25%;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;

		.labelDivItem {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.active {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			// background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

	}



	.couponMain {
		padding: 10px;
		display: flex;
		flex-direction: column;
		// background: #ffffff;
	}

	.coupon {
		margin-bottom: 20px;
	}

	.couponTop {
		display: flex;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
	}

	.couponTopLeft {
		flex: 1.2;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		position: relative;
		overflow: hidden;
	}

	.couponTypeDiv {
		/* overflow: hidden; */
		/* position: relative; */
	}

	.imgType {
		width: 46px;
		height: 40px;
		position: absolute;
		top: 0;
		left: 0;
	}

	.tpyeNameSty {
		font-size: 19rpx;
		transform: rotate(-40deg);
		position: absolute;
		top: 10rpx;
		left: -52rpx;
		background-color: grey; // 背景色
		color: #fff;
		// 以下属性会影响斜边标签的显示
		width: 80%;
		height: 18px;
		line-height: 18px;
		transform: rotate(-40deg);
		text-align: center;
	}

	.valueSty {
		margin-bottom: 10px;
	}

	.symbolMoney {
		font-size: 16px;
	}

	.moneyVal {
		font-size: 24px;
	}

	.useCondition {
		font-size: 10px;
		padding: 4px 10px;
		border-radius: 12px;
	}

	.couponTopRight {
		flex: 3;
		display: flex;
		padding-bottom: 12px;
	}

	.ctr-left {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.couponName {
		font-size: 16px;
		padding: 14px 0;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.couponStore {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
		padding-bottom: 8px;
	}

	.couponDate {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.ctr-right {
		display: flex;
		align-items: flex-end;
		justify-content: center;
		padding: 0 10px;
	}

	.useBtn {
		color: #fff;
		font-size: 12px;
		padding: 6px 20px;
		border-radius: 18px;
	}

	.couponBottom {
		display: flex;
		flex-direction: column;
		padding: 10px;
		border-bottom-left-radius: 4px;
		border-bottom-right-radius: 4px;
	}

	.ruleLabel {
		display: flex;
		align-items: center;
	}

	.ruleLabel-left {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.overlay {
		margin-right: 10px;
		padding: 4px 10px;
		font-size: 10px;
		border-radius: 11px;
	}

	.limit {
		font-size: 12px;
	}

	.ruleBtn {
		display: flex;
		align-items: center;
		font-size: 12px;
	}

	.arrowIcon {
		position: relative;
		width: 6px;
		height: 6px;
		transform: rotate(135deg);

	}

	.rotate {
		transform: rotate(-45deg);
		bottom: -2px;
	}

	.backRotate {
		transform: rotate(135deg);
		top: -2px;
	}

	.ruleDetail {
		display: flex;
		flex-direction: column;
	}

	.ruleList {
		padding-top: 10px;
		font-size: 12px;
	}

	/* 颜色配置 */
	.borderColor1 {
		border: 1px solid #5f98ff;
	}

	.borderColor2 {
		border: 1px solid #ff7979;
	}

	.borderColor3 {
		border: 1px solid #fc932c;
	}

	.borderColor4 {
		border: 1px solid #c3c3c3;
	}

	.arrowIcon1 {
		border-top: 1px solid #5f98ff;
		border-right: 1px solid #5f98ff;
	}

	.arrowIcon2 {
		border-top: 1px solid #ff7979;
		border-right: 1px solid #ff7979;
	}

	.arrowIcon3 {
		border-top: 1px solid #fc932c;
		border-right: 1px solid #fc932c;
	}

	.arrowIcon4 {
		border-top: 1px solid #c3c3c3;
		border-right: 1px solid #c3c3c3;
	}

	.useBtnBgColor1 {
		background-color: #2b6feb;
	}

	.useBtnBgColor2 {
		background-color: #ff5555;
	}

	.useBtnBgColor3 {
		background-color: #fa830e;
	}

	.useBtnBgColor4 {
		background-color: #bebebe;
	}

	.color1 {
		color: #2b6feb;
	}

	.color2 {
		color: #5f98ff;
	}

	.color3 {
		color: #ff5555;
	}

	.color4 {
		color: #ff7979;
	}

	.color5 {
		color: #fa830e;
	}

	.color6 {
		color: #fc932c;
	}

	.color7 {
		color: #bebebe;
	}

	.color8 {
		color: #c3c3c3;
	}

	.bgColor1 {
		background-color: #edf4ff;
	}

	.bgColor2 {
		background-color: #ffeeee;
	}

	.bgColor3 {
		background-color: #fff2e5;
	}

	.bgColor4 {
		background-color: #f3f3f3;
	}

	.bgColorTop1 {
		background: radial-gradient(circle at left bottom, transparent 6px, #edf4ff 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #edf4ff 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom1 {
		background: radial-gradient(circle at left top, transparent 6px, #dae6ff 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #dae6ff 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop2 {
		background: radial-gradient(circle at left bottom, transparent 6px, #ffeeee 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #ffeeee 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom2 {
		background: radial-gradient(circle at left top, transparent 6px, #ffd7d7 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffd7d7 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop3 {
		background: radial-gradient(circle at left bottom, transparent 6px, #fff2e5 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #fff2e5 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom3 {
		background: radial-gradient(circle at left top, transparent 6px, #ffe0c1 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffe0c1 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop4 {
		background: radial-gradient(circle at left bottom, transparent 6px, #f3f3f3 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #f3f3f3 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom4 {
		background: radial-gradient(circle at left top, transparent 6px, #ededed 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ededed 0) top right / 50% 100% no-repeat;
	}
</style>