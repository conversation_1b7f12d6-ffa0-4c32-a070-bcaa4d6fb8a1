<template>
	<view>
		<view class="couponBox" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
			<view class="title">
				<text style="color: #a07340;font-weight: 600;">{{userInfo.grade_name?userInfo.grade_name:''}} · 本单专享</text>
			</view>
			<view class="room" @click="chooseCoup">
				<text style="padding-right: 80rpx;">优惠券</text>
				<view class="" style="padding: 0;display: flex;align-items: center;">
					<view style="color: #e78727;font-size: 28rpx;" v-if="choosePrice>0">{{'已优惠'+choosePrice+'元'}}</view>
					<view class="" v-if="choosePrice==0&&index==0"
						style="border: 1px solid #e78727;color:#e78727;padding: 6rpx;font-size: 26rpx;border-radius: 6rpx;"
						v-for="(item, index) in coupons">
						<text v-if="item.discount_type==1">可选{{item.coupon_info.name}}减￥{{item.coupon_info.discounts}}</text>
						<text v-if="item.discount_type==2">可选{{item.coupon_info.name}}享{{item.coupon_info.discount_rate*10}}折优惠</text>
					</view>
					<view class="icon-jiantou" :style="{color:themeColor.text_title_color}">
					</view>
				</view>
			</view>
			<view class="pointService" v-if="serviceItmes.length > 0">
				<view class="">
					<text style="padding-right: 80rpx;">专享权益</text>
					<text>有{{userInfo.point?userInfo.point:0}}积分可用</text>
				</view>

				<view class="skill-sequence-panel-content-wrapper">
					<!--横向滚动-->
					<scroll-view class="skill-sequence-panel-content" scroll-x>
						<view class="skill-sequence-skill-wrapper" v-for="item in serviceItmes" @click="getService(item)" :key="item.id">
							<view class="skill_bot" style="">
								<p style="font-size: 28rpx;font-weight: 600;">{{item.title}}</p>
								<p style="font-size: 24rpx;font-weight: 300;padding-top: 20rpx;">{{item.desc}}</p>
								
							</view>
							<view class="" style="padding: 20rpx;font-size: 28rpx;display: flex;align-items: center;">
								<text>{{item.point}}积分</text>
								<view class="checkBox" :style="'border:1px solid '+themeColor.main_color">
									<view v-if="chooseService.includes(item)" class="choose" :style="'background:'+themeColor.main_color">
									</view>
								</view>
							</view>

						</view>
					</scroll-view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-pointService",
		data() {
			return {
				coupons: [],
				coupNum: 0,
				limit_num: 0,
				serviceItmes: [],
				chooseService:[]
			};
		},
		props: {
			coupType: {
				type: Number
			},
			limitNum: {
				type: Number
			},
			choosePrice: {
				type: Number
			},
			limitTime: {
				type: Number
			},
			roomType: {
				type: Number
			},
		},
		watch: {

			choosePrice: {
				handler(oldData, newData) {
					console.log(this.choosePrice, 'kkkkk');
					this.choosePrice = this.choosePrice
				},
				immediate: true
			},
			limitNum: {
				handler(newVal, oldVal) {
					console.log(this.limitNum, 'kkkkkwwww');
			
					if(this.coupType==1){
						this.$iBox.http('getUserCoupon', {
							type_id: this.coupType,
							use_status: 0,
							shop_id: this.hotel.id,
							room_type_id: this.roomType
						})({
							method: 'post'
						}).then(res => {
							let a = 0
							let coupons = res.data
							coupons.forEach(item => {
								
								if( item.coupon_info.type_id && Number(item.coupon_info.use_condition) <= newVal && this.$moment().unix() <= item.limit_time && (item.usable_week.length > 0 && item.usable_week.includes(this.$moment().isoWeekday().toString()) || item.usable_week.length == 0)){
									item.usable = 1
								}else{
									item.usable = 0
								}
							})
							
							let list_new = []
							coupons.forEach(item => {
								if ((item.usable)) {
									a += 1
									list_new.push(item)
								}
							})
							this.coupNum = a
							this.coupons = list_new
						})
					}else {
						this.$iBox.http('getUserMarketCoupon', {
							type_id: this.coupType,
							use_status: 0,
							shop_id: this.hotel.id
						})({
							method: 'post'
						}).then(res => {
							let a = 0
							let coupons = res.data
							coupons.forEach(item => {
								
								if( item.coupon_info.type_id && Number(item.coupon_info.use_condition) <= newVal && this.$moment().unix() <= item.limit_time && (item.usable_week.length > 0 && item.usable_week.includes(this.$moment().isoWeekday().toString()) || item.usable_week.length == 0)){
									item.usable = 1
								}else{
									item.usable = 0
								}
							})
							
							let list_new = []
							coupons.forEach(item => {
								if ((item.usable)) {
									a += 1
									list_new.push(item)
								}
							})
							this.coupNum = a
							this.coupons = list_new
							uni.hideLoading()
						})
					}
					
				},
				immediate: true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		mounted() {
			// this.serviceItmes = [{
			// 		id: 1,
			// 		title: '房费立减10块',
			// 		desc: '积分抵现金',
			// 		point: '400'
			// 	}, {
			// 		id: 2,
			// 		title: '延迟退房至14:00',
			// 		desc: '安心多睡',
			// 		point: '300'
			// 	}, {
			// 		id: 3,
			// 		title: '延迟退房至14:00',
			// 		desc: '安心多睡',
			// 		point: '300'
			// 	}, {
			// 		id: 3,
			// 		title: '延迟退房至14:00',
			// 		desc: '安心多睡',
			// 		point: '300'
			// 	}, ]
		},

		methods: {
			chooseCoup() {
				this.$emit('chooseCoup', '1', )
			},
			getService(e){
				if(this.chooseService.includes(e)){
					this.chooseService = this.chooseService.filter(item=>{
						return item.id != e.id
					})
				}else{
					this.chooseService.push(e)
				}
				
			}
		}
	}
</script>

<style lang="scss">
	.couponBox {
		width: 688rpx;
		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;
		padding: 0rpx 30rpx;

		.title {
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-top-right-radius: 20rpx;
			border-top-left-radius: 20rpx;
			padding: 30rpx 0;
			// border-bottom: 1px solid #e4e7ed;
		}

		.room {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid #e4e7ed;
		}

		.pointService {
			padding: 30rpx 0;

			/*scroll-view外层*/
			.skill-sequence-panel-content-wrapper {
				position: relative;
				// height: 300rpx;
				white-space: nowrap;
			}

			/*scroll-view本身*/
			.skill-sequence-panel-content {
				min-width: 100%;
			}

			/*scroll-view内层*/
			.skill-sequence-skill-wrapper {
				display: inline-block;
				width: 250rpx;
				height: 200rpx;
				margin: 20rpx 20rpx 20rpx 0;
				border-radius: 10rpx;

				border: 1px solid rgba(156, 99, 42, .5);
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				word-break: break-all;

				.skill_bot {
					height: 110rpx;
					width: 100%;
					padding: 10rpx;
					background-color: rgba(249, 159, 69, 0.1);
					color: rgba(157, 100, 43, 0.9)
				}
				
				.checkBox {
					height: 40rpx;
					width: 40rpx;
					border-radius: 50%;
					// border: 1px solid #0055ff;
					margin-left: 30rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					.choose {
						height: 24rpx;
						width: 24rpx;
						border-radius: 50%;
						// background-color: #0055ff;
					}
				}
				
			

			}

		}
	}
</style>
