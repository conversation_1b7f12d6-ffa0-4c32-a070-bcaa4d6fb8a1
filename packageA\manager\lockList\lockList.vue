<template>
	<view>
		<view class="box" :style="item.sign== 'tongtong'||item.sign=='yaya'?'background:#fff':'background:#eee'" hover-class="bind_lock" v-for="(item, index) in lockList" :key="index"  @click="goLock(item)">
			<view class="content">
				<text class="icon-mimasuo" style="font-size: 80rpx;color:#6164ff;"></text>
				
				<text style="padding-left: 20rpx;">{{item.name}}</text>
			</view>
			<text class="icon-jiantou" v-if="item.sign=='tongtong'||item.sign=='yaya'"></text>
			<text v-else style="color: #333333;">暂未开放</text>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				lockList: [{id:1,sign:'tongtong',lock_name:'通通锁'}]
			}
		},
		computed: {
			...mapState('login', ['userInfo', 'theme', 'menu']),
			...mapState('hotel', ['hotel', 'roles_list']),
		},
		async onLoad() {
			await this.$onLaunched;
			this.getWxAuthorizeLocation()
			this.getWxAuthorizeBle()
		},
		async onShow() {
			await this.$onLaunched;
			this.$iBox.http('getLockTypeList', {
					
				})({
					method: 'post'
				})
				.then(res => {
					this.lockList = res.data
					uni.hideLoading()
				})
			
			
			
		},
		methods: {
			goLock(e){
				console.log(e,'点击');
				if(e.sign == 'tongtong'){
					uni.navigateTo({
						url:'/packageA/manager/lockList/TTLock/lockDetail'
					})
				}else if(e.sign == 'yaya'){
					uni.navigateTo({
						url:'/packageA/manager/lockList/YYLock/lockDetail'
					})
				}else{
					uni.showToast({
						icon:'none',
						title:'暂不支持'
					})
				}
			},
			//===========================================权限验证=================================================
			
			getWxAuthorizeLocation: function() {
				wx.getSetting({
					success(res) {
						console.log(res);
						// 如果从未申请定位权限，则申请定位权限
						if (res.authSetting['scope.userLocation'] == null) {
							wx.authorize({
								scope: 'scope.userLocation',
								success() {
									// 用户同意
									// 相关操作
								},
								fail() {
									wx.showToast({
										title: '无法申请定位权限,请确认是否已经授权定位权限',
										icon: "none",
										duration: 2000
									})
								}
							})
						}
						// 如果已经有权限，就查询
						else if (res.authSetting['scope.userLocation'] == true) {
							// 相关操作
						}
						// 被拒绝过授权，重新申请
						else {
							wx.showModal({
								title: '位置信息授权',
								content: '位置授权暂未开启，将导致无法正常手机开门',
								cancelText: '仍然拒绝',
								confirmText: '开启授权',
								success: function(res) {
									if (res.confirm) {
										wx.openSetting({
											fail: function() {}
										})
									} else {
			
									}
								}
							})
						}
					}
				});
			},
			
			getWxAuthorizeBle: function() {
				uni.getSystemInfo({
					success(res) {
						console.log(res,'蓝牙');
						if (!res.bluetoothEnabled) {
							uni.showModal({
								title: '提示!',
								content: '系统蓝牙未打开，请打开后重试！',
								showCancel: false,
								success: res => {
									
								}
							})
						}
						
						if (!res.locationEnabled) {
							uni.showModal({
								title: '提示!',
								content: '手机定位未打开！',
								showCancel: false,
								success: res => {
									
								}
							})
						}	
						
						if (!res.locationAuthorized) {
							uni.showModal({
								title: '提示!',
								content: '请授权微信使用定位功能!',
								showCancel: false,
								success: res => {
									
								}
							})
						}
						
					}
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.box{
		height: 160rpx;
		width: 100%;
		border: 1px solid #eee;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		
		.content {
			display: flex;
			align-items: center;
			justify-content: center;
			
		}
	}

.bind_lock{
		opacity: 0.9;
		  background: #f7f7f7;
	}
</style>
