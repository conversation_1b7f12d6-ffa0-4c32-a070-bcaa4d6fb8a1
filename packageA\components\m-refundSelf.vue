<template>
	<view>
		<m-popup :show="poprc" mode="center" @closePop="closePopPay">
			<scroll-view scroll-y="true" style="height: 70vh;">
				<view class="payBox">
					<p style="width: 100%;display: flex;justify-content: center;">自定义退款</p>
					<view class="itemBox">
						<p style="font-size: 28rpx;">系统生成账户</p>
						<uni-data-checkbox mode="button" @change="changePay" class="radioPad" v-model="payName"
							:localdata="payList"></uni-data-checkbox>
					</view>
					<view class="itemBox">
						<p style="font-size: 28rpx;">自定义账户</p>
						<uni-data-checkbox mode="button" @change="changeSelfPay" class="radioPad" v-model="paySelfName"
							:localdata="selfList"></uni-data-checkbox>
					</view>
					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">金额</p>
						<!-- <uni-number-box v-model="billMoney" max="1000000"/> -->
						<uni-easyinput v-model="billMoney" placeholder="0" type="digit"></uni-easyinput>
					</view>
					<view class="itemBox" style="background-color: #ffffff;">
						<p style="font-size: 28rpx;">备注</p>
						<uni-easyinput type="textarea" v-model="remark" placeholder="请输入内容"></uni-easyinput>
					</view>
					<view class="btnClass" @click="getPayTh">
						退款
					</view>
				</view>
			</scroll-view>

		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				payList: [],
				selfList: [],
				groupList: [],
				payName: 0,
				paySelfName: 0,
				billMoney: 0,
				remark: ''
			};
		},
		props: {
			poprc: {
				type: Boolean,
				default: false
			},
			bill: {
				type: Object
			},
			refundType: {
				type: Number
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		watch: {
			poprc: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.$iBox
							.http('getAccountList', {
								type: 5
							})({
								method: 'post'
							})
							.then(res => {
						
								this.payList = res.data.filter(item => {
									return  ['wx_pay_off_line','ali_pay','cash_pay','independent','member_pay','jl_pay'].includes(item.sign)
								})
						
								this.payList.forEach(item => {
									item.text = item.account_name
									item.value = item.id
								})
						
								this.payName = this.payList[0].id
						
								this.selfList = res.data.filter(item => {
									return  !['wx_pay_off_line','ali_pay','cash_pay','independent','member_pay','jl_pay'].includes(item.sign)
								})
						
								this.selfList.forEach(item => {
									item.text = item.account_name
									item.value = item.id
								})
						
								uni.hideLoading()
							})
						this.remark = ''
						if (this.refundType == 1) {
							this.$iBox
								.http('getRoomBillFund', {
									type: 2,
									bill_id: this.bill.id,
									pid: 0
								})({
									method: 'post'
								}).then(res => {

									this.groupList = res.data.list.filter(item => {
										return item.account_type == 1 && item.sign != 'member_pay' && item
											.sign != 'independent'

									})
									console.log(this.groupList, 'this.groupList');
									let price = 0
									this.groupList.forEach(item => {
										price += (item.amount - item.edit_amount - item.refund_amount)
									})
									this.billMoney = Number(price.toFixed(2))
								})
						} else if (this.refundType == 2) {
							this.$iBox
								.http('getRoomBillFundByConnectCode', {
									type: 2,
									bill_id: this.bill.id,
									pid: 0,
									connect_code: this.bill.connect_code
								})({
									method: 'post'
								})
								.then(res => {
									this.groupList = res.data.list.filter(item => {
										return item.account_type == 1 && item.sign != 'member_pay' && item
											.sign != 'independent'

									})
									console.log(this.groupList, 'this.groupList');
									let price = 0
									this.groupList.forEach(item => {
										price += (item.amount - item.edit_amount - item.refund_amount)
									})
									this.billMoney = Number(price.toFixed(2))
								})
						}


					}
				},
				immediate: true,
				deep: true
			}
		},
		mounted() {
			


		},
		methods: {
			closePopPay() {
				this.$emit('closeRefund', '')
			},
			changePay(e) {
				console.log(e);
				this.paySelfName = 0

			},
			changeSelfPay(e) {
				console.log(e);
				this.payName = 0
			},
			getPayTh() {
				this.$iBox.throttle(() => {
					this.getPay()
				}, 2000);
			},
			getPay() {
				if (!this.remark) {
					uni.showToast({
						icon: 'none',
						title: '请填写退款原因!'
					})
					return
				}
				let params = {
					bill_id: this.bill.id,
					refund_amount: this.billMoney,
					memo: this.remark,
					account_id: this.payName
				}
				console.log(params);
				this.$iBox.http('refundBillDetailByAdmin', params)({
					method: 'post'
				}).then(res => {
					this.$emit('closeRefund', '')
					this.$emit('upBillDetail', '')
				})
			}

		}

	}
</script>

<style lang="scss" scoped>
	.payBox {
		height: 70vh;
		width: 680rpx;
		padding: 10rpx;

		.itemBox {
			width: 98%;
			background: #f6f6f6;
			margin: 0 auto;
			padding: 20rpx 10rpx;

			.pickerBox {
				position: relative;
				height: 60rpx;
				width: 380rpx;
				border-radius: 14rpx;
				border: 1px solid #eee;
				display: flex;
				margin-top: 14rpx;
				font-size: 30rpx;
				align-items: center;
				padding: 0 10rpx;
			}
		}
	}

	.btnClass {
		width: fit-content;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		// border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		background-color: cornflowerblue;
		margin: 10rpx auto;
	}
</style>
