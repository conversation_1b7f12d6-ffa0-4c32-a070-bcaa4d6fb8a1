<template>
	<view>
		<p class="right">{{copyRight.set_web_copyright}}</p>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name:"m-banner",
		props:{
		
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['copyRight'])
		},
		data() {
			return {
				
			};
		},
		mounted() {
			console.log(this.img_url,'imgurl');
		},
		methods:{
			
		}
	}
</script>

<style lang="scss" scoped>
	.right {
		display: flex;
		align-items: center;
		justify-content: center;
		height: 80rpx;
		font-size: 22rpx;
		color: #c0c4cc;
		width: 100vw;
		margin-top: 80rpx;
		text-align: center;
		padding-left: 30rpx;
		padding-right: 30rpx;
	}
</style>
