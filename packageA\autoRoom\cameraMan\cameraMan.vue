<template>
	<view>
		<view class="" v-if="!billDetail">
			<m-popup mode="center" :show="!billDetail">
				<view class=""
					style="width: 600rpx;height: 300rpx;display: flex;align-items: center;justify-content: center;">
					<text>请去前台办理！</text>
				</view>
			</m-popup>
		</view>
		<view class="camera_box" v-else>
			<camera class="camera" device-position="front" v-if="!show" flash="off">
				<cover-view class="id_m">
					<cover-view style="font-size: 40rpx;">请点击下方拍摄按钮进行拍照识别！</cover-view>
					<cover-view style="font-size: 26rpx;margin-top: 20rpx;">(非活体自动识别，请手动拍照!)</cover-view>
					<cover-image class="img1" src="/static/images/manAround.png"
						:style="{transform:'scale('+tipSize/100+')'}"></cover-image>

				</cover-view>
			</camera>
			<image class="camera_img" :src="img_url" v-if="show"></image>
			<cover-view class="" v-if="numSet>0" 
			style="font-size: 330rpx;color: #FFFFFF;
			position: fixed;top: 0;left: 0;bottom:0;right:0;width: 300rpx;height: 450rpx;z-index: 999;margin: auto;display: flex;align-items: center;justify-content: center;">
				{{numSet}}
			</cover-view>
			<view class="action">
				<view class="btnTake" @click="takePhoto" :style="{background:themeColor.main_color}">
					<text>点击拍照</text>
				</view>
				<text class="icon-dianji my_xing"
					style="color: #FFFFFF;font-size: 90rpx;position: absolute;top: 70rpx;right: 140rpx;"></text>
			</view>
		</view>
	</view>
</template>

<script>
	const bgAudioManager = uni.getBackgroundAudioManager();
	import {
		mapState,
		mapActions
	} from 'vuex';

	export default {
		data() {
			return {
				src: '',
				show: false,
				forword: 'front',
				action: '',
				action1: '',
				img_url: '',
				errNum: 0,
				tipSize: 100,
				cameraContext: null,
				numSet: 3
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting']),
			...mapState('room', ['billDetail']),
		},
		onLoad() {

			if (uni.createCameraContext) {
				setTimeout(() => {
					this.cameraContext = uni.createCameraContext();
				}, 200)
			} else {
				// 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
				uni.showModal({
					title: '提示',
					content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
				})
			}


		},
		onShow() {
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					console.log(res.data);
					this.action = res.data + '/wx/RoomBill/faceContrast'
				}
			});

			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					console.log(res.data);
					this.action1 = res.data + '/wx/Resource/uploadFile'
				}
			});

			// 设置人脸大小
			console.log(this.shopSetting, 'this.shopSetting', this.billDetail);
			if (this.shopSetting.length > 0) {
				this.tipSize = this.shopSetting.filter(item => {
					return item.sign == 'user_image_size'
				})[0].property[0].value
			}

			if (this.billDetail) {
				bgAudioManager.title = '提醒'
				bgAudioManager.epname = '提醒'
				bgAudioManager.singer = '提醒'
				bgAudioManager.src =
					'https://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_mp3/%E6%8B%8D%E7%85%A7%E6%8C%89%E9%92%AE.mp3'

				let a = setInterval(item => {
					if (this.numSet > 0) {
						this.numSet--
					} else {
						this.takePhoto()
						clearInterval(a)
						
					}
				}, 1000)
			}

		},
		methods: {
			saveImg() {
				uni.showLoading({
					title: '识别中...'
				})
				console.log(this.src, 'this.src');
				uni.uploadFile({
					url: this.action,
					header: {
						'AUTHTOKEN': this.userInfo.user_token,
						'Content-Type': 'application/x-www-form-urlencoded',
						'chartset': 'utf-8'
					},
					filePath: this.src,
					name: 'file',
					formData: {
						'shop_id': this.hotel.id,
						'phone': this.userInfo.phone,
						'bill_id': this.billDetail.id
					},
					success: (uploadFileRes) => {
						console.log(uploadFileRes, 'ipipipipip');
						uni.hideLoading()
						if (JSON.parse(uploadFileRes.data).data) {
							uni.setStorage({
								key: 'authErr',
								data: 0
							})
							this.$iBox.http('selfCheckIn', {
								bill_id: this.billDetail.id
							})({
								method: 'post'
							}).then(res => {
								console.log('提示',res);
								uni.showModal({
									title: '提示',
									content: '认证成功!',
									showCancel: false,
									success: res => {
										if (res.confirm) {
											uni.reLaunch({
												url: '/pages/myRoom/myRoom'
											})
										}
									}
								})
							}).catch(err => {
								console.log('提示2',err);
								uni.showModal({
									title: '提示',
									content: err,
									showCancel: false,
									success: res => {
										if (res.confirm) {
											this.$iBox.http('selfCheckIn', {
												bill_id: this.billDetail.id
											})({
												method: 'post'
											}).then(res => {
												uni.showModal({
													title: '提示',
													content: '认证成功!',
													showCancel: false,
													success: res => {
														if (res.confirm) {
															uni.reLaunch({
																url: '/pages/myRoom/myRoom'
															})
														}
													}
												})
											}).catch(err => {
												uni.showModal({
													title: '提示',
													content: err,
													showCancel: false,
													success: res => {
														if (res
															.confirm) {
															uni.reLaunch({
																url: '/pages/myRoom/myRoom'
															})
														}
													}
												})
											})
										}
									}
								})
							})

						} else {

							++this.errNum
							if (this.errNum == 5) {
								uni.showModal({
									title: '提示',
									content: '身份证人像图无法识别，请重新上传身份证！',
									showCancel: false,
									success: res1 => {
										this.show = false
										uni.navigateBack({})
										uni.setStorage({
											key: 'authErr',
											data: this.errNum
										})

									}
								})
							} else {

								uni.showModal({
									title: '提示',
									content: JSON.parse(uploadFileRes.data).msg,
									showCancel: false,
									success: res1 => {
										this.show = false

									}
								})
							}
						}
					},
					fail: err => {
						uni.hideLoading()
						this.show = false
						uni.showModal({
							title: '提示',
							content: err
						})
					}
				});

			},
			takePhoto() {
				this.$iBox.throttle(() => {
					this.toTake()
				}, 5000);
			},
			toTake() {
				
				// const listener = ctx.onCameraFrame((frame) => {
				// 	console.log(frame)
				// })
				this.cameraContext.takePhoto({
					quality: 'normal',
					success: (res) => {
						console.log(res)
						this.src = res.tempImagePath,
							uni.uploadFile({
								url: this.action1,
								header: {
									'AUTHTOKEN': this.userInfo.user_token,
									'Content-Type': 'application/x-www-form-urlencoded',
									'chartset': 'utf-8'
								},
								filePath: this.src,
								name: 'file',
								formData: {
									'shop_id': this.hotel.id
								},
								success: (uploadFileRes) => {
									console.log(uploadFileRes);
									this.img_url = JSON.parse(uploadFileRes.data).data
									this.show = true

									this.saveImg()
								}
							});


						// listener.stop({
						// 	success: (res) => {
						// 		console.log(res)
						// 	},
						// 	fail: (err) => {
						// 		console.log(err)
						// 	}
						// })
					},
					fail: err => {

						uni.showModal({
							title: '提示',
							content: '拍照失败，无法调用摄像头'
						})
					}
				})
			},
			error() {
				uni.showModal({
					title: '提示',
					showCancel: false,
					confirmText: '授权',
					content: '请确认摄像头是否授权，否则无法拍照',
					success: res => {
						if (res.confirm) {
							uni.openSetting({

							})
							uni.navigateBack()
						}
					}
				})
			},
			//===========================================权限验证=================================================

		},

	}
</script>

<style lang="scss" scoped>
	/* pages/unit/camera/camera.wxss */
	.camera_box {
		height: 100vh;
		width: 100vw;
		position: relative;
	}

	.camera {
		height: 85vh;
		width: 100vw;
		z-index: 1;
	}

	.id_m {
		height: 85vh;
		width: 100vw;
		z-index: 999;
		background: rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: absolute;
		color: #ffffff;
	}

	.btnTake {
		width: 500rpx;
		height: 100rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #ffffff;
	}

	.img {
		width: 1200rpx;
		height: 550rpx;
		display: block;
		margin-top: 200rpx;
		// position: absolute;
		// left: 0; right: 100rpx; margin: auto auto;
		// top: 40rpx; bottom: 0;
		transform: rotate(90deg);
	}

	.img1 {
		width: 730rpx;
		height: 730rpx;
		display: block;
		margin-top: 40rpx;
		z-index: 996;
	}

	.id_m .tips_txt {
		transform: rotate(90deg);
	}

	.camera_box .action {
		height: 15vh;
		position: relative;
		display: flex;
		justify-content: space-around;
		// align-items: center;
		padding-top: 60rpx;
		background-color: #000;


		@keyframes xing {
			0% {
				transform: scale(1);
			}

			25% {
				transform: scale(1.4);
			}

			50% {
				transform: scale(1);
			}

			75% {
				transform: scale(1.4);
			}
		}

		.my_xing {
			-webkit-animation-name: xing;
			-webkit-animation-timing-function: ease-in-out;
			-webkit-animation-iteration-count: infinite;
			-webkit-animation-duration: 2s;
		}
	}

	// .camera_box .takeBtn {
	// 	height: 120rpx;
	// 	width: 120rpx;
	// 	border-radius: 50%;
	// 	font-size: 24rpx;
	// 	background: url('http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/xcx/%E6%8B%8D%E7%85%A7%E6%8C%89%E9%92%AE%20(1).png') no-repeat center;
	// 	background-size: contain;
	// 	border: none;
	// }

	// .camera_box .cancelBtn {
	// 	font-size: 24rpx;
	// 	height: 120rpx;
	// 	width: 120rpx;
	// 	border-radius: 50%;
	// 	background: url('http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/xcx/%E5%88%B7%E6%96%B0.png') no-repeat center;
	// 	background-size: contain;
	// 	border: none;
	// 	background-size: contain;
	// 	border: none;
	// }

	// .camera_box .saveImg {
	// 	background: url('http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/xcx/%E7%A1%AE%E8%AE%A4.png') no-repeat center;
	// 	font-size: 24rpx;
	// 	height: 120rpx;
	// 	width: 120rpx;
	// 	border-radius: 50%;
	// 	background-size: contain;
	// 	border: none;
	// }

	.camera_box .takeBtn::after {
		border: none;
	}

	.btnc {
		opacity: 0.9;
		background: #ffffff;
	}

	.camera_img {
		height: 80vh;
		width: 100%;
		z-index: 998;
	}
</style>