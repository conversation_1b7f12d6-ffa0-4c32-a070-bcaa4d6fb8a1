<template>
	<view>
		<view class="" v-if="!teamId">
			<m-popup mode="center" :show="!teamId">
				<view class=""
					style="width: 600rpx;height: 300rpx;display: flex;align-items: center;justify-content: center;">
					<text>请去前台办理！</text>
				</view>
			</m-popup>
		</view>
		<view class="camera_box" v-else>
			<camera class="camera" device-position="front" v-if="!show" flash="off">
				<cover-view class="id_m">
					<cover-view style="font-size: 40rpx;">请点击下方拍摄按钮进行拍照识别！</cover-view>
					<cover-view style="font-size: 26rpx;margin-top: 20rpx;">(非活体自动识别，请手动拍照!)</cover-view>
					<cover-image class="img1" src="/static/images/manAround.png"
						:style="{transform:'scale('+tipSize/100+')'}"></cover-image>

				</cover-view>
			</camera>
			<image class="camera_img" :src="img_url" v-if="show"></image>
			<!-- <cover-view class="" v-if="numSet>0"
				style="font-size: 330rpx;color: #FFFFFF;
			position: fixed;top: 0;left: 0;bottom:0;right:0;width: 300rpx;height: 450rpx;z-index: 999;margin: auto;display: flex;align-items: center;justify-content: center;">
				{{numSet}}
			</cover-view> -->
			<view class="action" @click="toTakeElse">
				<view class="btnTake" :style="{background:themeColor.main_color}">
					<!-- <text v-if="numSet>0">等待拍照</text> -->
					<text>点击拍照</text>
				</view>
				<text class="icon-dianji my_xing"
					style="color: #FFFFFF;font-size: 90rpx;position: absolute;top: 70rpx;right: 140rpx;"></text>
			</view>
		</view>


	</view>
</template>

<script>
	const plugin = requirePlugin("yayaLock");
	import {
		mapState,
		mapActions
	} from 'vuex';

	export default {
		data() {
			return {
				src: '',
				show: false,
				forword: 'front',
				action: '',
				action1: '',
				img_url: '',
				errNum: 0,
				tipSize: 100,
				cameraContext: null,
				numSet: 5,
				teamId: '',
				shareCode: '',
				params: {
					team_id: '',
					share_code: ''
				},
				time1: null,
				startTime:'',
				endTime:''
			}
		},
		watch: {
			img_url: {
				handler(newVal, oldVal) {
					if (newVal) {
						this.numSet == 0
						clearInterval(this.time1)
					}
				},
				immediate: true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log(options);
			if (options.team_id && !options.share_code) {
				console.log(options.team_id);
				this.teamId = options.team_id
				this.params.team_id = options.team_id
				this.startTime = options.startTime
				this.endTime = options.endTime
			} else if (options.team_id && options.share_code) {
				this.teamId = options.team_id
				this.shareCode = options.share_code
				this.startTime = options.startTime
				this.endTime = options.endTime
				this.params.team_id = options.team_id
				this.params.share_code = options.shareCode
			}
			if (uni.createCameraContext) {
				setTimeout(() => {
					this.cameraContext = uni.createCameraContext();
				}, 200)
			} else {
				// 如果希望用户在最新版本的客户端上体验您的小程序，可以这样子提示
				uni.showModal({
					title: '提示',
					content: '当前微信版本过低，无法使用该功能，请升级到最新微信版本后重试。'
				})
			}
		},
		async onShow() {
			await this.$onLaunched;
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					console.log(res.data);
					this.action = res.data + '/wx/TeamStandardRoomBill/faceContrast'
				}
			});

			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					console.log(res.data);
					this.action1 = res.data + '/wx/Resource/uploadFile'
				}
			});

			// this.time1 = setInterval(res => {
			// 	if (this.numSet < 1) {
			// 		clearInterval(this.time1)
			// 		this.toTake()
			// 	} else {
			// 		this.numSet--
			// 	}
			// }, 1000)
			// 设置人脸大小

			if (this.shopSetting.length > 0) {
				this.tipSize = this.shopSetting.filter(item => {
					return item.sign == 'user_image_size'
				})[0].property[0].value
			}

		},
		methods: {
			saveImg() {
				uni.showLoading({
					title: '识别中...'
				})
				if (!this.teamId) {
					uni.showModal({
						title: '提示',
						content: '未获取到团队码' + this.teamId,
						showCancel: false,
						confirmText: '返回重试',
						success() {
							uni.navigateBack()
						}
					})
				}
				uni.uploadFile({
					url: this.action,
					header: {
						'AUTHTOKEN': this.userInfo.user_token,
						'Content-Type': 'application/x-www-form-urlencoded',
						'chartset': 'utf-8'
					},
					filePath: this.src,
					name: 'file',
					formData: {
						'team_id': this.teamId
					},
					success: (uploadFileRes) => {
						
						if (JSON.parse(uploadFileRes.data).code == 0) {
							uni.hideLoading()
							// 判断时间是否到入住时间,到了则调用入住接口
							// 获取最早入住时间
							let time = this.shopSetting.filter(item => {
								return item.sign == 'enter_time'
							})[0].property.value
							
							let start = this.$moment(this.startTime*1000).format('YYYY-MM-DD') +' ' +time+':00'
							start = this.$moment(start,'YYYY-MM-DD hh:mm:ss').unix()
							
							let now = this.$moment().unix()
							
							if(now < start || now>this.endTime){
								if(now < start){
									uni.showModal({
										title:'提示',
										content:'您正在提前办理认证,请在入住当天,在酒店前台扫码领取钥匙!',
										showCancel:false,
										success() {
											uni.reLaunch({
												url: '/packageB/teamCheckIn/teamCheckIn'
											})
										}
									})
								}else{
									uni.showModal({
										title:'提示',
										content:'入住时间已经超过离店时间!',
										showCancel:false,
										success() {
											uni.reLaunch({
												url: '/packageB/teamCheckIn/teamCheckIn'
											})
										}
									})
								}
								
							}else{
								// this.$iBox
								// 	.http('teamCheckIn', {
								// 		teamId:this.teamId
								// 	})({
								// 		method: 'post'
								// 	})
								// 	.then(res => { 
										uni.reLaunch({
											url: '/packageB/teamCheckIn/teamCheckIn'
										})
									// })
							
								
							}
					
							
							
						} else {
							uni.showModal({
								title: '提示',
								content: JSON.parse(uploadFileRes.data).msg,
								showCancel: false,
								success: res => {
									uni.navigateBack({})
								}
							})
						}


					},
					fail: err => {
						uni.hideLoading()
						console.log(err, 'err');
						uni.showModal({
							title: '提示',
							content: err.errMsg,
							showCancel: false,
							success: res => {
								uni.navigateBack({})
							}
						})
					}
				});

			},
			takePhoto() {

				this.$iBox.throttle(() => {
					this.toTake()
				}, 8000);
			},
			toTakeElse() {
				this.$iBox.throttle(() => {
					this.toTake()
				}, 4000);
			},
			toTake() {
			
				this.cameraContext.takePhoto({
					quality: 'normal',
					success: (res) => {
						console.log(res)
						this.src = res.tempImagePath,
							uni.uploadFile({
								url: this.action1,
								header: {
									'AUTHTOKEN': this.userInfo.user_token,
									'Content-Type': 'application/x-www-form-urlencoded',
									'chartset': 'utf-8'
								},
								filePath: this.src,
								name: 'file',
								formData: {
									'shop_id': this.hotel.id
								},
								success: (uploadFileRes) => {
									this.img_url = JSON.parse(uploadFileRes.data).data
									this.show = true

									this.saveImg()
								}
							});
					},
					fail: err => {
						uni.hideLoading()
						uni.showModal({
							title: '提示',
							content: '拍照失败，无法调用摄像头'
						})
					}
				})
			},
			error() {
				uni.hideLoading()
				uni.showModal({
					title: '提示',
					showCancel: false,
					confirmText: '授权',
					content: '请确认摄像头是否授权，否则无法拍照',
					success: res => {
						if (res.confirm) {
							uni.openSetting({

							})

						}
					}
				})
			},
			//===========================================权限验证=================================================
			goMain() {
				uni.reLaunch({
					url: '/pages/myRoom/myRoom'
				})
			},
			reload() {
				this.stopBluetoothDevicesDiscovery()
				uni.showLoading({
					title: '正在查找设备...'
				})
				setTimeout(res => {
					this.peiDui()
				}, 1000)

				setTimeout(res => {
					uni.hideLoading()
				}, 3000)

			},

			////////////////////////=============================蓝牙U Key盾检测==========================
			peiDui() {
				//在页面加载时候初始化蓝牙适配器

				uni.openBluetoothAdapter({
					success: e => {
						console.log('初始化蓝牙成功:' + e.errMsg);
						// 初始化完毕开始搜索
						this.startBluetoothDeviceDiscovery()

					},
					fail: e => {

						console.log('初始化蓝牙失败，错误码：' + (e.errCode || e.errMsg));
						bgAudioManager.title = '提醒'
						bgAudioManager.epname = '提醒'
						bgAudioManager.singer = '提醒'
						bgAudioManager.src =
							'http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_mp3/%E8%AF%B7%E6%89%8B%E5%8A%A8%E6%89%93%E5%BC%80%E6%89%8B%E6%9C%BA%E8%93%9D%E7%89%99.mp3'
						uni.showToast({
							icon: "none",
							title: "查找设备失败！请检查手机是否打开蓝牙！",
							duration: 3000
						})
					}
				});
			},
			startBluetoothDeviceDiscovery() {
				//在页面显示的时候判断是都已经初始化完成蓝牙适配器若成功，则开始查找设备
				let self = this;
				console.log("开始搜寻智能设备");
				// setTimeout(res => {
				uni.startBluetoothDevicesDiscovery({
					success: res => {
						self.onBluetoothDeviceFound();
					},
					fail: res => {
						console.log("查找设备失败!");
						uni.showToast({
							icon: "none",
							title: "查找设备失败！",
							duration: 3000
						})
					}
				});
				// }, 300)
			},
			/**
			 * 停止搜索蓝牙设备
			 */
			stopBluetoothDevicesDiscovery() {
				uni.stopBluetoothDevicesDiscovery({
					success: e => {
						console.log('停止搜索蓝牙设备:' + e.errMsg);
					},
					fail: e => {
						console.log('停止搜索蓝牙设备失败，错误码：' + e.errCode);
					}
				});
			},
			/**
			 * 发现外围设备
			 */
			onBluetoothDeviceFound() {
				let self = this
				self.showDevice = true
				uni.onBluetoothDeviceFound(devices => {
					let mac = ''
					let macname = ''
					for (var i = 0; i < devices.devices.length; i++) {
						if (devices.devices[i].localName) {
							macname = devices.devices[i].localName.slice(3)
							mac = ""
							for (let i = 0, len = macname.length; i < len; i++) {
								mac += macname[i];
								if (i % 2 == 1 && i <= len - 2) mac += ":";
							}
						}

						console.log('devices', self.blueList);
						// 循环判断是否存在,并且信号值达标
						for (let item of self.blueList) {
							console.log(mac, 'dddd', item, devices.devices[i].RSSI);
							if (item.mac == mac) {
								if (item.max_value != 0) {
									if (devices.devices[i].RSSI > item.max_value) {
										self.showDevice = false
										let a = setInterval(res => {
											if (this.numSet < 1) {
												this.toTake()
												clearInterval(a)
											} else {
												this.numSet--
											}
										}, 1000)
										self.stopBluetoothDevicesDiscovery()
										uni.closeBluetoothAdapter({
											success(res) {
												console.log(res)
											}
										})
										break
									}
								} else {
									self.showDevice = false
									let a = setInterval(res => {
										if (this.numSet < 1) {
											this.toTake()
											clearInterval(a)
										} else {
											this.numSet--
										}
									}, 1000)
									self.stopBluetoothDevicesDiscovery()
									uni.closeBluetoothAdapter({
										success(res) {
											console.log(res)
										}
									})
									break
								}

							}
						}
					}
				})

			}
		},

	}
</script>

<style lang="scss" scoped>
	.deviceBox {
		height: 50vh;
		width: 700rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.deBtn {
			width: 500rpx;
			height: 80rpx;
			border-radius: 40rpx;

			color: #FFFFFF;
			margin-top: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}

	/* pages/unit/camera/camera.wxss */
	.camera_box {
		height: 100vh;
		width: 100vw;
		position: relative;
	}

	.camera {
		height: 85vh;
		width: 100vw;
		z-index: 1;
	}

	.id_m {
		height: 85vh;
		width: 100vw;
		z-index: 999;
		background: rgba(0, 0, 0, 0.1);
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		position: absolute;
		color: #ffffff;
	}

	.btnTake {
		width: 500rpx;
		height: 100rpx;
		border-radius: 40rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		color: #ffffff;
	}

	.img {
		width: 1200rpx;
		height: 550rpx;
		display: block;
		margin-top: 200rpx;
		// position: absolute;
		// left: 0; right: 100rpx; margin: auto auto;
		// top: 40rpx; bottom: 0;
		transform: rotate(90deg);
	}

	.img1 {
		width: 730rpx;
		height: 730rpx;
		display: block;
		margin-top: 40rpx;
		z-index: 996;
	}

	.id_m .tips_txt {
		transform: rotate(90deg);
	}

	.camera_box .action {
		height: 15vh;
		position: relative;
		display: flex;
		justify-content: space-around;
		// align-items: center;
		padding-top: 60rpx;
		background-color: #000;


		@keyframes xing {
			0% {
				transform: scale(1);
			}

			25% {
				transform: scale(1.4);
			}

			50% {
				transform: scale(1);
			}

			75% {
				transform: scale(1.4);
			}
		}

		.my_xing {
			-webkit-animation-name: xing;
			-webkit-animation-timing-function: ease-in-out;
			-webkit-animation-iteration-count: infinite;
			-webkit-animation-duration: 2s;
		}
	}

	// .camera_box .takeBtn {
	// 	height: 120rpx;
	// 	width: 120rpx;
	// 	border-radius: 50%;
	// 	font-size: 24rpx;
	// 	background: url('http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/xcx/%E6%8B%8D%E7%85%A7%E6%8C%89%E9%92%AE%20(1).png') no-repeat center;
	// 	background-size: contain;
	// 	border: none;
	// }

	// .camera_box .cancelBtn {
	// 	font-size: 24rpx;
	// 	height: 120rpx;
	// 	width: 120rpx;
	// 	border-radius: 50%;
	// 	background: url('http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/xcx/%E5%88%B7%E6%96%B0.png') no-repeat center;
	// 	background-size: contain;
	// 	border: none;
	// 	background-size: contain;
	// 	border: none;
	// }

	// .camera_box .saveImg {
	// 	background: url('http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_pic/xcx/%E7%A1%AE%E8%AE%A4.png') no-repeat center;
	// 	font-size: 24rpx;
	// 	height: 120rpx;
	// 	width: 120rpx;
	// 	border-radius: 50%;
	// 	background-size: contain;
	// 	border: none;
	// }

	.camera_box .takeBtn::after {
		border: none;
	}

	.btnc {
		opacity: 0.9;
		background: #ffffff;
	}

	.camera_img {
		height: 80vh;
		width: 100%;
		z-index: 998;
	}
</style>