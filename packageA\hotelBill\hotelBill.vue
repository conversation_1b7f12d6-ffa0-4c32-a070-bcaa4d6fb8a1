<template>
	<view>
		<m-tabs :list="list1"
		style="position: sticky;top: 0;width: 100%;z-index: 99;"
				@tabClick="tab_click"
				:activeIndex="current"
				:config="{color:themeColor.main_color,
						  fontSize:30,
						  activeColor:themeColor.main_color,
						  underLineColor:themeColor.main_color,
						  underLineWidth:80,
						  underLineHeight:10}">
		</m-tabs>
		<view class="contentBox">
			<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="bill_list.length==0">
				<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
				</view>
				<p :style="{color:themeColor.com_color1}">暂无订单</p>
			</view>
			<view class="billCard" style="position: relative;" @click="toDetail(item)" v-for="item in bill_list" v-else>
				<view class="" style="position: absolute;top: 0;right: 0;height: 60rpx;width: fit-content;padding: 10rpx;display: flex;align-items: center;justify-content: center;">
					<view style="height: 40rpx;padding: 0 20rpx;width: fit-content;border-radius: 8rpx;display: flex;align-items: center;justify-content: center;">{{billStatus(item.bill_status)}}</view>
				</view>
				<view class="title" style="margin-top: 24rpx;">
					<view class="v1">
						<image src="../../static/images/hotelImg.png" style="width: 50rpx;height: 50rpx;" mode=""></image>
						<text style="padding-left: 10rpx;color: #000000A3;font-size: 28rpx;">{{item.shop_name}}</text>
					</view>
					<text style="font-size: 24rpx;color: #00000066;">订单号: {{item.bill_code}}</text>
					
				</view>
				<view class="content" style="background-color: #F5F5F5;padding: 32rpx;border-radius: 32rpx;">
					<view class="title">
						<view class="" style="display: flex;align-items: center;width: 90%;">
							<text style="font-weight: 600;font-size: 34rpx;">{{item.room_type_name}}</text>
							
						</view>
						
						<text style="font-size: 34rpx;" v-if="thirdBill&&(item.bill_source_sign=='wxxcx'||item.bill_source_sign=='buru')">￥{{(item.bill_amount-item.amount_reduction).toFixed(2)}}</text>
					</view>
					<view class="info">
						<!-- 全日房 -->
						<view class="infoData" v-if="item.room_sale_type_sign == 'standard'">
							{{item.enter_time_plan | moment}} 至 {{item.leave_time_plan | moment}}
						</view>
						<view class="infoData"  v-if="item.room_sale_type_sign == 'standard'">
							{{item.stay_time}}晚，1间
						</view>
						<!-- 时租房 和会议室-->
						<view class="infoData" v-if="item.room_sale_type_sign == 'hour'||item.room_sale_type_sign == 'conference_room'">
							{{item.enter_time_plan | moment}} 
						</view>
						<view class="infoData"  v-if="item.room_sale_type_sign == 'hour'||item.room_sale_type_sign == 'conference_room'">
							{{item.room_sale_type_stay_time}}小时，1间
						</view>
						<!-- 月租房 -->
						<view class="infoData" v-if="item.room_sale_type_sign == 'long_standard'">
							{{item.enter_time_plan | moment}} 至 {{item.leave_time_plan | moment}}
						</view>
						<view class="infoData"  v-if="item.room_sale_type_sign == 'long_standard'">
							{{item.stay_time}}月，1间
						</view>
						<view class="infoData">
							订单号:{{item.bill_code}}
						</view>
					</view>
					
				
				</view>
				<view class="bottomBox">
					<view class="btnBoss" :style="{color:themeColor.main_color,border:'1px solid '+ themeColor.main_color}" v-if="item.bill_status == 2 || item.bill_status == 3">
							申请取消
					</view>
					<view style="border: 1px solid #D9D9D9;border-radius: 40rpx;width: fit-content;padding:10rpx 20rpx;" v-if="item.bill_status == 5&&item.evaluate_status==0">
							去评价
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				list1: [{
						id: 0,
						name: '全部'
					},
					{
						id: 1,
						name: '待入住'
					},
					{
						id: 2,
						name: '入住中'
					},
					{
						id: 3,
						name: '历史订单'
					},
				],
				current:0,
				params:{
					page:1,
					limit:10,
					bill_status:''
				},
				bill_list:[],
				bool:true,
				thirdBill:1
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting']),
		},
		async onLoad(options) {
			await this.$onLaunched;
			if(options){
				console.log(options);
				this.current = Number(options.current)
				if(this.current == 0){
					this.params.bill_status = ''
				}else if(this.current == 1){
					this.params.bill_status = 3
				}else if(this.current == 2){
					this.params.bill_status = 4
				}else if(this.current == 3){
					this.params.bill_status = 5
				}
			}
			
			this.getBill()
			
			
			
		},
		async onShow() {
			await this.$onLaunched;
			this.thirdBill = this.shopSetting.filter(item => {
				return item.sign == 'third_bill_show_price'
			})[0].property.status
		},
		methods : {
			...mapActions('hotel',['getHotelBillDetail']),
			toDetail(e){
				this.getHotelBillDetail(e)
				uni.navigateTo({
					url:'/packageA/hotelBill/hotelBillDetail'
				})
			},
			billStatus(e){
				let a =''
				if(e==2){
					a = '待确认'
				}else if(e==3){
					a = '待入住'
				}else if(e==4){
					a = '入住中'
				}else if(e==5){
					a = '已完成'
				}else if(e==6){
					a = '待取消'
				}else if(e==7){
					a = '已取消'
				}else if(e==8){
					a = '申请退房'
				}else if(e==9){
					a = '预定未到'
				}else if(e==10){
					a = '走结'
				}else if(e==11){
					a = '已完成'
				}
				return a
			},
			tab_click(e){
				console.log(e);
				if(e == 0){
					this.params.bill_status = ''
				}else if(e == 1){
					this.params.bill_status = 3
				}else if(e == 2){
					this.params.bill_status = 4
				}else if(e == 3){
					this.params.bill_status = 5
				}
				this.getBill()
			},
			getBill(e){
				// 查询订单
				uni.showLoading({
					title: '加载中...'
				})
				this.params.page = 1
				this.$iBox.http('getRoomBillList', this.params)({
					method: 'post'
				}).then(res => {
					this.bill_list = res.data.list
					uni.hideLoading()
				})
			}
		},
		// // 上拉加载
		onReachBottom() {
		
			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getRoomBillList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.bill_list.concat(res.data.list)
					this.bill_list = new_list
					if (this.bill_list.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.contentBox {
		width: 100%;
		height: auto;
		margin-top: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		.billCard {
			min-height: 240rpx;
			background: #ffffff;
			border-radius: 16rpx;
			width: 94%;
			padding:20rpx 30rpx;
			margin-top: 30rpx;
			.title {
				width: 100%;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.v1 {
					display: flex;
					align-items: center;
				}
			}
			
			.content {
				height: 184rpx;
				.title{
					display: flex;
					width: 100%;
					height: 44rpx;
					align-items: center;
					justify-content: space-between;
				}
				
				.info {
					.infoData{
						font-size: 24rpx;
						color: darkgray;
						line-height: 34rpx;
						
					}
				}
				
				
				
			}

		}
	}
	
	.bottomBox {
		display: flex;
		align-items: center;
		// border-top: 1px solid #eee;
		margin-top: 20rpx;
		justify-content: flex-end;
		width: 100%;
		.btnBoss {
			margin-top: 20rpx;
			width: fit-content;
			padding: 20rpx;
			height: 60rpx;
			border-radius: 30rpx;
			// border: 1px solid #5555ff;
			display: flex;
			align-items: center;
			justify-content: center;
			// color: #5555ff;
		}
	}
</style>
