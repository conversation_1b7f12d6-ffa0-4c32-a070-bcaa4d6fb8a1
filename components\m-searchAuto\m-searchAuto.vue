<template>
	<view>
		<view class="box" :style="{color:themeColor.text_main_color}">
			<view class="" style="display: flex;align-items: center;width: 100%;">
				<text style="font-size: 40rpx;">一键自助入住</text>
			</view>

			<view class="" style="width: 100%;margin-top: 30rpx;position: relative;">
				<uni-easyinput prefixIcon="search" placeholderStyle="font-size:30rpx" clearSize="28"
					:styles="{borderColor:themeColor.main_color,height:'90rpx'}" v-model="searchKey"
					placeholder="请搜索入住人姓名/电话/订单号查询">
				</uni-easyinput>

			</view>
			<p style="font-size: 28rpx;margin-top: 10rpx;" :style="{color:themeColor.text_title_color}"><text
					style="color: brown;">*</text>可搜索预定信息快速办理自助入住</p>
			<view class="" @click="toSearch" style="width: 100%;border-radius: 30rpx;height: 90rpx;z-index: 99;
				display: flex;align-items: center;justify-content: center;font-size: 34rpx;padding: 10rpx;margin:30rpx auto;"
				:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}">
				<text style="color:#FFFFFF">一键查询订单</text>
			</view>


		</view>

		<!-- 搜索到订单 -->
		<m-popup mode="bottom" :show="billShow" @closePop="closePop">
			<view class="BillBox" v-if="billList.length > 0">
				<scroll-view scroll-y="true" style="height: 100%;width: 100%;">
					<view class="icon-close" style="position: absolute;top: 8rpx;right: 8rpx;font-size: 40rpx;"
						@click="closePop">
					</view>
					<p style="font-size: 34rpx;"><text class="icon-fengefu"
							:style="{color:themeColor.main_color}"></text>
						订单列表：请选择一个订单入住</p>
					<view class="billCard" v-for="item in billList" style="background:#c5c3ca59">
						<view class="" style="width: 80%;padding-right: 20rpx;line-height: 46rpx;">
							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text style="font-size: 28rpx;">订单号:{{item.bill_code}}</text>
							</view>
							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text>预定人:{{item.link_man}}</text>
								<text>已入住人数:{{item.users.length}}人</text>
							</view>
							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text>房型:{{item.room_type_name}}</text>
								<text>{{item.room_number?'房间:'+item.room_number:'未排房'}}</text>
							</view>

							<view class="" style="display: flex;align-items: center;justify-content: space-between;">
								<text>{{formatPass(item.link_phone)}}</text>
								<text>{{item.enter_time_plan | moment}}</text>

							</view>
						</view>
						<view class="" style="width: 20%;display: flex;align-items: center;justify-content: center;">
							<view class=""
								style="height: 60rpx;width: 120rpx;padding:16rpx 20rpx;border-radius: 30rpx;display: flex;align-items: center;justify-content: center;"
								:style="{'background':themeColor.main_color}">
								<text style="font-size: 24rpx;font-weight: 600;color: #FFFFFF;"
									@click="chooseRoom(item)">选择</text>
							</view>

						</view>


					</view>
				</scroll-view>

			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-searchAuto",
		data() {
			return {
				searchKey: '',
				billList: [],
				billShow: false,
				hotelBill: null,
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate','setting']),
		},
		methods: {
			...mapActions('room', ['getHardWareList', 'getBillDetail', 'getRoomBillUser']),
			toSearch(e) {
				this.$iBox.throttle(() => {
					uni.showModal({
						title: '提示',
						content: '尊敬的顾客：使用手机快速办理入住，我们需要采集您的身份证信息并进行人脸比对。请问您是否同意我们进行此操作？',
						confirmText: '是',
						cancelText: '否',
						success: res => {
							this.search()
						}

					})
					
				}, 2000);
			},
			foacsInput(e) {
				setTimeout(() => {
					// 获取键盘高度
					const windowHeight = uni.getSystemInfoSync().windowHeight;
					const keyboardHeight = windowHeight - (e.detail.height + e.detail.top);

					// 滚动页面
					uni.pageScrollTo({
						scrollTop: keyboardHeight,
						duration: 300
					});
				}, 300);
			},
			search() {
				uni.showLoading({
					title: '搜索中...'
				})
				// 查询订单,如果订单数量为1则自动跳转选房页，多于1则选择订单
				this.$iBox.http('getUserRoomBillList', {
					search_word: this.searchKey.trim()
				})({
					method: 'post'
				}).then(res => {
					
					
					if (res.data == 'no_login_bill') {
						this.$emit('showPhone', 'no_login')
					} else {
						let set = this.setting.filter(item => {
							return item.sign == 'auto_register_member'
						})
						if (set[0].property) {
							let a = set[0].property.value
							if (a == 2) {
								if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
									1) {
									if (res.data.length > 1) {
										this.billShow = true
										this.billList = res.data
									} else if (res.data.length == 1) {
										this.$iBox.http('getRoomBillInfo', {
											bill_id: res.data[0].id
										})({
											method: 'post'
										}).then(res1 => {
											if (res1.data.bill_status == 4) {
												uni.showModal({
													title: '提示',
													content: '您已入住房间，是否到房卡页？',
													success: res => {
														if (res.confirm) {
															uni.switchTab({
																url: '/pages/myRoom/myRoom'
															})
														} else {
									
														}
													}
												})
											} else {
												this.hotelBill = res1.data
												this.getBillDetail(res1.data)
												this.$iBox.http('getRoomBillUser', {
													bill_id: res.data[0].id
												})({
													method: 'post'
												}).then(res2 => {
													this.getRoomBillUser(res2.data)
													this.billShow = false
													uni.navigateTo({
														url: '/packageA/autoRoom/chooseRoom/chooseRoom'
													})
													uni.hideLoading()
												})
											}
										})
									
									} else {
										uni.showToast({
											icon: 'none',
											title: '未搜索到相关订单，请去前台！',
											duration: 3400
										})
										setTimeout((res) => {
											uni.hideLoading()
										}, 3400)
									}
								
								} else {
									this.$emit('showPhone', 'no_login')
								}
						
							} else if (a == 1) {
								// this.pop = true
								if (this.userInfo.phone) {
									if (res.data.length > 1) {
										this.billShow = true
										this.billList = res.data
									} else if (res.data.length == 1) {
										this.$iBox.http('getRoomBillInfo', {
											bill_id: res.data[0].id
										})({
											method: 'post'
										}).then(res1 => {
											if (res1.data.bill_status == 4) {
												uni.showModal({
													title: '提示',
													content: '您已入住房间，是否到房卡页？',
													success: res => {
														if (res.confirm) {
															uni.switchTab({
																url: '/pages/myRoom/myRoom'
															})
														} else {
									
														}
													}
												})
											} else {
												this.hotelBill = res1.data
												this.getBillDetail(res1.data)
												this.$iBox.http('getRoomBillUser', {
													bill_id: res.data[0].id
												})({
													method: 'post'
												}).then(res2 => {
													this.getRoomBillUser(res2.data)
													this.billShow = false
													uni.navigateTo({
														url: '/packageA/autoRoom/chooseRoom/chooseRoom'
													})
													uni.hideLoading()
												})
											}
										})
									
									} else {
										uni.showToast({
											icon: 'none',
											title: '未搜索到相关订单，请去前台！',
											duration: 3400
										})
										setTimeout((res) => {
											uni.hideLoading()
										}, 3400)
									}
						
								} else {
									this.$emit('showPhone', 'no_login')
								}
							}
						}
						
					}

				})
			},
			closePop() {
				this.billShow = false
			},
			formatPass(e) {
				return e.substring(0, 3) + '****' + e.substring(7, 11)
			},
			chooseRoom(e) {
				this.$iBox.http('getRoomBillInfo', {
					bill_id: e.id
				})({
					method: 'post'
				}).then(res1 => {

					if (res1.data.bill_status == 4) {
						uni.showModal({
							title: '提示',
							content: '您已入住房间，是否到房卡页？',
							success: res => {
								if (res.confirm) {
									uni.switchTab({
										url: '/pages/myRoom/myRoom'
									})
								} else {

								}
							}
						})
					} else {

						let detail = null
						this.hotelBill = res1.data
						this.getBillDetail(res1.data)
						this.$iBox.http('getRoomBillUser', {
							bill_id: e.id
						})({
							method: 'post'
						}).then(res2 => {
							this.getRoomBillUser(res2.data)
							uni.navigateTo({
								url: '/packageA/autoRoom/chooseRoom/chooseRoom'
							})
							uni.hideLoading()
						})
					}
				})


			},
			goMain() {
				uni.switchTab({
					url: '/pages/index/index'
				})
			},
			makePhone() {
				uni.makePhoneCall({
					phoneNumber: this.hotel.link_phone
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		min-height: 280rpx;
		width: 750rpx;
		border-radius: 30rpx;
		background-color: #FFFFFF;
		padding: 16rpx 30rpx;
		margin: 4rpx 0;
		position: relative;

	}

	.BillBox {
		width: 100%;
		margin-top: 30rpx;
		padding: 30rpx;
		height: 70vh;

		.billCard {
			width: 660rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-radius: 10rpx;
			// font-weight: 600;
			font-size: 28rpx;
			margin: 20rpx auto;
		}
	}
</style>