<template>
	<view style="height: 100%;">
		<view class="" v-if="billDetail&&billDetail.room_number">
			<view class="" v-if="styleModel.style==1">
				<view class="BillBox" v-if="billDetail">
					<view class="bill" :style="{color:themeColor.bg_color}">
						<image :src="styleModel.bg_image" 
							style="height: 100%;width: 100%;position: absolute;top: 0;right: 0;border-radius: 24rpx;z-index: 0;">
						</image>
						<view class="bill_t1">
							<view class="" style="width: 70%;" @click="changeHotel">
								<text style="font-size: 32rpx;">{{hotel.shop_name}}</text>
								<text class="icon-qiehuan1" style="padding-left: 4rpx;"></text>
							</view>
							<view class="" style="width: 30%;display: flex;justify-content: flex-end;align-items: center;"
								@click="getAddress">
								<text class="icon-Location"></text>
								<text style="font-size: 32rpx;">导航</text>
							</view>
						</view>
						<view class="bill_t1" style="font-size: 30rpx;">
							<view class="bill_t1_text2" style="font-size: 34rpx;padding-left: 6rpx;">
								<text>{{billDetail.building_name?billDetail.building_name:''}}</text>
								<text style="color: #c97106;">{{billDetail.floor_name?billDetail.floor_name:''}}</text>
							</view>
							<view class="bill_t1_text1">
								<text style="font-size: 34rpx;">房间</text>
								<text
									style="font-size: 34rpx;font-weight: 600;padding-left: 6rpx;color: #c97106;">{{billDetail.room_number}}</text>
							</view>
							
							<view class="bill_t1_text3">
								<text>{{billDetail.room_type_name}}</text>
							</view>
						</view>
						<view class="bill_t2" style="font-size:28rpx;height: 42rpx" @click="toBreakfast" v-if="breakList.length>0">
							<view class="bill_t2_title">{{breakList[0].name}}<text class="icon-erweima"></text></view>
							<view class="bill_t2_title" v-if="breakList.length>0"><text class="icon-Location"></text>{{breakfastSetting.address}}</view>
							<view class="bill_t2_title" v-if="breakList.length>0">时间:{{breakfastSetting.time}}</view>
						</view>
						<view class="bill_t3" style="">
							<view class="start flex_c">
								<text>周{{weekMate}}入住</text>
								<text>{{dateMate}}</text>
							</view>
							<view class="mid">
								<text style="padding-left: 10rpx;"
									v-if="billDetail.room_sale_type_sign=='standard'">{{billDetail.stay_time+ '晚'}}</text>
								<text style="padding-left: 10rpx;"
									v-if="billDetail.room_sale_type_sign=='hour'||billDetail.room_sale_type_sign=='conference_room'">{{billDetail.stay_time+ '小时'}}</text>
								<text style="padding-left: 10rpx;"
									v-if="billDetail.room_sale_type_sign=='long_standard'">{{billDetail.stay_time+ '月'}}</text>
							</view>
							<view class="end flex_c">
								<text>周{{weekMate1}}离店</text>
								<text>{{dateMate1}}</text>
							</view>
						</view>
						<view class="bill_t3" style="height: 100rpx;" v-if="styleModel.show_user_qr">
							<view class="" style="font-size: 26rpx;margin-left: 30rpx;display: flex;align-items: center;color: #FFFFFF;" @click="goQr">
								<text class="icon-erweima" style="font-size: 100rpx;"></text>
								<view class="" style="display: flex;flex-direction: column;margin-left: 6rpx;font-size: 22rpx;">
									<text style="padding-left: 4rpx;">展示会员码</text>
									<text>点击可展示会员码线下付款</text>
									<text>订单号:{{billDetail.bill_code}}</text>
								</view>
								
							</view>
						</view>
						<p v-if="otherMan()" style="margin: 10rpx 20rpx;font-size: 30rpx;color:#FFFFFF;z-index: 1;position: relative;display: flex;justify-content: center;">同住人：{{otherMan()}}</p>
					</view>
				</view>
			</view>
			
			<view class="" v-if="styleModel.style==2" style="margin-top: 30rpx;">
				<view class="BillBox" v-if="billDetail" style="padding: 0;">
					<view class="bill" style="padding: 0;" :style="{color:themeColor.text_main_color,background: themeColor.bg_main_color+'20'}">
						<view class="bill_t1" style="padding: 0 30rpx;" :style="{background: 'linear-gradient(90deg, '+themeColor.bg_main_color+'80' +' 25%, '+themeColor.bg_main1_color+'80' +' 100%);'}">
							<view class="" style="width: fit-content;display: flex;align-items: center;max-width: 90%;">
								<view class="icon-fangjianshu-" style="font-size: 40rpx;margin-right: 8rpx;" :style="{color:themeColor.main_color}"></view>
								<text style="font-size: 36rpx;">{{billDetail.room_type_name}}</text>
							</view>
							<p v-if="otherMan()" style="margin: 10rpx 20rpx;font-size: 30rpx;z-index: 1;position: relative;display: flex;justify-content: center;">同住人:{{otherMan()}}</p>
						</view>
			
						<view class="bill_t1" style="font-size: 30rpx;padding: 0 30rpx;" :style="{color:text_main_color}">
							<view class="bill_t1_text2" style="font-size: 34rpx;padding-left: 6rpx;">
								<text>{{billDetail.building_name?billDetail.building_name:''}}</text>
								<text >{{billDetail.floor_name?billDetail.floor_name:''}}</text>
							</view>
							<view class="bill_t1_text1">
								<text style="font-size: 34rpx;">房间</text>
								<text
									style="font-size: 34rpx;font-weight: 600;padding-left: 6rpx;">{{billDetail.room_number}}</text>
							</view>
						</view>
						<view class="bill_t2" style="font-size:28rpx;height: 42rpx;padding: 0 30rpx;" @click="toBreakfast" v-if="breakList.length>0">
							<view class="bill_t2_title">{{breakList[0].name}}<text class="icon-erweima"></text></view>
							<view class="bill_t2_title" v-if="breakList.length>0"><text class="icon-Location"></text>{{breakfastSetting.address}}</view>
							<view class="bill_t2_title" v-if="breakList.length>0">时间:{{breakfastSetting.time}}</view>
						</view>
						<view class="bill_t3" style="padding: 0 30rpx;font-size: 34rpx;">
							<view class="start flex_c">
								<text>周{{weekMate}}入住</text>
								<text>{{dateMate}}</text>
							</view>
							<view class="mid" :style="{border: '2px solid '+ themeColor.text_main_color}">
								<text style="padding-left: 10rpx;"
									v-if="billDetail.room_sale_type_sign=='standard'">{{billDetail.stay_time+ '晚'}}</text>
								<text style="padding-left: 10rpx;"
									v-if="billDetail.room_sale_type_sign=='hour'||billDetail.room_sale_type_sign=='conference_room'">{{billDetail.stay_time+ '小时'}}</text>
								<text style="padding-left: 10rpx;"
									v-if="billDetail.room_sale_type_sign=='long_standard'">{{billDetail.stay_time+ '月'}}</text>
							</view>
							<view class="end flex_c">
								<text>周{{weekMate1}}离店</text>
								<text>{{dateMate1}}</text>
							</view>
						</view>
						<view class="" style="height: 80rpx;width: 100%;display: flex;align-items: center;justify-content: space-between;padding: 0 30rpx;">
							<view class="" @click="xufang" :style="{color:themeColor.text_main_color,background:themeColor.com_color1+'99'}"
							 style="display: flex;align-items: center;justify-content: center;height: 80rpx;width: 315rpx;border-radius: 60rpx;">
								续房
							</view>
							<view class="" @click="tuifang" :style="{color:themeColor.text_main_color,background:themeColor.bg_main1_color+'99'}"
							 style="display: flex;align-items: center;justify-content: center;height: 80rpx;width: 315rpx;border-radius: 60rpx;">
								退房
							</view>
						</view>
						
					</view>
				</view>
			</view>
		</view>
		
		<view class="" v-if="billDetail&&!billDetail.room_number">
			<view class="" style="margin: 230rpx auto;position: relative;width: 686rpx;height: 64vh;">
				<image src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/Subtract.png" style="height: 100%;width: 100%;position: absolute;top: 0;left: 0;z-index: -1;" mode=""></image>
				<view class="" style="height: 100%;width: 100%;padding: 64rpx;z-index: 9;">
					<view class="" style="">
						<p style="font-size: 52rpx;color: #000000E0;" v-if="billDetail.bill_status==2">订单预定成功！</p>
						<p style="font-size: 52rpx;color: #000000E0;" v-if="billDetail.bill_status==3">订单预定成功！请等待酒店确认订单!</p>
						<view style="font-size: 30rpx;color: #000000E0;display: flex;align-items: center;width: 100%;margin-top: 30rpx;" v-if="billDetail.bill_status==2">
							<image src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/Group-28.png" style="width: 48rpx;height: 48rpx;margin-right: 10rpx;" mode=""></image>
								<p style="font-size: 30rpx;color: #000000E0;width: 460rpx;">酒店确认订单中，请耐心等待或联系酒店前台!</p>	
						</view>
						<p style="font-size: 30rpx;color: #000000E0;width: 500rpx;" v-if="billDetail.bill_status==3">酒店已确认订单!请选择房间入住!</p>
					</view>
					<view class="" style="margin-top: 200rpx;">
						<view class="" style="color: #000000E0;font-size: 32rpx;">
							订单详情
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
							<view class="" style="color: #00000066;font-size: 28rpx;;width: 140rpx;display: flex;align-items: center;justify-content: space-between;">
								房型
							</view>
							<text style="font-size: 28rpx;color: #000000E0;">{{billDetail.room_type_name}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
							<view class="" style="color: #00000066;font-size: 28rpx;;width: 140rpx;display: flex;align-items: center;justify-content: space-between;">
								联系人
							</view>
							<text style="font-size: 28rpx;color: #000000E0;">{{billDetail.link_man}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
							<view class="" style="color: #00000066;font-size: 28rpx;;width: 140rpx;display: flex;align-items: center;justify-content: space-between;">
								联系电话
							</view>
							<text style="font-size: 28rpx;color: #000000E0;">{{billDetail.link_phone}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
							<view class="" style="color: #00000066;font-size: 28rpx;;width: 140rpx;display: flex;align-items: center;justify-content: space-between;">
								入住时间
							</view>
							<text style="font-size: 28rpx;color: #000000E0;">{{billDetail.enter_time_plan | moment1}}</text>
						</view>
						<view class="" style="display: flex;align-items: center;margin-top: 20rpx;">
							<view class="" style="color: #00000066;font-size: 28rpx;;width: 140rpx;display: flex;align-items: center;justify-content: space-between;">
								离店时间
							</view>
							<text style="font-size: 28rpx;color: #000000E0;">{{billDetail.leave_time_plan | moment1}}</text>
						</view>
					</view>
					<view class="" v-if="billDetail.bill_status==2" :style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}" style=";width: 522rpx;height: 96rpx;border-radius: 60rpx;display: flex;align-items: center;justify-content: center;margin:0 auto;margin-top: 90rpx;">
						<text style="color: #FFFFFF;">等待酒店确认</text>
					</view>
					<view class="" v-if="billDetail.bill_status==3&&!billDetail.room_number" @click="toChoose" :style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}" style="width: 522rpx;height: 96rpx;border-radius: 60rpx;display: flex;align-items: center;justify-content: center;margin:0 auto;margin-top: 90rpx;">
						<text style="color: #FFFFFF;">选择房间</text>
					</view>
					<view class="" v-if="billDetail.bill_status==3&&billDetail.room_number" @click="toAuth" :style="{'background-image': 'linear-gradient(-90deg,'+themeColor.bg_main_color+','+themeColor.bg_main1_color+')'}" style="width: 522rpx;height: 96rpx;border-radius: 60rpx;display: flex;align-items: center;justify-content: center;margin:0 auto;margin-top: 90rpx;">
						<text style="color: #FFFFFF;">去支付并认证</text>
					</view>
				</view>
				
			</view>
		</view>
		
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				breakfastSetting: null,
				if_auth: true,
				params: {
					shop_id: '',
					bill_id: '',
					page: 1,
					limit: 10
				},
				breakList: []
			};
		},
		props: {
			styleModel: {
				type: Object
			},
			billDetail: {
				type: Object
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'shopSetting']),
			weekMate() {
				if (this.billDetail) {
					let a = this.$moment(this.billDetail.enter_time_plan * 1000).format('E')
					return a == 1 ? '一' : (a == 2 ? '二' : (a == 3 ? '三' : (a == 4 ? '四' : (a == 5 ? '五' : (a == 6 ? '六' :
						'日')))))
				}

			},
			weekMate1() {
				if (this.billDetail) {
					let a = this.$moment(this.billDetail.leave_time_plan * 1000).format('E')
					console.log(a, 'aa');
					return a == 1 ? '一' : (a == 2 ? '二' : (a == 3 ? '三' : (a == 4 ? '四' : (a == 5 ? '五' : (a == 6 ? '六' :
						'日')))))
				}

			},
			dateMate() {
				if (this.billDetail) {
					let a = this.$moment(this.billDetail.enter_time_plan * 1000).format('MM月DD日 HH:mm')
					return a
				}

			},
			dateMate1() {
				if (this.billDetail) {
					let a = this.$moment(this.billDetail.leave_time_plan * 1000).format('MM月DD日 HH:mm')
					return a
				}

			}

		},
		mounted() {
			// 获取早餐设置
			this.breakfastSetting = this.shopSetting.filter(item => {
				return item.sign == 'breakfast_setting'
			})[0].property

			// 获取自助入住设置，
			this.autoRoomSetting = this.shopSetting.filter(item => {
				return item.sign == 'self_check_in'
			})[0].property.status
			
			console.log(this.hotel,'hotel');
			
			this.params.shop_id = this.hotel.id
			if (this.billDetail) {
				this.params.bill_id = this.billDetail.id
			}

			this.$iBox.http('getBreakfastCouponList', this.params)({
				method: 'post'
			}).then(res => {
				this.breakList = res.data.list
			})

		},
		methods: {

			chooseImg() {
				wx.chooseMedia({
					count: 9,
					mediaType: ['image', 'video'],
					sourceType: ['album', 'camera'],
					maxDuration: 30,
					camera: 'back',
					success(res) {
						console.log(res.tempFiles.tempFilePath)
						console.log(res.tempFiles.size)
					}
				})
			},
			xufang(){
				uni.navigateTo({
					url:'/packageA/autoRoom/continuationRoom/continuationRoom'
				})
			},
			tuifang(){
				uni.navigateTo({
					url:'/packageA/autoRoom/checkOutRoom/checkOutRoom'
				})
			},
			toChoose(){
				uni.navigateTo({
					url: '/packageA/autoRoom/chooseRoom/chooseRoom'
				})
			},
			toAuth(){
				uni.navigateTo({
					url: '/packageA/autoRoom/autoRoom'
				})
			},
			getAddress() {
				uni.openLocation({
					name: this.hotel.shop_name,
					address: this.hotel.address,
					latitude: parseFloat(this.hotel.latitude),
					longitude: parseFloat(this.hotel.longitude),
					scale: 18,
					success: function() {
						console.log('success');
					}
				})


			},
			changeHotel() {
				console.log(this.cityModel);
				if (this.cityModel) {
					uni.navigateTo({
						url: '/pages/cityList/cityList'
					})
				} else {
					uni.navigateTo({
						url: '/pages/hotelList/hotelList'
					})
				}

			},
			toBreakfast() {
				uni.navigateTo({
					url: '/packageA/breakfastCard/breakfastCard'
				})
			},
			goQr(){
				uni.navigateTo({
					url:"/pages/payCode/payCode"
				})
			},
			otherMan(){
				let name = this.billDetail.users.filter(item => {
					return item.common_code != this.userInfo.common_code
				})
				console.log(name,'nm');
				if(name&&name.length>0){
					let nameArr = ''
					name.forEach(item=>{
						nameArr += item.name +'，'
					})
					return nameArr
				}else {
					return false
				}
			}

		}
	}
</script>
<style>
	page {
		background-color: #F5F5F5;
	}
	
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.noBillBox {
		margin: 10rpx auto;
		width: 680rpx;
		// min-height: 300rpx;
		display: flex;
		align-items: center;

		.noBillBtn {
			height: 70rpx;
			width: 260rpx;
			border-radius: 40rpx;
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
		}
	}

	.BillBox {
		margin-bottom: 10rpx;
		position: relative;

		.bill {
			margin: 10rpx auto;
			width: 710rpx;
			min-height: 400rpx;
			border-radius: 30rpx;
			// background-color: #FFFFFF;
			padding: 20rpx;
			position: relative;
			font-size: 34rpx;
			// box-shadow: rgba(50, 50, 93, 0.25) 0px 6px 12px -2px, rgba(0, 0, 0, 0.3) 0px 3px 7px -3px;

			.bill_t1 {
				display: flex;
				align-items: center;
				justify-content: space-between;
				z-index: 1;
				height: 96rpx;
				position: relative;
				border-radius: 32rpx;
				.bill_t1_text1 {
					width: fit-content;
					display: flex;
					align-items: center;
				}

				.bill_t1_text2 {
					width: fit-content;
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.bill_t1_text3 {
					width: fit-content;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					font-size: 30rpx;
				}
			}

			.bill_t2 {
				padding: 0 30rpx;
				font-size: 34rpx;
				z-index: 1;
				height: 70rpx;
				position: relative;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.bill_t2_text {
					width: 33%;
					display: flex;
					align-items: center;
				}
			}

			.bill_t3 {
				display: flex;
				align-items: center;
				justify-content: space-between;
				// padding:0 30rpx;
				z-index: 1;
				height: 120rpx;
				position: relative;

				.flex_c {
					display: flex;
					flex-direction: column;
					align-items: center;
				}

				.start {
					font-size: 28rpx;
					font-weight: 600;
				}

				.mid {
					padding: 6rpx 30rpx;
					border-radius: 30rpx;
					
				}

				.end {
					font-size: 28rpx;
					font-weight: 600;
				}
			}
		}

		.authBox {
			width: 680rpx;
			height: 120rpx;
			border-radius: 30rpx;
			margin: 30rpx auto;
			display: flex;
			align-items: center;
			padding: 0 30rpx;
			justify-content: space-between;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
		}

		.deviceBox {
			.title {
				padding: 30rpx;
			}

			.deviceItemBox {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 30rpx;

				.item1 {
					height: 200rpx;
					width: 330rpx;
					border-radius: 30rpx;
					box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;

					&_title {
						padding: 30rpx;
						font-size: 28rpx;
					}

					&_content {
						padding: 0rpx 30rpx;
						display: flex;
						align-items: center;
						justify-content: space-between;

						.img_lock {
							height: 80rpx;
							width: 80rpx;
						}

						&_text {
							display: flex;
							flex-direction: column;
						}
					}
				}
			}
		}
	}
</style>