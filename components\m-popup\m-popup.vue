<template>
	<view class="m-popup" v-if="show">
		<view class="m-popup_overlay" @click="close" v-if="overlay">
		</view>

		<view class="m-popup_box" v-if="mode=='center'">
			<image v-if="closeable" @click="close" src="/static/images/index/round_close_btn.png" style="position: absolute;right: 30rpx;top: 26rpx;width: 48rpx;height: 48rpx;z-index: 999999;" mode=""></image>
			<slot></slot>
		</view>

		<view class="m-popup_box1" v-if="mode=='bottom'" :style="{'z-index':customStyles.zindex}">
			<image v-if="closeable" @click="close" src="/static/images/index/round_close_btn.png" style="position: absolute;right: 30rpx;top: 26rpx;width: 48rpx;height: 48rpx;z-index: 999999;" mode=""></image>
			<slot></slot>
		</view>
	</view>
</template>

<script>
	export default {
		name: "m-popup",
		props: {
			show: {
				type: Boolean,
				default: false
			},
			mode: {
				type: String,
				default: 'bottom'
			},
			closeable:{
				type: <PERSON>olean,
				default: true
			},
			overlay:{
				type: Boolean,
				default: true
			},
			customStyles:{
				type:Object,
				default: null
			}
		},
		data() {
			return {
				
			};
		},
		mounted() {
			console.log(this.customStyles,'dsd');
		},
		methods: {
			close() {
				if(this.closeable){
					this.$emit('closePop', true)
				}
				
			}
		}

	}
</script>

<style lang="scss" lang="scss">
	.m-popup {
		&_overlay {
			position: fixed;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			width: 100%;
			height: 100%;
			background-color: rgba($color: #000000, $alpha: 0.4);
			z-index: 999;
		}

		&_box {
			position: fixed;
			left: 50%;
			top: 50%;
			-webkit-transform: translate(-50%, -50%);
			transform: translate(-50%, -50%);
			// width: 600rpx;
			// height: auto;
			// height: 500rpx;
			// padding: 20rpx;
			background: #FFFFFF;
			z-index: 1000;
			border-radius: 20rpx;
		}

		&_box1 {
			position: fixed;
			left: 0;
			right: 0;
			bottom: 0;
			width: 100%;
			// padding: 20rpx;
			height: auto;
			background: #FFFFFF;
			z-index: 1000;
			border-radius: 20rpx 20rpx 0 0;
			animation-name: to_up_show;
			animation-duration: 0.3s;
			animation-timing-function: linear;
			/* 升起动画 */

			@keyframes to_up_show {
				from {
					transform: translateY(100%);
				}

				to {
					transform: translateY(0);
				}
			}
		}

	}
</style>
