<template>
	<view>
		<view class="" style="padding:30rpx">
			<p>请填写房号和登记人姓名核销房券</p>
			<view class=""
				style="height: 150rpx;width: 670rpx;display: flex;flex-direction: column;margin-top: 40rpx;justify-content: space-between;">
				<p style="color: #000000EB;font-size: 28rpx;margin-bottom: 40rpx;"><text
						style="color: brown;">*</text>房号</p>
				<uni-easyinput :styles="styles" trim="all" v-model="room_number" placeholder="例填写您的房号"></uni-easyinput>
			</view>
			<view class=""
				style="height: 150rpx;width: 670rpx;display: flex;flex-direction: column;margin-top: 40rpx;justify-content: space-between;">
				<p style="color: #000000EB;font-size: 28rpx;margin-bottom: 40rpx;"><text
						style="color: brown;">*</text>登记人姓名</p>
				<uni-easyinput :styles="styles" trim="all"  v-model="name"
					placeholder="填写登记人姓名"></uni-easyinput>
			</view>
			<view class=""
				style="display: flex;align-items: center;justify-content: center;width: 100%;margin: 40rpx auto;">
				<view class="" @click="toSearch"
					style="width: 600rpx;height: 80rpx;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;"
					:style="{background:themeColor.main_color}">
					<text style="color: #FFFFFF;">点击查询</text>
				</view>
				
			</view>
			<view class="" style="margin-top: 30rpx;width: 100%;display: flex;align-items: center;justify-content: center;">
				<text style="font-size: 32rpx;color: #55aa00;text-decoration:underline;" @click="toFood">立即购买早餐</text>
			</view>
		</view>

		<view class="" style="margin: 30rpx;">
			<p style="color: #000000EB;">核销成功早餐券{{versonList.length}}张!请出示此页面!</p>
			<view class="outer-container">
				<view class="animated-view" v-for="(item, index) in versonList"
					style="width: 700rpx;display: flex;align-items: center;justify-content: space-between;margin: 20rpx auto;height: 160rpx;background-color: #FFFFFF;border-radius: 32rpx;padding: 0 30rpx;">
					<view class="" style="display: flex;align-items: center;">
						<image src="http://doc.hanwuxi.cn/wp-content/uploads/2025/05/lvma.png"
							style="width: 100rpx;height: 100rpx;" mode=""></image>
						<text style="margin-left: 30rpx;">{{item.content}}</text>
					</view>
				
					<text style="color: #00000099;font-size: 44rpx;font-weight: 600;">核销成功</text>
				</view>
			</view>
			
		</view>

		<!-- 二维码 -->
		<m-popup :show="pop" mode="center" @closePop="closePop">
			<view class=""
				style="width: 600rpx;height: 600rpx;display: flex;align-items: center;flex-direction: column;justify-content: space-around;padding: 60rpx 0;">
				<p style="margin-bottom: 30rpx;" v-if="couponList.length>0">查询到{{couponList.length}}张早餐券,点击使用自动核销</p>
				<view class="" style="width: 100%;padding: 0 30rpx;" v-if="couponList.length>0">
					<view class="" v-for="(item, index) in couponList" @click="choose(item)"
						style="border: 1px solid #e5e5e5;border-radius: 8rpx;margin-bottom: 20rpx;padding: 0 20rpx;display: flex;align-items: center;justify-content: space-between;">
						<view class="" style="display: flex;align-items: center;">
							<img src="http://doc.hanwuxi.cn/wp-content/uploads/2025/05/zaocan.png"
								style="height: 80rpx;width: 80rpx;" alt="" />
							<text style="margin-left: 20rpx;">{{item.content}}</text>
						</view>
						
						<view class=""
							style="width: 50rpx;height: 50rpx;background-color: #e5e5e5;border-radius: 50%;display: flex;align-items: center;justify-content: center;">
								<view class="" :style="chooseListIds.includes(item.id)?'width:36rpx;height:36rpx;border-radius:50%;background:#55aa00;':''">
									
								</view>
						</view>
					</view>
				</view>
				<view class="" style="width: 100%;padding: 0 30rpx;display: flex;align-items: center;justify-content: center;" v-if="couponList.length==0">
					<p>未查到相关早餐券</p>
				</view>
				<view class="" @click="toUse" v-if="chooseList.length>0"
					style="width: 440rpx;height: 80rpx;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;"
					:style="{background:themeColor.main_color}">
					<text style="color: #FFFFFF;">点击使用</text>
				</view>
				<view class="" v-if="chooseList.length==0" @click="tips"
					style="width: 440rpx;height: 80rpx;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;background-color: #e5e5e5;"
					>
					<text style="color: #FFFFFF;">点击使用</text>
				</view>
			</view>
		</m-popup>

		<!-- 阻止返回 -->
<!-- 		<view class="" v-if="isShow">
			<page-container :show="isShow" :overlay="false" @beforeleave="beforeleave"></page-container>
		</view> -->
		
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess" :customStyle="cusStyle"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				room_number: '',
				name: '',
				styles: {
					backgroundColor: '#F9F9F9'
				},
				pop: false,
				isShow: true,
				hotel_id:'',
				couponList:[],
				chooseList:[],
				chooseListIds:[],
				versonList:[],
				ifSuccess:false,
				time1:null,
				tipsNum:300,
				hackReset:true,
				if_login: false,
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'hotelList','setting'])
		},
		async onShow() {
			await this.$onLaunched;
			this.BeaforeUnLoad();
			let scene = wx.getEnterOptionsSync()
			if (scene.query.scene) {
				// 扫码场景
				let query = decodeURIComponent(scene.query.scene)
				//解析参数
				if (query.includes("shop_id=")) {
					this.hotel_id = this.$iBox.linkFormat(query, "shop_id")
				}
			
			} else {
				this.hotel_id = this.hotel.id
			}
			console.log(this.setting);
			let set = this.setting.filter(item => {
				return item.sign == 'auto_register_member'
			})
			if (set[0].property) {
				let a = set[0].property.value
				if (a == 2) {
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
						.upgrade_growth_value > -1) {
						this.if_login = false
						this.$iBox.http('getVerificationBreakfastCoupon', {shop_id:this.hotel_id})({
								method: 'post'
							}).then(res => {
								this.versonList = res.data.filter(item=>{
									return this.$moment(item.use_time*1000).format('YYYY-MM-DD')== this.$moment().format('YYYY-MM-DD')
								})
							});
						
							  
					} else {
						this.if_login = true
					}
							  
				} else if (a == 1) {
					// this.pop = true
					if (this.userInfo.phone) {
						this.if_login = false
						this.$iBox.http('getVerificationBreakfastCoupon', {shop_id:this.hotel_id})({
								method: 'post'
							}).then(res => {
								res.data.forEach(item => {
									console.log(this.$moment().format('YYYY-MM-DD'));
								})
								
								this.versonList = res.data.filter(item=>{
									return this.$moment(item.use_time*1000).format('YYYY-MM-DD')== this.$moment().format('YYYY-MM-DD')
								})
							});
					} else {
						this.if_login = true
					}
				}
			}
			
		},
		methods: {
			toSearch() {
				if(!this.room_number||!this.name){
					uni.showToast({
						icon:'error',
						title:'请输入房号姓名'
					})
					return
				}
				
				this.pop = true
				let params = {
					shop_id:this.hotel_id,
					room_number:this.room_number,
					user_name:this.name,
					page:1,
					limit:100
				}
				this.$iBox.http('getUserBreakfastCouponList', params)({
						method: 'post'
					}).then(res => {
						this.couponList = res.data.list.filter(item=>{
									return item.date == this.$moment().format('YYYY-MM-DD')
								})
					});
			},
			closePop() {
				this.pop = false
			},
			beforeleave() {
				this.isShow = false
				uni.showModal({
					title: '确定要退出吗?',
					content: '退出此页面后早餐券将失效!',
					success: res => {
						if (res.confirm) {
							uni.reLaunch({
								url: '/pages/index/index'
							})
						} else {
							this.isShow = true
						}
					}
				})
			},
			 BeaforeUnLoad() {
			    wx.enableAlertBeforeUnload({
			      message: '离开此页后早餐券将失效哦',
			      success: (e) => {
			        console.log(e);
			      },
			      fail: (e) => {
			        console.log(e);
			      }
			    })
			  },
			  choose(e){
				  if(!this.chooseListIds.includes(e.id)){
					  this.chooseList.push(e)
					  this.chooseListIds.push(e.id)
				  }else{
					   this.chooseListIds =  this.chooseListIds.filter(item=>{
						   return item != e.id
					   })
					   
					   this.chooseList = this.chooseList.filter(item=>{
					   		return item.id != e.id
					   })
				  }
			  },
			  tips(){
				  uni.showToast({
				  	icon:'none',
					title:'请选择早餐券!'
				  })
			  },
			  toUse(){
				  
				  this.$iBox.http('wxVerificationBreakfastCoupon', {ids:this.chooseListIds})({
				  		method: 'post'
				  	}).then(res => {
						this.pop = false
				  		this.ifSuccess = true
						// this.time1 = setInterval(item=>{
						// 	--this.tipsNum
						// 	if(this.tipsNum==0){
						// 		uni.showModal({
						// 			title:'提示',
						// 			content:'核销后超过5分钟,此页面已过期!',
						// 			showCancel:false,
						// 			success() {
						// 				uni.reLaunch({
						// 					url:'/pages/index/index'
						// 				})
						// 			}
						// 		})
						// 	}
							
						// },1000)
				  	});
			  },
			  toFood(){
				  uni.navigateTo({
				  	url:'/packageB/buyFood/buyFood?id='+ this.hotel_id
				  })
			  },
			  closeLogin(e){
			  	this.if_login = false
			  },
			  loginSucess() {
			  
			  	this.hackReset = false
			  	this.$nextTick(() => {
			  		this.hackReset = true
			  
			  		let set = this.setting.filter(item => {
			  			return item.sign == 'auto_register_member'
			  		})
			  		if (set[0].property) {
			  			let a = set[0].property.value
			  			if (a == 2) {
			  				if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
			  					.upgrade_growth_value > -1) {
			  					this.if_login = false
			  
			  				} else {
			  					this.if_login = true
			  				}
			  
			  			} else if (a == 1) {
			  				// this.pop = true
			  				if (this.userInfo.phone) {
			  					this.if_login = false
			  
			  				} else {
			  					this.if_login = true
			  				}
			  			}
			  		}
			  
			  
			  	})
			  },

		}
	}
</script>
<style>	
	page {
		background: #FFFFFF;
	}
</style>
<style scoped lang="scss">
	
	
	.animated-view {
	  width: 300rpx;
	  height: 200rpx;
	  background: linear-gradient(90deg, 
	    rgba(0,255,0,0.2) 0%,   /* 浅绿起始 */
	    #00ff00 50%,            /* 主绿色 */
	    rgba(255,255,255,0.6) 100%  /* 浅绿结束 */
	  );
	  background-size: 200% 100%;  /* 扩大背景尺寸 */
	  animation: slide 3s linear infinite;
	  
	}
	
	.outer-container {
	  animation: zoom 2s ease-in-out infinite; /* 整体缩放 */
	}

	
	@keyframes slide {
	  0% { background-position: 100% 0; }  /* 从右开始滑动 */
	  100% { background-position: -100% 0; } /* 向左滑动结束 */
	}
	
	@keyframes zoom {
	  0%, 100% { transform: scale(0.9); } /* 初始/结束状态 */
	  50% { transform: scale(1); }     /* 峰值放大效果 */
	}

</style>