<template>
	<view>
		<view class="m-hotel" v-for="item in hotel_List" @click="chooseHotel(item)" :key="item.id"
			:style="{background:themeColor.bg_color}" v-if="mode==1">
			<image :src="item.cover_pic" mode="aspectFill" class="m-hotel_img"></image>
			<view class="m-hotel_content" :style="{color:themeColor.text_main_color}">
				<text class="m-hotel_content_name">{{item.shop_name}}</text>
				<view class="m-hotel_content_rate">
					<text class="m-hotel_content_rate_num">{{item.socre*1==0?'5.0':item.socre}}</text>
					<!-- <text>.0</text> -->
					<text style="padding-left: 10rpx;padding-right: 20rpx;"
						v-if="item.socre*1 > 0">{{item.socre*1 > 4?'很棒':(item.socre*1 <= 4&&item.socre*1 >3?'一般':'很差')}}</text>
					<m-rate :value="item.socre*1==0?'5':item.socre" :size="20" :readonly="true"></m-rate>
					<view class="m-hotel_content_rate_bg"
						:style="{'background-image': 'linear-gradient(-90deg,'+themeColor.main_color+'40,'+themeColor.com_color2+'40)'}">
					</view>
				</view>
				<view class="m-hotel_content_address" :style="{color:themeColor.text_title_color}">
					<text>{{item.address?item.address:'暂无地址'}}</text>
				</view>
				<view class="m-hotel_content_address" :style="{color:themeColor.text_title_color}">
					<text
						v-if="item.distance">距您{{item.distance*1<1000?item.distance+'m':(item.distance/1000).toFixed(1)+'km'}}</text>
					<text v-if="!item.distance">暂无距离</text>
				</view>
				<view class="m-hotel_content_tags">
					<m-tags :name="item1" v-for="(item1,index1) in item.tags" v-if="index1<2" :key="item1.id"
						:type="item1.isHot?'hot':'primary'"></m-tags>
				</view>
				<view class="" v-if="item.slogan" style=" font-size: 26rpx;
    color: #666666;
    margin-left: 8rpx;
	max-width: 480rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;">
					<text>{{item.slogan}}</text>
				</view>
				<view class="m-hotel_content_price" :style="{color:themeColor.text_title_color}">
					<!-- <view class="m-hotel_content_price_hx">
							<text
								style="font-size: 26rpx;text-decoration:line-through">￥{{item.hxPrice}}</text>
					</view> -->
					<view class="m-hotel_content_price_low">
						<text style="font-size: 30rpx;font-weight: 500;color: #FF2B2B;">￥</text>
						<text class="m-roomList1_box_header_item2_price_price">{{item.price}}</text>

					</view>
					<text>起</text>
				</view>
			</view>
		</view>

		 <!-- 第二套UI - 完全复刻图片样式 -->
		    <view class="homestay-card" v-for="item in hotel_List" @click="chooseHotel(item)"
			 :key="item.id" v-if="mode==2">
		      <!-- 图片区域 -->
		      <view class="homestay-card__image-container">
		        <image :src="item.cover_pic || 'https://placeholder.com/300x200'" mode="aspectFill" class="homestay-card__image"></image>
		        <!-- 评分和评论 -->
		        <view class="homestay-card__rating-box">
		          <text class="homestay-card__rating-score">{{item.socre=='0.0'?'5.0':item.socre}}</text>
		          <text class="homestay-card__rating-text">{{item.room_bill_evaluate?item.room_bill_evaluate:''}}</text>
		          <!-- <text class="homestay-card__rating-count">{{item.sales || 2757}}人消费</text> -->
		        </view>
		      </view>
		      
		      <!-- 内容区域 -->
		      <view class="homestay-card__content">
		        <!-- 标题和房型 -->
		        <view class="homestay-card__title">
		          <text class="homestay-card__name">【{{item.shop_name }}】</text>
		          <text class="homestay-card__room-type">{{item.slogan?item.slogan:''}}</text>
		        </view>
		        
		        <!-- 标签区域 -->
		        <view class="homestay-card__tags">
		          <text class="homestay-card__tag" v-for="(tag, index) in item.tags" :key="index" v-if="index < 6">
		            {{tag}}
		          </text>
		        </view>
		        
		        <!-- 位置和距离 -->
		        <view class="homestay-card__location">
		          <text class="homestay-card__address">{{item.address}}·距您直线{{item.distance*1<1000?item.distance+'m':(item.distance/1000).toFixed(1)+'km' || '2.2公里'}}</text>
		          <!-- <text class="homestay-card__room-info">| 整套房源{{item.area || 47}}m²·{{item.room_count || '1室1床'}}·{{item.capacity || 2}}人</text> -->
		        </view>
		        
		        <!-- 价格区域 -->
		        <view class="homestay-card__price">
		          <view class="homestay-card__current-price">
		            <text class="homestay-price__symbol">￥</text>
		            <text class="homestay-price__number">{{item.price}}起</text>
		          </view>
		  <!--        <view class="homestay-card__original-price">
		            <text class="homestay-original-price__text">￥{{item.price?item.price:'**'}}起</text>
		          </view> -->
		         <!-- <view class="homestay-card__discount">
		            <text class="homestay-discount__text">首单特惠已减{{item.discount || 50}}</text>
		          </view> -->
		        </view>
		        
		        <!-- 排名徽章 -->
		      <!--  <view class="homestay-card__badge" v-if="item.rank">
		          <text class="homestay-badge__text">{{item.rank || 'TOP武汉民宿收藏榜TOP3'}}</text>
		        </view> -->
		      </view>
		    </view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';

	export default {
		name: "m-hotelList",
		props: {
			hotel_List: {
				type: Array,
				default: []
			},
			mode: {
				type: [String, Number],
				default: 1
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'unit'])
		},
		data() {
			return {
				isBill: false,
				params1: {
					page: 1,
					limit: 10,
					bill_status: 4
				}
			};
		},
		mounted() {


		},
		methods: {
			...mapActions('hotel', ['getHotel']),
			chooseHotel(e) {
				this.getHotel(e)
				this.params1.page = 1
				this.$iBox.http('getRoomBillList', this.params1)({
					method: 'post'
				}).then(res => {

					if (res.data.list.length > 0) {

						let list = res.data.list.filter(item => {
							return item.shop_id == e.id
						})
						// 过滤出有订单后，先判断
						if (list.length > 0) {
							this.isBill = true
						} else {
							this.isBill = false
						}

					} else {
						this.isBill = false
					}
					console.log();
					const pages = getCurrentPages();
					let currentPage = pages[pages.length - 2]; //获取上页面的对象
					console.log(pages);
					if (this.cityModel && currentPage.route != 'pages/myRoom/myRoom') {
						uni.navigateTo({
							url: '/pages/hotelDetail/hotelDetail'
						})
					} else if (this.cityModel && currentPage.route == 'pages/myRoom/myRoom' && !this.isBill) {
						uni.navigateTo({
							url: '/pages/hotelDetail/hotelDetail'
						})
					} else if (this.cityModel && currentPage.route == 'pages/myRoom/myRoom' && this.isBill) {
						uni.navigateBack({
							delta: 1
						})
					} else if (!this.cityModel) {
						uni.navigateTo({
							url: '/pages/hotelDetail/hotelDetail'
						})
					}
				})


			}
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.m-hotel {
		// width: 100%;
		// border-radius: 40rpx;
		display: flex;
		padding: 20rpx;
		margin: 20rpx 0;
		z-index: 3;

		&_img {
			width: 192rpx;
			min-height: 256rpx;
			max-height: 300rpx;
			border-radius: 8rpx;
		}

		&_content {
			width: 480rpx;
			display: flex;
			flex-direction: column;
			word-wrap: break-all;
			padding: 0 0rpx 0 20rpx;
			position: relative;

			&_name {
				font-size: 38rpx;
				font-weight: 500;
			}

			&_rate {
				display: flex;
				align-items: center;
				position: relative;
				margin-top: 14rpx;

				&_num {
					font-size: 34rpx;
					font-weight: 600;
					z-index: 3;
				}

				&_bg {
					position: absolute;
					width: 80rpx;
					height: 32rpx;
					border-radius: 20rpx;
					z-index: 1;
					bottom: -2rpx;
				}
			}

			&_address {
				font-size: 28rpx;
				line-height: 46rpx;
				word-wrap: break-all;
			}

			&_tags {
				display: flex;
				flex-wrap: wrap;
			}

			&_price {
				display: flex;
				justify-content: flex-end;
				align-items: flex-end;
				position: absolute;
				bottom: 2rpx;
				right: 4rpx;

				&_low {
					font-size: 44rpx;
					font-weight: 600;
					color: #FF2B2B;
				}
			}
		}
	}

/* 完全复刻图片样式的CSS */
.homestay-card {
  width: 100%;
  background-color: #FFFFFF;
  border-radius: 16rpx;
  overflow: hidden;
  // margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  padding:10rpx 30rpx;
  &__image-container {
    position: relative;
    height: 480rpx;
	
  }
  
  &__image {
    width: 100%;
    height: 100%;
    display: block;
	border-radius: 32rpx;
  }
  
  &__rating-box {
    position: absolute;
    left: 20rpx;
    bottom: 20rpx;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 8rpx;
    padding: 8rpx 16rpx;
    display: flex;
    align-items: center;
    color: #FFFFFF;
    
    &-score {
      font-size: 32rpx;
      font-weight: bold;
      color: #FFD700;
    }
    
    &-text {
      font-size: 24rpx;
      margin-left: 12rpx;
      max-width: 300rpx;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    &-count {
      font-size: 24rpx;
      margin-left: 20rpx;
      color: rgba(255, 255, 255, 0.8);
    }
  }
  
  &__content {
    padding: 20rpx 0;
    position: relative;
  }
  
  &__title {
    margin-bottom: 6rpx;
    display: flex;
    align-items: center;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
    // flex-wrap: wrap;
  }
  
  &__name {
    font-size: 28rpx;
    font-weight: bold;
    color: #333333;
  }
  
  &__room-type {
    font-size: 26rpx;
    color: #666666;
    margin-left: 8rpx;
	max-width: 480rpx;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
  }
  
  &__tags {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 6rpx;
  }
  
  &__tag {
    font-size: 22rpx;
    color: #666666;
    background-color: #F5F5F5;
    border-radius: 4rpx;
    padding: 4rpx 12rpx;
    margin-right: 12rpx;
    // margin-bottom: 8rpx;
  }
  
  &__location {
    display: flex;
    align-items: center;
    // margin-bottom: 20rpx;
    font-size: 24rpx;
    color: #999999;
	width: 100%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
    // flex-wrap: wrap;
  }
  
  &__address {
    margin-right: 12rpx;
  }
  
  &__room-info {
    margin-left: 12rpx;
  }
  
  &__price {
    display: flex;
    align-items: flex-end;
    margin-top: 20rpx;
    
    &-current {
      display: flex;
      align-items: flex-end;
    }
  }
  
  .homestay-price {
    &__symbol {
      font-size: 28rpx;
      color: #FF2B2B;
      font-weight: bold;
      margin-right: 4rpx;
    }
    
    &__number {
      font-size: 40rpx;
      color: #FF2B2B;
      font-weight: bold;
    }
  }
  
  &__original-price {
    margin-left: 12rpx;
    text-decoration: line-through;
    font-size: 24rpx;
    color: #999999;
  }
  
  &__discount {
    margin-left: 16rpx;
    background-color: #FFF0F0;
    border-radius: 4rpx;
    padding: 4rpx 8rpx;
  }
  
  .homestay-discount {
    &__text {
      font-size: 22rpx;
      color: #FF2B2B;
    }
  }
  
  &__badge {
    position: absolute;
    top: 20rpx;
    right: 20rpx;
    background-color: #FF2B2B;
    border-radius: 4rpx;
    padding: 4rpx 12rpx;
  }
  
  .homestay-badge {
    &__text {
      font-size: 22rpx;
      color: #FFFFFF;
      font-weight: bold;
    }
  }
}
</style>