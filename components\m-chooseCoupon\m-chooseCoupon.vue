<template>
	<view class="content">
		<view class="" style="padding: 30rpx;display: flex;align-items: center;justify-content: space-between;">
			<p style="font-weight: 600;font-size: 40rpx;">优惠券</p>
			<view class="" style="width: fit-content" v-if="isNum ===1">
				<view class="btn_register" :style="{background:themeColor.main_color,color:themeColor.bg_color}"
					@click="register">确定</view>
			</view>
		</view>

		<!-- 	<view class="tabHeads">
			<view class="tabLabel" @click="tabsClick(item)" v-for="(item,index) in tabsArray" :key="index">
				<view :class="[isNum === item.inx?'isTabActive':'default']"
					:style="isNum === item.inx?'color:#000;font-weight:700;border-bottom: 2px solid '+themeColor.main_color:'default'">
					{{item.name}}
				</view>
			</view>
		</view> -->

		<scroll-view style="height:900rpx" :scroll-top="scrollTop" scroll-y="true">
			<view class="couponMain">
				<view class=""
					style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;width: 100%;"
					v-if="coupons.length==0">
					<view class="icon-zanwuyouhuiquan1" style="font-size: 260rpx;color: #c1c1c1;">
					</view>
					<p style="color: #c1c1c1;">暂无优惠券</p>
				</view>
				<!-- <radio-group @change="radioChange" v-else> -->
				<view class="coupon" v-for="(item,index) in coupons" :key="index" style="position: relative;"
					:style="item.usable?'':'opacity:0.4'">
					<!-- 	<view class="" style="position: absolute;width: 100%;height: 100%;background-color: #c0c4cc;border-radius: 24rpx;" v-if="!item.usable">
						
					</view> -->
					<!-- :class="[isNum ===1?item.coupon_info.type_id ===1?'bgColorTop1':item.coupon_info.type_id ===2?'bgColorTop1':'bgColorTop1':'bgColorTop1']" -->
					<view class="couponTop">
						<view class="tpyeNameSty"
							:style="isNum ===1?item.coupon_info.type_id ===1?'background:'+themeColor.com_color1:'background:'+item.coupon_info.type_id ===2?'background:'+themeColor.com_color2:'background:'+themeColor.com_color3:'background:'+themeColor.com_color">
							{{item.coupon_info.type_id ===1?'订房券':item.coupon_info.type_id ===2?'超市券':(item.coupon_info.type_id ===3?'周边券':(item.coupon_info.type_id ===4?'点餐券':'商城券'))}}
						</view>
						<view class="couponTopLeft"
							:style="isNum ===1?item.coupon_info.type_id ===1?'background:'+themeColor.com_color1+'33':item.coupon_info.type_id ===2?'background:'+themeColor.com_color2+'33':'background:'+themeColor.com_color3+'33':'background:'+themeColor.com_color+'33'">
							<view class="valueSty">
								<text class="symbolMoney">￥</text>
								<text class="moneyVal" v-if="item.discount_type==1">{{item.coupon_info.discounts}}</text>
								<text v-if="item.discount_type ===2" class="moneyVal">{{item.discount_rate*10}}</text>
								<text v-if="item.discount_type ===2" style="font-size: 14px;">折</text>
							</view>
							<view class="valueSty" :class="[isNum ===1?item.type_id ===1?'color1':item.type_id ===2?'color3':'color5':'color7']" v-if="item.discount_type==2&&item.discount_max>0">
								<text class="" style="font-size: 22rpx;">最多抵扣{{item.discount_max}}元</text>
							</view>
							<view v-if="item.coupon_info.use_condition ===0 " class="useCondition">
								无门槛使用
							</view>
							<view v-else class="useCondition">
								<text>满{{item.coupon_info.use_condition}}元可用</text>
								<!-- <text>{{item.type_id ===3?'兑':'用'}}</text> -->
							</view>
						</view>
						<view class="couponTopRight">
							<view class="ctr-left">
								<view class="couponName">
									{{item.coupon_info.name}}
								</view>
								<view class="couponStore">
									{{item.coupon_info.desc}}
								</view>
								<!-- 	<view class="couponDate"
										:class="[isNum ===1?item.coupon_info.type_id ===1?'color2':item.coupon_info.type_id ===2?'color2':'color2':'color2']">
										{{item.coupon_info.start_time | moment}}~{{item.coupon_info.end_time | moment}}
									</view> -->
								<view class="couponDate">
									有效期:{{item.limit_time | moment}}
								</view>
							</view>
							<view class="ctr-right" v-if="item.usable">
								<view class="" @click="radioChange(item)"
									style="height: 50rpx;width: 50rpx;border-radius: 50%;margin-top: 30rpx;
									display: flex;align-items: center;justify-content: center;background-color: #FFFFFF;border: 1px solid #d7d9de;">
									<uni-icons v-if="item.id==currentId" type="checkmarkempty"
										:color="themeColor.main_color" size="24"></uni-icons>
								</view>
								<!-- <label>
										<radio v-if="isNum ===1" class="round" :checked="item.ifChecked" @click="radioChange"
											:color="themeColor.com_color1" :value="item.id" />
									</label> -->
							</view>
							<view class="ctr-right" v-else>
								<view class=""
									style="height: 50rpx;width: 50rpx;border-radius: 50%;margin-top: 30rpx;
									display: flex;align-items: center;justify-content: center;background-color: #d7d9de;border: 1px solid #d7d9de;">
								</view>
							</view>
						</view>
					</view>

					<view class="couponBottom">
						<view class="ruleLabel">
							<view class="ruleLabel-left"  @click.stop="viewRules(item)">
								<text class="limit" style="margin-right: 30rpx;">
									{{item.coupon_info.usable_date.length > 0||item.coupon_info.usable_time||item.coupon_info.usable_week.length > 0||item.usable_shop_List.length > 0?'点击查看使用条件':'无限制条件'}}
								</text>
							</view>
							<view class="ruleBtn" @click.stop="viewRules(item)">
								<text style="margin-right: 6px;">使用规则</text>
								<view class="arrowIcon"
									:class="[item.coupon_info.isViewRule?isNum ===1?item.coupon_info.type_id ===1?'rotate arrowIcon1':item.coupon_info.type_id ===2?'rotate arrowIcon1':'rotate arrowIcon1':'rotate arrowIcon1':isNum ===1?item.coupon_info.type_id ===1?'backRotate arrowIcon1':item.coupon_info.type_id ===2?'backRotate arrowIcon2':'backRotate arrowIcon3':'backRotate arrowIcon4']">
								</view>
							</view>
						</view>
						<view class="ruleDetail" v-if="priceIndex.includes(item.coupon_info.id)"
							style="color: #909399;">
							<view class="" style="display: flex;align-items: flex-start;"
								v-if="item.usable_shop_List.length > 0">
								<text
									style="font-size: 24rpx;margin-top: 16rpx;max-width: 258rpx;min-width: 116rpx;">可用酒店：</text>
								<view class="ruleList" style="flex-wrap: wrap;margin-top: 16rpx;padding-top: 0;">
									<view style="margin-left:20rpx;display: flex;align-items: center;flex-wrap: wrap;"
										v-for="item1 in item.usable_shop_List">
										<view class="">
											{{item1.shop_name}}可用
										</view>
										<view class="" style="margin-left: 40rpx;">
											适用商品:<text
												v-for="item2 in item1.usable_goods_list">{{item2.goods_name}}、</text>
										</view>
									</view>
								</view>
							</view>
							<view class="ruleList" v-if="item.coupon_info.usable_date.length > 0">
								使用日期：<text style="margin-left:30rpx"
									v-for="item1 in item.coupon_info.usable_date">{{item1}}可用</text>
							</view>
							<view class="ruleList" v-if="item.coupon_info.usable_time">
								使用时间: <text style="margin-left:30rpx">{{item.coupon_info.usable_time}}前可用</text>
							</view>
							<view class="ruleList" v-if="item.coupon_info.usable_week.length > 0">
								使用星期: <text style="margin-left: 30rpx;"
									v-for="item1 in item.coupon_info.usable_week">星期{{item1==1?'一':(item1==2?'二':(item1==3?'三':(item1==4?'四':(item1==5?'五':(item1==6?'六':'日')))))}}可用</text>
							</view>
						</view>
					</view>
				</view>
				<!-- </radio-group> -->
				<view class="" style="height: 160rpx;background: #F4F6F8;width: 100%;">

				</view>
			</view>

		</scroll-view>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				wHeight: 0,
				scrollTop: 0,
				isNum: 1,
				tabsArray: [{
					inx: 1,
					name: '可用优惠券'
				}, {
					inx: 2,
					name: '不可用优惠券'
				}],
				priceIndex: [],
				coupons: [],
				currentId: ''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		props: {
			coupType: {
				type: Number
			},
			limit: {
				type: Object,
				default: ''
			},
			shopId: {
				type: [Number, String]
			},
			roomType: {
				type: [Number, String]
			},
			goods: {
				type: Array
			}

		},
		watch: {
			limit: {
				handler(newVal, oldVal) {
					console.log(newVal, 'newval', this.cart);
					uni.showLoading({
						title: '加载中...'
					})

					if (newVal) {
						if (this.coupType == 1) {
							this.$iBox.http('getUserCoupon', {
								type_id: this.coupType,
								use_status: 0,
								shop_id: this.hotel.id,
								room_type_id: this.roomType
							})({
								method: 'post'
							}).then(res => {
								// 默认显示可以使用的优惠券
								this.isNum = 1
								let coupons = res.data
								coupons.forEach(item => {
									if (item.coupon_info.type_id && Number(item.coupon_info
											.use_condition) <= newVal.limitNum && this.$moment().unix() <=
										item
										.limit_time && (item.usable_week.length > 0 && item.usable_week
											.includes(this.$moment().isoWeekday().toString()) || item
											.usable_week.length == 0) && item.usable) {
										item.usable = 1
									} else {
										item.usable = 0
									}
								})
								console.log(coupons, 'klk');
								this.coupons = coupons

								uni.hideLoading()
							})
						} else if(this.coupType == 2){
							let goods_list = []
							this.goods.forEach(item => {
								let item1 = {
									goods_id: '',
									count: 0
								}
								item1.goods_id = item.id
								item1.count = item.number
								goods_list.push(item1)
							})
							this.$iBox.http('getUserMarketCoupon', {
								type_id: this.coupType,
								use_status: 0,
								shop_id: this.hotel.id,
								goods_list: goods_list
							})({
								method: 'post'
							}).then(res => {
								// 默认显示可以使用的优惠券
								this.isNum = 1
								let coupons = res.data
								coupons.forEach(item => {
									if (item.coupon_info.type_id && Number(item.coupon_info
											.use_condition) <= newVal.limitNum && this.$moment().unix() <=
										item
										.limit_time && (item.usable_week.length > 0 && item.usable_week
											.includes(this.$moment().isoWeekday().toString()) || item
											.usable_week.length == 0) && item.usable) {
										item.usable = 1
									} else {
										item.usable = 0
									}
								})

								this.coupons = coupons

								uni.hideLoading()
							})
						}else if(this.coupType == 3){
							let goods_list = []
							this.goods.forEach(item => {
								let item1 = {
									goods_id: '',
									count: 0
								}
								item1.goods_id = item.id
								item1.count = item.number
								goods_list.push(item1)
							})
							this.$iBox.http('getUserFoodCoupon', {
								type_id: this.coupType,
								use_status: 0,
								shop_id: this.hotel.id,
								goods_list: goods_list
							})({
								method: 'post'
							}).then(res => {
								// 默认显示可以使用的优惠券
								this.isNum = 1
								let coupons = res.data
								coupons.forEach(item => {
									if (item.coupon_info.type_id && Number(item.coupon_info
											.use_condition) <= newVal.limitNum && this.$moment().unix() <=
										item
										.limit_time && (item.usable_week.length > 0 && item.usable_week
											.includes(this.$moment().isoWeekday().toString()) || item
											.usable_week.length == 0) && item.usable) {
										item.usable = 1
									} else {
										item.usable = 0
									}
								})
							
								this.coupons = coupons
							
								uni.hideLoading()
							})
						}

					}

				},
				deep: true,
				immediate: true
			},
			goods: {
				handler(newVal, oldVal) {
					console.log(newVal, 'dsdsd');
				},
				deep: true,
				immediate: true
			}

		},
		mounted() {},
		methods: {
			// tabsClick(e) {
			// 	console.log(e.inx);
			// 	this.isNum = e.inx
			// 	uni.showLoading({
			// 		title: '加载中...'
			// 	})
			// 	if(this.coupType==1){
			// 		this.$iBox.http('getUserCoupon', {
			// 			type_id: this.coupType,
			// 			use_status: e.inx - 1,
			// 			shop_id: this.hotel.id
			// 		})({
			// 			method: 'post'
			// 		}).then(res => {

			// 			let coupons = res.data.forEach(item => {
			// 				item.ifChecked = false
			// 			})
			// 			this.coupons = coupons
			// 			if (e.inx == 1) {
			// 				this.coupons = res.data.filter(item => {
			// 					return this.coupType == item.coupon_info.type_id && Number(item.coupon_info
			// 							.use_condition) <= this.limit.limitNum && this.$moment().unix() <= item
			// 						.limit_time && (item.usable_week.length > 0 && item.usable_week.includes(
			// 								this.$moment().isoWeekday().toString()) || item.usable_week
			// 							.length == 0)
			// 				})


			// 				this.tabsArray[e.inx - 1].name = this.tabsArray[e.inx - 1].name.split('(')[0]
			// 			} else {
			// 				this.coupons = res.data.filter(item => {
			// 					return this.coupType != item.coupon_info.type_id || Number(item.coupon_info
			// 							.use_condition) > this.limit.limitNum || this.$moment().unix() > item
			// 						.limit_time || (item.usable_week.length > 0 || !item.usable_week.includes(
			// 								this.$moment().isoWeekday().toString()) || item.usable_week
			// 							.length == 0)
			// 				})

			// 				this.tabsArray[e.inx - 1].name = this.tabsArray[e.inx - 1].name.split('(')[0]
			// 			}

			// 			uni.hideLoading()
			// 		})
			// 	}else {
			// 		this.$iBox.http('getUserMarketCoupon', {
			// 			type_id: this.coupType,
			// 			use_status: e.inx - 1,
			// 			shop_id: this.hotel.id
			// 		})({
			// 			method: 'post'
			// 		}).then(res => {

			// 			let coupons = res.data.forEach(item => {
			// 				item.ifChecked = false
			// 			})
			// 			this.coupons = coupons
			// 			if (e.inx == 1) {
			// 				this.coupons = res.data.filter(item => {
			// 					return this.coupType == item.coupon_info.type_id && Number(item.coupon_info
			// 							.use_condition) <= this.limit.limitNum && this.$moment().unix() <= item
			// 						.limit_time && (item.usable_week.length > 0 && item.usable_week.includes(
			// 								this.$moment().isoWeekday().toString()) || item.usable_week
			// 							.length == 0)
			// 				})

			// 				this.tabsArray[e.inx - 1].name = this.tabsArray[e.inx - 1].name.split('(')[0]
			// 			} else {
			// 				this.coupons = res.data.filter(item => {
			// 					return this.coupType != item.coupon_info.type_id || Number(item.coupon_info
			// 							.use_condition) > this.limit.limitNum || this.$moment().unix() > item
			// 						.limit_time || (item.usable_week.length > 0 || !item.usable_week.includes(
			// 								this.$moment().isoWeekday().toString()) || item.usable_week
			// 							.length == 0)
			// 				})

			// 				this.tabsArray[e.inx - 1].name = this.tabsArray[e.inx - 1].name.split('(')[0]
			// 			}

			// 			uni.hideLoading()
			// 		})
			// 	}

			// },

			radioChange(e) {
				console.log(e);
				if (this.currentId&&e.id==this.currentId) {
					this.currentId = ''
				} else {
					this.currentId = e.id
				}
				console.log(this.coupons);
			},
			register() {
				console.log(this.coupons, 'this.coupons');
				let chooseCoupon = this.coupons.filter(item => {
					return item.id == this.currentId
				})
				let choosed = ''
				if (chooseCoupon.length > 0) {
					choosed = chooseCoupon[0]
				}

				this.$emit('getCouponIfo', choosed)
			},

			viewRules(e) {

				if (this.priceIndex.includes(e.id)) {
					this.priceIndex = this.priceIndex.filter(item => {
						return item != e.id
					})
				} else {
					this.priceIndex.push(e.id)
				}
				console.log(e, this.priceIndex);
			}
		}
	}
</script>

<style scoped lang="scss">
	.content {
		padding: 0;
		overflow: hidden;
		// height:800rpx;
		position: relative;
	}

	.tabHeads {
		display: flex;
		background-color: #fff;
		margin-bottom: 1px;
	}

	.tabLabel {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 16px;
	}

	.default {
		color: #999999;
		padding: 10px 0;
		border-bottom: 1px solid #ffffff;
	}

	.isTabActive {
		padding: 10px 0;
	}

	.headWrap {
		padding: 10px;
		background-color: #fff;
		display: flex;
		align-items: center;
		/* justify-content: space-between; */
		flex-wrap: wrap;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
	}

	.labelDiv {
		width: 25%;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-bottom: 20rpx;

		.labelDivItem {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.active {
			width: 90%;
			height: 100%;
			padding: 8px 8px;
			border-radius: 4px;
			// background-color: #eeeeee;
			color: #999999;
			font-size: 12px;
			display: flex;
			align-items: center;
			justify-content: center;
		}

	}



	.couponMain {
		padding: 10px;
		display: flex;
		flex-direction: column;
		background: #F4F6F8;
		height: 100%;
	}

	.coupon {
		margin-bottom: 20px;
		background-color: #FFFFFF;
		border-radius: 16rpx;
	}

	.couponTop {
		display: flex;
		border-top-left-radius: 4px;
		border-top-right-radius: 4px;
		position: relative;
	}

	.couponTopLeft {
		flex: 1.2;
		display: flex;
		align-items: center;
		justify-content: center;
		flex-direction: column;
		position: relative;
		overflow: hidden;
		margin: 10rpx;
		border-radius: 16rpx;
	}

	.couponTypeDiv {
		/* overflow: hidden; */
		/* position: relative; */
	}

	.imgType {
		width: 46px;
		height: 40px;
		position: absolute;
		top: 0;
		left: 0;
	}

	.tpyeNameSty {
		font-size: 19rpx;
		// transform: rotate(-40deg);
		position: absolute;
		top: 0rpx;
		right: 0rpx;
		// background-color: grey; // 背景色
		color: #fff;
		// 以下属性会影响斜边标签的显示
		width: fit-content;
		height: 40rpx;
		padding: 0rpx 20rpx;
		line-height: 18px;

		text-align: center;
		border-top-right-radius: 16rpx;
		border-bottom-left-radius: 12rpx;
	}

	.valueSty {
		margin-bottom: 10px;
	}

	.symbolMoney {
		font-size: 16px;
	}

	.moneyVal {
		font-size: 24px;
	}

	.useCondition {
		font-size: 10px;
		padding: 4px 10px;
		border-radius: 12px;
	}

	.couponTopRight {
		flex: 3;
		display: flex;
		padding-bottom: 12px;
	}

	.ctr-left {
		flex: 1;
		display: flex;
		flex-direction: column;
	}

	.couponName {
		font-size: 16px;
		padding: 14px 0;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.couponStore {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
		padding-bottom: 8px;
	}

	.couponDate {
		font-size: 12px;
		text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.04);
	}

	.ctr-right {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 0 10px;
	}

	.useBtn {
		color: #fff;
		font-size: 12px;
		padding: 6px 20px;
		border-radius: 18px;
	}

	.couponBottom {
		display: flex;
		flex-direction: column;
		padding: 10px;
		border-bottom-left-radius: 4px;
		border-bottom-right-radius: 4px;
		border: 1px solid #e4e7ed;
	}

	.ruleLabel {
		display: flex;
		align-items: center;
	}

	.ruleLabel-left {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.overlay {
		margin-right: 10px;
		padding: 4px 10px;
		font-size: 10px;
		border-radius: 11px;
	}

	.limit {
		font-size: 12px;
	}

	.ruleBtn {
		display: flex;
		align-items: center;
		font-size: 12px;
	}

	.arrowIcon {
		position: relative;
		width: 6px;
		height: 6px;
		transform: rotate(135deg);

	}

	.rotate {
		transform: rotate(-45deg);
		bottom: -2px;
	}

	.backRotate {
		transform: rotate(135deg);
		top: -2px;
	}

	.ruleDetail {
		display: flex;
		flex-direction: column;
		height: auto;
	}

	.ruleList {
		padding-top: 10px;
		font-size: 12px;
	}

	/* 颜色配置 */
	.borderColor1 {
		border: 1px solid #5f98ff;
	}

	.borderColor2 {
		border: 1px solid #ff7979;
	}

	.borderColor3 {
		border: 1px solid #fc932c;
	}

	.borderColor4 {
		border: 1px solid #c3c3c3;
	}

	.arrowIcon1 {
		border-top: 1px solid #5f98ff;
		border-right: 1px solid #5f98ff;
	}

	.arrowIcon2 {
		border-top: 1px solid #ff7979;
		border-right: 1px solid #ff7979;
	}

	.arrowIcon3 {
		border-top: 1px solid #fc932c;
		border-right: 1px solid #fc932c;
	}

	.arrowIcon4 {
		border-top: 1px solid #c3c3c3;
		border-right: 1px solid #c3c3c3;
	}

	.useBtnBgColor1 {
		background-color: #2b6feb;
	}

	.useBtnBgColor2 {
		background-color: #ff5555;
	}

	.useBtnBgColor3 {
		background-color: #fa830e;
	}

	.useBtnBgColor4 {
		background-color: #bebebe;
	}

	.color1 {
		color: #2b6feb;
	}

	.color2 {
		color: #5f98ff;
	}

	.color3 {
		color: #ff5555;
	}

	.color4 {
		color: #ff7979;
	}

	.color5 {
		color: #fa830e;
	}

	.color6 {
		color: #fc932c;
	}

	.color7 {
		color: #bebebe;
	}

	.color8 {
		color: #c3c3c3;
	}

	.bgColor1 {
		background-color: #edf4ff;
	}

	.bgColor2 {
		background-color: #ffeeee;
	}

	.bgColor3 {
		background-color: #fff2e5;
	}

	.bgColor4 {
		background-color: #f3f3f3;
	}

	.bgColorTop1 {
		background: radial-gradient(circle at left bottom, transparent 6px, #edf4ff 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #edf4ff 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom1 {
		background: radial-gradient(circle at left top, transparent 6px, #dae6ff 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #dae6ff 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop2 {
		background: radial-gradient(circle at left bottom, transparent 6px, #ffeeee 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #ffeeee 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom2 {
		background: radial-gradient(circle at left top, transparent 6px, #ffd7d7 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffd7d7 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop3 {
		background: radial-gradient(circle at left bottom, transparent 6px, #fff2e5 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #fff2e5 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom3 {
		background: radial-gradient(circle at left top, transparent 6px, #ffe0c1 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ffe0c1 0) top right / 50% 100% no-repeat;
	}

	.bgColorTop4 {
		background: radial-gradient(circle at left bottom, transparent 6px, #f3f3f3 0) bottom left / 50% 100% no-repeat,
			radial-gradient(circle at right bottom, transparent 6px, #f3f3f3 0) bottom right / 50% 100% no-repeat;
	}

	.bgColorTBottom4 {
		background: radial-gradient(circle at left top, transparent 6px, #ededed 0) top left / 50% 100% no-repeat,
			radial-gradient(circle at right top, transparent 6px, #ededed 0) top right / 50% 100% no-repeat;
	}

	.btn_register {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 160rpx;
		padding: 0rpx 20rpx;
		height: 80rpx;
		border-radius: 20rpx;
		margin-right: 60rpx;
	}
</style>