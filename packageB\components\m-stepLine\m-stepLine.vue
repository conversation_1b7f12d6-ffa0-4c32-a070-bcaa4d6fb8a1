<template>
	<view>
		<view class="setp_box">
			<view class="setp_box_item" v-for="(item,index) in list" >
				<view class="setp_box_item_title" >
					<view :class="index<=current?'dotAc':'dot'" :style="index<=current?'background:'+themeColor.main_color:'background:#e7e7e7'">
						
					</view>
					<p style="font-size: 22rpx;margin-top: 8rpx;">
						{{item}}
					</p>
				</view>
				<view class="setp_box_item_line" v-if="index<list.length-1" :style="index<=current?'background:'+themeColor.main_color:'background:#e7e7e7'">
					
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name:"m-stepLine",
		data() {
			return {
				acIndex:0
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting']),
		
		},
		props:{
			current:{
				type:Number
			},
			activeColor:{
				type:String
			},
			list:{
				type:Array
			},
		},
		watch:{
			current:{
				handler(newVal,oldVal){
					console.log(newVal,'newVal');
					this.acIndex = newVal
				},
				immediate:true
			}
		},
		methods: {
			
		}
	}
</script>

<style scoped lang="scss">
	.setp_box {
		display: flex;
		justify-content: center;
		// flex:1;
		padding: 30rpx 0;
		.setp_box_item {
			// flex: 1;
			display: flex;
			align-items: center;
			// justify-content: center;
			height: 80rpx;
			margin: 0 10rpx;
			.setp_box_item_title{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: space-around;
				height: 80rpx;
				.dotAc{
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
				}
				
				.dot{
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
				}
			}
			
			.setp_box_item_line{
				width: 50rpx;
				height: 3rpx;
				margin-bottom: 40rpx;
				// border-top: 4rpx solid #ee2b09;
			}
		}
	}
</style>
