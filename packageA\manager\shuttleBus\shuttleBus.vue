<template>
	<view>
		<view class="" style="display: flex;align-items: center;padding: 30rpx;" >
			<view :style="current==index?'color:#55aa7f':''" style="margin-right: 30rpx;display: flex;align-items: center;" @click="chooseType(item)" v-for="(item,index) in tabList">
			{{item.name}}
			<view class="" style="width: 40rpx;height: 40rpx;border-radius: 50%;display: flex;align-items: center;justify-content: center;color: #FFFFFF;background: #ff0000;">
				{{index==0?tips.tip_0:(index==1?tips.tip_1:(index==2?tips.tip_2:tips.tip_3))}}
			</view>
			</view>
		</view>
		<view class="" style="padding: 30rpx;width: 690rpx;margin: 20rpx auto;background-color: #FFFFFF;border-radius: 30rpx;min-height: 250rpx;" v-for="item in billList">
		
			<p><text style="color: #666666;">起点站:</text>{{item.start_station_name}}</p>
			<p><text style="color: #666666;">终点站:</text>{{item.end_station_name}}</p>
			<p><text style="color: #666666;">呼叫时间:</text>{{item.create_time | moment1}}</p>
			<view class="" style="margin-top: 20rpx;">
				<button type="primary" @click="sureCall(item)" size="mini" v-if="current==0">接单</button>
				<button type="warn" @click="cancleCall(item)" size="mini" style="margin-left: 20rpx;" v-if="current==0">取消</button>
				<button type="warn" @click="completeCall(item)" size="mini" style="margin-left: 20rpx;" v-if="current==1">完成</button>
			</view>
		</view>
		<view class="" style="height: 80rpx;">
			
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				tabList:[{id:1,name:'待接单'},{id:2,name:'已接单'},{id:3,name:'已完成'},{id:4,name:'已取消'}],
				current:0,
				billList:[],
				tips:null,
				params: {
					page: 1,
					limit: 100,
					status:0
				},
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['city', 'hotel']),
		},
		onLoad() {
		// getShuttleBusBillList	
		this.params.page = 1
		this.getBillList()
		},
		methods: {
			chooseType(e){
				this.current = e.id-1
				this.params.status = e.id-1
				this.getBillList()
			},
			getBillList(){
				this.$iBox.http('getShuttleBusBillList', this.params)({
					method: 'post'
				}).then(res => {
					this.billList = res.data.list
					this.tips = res.data.tips
				})
			},
			sureCall(e){
				
				this.$iBox.http('updateShuttleBusBill', {id:e.id,status:1})({
					method: 'post'
				}).then(res => {
					this.getBillList()
				})
			},
			cancleCall(e){
				
				this.$iBox.http('updateShuttleBusBill', {id:e.id,status:3})({
					method: 'post'
				}).then(res => {
					this.getBillList()
				})
			},
			completeCall(e){
				
				this.$iBox.http('updateShuttleBusBill', {id:e.id,status:2})({
					method: 'post'
				}).then(res => {
					this.getBillList()
				})
			},
		}
	}
</script>

<style>

</style>
