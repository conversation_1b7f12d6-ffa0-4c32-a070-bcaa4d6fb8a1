# 实时接口配置说明

## 概述

为了确保关键数据的实时性和准确性，我们将某些接口配置为不使用缓存，每次都获取最新数据。

## 实时接口列表

### 1. getShopList (酒店列表)
**原因**: 
- 酒店库存实时变化
- 房间价格动态调整
- 可用性状态需要准确反映

**影响**:
- 用户看到的酒店信息始终是最新的
- 避免因缓存导致的预订失败
- 确保价格信息准确性

### 2. getUI (界面配置)
**原因**:
- 活动信息需要实时更新
- 界面布局可能动态调整
- 主题配置实时生效

**影响**:
- 用户界面始终显示最新配置
- 活动信息及时展示
- 界面调整立即生效

## 配置文件

**位置**: `config/cacheConfig.js`

```javascript
// 实时接口配置
export const REAL_TIME_APIS = [
    'getShopList',    // 酒店列表
    'getUI'           // UI配置
]

// 检查是否为实时接口
export function isRealTimeApi(apiName) {
    return REAL_TIME_APIS.includes(apiName)
}
```

## 技术实现

### 1. 请求层面
```javascript
// 在 RequestOptimizer 中
shouldSkipCache(url, config) {
    // 检查是否为实时接口
    if (isRealTimeApi(url)) {
        console.log(`实时接口跳过缓存: ${url}`)
        return true
    }
    return false
}
```

### 2. 应用层面
```javascript
// App.vue 中的配置
const parallelTasks = [
    { 
        url: 'getUI', 
        params: {}, 
        config: { skipLoading: true, skipCache: true } // 确保实时数据
    },
    { 
        url: 'getShopList', 
        params: { latitude, longitude, page: 1, limit: 10 },
        config: { skipLoading: true, skipCache: true } // 确保实时数据
    }
]
```

## 性能考虑

### 1. 网络请求增加
- **影响**: 实时接口每次都发起网络请求
- **优化**: 使用请求去重避免重复请求
- **监控**: 关注网络请求频率和响应时间

### 2. 用户体验
- **优势**: 数据准确性提升
- **挑战**: 可能增加加载时间
- **平衡**: 使用骨架屏和动画优化等待体验

### 3. 服务器压力
- **考虑**: 增加服务器请求量
- **建议**: 服务器端优化响应速度
- **监控**: 关注服务器性能指标

## 缓存策略对比

| 接口类型 | 缓存策略 | 数据特点 | 更新频率 |
|---------|---------|---------|---------|
| 实时接口 | 不缓存 | 动态变化 | 高频更新 |
| 用户信息 | 24小时 | 相对稳定 | 低频更新 |
| 系统设置 | 1小时 | 较稳定 | 中频更新 |
| 城市列表 | 7天 | 基本不变 | 极低频更新 |

## 配置管理

### 1. 添加新的实时接口
```javascript
// 在 config/cacheConfig.js 中添加
export const REAL_TIME_APIS = [
    'getShopList',
    'getUI',
    'newRealTimeApi'  // 新增实时接口
]
```

### 2. 调整缓存策略
```javascript
// 修改缓存时间
export const CACHE_STRATEGIES = {
    'existingApi': 60 * 60 * 1000,  // 调整为1小时
    // ...
}
```

### 3. 临时跳过缓存
```javascript
// 在请求时临时跳过缓存
this.$iBox.http('apiName', params)({
    method: 'post',
    skipCache: true  // 临时跳过缓存
})
```

## 监控和调试

### 1. 控制台日志
```javascript
// 实时接口会输出日志
console.log(`实时接口跳过缓存: ${url}`)
console.log(`跳过缓存: ${url}`)
```

### 2. 性能监控
```javascript
// 使用 PerformanceMonitor 监控
performanceMonitor.recordNetworkRequest(url, duration, fromCache)
```

### 3. 缓存命中率
```javascript
// 查看缓存命中率
const hitRate = performanceMonitor.getCacheHitRate()
console.log(`缓存命中率: ${hitRate}%`)
```

## 最佳实践

### 1. 接口分类原则
- **实时性要求高** → 不缓存
- **数据变化频繁** → 短缓存
- **数据相对稳定** → 长缓存
- **基本不变数据** → 超长缓存

### 2. 用户体验优化
- 实时接口使用骨架屏
- 提供加载状态反馈
- 合理的超时处理

### 3. 错误处理
- 实时接口失败时的降级策略
- 网络异常时的用户提示
- 重试机制的合理配置

## 业务影响

### 1. 酒店预订准确性
- 实时库存信息避免超售
- 准确的价格信息提升用户信任
- 及时的可用性状态减少预订失败

### 2. 用户界面体验
- 最新的活动信息及时展示
- 界面配置变更立即生效
- 个性化内容实时更新

### 3. 运营效率
- 活动上线立即生效
- 价格调整实时反映
- 库存管理更加精确

## 注意事项

1. **网络环境**: 在网络较差的环境下，实时接口可能影响用户体验
2. **服务器性能**: 需要确保服务器能够处理增加的请求量
3. **数据一致性**: 实时数据和缓存数据之间的一致性管理
4. **成本考虑**: 增加的网络请求可能带来额外的服务器成本

## 总结

通过将 `getShopList` 和 `getUI` 配置为实时接口，我们确保了：

- ✅ **数据准确性** - 关键业务数据始终最新
- ✅ **用户体验** - 避免因过期数据导致的问题
- ✅ **业务效率** - 运营变更立即生效
- ✅ **可配置性** - 通过配置文件灵活管理

这种配置在保证数据准确性的同时，通过其他优化手段（骨架屏、动画等）确保了良好的用户体验。
