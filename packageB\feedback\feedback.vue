<template>
	<view class="rateBox">
		<!-- 评分 -->
		<view class="" @click="goList"
			style="width: 100%;display: flex;align-items: center;justify-content: flex-start;font-size: 44rpx;color: #000000;padding: 30rpx;">
			问题和建议
		</view>
		<view class=""
			style="min-height: 280rpx;width: 96vw;border-radius: 20rpx;position: relative;display: flex;flex-direction: column;">
			<view class=""
				style="min-height: 140rpx;width: 100%;border-radius: 20rpx;display: flex;align-items: center;justify-content: center;">
				<textarea v-model="remark" maxlength="300" placeholder="5个字以上,将直接发送给酒店总经理"
					disable-default-padding="true"
					style="height:220rpx;background: #fff;width: 96%;margin-left: 10rpx;margin-right: 10rpx;border: 1px solid #EEEEEE;padding: 20rpx;border-radius: 8rpx;" />
			</view>
			<view class="" style="position: absolute;bottom: 20rpx;right: 20rpx;">
				<text style="color:#909399">{{ remark.length }}/300</text>
			</view>
		</view>
		<view class="" style="width: 100vw;padding:10rpx 40rpx;margin-top: 30rpx;">
			<p style="font-size: 44rpx;">图片(选填)</p>
			<p style="font-size: 26rpx;color: #909399;margin-top: 12rpx;">提供问题照片,最多9张</p>
		</view>

		<view class="" style="min-height: 220rpx;width: 100vw;display: flex;flex-wrap: wrap;padding: 0 40rpx;">

			<view class=""
				style="position: relative;height: 220rpx;width: 220rpx;display: flex;align-items: center;justify-content: center;border-radius: 6rpx;"
				v-if="filesArr.length>0" v-for="(item, index) in filesArr">
				<image :src="item" mode="aspectFill" style="height: 1600rpx;width: 160rpx;border-radius: 6rpx;"></image>
				<view class="icon-close" style="color: red;position: absolute;right: -5rpx;top: -5rpx;font-size: 36rpx;"
					@click="del(index)">

				</view>
			</view>
			<view class=""
				style="height: 160rpx;width: 160rpx;display: flex;align-items: center;justify-content: center;border-radius: 6rpx;background-color: #fff;margin-top: 30rpx;"
				@click="upload">
				<uni-icons type="camera-filled" size="30"></uni-icons>
			</view>
		</view>
		<view class="" style="height: 300rpx;">

		</view>
		<view class="btn_pay">
			<view class="btn" :style="'background:'+themeColor.main_color+';color:'+themeColor.bg_color"
				@click="submit">
				<text>确认</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';

	export default {
		data() {
			return {
				remark: '',
				if_dis: true,
				if_load: false,
				filesArr: [],
				img_length: 0,
				action: '',
				room_id:''
			};
		},
		components: {},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel', 'hotelList'])
		},
		watch: {
			text_num() {
				this.if_dis = false;
			},
			filesArr() {
				this.img_length = this.filesArr.length;
			}
		},
		async onLoad(e) {
			await this.$onLaunched;
			console.log(this.hotel);
			uni.getStorage({
				key: 'baseUrl',
				success: (res) => {
					console.log(res.data);
					this.action = res.data + '/wx/Resource/uploadFile'
				}
			});
			let scene = wx.getEnterOptionsSync()
			console.log(scene, 'scene');
			if (scene.query.scene) {
				// 扫码场景
				let query = decodeURIComponent(scene.query.scene)
				//解析参数
				if (query.includes("shop_id=")) {
					this.hotel_id = this.$iBox.linkFormat(query, "shop_id")
					if(query.includes("room_id=")){
						this.room_id = this.$iBox.linkFormat(query, "room_id")
					}
					
				}

			} else {
				this.hotel_id = this.hotel.id
			}



		},
		methods: {
			onUploaded(lists) {
				this.filesArr = lists;
			},
			upload(res) {
				let i = 0
				let files = this.filesArr
				uni.chooseMedia({
					count: 9,
					mediaType: ['image', 'video'],
					sourceType: ['album', 'camera'],
					maxDuration: 30,
					camera: 'back',
					success: (res) => {
						console.log(res.tempFiles)

						res.tempFiles.forEach(item => {
							uni.uploadFile({
								url: this.action, //仅为示例，非真实的接口地址
								filePath: item.tempFilePath,
								header: {
									'AUTHTOKEN': this.userInfo.user_token
								},
								formData: {
									'shop_id': this.hotel.id
								},
								name: 'file',
								success: (uploadFileRes) => {
									i++;
									files.push(JSON.parse(uploadFileRes.data).data)
								}
							});
						})

						this.img_length = i
						this.filesArr = files
						console.log(this.filesArr, 'file');
					}
				});

			},
			del(e) {
				this.filesArr.splice(e, 1);
			},
			submit() {
				if (!this.remark) {
					uni.showToast({
						icon: 'error',
						title: '请填写吐槽内容！'
					})
					return
				}

				let params = {
					shop_id: this.hotel_id,
					room_id:this.room_id,
					content: this.remark,
					images: this.filesArr
				}
				this.$iBox
					.http('addHotelFeedback', params)({
						method: 'post'
					})
					.then(res => {
						uni.showModal({
							title: '提示',
							content: '提交成功!感谢您的反馈!',
							showCancel: false,
							success() {
								uni.reLaunch({
									url: '/pages/index/index'
								})
							}
						})
					});
			}
		}
	};
</script>

<style lang="scss">
	page {
		background-color: #fff;
	}

	.rateBox {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.card {
		/* height:200rpx; */
		min-height: 260rpx;
		width: 90vw;
		background: #ffffff;
		border-radius: 20rpx;
		// border-bottom: 1px solid #eee;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px;
		margin-top: 40rpx;
		margin-bottom: 32rpx;
		padding-left: 10rpx;
		// border: 1px solid #e4e7ed;
		display: flex;
		align-items: ceter;
		justify-content: center;
		padding: 20rpx;
	}

	.l1 {
		width: 40%;
		height: 80%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.l1 image {
		width: 90%;
		height: 200rpx;
		border-radius: 20rpx;
	}

	.l2 {
		width: 60%;
		height: 100%;
		padding-left: 10rpx;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: flex-start;
	}

	.tag {
		width: 80rpx;
		height: 30rpx;
		background: #dd514c;
		font-size: 16rpx;
		color: #ffffff;
		border-radius: 4rpx;
	}

	.l3 {
		width: 24%;
		height: 100%;
	}

	.btn_pay {
		width: 100%;
		height: 100rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		position: fixed;
		bottom: 30rpx;

		.btn {
			width: 80%;
			height: 100%;
			border-radius: 20rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>