<template>
	<view class="m-tags">
		<view class="m-tags_box" v-if="type=='primary'" :style="mode=='tag'?'background:'+ color+';color:'+themeColor.text_main_color:'color:#ff7f17;border:1px solid #ff7f17;background:#ffffff'">
			<text>{{name}}</text>
		</view>
		
		<view class="m-tags_box" v-if="type=='hot'" :style="mode=='tag'?'background-image: linear-gradient(-90deg,#ff554f,#ff1a13);':'color:#ff1a13;border:1px solid #ff554f;background:#ffffff'">
			<text>{{name}}</text>
		</view>
		
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-tags",
		props: {
			name: {
				type: [String, Number],
				default: ''
			},
			type: {
				type: String,
				default: 'primary'
			},
			mode: {
				type:String,
				default:'tag'
			},
			color:{
				type:String
			}
		},
		computed: {
					...mapState('login', ['userInfo']),
					...mapState('ui', ['tabbar', 'themeColor'])
				},
		data() {
			return {

			};
		}
	}
</script>

<style lang="scss" scoped>
	.m-tags {
		.m-tags_box {
			margin: 10rpx;
			padding: 4rpx 8rpx;
			font-size: 22rpx;
			color: #FFFFFF;
			width:fit-content;
			border-radius: 4rpx;
		}

	}
</style>
