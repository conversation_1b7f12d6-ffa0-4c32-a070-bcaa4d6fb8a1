<template>
	<view>
		<p style="height: 60rpx;margin: 40rpx auto;width: 100%;display: flex;justify-content: center;font-size: 44rpx;">
			{{lockDetail.lock_alias?lockDetail.lock_alias:''}}
		</p>

		<view class="open" style="position: relative;">
			<!-- 蓝牙 -->
			<view class="blue">
				<view :class="Cstyle!='click'?'blueTeeth':'blueTeeth_active'" @click="open('click')"
					hover-class="bind_lock">
					<text class="icon-mimasuo" style="font-size: 80rpx;"></text>
					<text>蓝牙开锁</text>
				</view>
			</view>
		</view>

		<!--  -->
		<view class="boxContent" style="width: 100%;padding: 30rpx;">
			<view class="itemBox" @click="lockInfo" v-if="roleType('lock_edit')">
				<view class="itemContent">
					<view class="icon-bianji" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">编辑门锁房间</text>
						<text style="font-size: 24rpx;color: #ccc;">修改门锁的绑定房间</text>
					</view>
				</view>
			</view>

			<view class="itemBox" v-if="roleType('lock_edit')" @click="openRecord">
				<view class="itemContent">
					<view class="icon-dingdanhao" style="font-size: 64rpx;"></view>
					<view class="itemContentText">
						<text style="font-size: 36rpx;font-weight: 600;">开锁记录</text>
						<text style="font-size: 24rpx;color: #ccc;">不需要连蓝牙</text>
					</view>
				</view>
			</view>
		</view>



		<view hover-class="bind_lock" :style="{background:themeColor.main_color}" v-if="roleType('lock_del_add')"
			@click="delLock(lockDetail)"
			style="position: fixed;bottom: 0;width: 100%;height: 100rpx;border-top: 1px solid #ccc;display: flex;align-items: center;justify-content: center;">
			<text :style="{color: themeColor.bg_color }">重置门锁</text>
		</view>

	</view>
</template>

<script>
	const plugin = requirePlugin("yayaLock");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {

			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		async onShow() {
			await this.$onLaunched;
			this.sysLockTime()
		},
		methods: {
			...mapActions('room', ['getType']),
			roleType(e) {
				let role = this.roles_list.filter(item => {
					return item.permission == e
				})

				if (role.length > 0) {
					return true
				} else {
					return false
				}
			},
			lockInfo(){
				uni.navigateTo({
					url: '/packageA/manager/lockList/YYLock/editRoom'
				})
			},
			toRecordLock() {
				uni.navigateTo({
					url: '/packageA/manager/lockList/YYLock/openRecord'
				})
			},
			// 校准锁时间
			sysLockTime(e) {
				var that = this;
				var deviceId = this.lockDetail.device_id;
				var devicePsw = '23458892D88C71D5AD78F3C6CF933725';
				var tStr = plugin.buildSysLockTime(deviceId, devicePsw);
				plugin.operateLockNoLoading(deviceId, "", 11, tStr, function(res) {

					if (res.data.result == 200) {
						var openResult = plugin.parseReceiveData(res.data.msg);
					}
				});
			},

			// 点击开锁
			openYYLock(e) {

				let that = this
				uni.showLoading({
					title: '开锁中...'
				})
				let a = this.lockDetail.device_id.split(":")
				let deviceId = a[2] + a[3] + a[4] + a[5];
				// let devicePsw = this.$u.random(10000000, 99999999);
				let devicePsw = '23458892D88C71D5AD78F3C6CF933725'

				let tStr = plugin.buildOpenDeviceO2O(deviceId, devicePsw);

				plugin.operateLockNoLoading(deviceId, "", 11, tStr, function(res) {
					var openResult = plugin.parseReceiveData(res.data.msg);
					if (res.data.result == 200) {
						// 上传日志
						let param = {
							lock_id: that.lockDetail.id,
							electric_quantity: openResult.battery ? openResult.battery : 0
						}
						that.$iBox
							.http('addYayaOpenDoorRecord', param)({
								method: 'post'
							}).then(res => {
								uni.showToast({
									icon: 'none',
									title: '开锁成功！'
								})
								uni.hideLoading()
							})
					}
				})

			},
			open(e) {
				console.log(e)
				this.Cstyle = e
				let a = setTimeout(() => {
					this.Cstyle = ''
					clearTimeout(a)
				}, 500)
				// 开锁前校准锁时间

				this.openYYLock()
			},

			openRecord() {
				uni.navigateTo({
					url: './openRecord'
				})
			},

			delLock(e) {
				let that = this
				uni.showModal({
					title: '是否删除门锁',
					content: '请谨慎操作，删除不可恢复！',
					success: res => {

						if (res.confirm) {

							uni.showLoading({
								icon: 'none',
								title: '正在重置'
							})
							// 同步到服务器
							this.$iBox.http('delYayaLock', {
								lock_id: this.lockDetail.id
							})({
								method: 'post'
							}).then(res => {
								this.recoverDevice()
								uni.showToast({
									title: '智能锁已删除',
									complete() {
										// 初始化
										setTimeout(() => {
											uni.hideLoading()
											wx.navigateBack()
										}, 1000);
									}
								})
							}).catch(function(error) {
								console.log('网络错误', error)
								uni.showToast({
									icon: 'none',
									title: `重置失败:${error}`
								})
							})
						} else if (res.cancel) {
							console.log('用户点击取消')
						}

					},
					fail: () => {
						console.log('seees')
					},
					complete: () => {}
				});

			},
			recoverDevice: function(e) {
				var that = this;
				let a = this.lockDetail.device_id.split(":")
				let deviceId = a[2] + a[3] + a[4] + a[5];
				// let devicePsw = this.$u.random(10000000, 99999999);
				let devicePsw = '23458892D88C71D5AD78F3C6CF933725'
				var tStr = plugin.buildRecoverDevice(deviceId, devicePsw);
				plugin.operateLockNoLoading(deviceId, "", 11, tStr, function(res) {
					if (res.data.result == 200) {

					}
				});
			},
		}
	}
</script>

<style lang="scss" scoped>
	.blue {
		position: absolute;
		top: 0;
		bottom: 0;
		left: 0;
		right: 0;
		margin: 0 auto;
	}

	.blueTeeth {
		height: 200rpx;
		width: 200rpx;
		margin: 20rpx auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #55555533;
		border-radius: 50%;
		color: #00557f;
		position: relative;

		.img_b {
			width: 80rpx;
			height: 80rpx;

		}


	}

	.blueTeeth_active {
		height: 240rpx;
		width: 240rpx;
		margin: 100rpx auto;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #55555533;
		border-radius: 50%;
		color: #00557f;
		position: relative;
		animation: mymove 0.5s infinite;
		/*轮流反向播放动画。*/
		animation-iteration-count: 1;


		/*动画的速度曲线*/
		.img_b {
			width: 200rpx;
			height: 200rpx;

		}
	}


	@keyframes mymove {
		0% {
			transform: scale(1);
			/*开始为原始大小*/
		}

		50% {
			transform: scale(1.1);
		}

		100% {
			transform: scale(1);
		}

	}

	.bind_lock {
		opacity: 0.9;
		background: #f7f7f7;
	}

	.boxContent {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		margin-top: 300rpx;

		.itemBox {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 50%;
			height: 200rpx;

			margin-top: 20rpx;

			.itemContent {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 14rpx;
				width: 92%;
				height: 90%;
				border-radius: 20rpx;
				background-color: #fff;

				.itemContentText {
					display: flex;
					flex-direction: column;
					justify-content: end;
				}
			}
		}
	}
</style>