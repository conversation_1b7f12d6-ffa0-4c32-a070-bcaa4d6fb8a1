let Fly = require("flyio/dist/npm/wx")
import store from '@/store'
import { getExtStoreId } from '@/plugins/ext'
const fly = new Fly()
const request1 = new Fly() //创建一个新的实例，在拦截器中进行异步操作
import Vue from 'vue'

// #ifdef MP-WEIXIN
const accountInfo = wx.getAccountInfoSync();
const appId = accountInfo.miniProgram.appId
// #endif
//寒武系测试域名
  // let baseURL = 'WXXCX_BASE_URL'  //发布线上 wxac3e6ec8242e7a7d
// let baseURL = 'https://try-hotel.hanwuxi.cn' //线上环境 wxac3e6ec8242e7a7d
// let baseURL ='https://pms.kemanfang.net'  //wxe63abb47ace30e16，wxc8e6c249ec80fe2e,影宿 wxe89e918a5d176e84 43def0d9e2e1ee044064983d90e7f77c
 // let baseURL = 'https://hotel-v3.hanwuxi.cn' // 测试 wx181f2564193c0b86
 // let baseURL = 'https://hotel-v3.hanwuxi.cn/sys/dev/data/public/index.php'
 // let baseURL = 'https://pms.hanwuxi.cn'  //sass
// let baseURL = 'https://hotel.nanv.cn/' //音符 wxf091425fe7d86bd9
// let baseURL = 'https://www.yunqihotel.com'  //wx2419383f3327c2eb,wxf53aa422a3c72c5b wx7c9ef62ca21eb51b
// let baseURL = 'https://pms.q14.cn' //wxbf82f08c79335c8f
// let baseURL = 'https://hotel.zhida.ren' // wxc20af6a51a6caddf wxee32201eb69dc3cc 

 let baseURL = 'https://zzrz.sghjd.top' // wx2904773e7efd9946 ,wx560c720a93502dff wxe7c39452f10bb08a, 
 // wx333be9a9f14c6c53, wx38f6d85d44a65f56, wxa01a2e4fb103c9ea wxa7665f8c90ca8fb9，wxe7c39452f10bb08a, wxd6138cece92ae3a2,wx01ac057fdeb971ca ,wx91187a8733151c90
 // let baseURL = "https://ys.wlxzg.com"  //李军  wx70ca8ab50d465e52
 // let baseURL = 'https://pms.jys0755.com'  // wx7ceabeff4b03b1b9
// let baseURL = 'https://pms.quyouyi.com' //线上环境 wx1a6448e8b58e0d26
  // let baseURL = 'https://jiudian.chengcaivip.com' // 

let version = ''
fly.interceptors.request.use(function(request) {
	if (store.state.login.userInfo.user_token) {
		request.headers["authtoken"] = store.state.login.userInfo.user_token
	} else {
		request.headers["authtoken"] = " ";
	}
// 	// ------------------线上环境，发布时解开注释,寒武系专用----------------------
	if (!version) {
		//锁定当天实例，后续请求会在拦截器外排队，详情见后面文档
		fly.lock();
		console.log();
		return request1.get(baseURL + "/sys/hotel_pms_extend/public/index.php/notify/SysSetting/getSystemVersion", {
	
		}).then((d) => {
			version = d.data.data
			request.baseURL = baseURL + '/sys/' + version + '/data/public/index.php'
			baseURL = baseURL + '/sys/' + version + '/data/public/index.php'
			console.log(request.baseURL, 'base', baseURL, version)
			return request;
		}).finally(() => {
			fly.unlock(); //解锁后，会继续发起请求队列中的任务，详情见后面文档
		})
	} else {
		request.baseURL = baseURL
	}
	
	// =====================================================源码用户使用======================
	// request.baseURL= baseURL

	uni.setStorage({
		key: 'baseUrl',
		data: request.baseURL,
	});
})

fly.interceptors.response.use(
	(response, promise) => {
		
		if (response.data.code == 886) {
			// const appId = accountInfo.miniProgram.appId;
			// wx.login({
			// 	success: res => {
			// 		if (res.code) {
			// 			request1.get(baseURL + '/User/login', {
			// 				code: res.code,
			// 				xcx_appid: appId,
			// 				shop_id:''
			// 			}).then((d) => {
			// 				console.log(d);
			// 				uni.reLaunch({
			// 					url:'/pages/index/index'
			// 				})
			// 			}).finally(() => {
							
			// 			})
			// 		}
			// 	}
			// })
			//如果请求过期则更换token，重新请求一遍登录
			uni.showModal({
				title: '提示',
				content: '登录过期！请点击右上角选择重新进入小程序！',
				confirmText: "确定", // 确认按钮文字  
				showCancel: false, // 是否显示取消按钮，默认为 true
				confirmColor: '#f55850',
				cancelColor: '#39B54A',
				success: (res) => {
					uni.reLaunch({
						url:'/pages/index/index'
					})
				}
			})

		}

		return promise.resolve(response.data)
	},
	(err, promise) => {
		return promise.reject(err)
	}
)
export default fly
