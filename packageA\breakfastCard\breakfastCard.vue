<template>
	<view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="breakList.length==0">
			<view class="icon-quesheng<PERSON>_z<PERSON><PERSON>" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无二维码</p>
		</view>
		<view class="breakBox" v-for="(item, index) in breakList" :key="index" v-else @click="showQr(item)">
			<view class="titleBox">
				<p class="title">{{item.name}}</p>
				<p style="color: crimson;" class="title">￥{{item.amount}}</p>
			</view>
			
			<p class="content">{{item.content}}</p>
			<p class="content">{{item.room_type_name}}</p>
			<p class="content" style="color:darkgray">使用日期:{{item.date}}</p>
			<view class="qrBox" style="position: absolute;bottom: 10rpx;right: 10rpx;width: 120rpx;height: 140rpx;">
				<text class="icon-erweima" style="font-size: 100rpx;"></text>
				<text style="font-size: 22rpx;">点击展示</text>
			</view>
		</view>
		
		<!-- 酒店详情说明 -->
		<m-popup :show="pop" @closePop="closePop" mode="center">
			<view class="" style="height: 500rpx;width: 600rpx;display: flex;align-items: center;justify-content: center;flex-direction: column;">
				<image :src="qr" style="height: 400rpx;width: 400rpx;" mode="" show-menu-by-longpress></image>
				<p>提示:长按二维码可以分享早餐券给朋友</p>
			</view>
		</m-popup>
		
		<!-- 提醒自动核销 -->
		<m-popup :show="popTips" @closePop="closePopTips" mode="center">
			<view class="manBox">
				<p>温馨提示:已检测到您正在餐厅，请选择用餐人数</p>
				<view class="itemBox">
					<view class="item_choose" style="background:#b9b9b966;color:black" @click="chooseMan(1)">
						<text style="font-size: 30rpx;font-weight: 600;">一人用餐</text>
						<text class="icon-renyuan" style="font-size: 100rpx;margin-top: 30rpx;"
						 :style="{color:themeColor.main_color}"></text>
						<text style="color: #55aa00;position: absolute;bottom: 10rpx;">选择</text>
					</view>
					<view class="item_choose" style="background:#b9b9b966;color:black" @click="chooseMan(2)">
						<text style="font-size: 30rpx;font-weight: 600;">两人用餐</text>
						<text class="icon-duoren" style="font-size: 100rpx;margin-top: 30rpx;"
						 :style="{color:themeColor.main_color}"></text>
						<text style="color: #55aa00;position: absolute;bottom: 10rpx;">选择</text>
					</view>
				</view>
			</view>
		</m-popup>
		
	</view>
</template>

<script>
	const bgAudioManager = uni.getBackgroundAudioManager();
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				params:{
					shop_id:'',
					bill_id:'',
					page:1,
					limit:10
				},
				breakList:[],
				bool:true,
				bill_id:'',
				pop:false,
				popTips:false,
				qr:'',
				blueList:[]
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel']),
			...mapState('room', ['billDetail'])
		},
		async onLoad(options) {
			await this.$onLaunched;
			console.log('billid',this.billDetail,options);
			if(options.id){
				this.bill_id = options.id
			}else{
				this.bill_id = this.billDetail.id
			}
			
			this.params.page = 1
			this.params.shop_id = this.hotel.id
			this.params.bill_id = this.bill_id
			this.getBreakList()
			
			this.$iBox.http('getBlueTooth', {
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {
				let blueList = res.data.filter(item => {
					return item.bluetooth_type == 2
				})
				
				this.blueList = blueList
				if (this.blueList.length > 0) {
					this.peiDui()
					uni.showLoading({
						title:'正在搜索蓝牙围栏'
					})
				} 
			})
		},
		methods:{
			getBreakList(){
				this.$iBox.http('getBreakfastCouponList', this.params)({
					method: 'post'
				}).then(res => {
					this.breakList = res.data.list
				})
			},
			showQr(e){
				this.pop = true
				this.qr = e.qr
			},
			closePop(){
				this.pop = false
			},
			closePopTips(){
				this.popTips = false
			},
			chooseMan(e){
				uni.showLoading({
					title:'正在核销...'
				})
				if(e==1){
					
					if(this.breakList.length>=1){
						this.$iBox
							.http('wxverificationBreakfastCoupon', {ids:[this.breakList[0].id]})({
								method: 'post'
							})
							.then(res => {
								this.popTips = false
								uni.showModal({
									title:'提示',
									content:'核销成功',
									showCancel:false,
									success() {
										
									}
								})
								uni.hideLoading()
							})
							.catch(function(error) {
								uni.hideLoading()
								console.log('33434', error);
							});
					}else {
						uni.showToast({
							icon:'none',
							title:'暂无早餐券'
						})
					}
				}else if(e==2){
					
					if(this.breakList.length>=2){
						this.$iBox
							.http('wxverificationBreakfastCoupon', {ids:[this.breakList[0].id,this.breakList[1].id]})({
								method: 'post'
							})
							.then(res => {
								this.popTips = false
								uni.showModal({
									title:'提示',
									content:'核销成功',
									showCancel:false,
									success() {
										
									}
								})
							})
							.catch(function(error) {
								uni.hideLoading()
								console.log('33434', error);
								uni.showModal({
									title:'提示',
									content:error,
									showCancel:false,
									success() {
										
									}
								})
							});
					}else {
						uni.showToast({
							icon:'none',
							title:'早餐券数量不足2张'
						})
					}
				}
			},
			// 搜索蓝牙
			////////////////////////=============================蓝牙U Key盾检测==========================
			peiDui() {
				//在页面加载时候初始化蓝牙适配器
			
				uni.openBluetoothAdapter({
					success: e => {
						console.log('初始化蓝牙成功:' + e.errMsg);
						// 初始化完毕开始搜索
						this.startBluetoothDeviceDiscovery()
					},
					fail: e => {
						console.log('初始化蓝牙失败，错误码：' + (e.errCode || e.errMsg));
						bgAudioManager.title = '提醒'
						bgAudioManager.epname = '提醒'
						bgAudioManager.singer = '提醒'
						bgAudioManager.src =
							'http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_mp3/%E8%AF%B7%E6%89%8B%E5%8A%A8%E6%89%93%E5%BC%80%E6%89%8B%E6%9C%BA%E8%93%9D%E7%89%99.mp3'
						uni.showModal({
							title:'提示',
							content:'暂未查找到蓝牙围栏设备,请返回上一页重新搜索!',
							showCancel:false,
							confirmText:'返回房卡页',
							success:(res)=> {
								uni.navigateBack({})
							}
						})
					}
				});
			},
			startBluetoothDeviceDiscovery() {
				//在页面显示的时候判断是都已经初始化完成蓝牙适配器若成功，则开始查找设备
				let self = this;
				console.log("开始搜寻智能设备");
				// setTimeout(res => {
				uni.startBluetoothDevicesDiscovery({
					success: res => {
						self.onBluetoothDeviceFound();
					},
					fail: res => {
						console.log("查找设备失败!");
						uni.showToast({
							icon: "none",
							title: "查找设备失败！",
							duration: 3000
						})
					}
				});
				// }, 300)
			},
			/**
			 * 停止搜索蓝牙设备
			 */
			stopBluetoothDevicesDiscovery() {
				uni.stopBluetoothDevicesDiscovery({
					success: e => {
						console.log('停止搜索蓝牙设备:' + e.errMsg);
					},
					fail: e => {
						console.log('停止搜索蓝牙设备失败，错误码：' + e.errCode);
					}
				});
			},
			/**
			 * 发现外围设备
			 */
			onBluetoothDeviceFound() {
				let self = this
				uni.onBluetoothDeviceFound(devices => {
					console.log(devices, 'devices',self.blueList);
					uni.hideLoading()
					let mac = ''
					let macname = ''
					for (var i = 0; i < devices.devices.length; i++) {
						if (devices.devices[i].localName) {
							macname = devices.devices[i].localName.slice(3)
							mac = ""
							for (let i = 0, len = macname.length; i < len; i++) {
								mac += macname[i];
								if (i % 2 == 1 && i <= len - 2) mac += ":";
							}
						}
						
						// 循环判断是否存在,并且信号值达标
						for (let item of self.blueList) {
							
							if (item.mac == mac) {
								if(item.max_value != 0){
									if(devices.devices[i].RSSI > item.max_value){
										self.popTips = true
										self.stopBluetoothDevicesDiscovery()
										uni.closeBluetoothAdapter({
											success(res) {
												console.log(res)
											}
										})
										break
									}
								}else {
									self.popTips = true
									self.stopBluetoothDevicesDiscovery()
									uni.closeBluetoothAdapter({
										success(res) {
											console.log(res)
										}
									})
									break
								}
								
							}else{
								uni.showModal({
									title:'提示',
									content:'暂未查找到蓝牙围栏设备,请返回上一页重新搜索!',
									showCancel:false,
									confirmText:'返回房卡页',
									success:(res)=> {
										uni.navigateBack({})
									}
								})
							}
						}
					}
				})
			},
		},
		// // 上拉加载
		onReachBottom() {
		
			if (this.bool) {
				++this.params.page
				uni.showLoading({
					title: '加载中...'
				})
				this.$iBox.http('getBreakfastCouponList', this.params)({
					method: 'post'
				}).then(res => {
					let new_list = this.breakList.concat(res.data.list)
					this.breakList = new_list
					if (this.breakList.length == res.data.count) {
						this.bool = false
					}
					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		
		}
	}
</script>

<style lang="scss" scoped>
	.breakBox {
		width: 700rpx;
		margin: 30rpx auto;
		height: 300rpx;
		border-radius: 20rpx;
		background-color: #fff;
		padding: 30rpx;
		position: relative;
		.titleBox {
			display: flex;
			height: 80rpx;
			width: 100%;
			align-items: center;
			justify-content: space-between;
			.title {
				font-size: 40rpx;
				font-weight: 700;
			}
		}
		
		
		
		.content {
			font-size: 30rpx;
			color: #454545;
		}
		
		.qrBox {
			display: flex;
			flex-direction: column;
			align-items: center;
		}
	}
	
	.manBox {
		height: 50vh;
		width: 700rpx;
		padding: 30rpx;
		.itemBox {
			display: flex;
			justify-content: space-around;
			margin-top: 80rpx;
			.item_choose {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				border-radius: 30rpx;
				height: 300rpx;
				width: 240rpx;
				
				position: relative;
			}
		}
	}
</style>
