import Vue from 'vue'

const state = {
	marketOrderDetail:null,
	cartList:[]//购物车
}

const mutations = {
	// 自定义tabbar栏
	PUSHDETAIL: (state, marketOrderDetail) => {
		state.marketOrderDetail = marketOrderDetail
		console.log(marketOrderDetail,'marketOrderDetail');
	},
	PUSHCARD: (state, cartList) => {
		state.cartList = cartList
	},
	
}

const actions = {
	getMarketOrderDetail({
		commit
	}, params) {
		commit('PUSHDETAIL', params)
	},
	getCart({
		commit
	}, params) {
		commit('PUSHCARD', params)
	},
}

export default {
	namespaced: true,
	state,
	mutations,
	actions
}
