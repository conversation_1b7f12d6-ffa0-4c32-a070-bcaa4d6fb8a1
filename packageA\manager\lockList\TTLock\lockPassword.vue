<template>
	<view class="box">
		<view class="title">
			<p style="width: 100%;white-space: normal;font-size: 28rpx;font-weight: 600;">{{passTime}}</p>
			<view class="" style="display: flex;width: 100%;height:40%;justify-content: space-between;">
				<button size="mini" type="primary" @click="addPass">新增随机{{passType}}密码</button>

				<button size="mini" type="warn" v-if="type.keyboardPwdType == 3"
					@click="addCustomPass">新增自定义{{passType}}密码</button>
			</view>

		</view>
		<view class="" style="height: 190rpx;"></view>
		<view class="" style="display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 60rpx;" v-if="password.length==0">
			<view class="icon-queshengye_zanwujilu" style="font-size: 140rpx;" :style="{color:themeColor.com_color1}">
			</view>
			<p :style="{color:themeColor.com_color1}">暂无记录</p>
		</view>
		<view class="passBox" v-else :class="item.useable==1?'useable':'unuseable'" style="position: relative;height: 300rpx;"
			v-for="item in password">
			<view class=""
				style="position: absolute;top: -10rpx;right: -10rpx;background-color: aliceblue;border-radius: 50%;">
				<view class="icon-close" style="color: black;font-size: 38rpx;" @click="deletePassword(item)"></view>
			</view>
			<view
				style="padding: 5rpx 0;font-size: 36rpx;font-weight: 600;width: 100%;display: flex;align-items: center;justify-content: space-between;">
				<text
					:style="item.useable==1?'color: #00ff00;':'color: #C51A15;'">{{item.keyboardPwdName?item.keyboardPwdName+(item.useable==1?``:`(不在使用时间段内)`):(item.useable==1?`${passType}密码`:`${passType}密码(不在使用时间段内)`)}}</text>
				<view
					style="width: fit-content;padding: 10rpx;border-radius: 10rpx;color: #ffffff;background: #C51A15 ;font-size: 26rpx;font-weight: 400;"
					v-if="type.keyboardPwdType == 3&&item.useable==1" @click="editPass(item)">修改密码</view>
			</view>
			<view class=""
				style="padding: 10rpx 0;font-size: 36rpx;font-weight: 600;display: flex;align-items: center;justify-content: space-between;">
				<view class="" style="display: flex;align-items: center;">
					<text>密码:{{item.keyboardPwd}}</text>
					<text style="font-size: 24rpx;color: blue;padding-left: 10rpx;"
						@click="copy(item.keyboardPwd)">点击复制</text>
				</view>

			</view>

			<text>锁名:{{lockDetail.lock_alias?lockDetail.lock_alias:lockDetail.lock_name}}</text>
			<view class="" v-if="item.keyboardPwdType == 1">
				<text>有效期:{{$moment.unix(parseInt(item.startDate)/1000).format('YYYY/MM/DD HH:mm')}}</text>
				<text style="padding: 0 20rpx;">至</text>
				<text>{{$moment.unix(parseInt(item.endDate)/1000).format('YYYY/MM/DD HH:mm')}}</text>
			</view>

			<view class="" v-if="item.keyboardPwdType == 2">
				<text>激活时间:{{$moment.unix(parseInt(item.startDate)/1000).format('YYYY/MM/DD HH:mm')}}</text>
				<text style="padding: 0 20rpx;">24小时内</text>
			</view>

			<view class="" v-if="item.keyboardPwdType == 3">
				<text>有效期:{{$moment.unix(parseInt(item.startDate)/1000).format('YYYY/MM/DD HH:mm')}}</text>
				<text style="padding: 0 20rpx;">至</text>
				<text>{{$moment.unix(parseInt(item.endDate)/1000).format('YYYY/MM/DD HH:mm')}}</text>
			</view>

			<view class="" v-if="item.keyboardPwdType == 4">
				<text>在锁上使用后会删除之前在锁上使用过的密码</text>

			</view>

			<view class="" v-if="[5,6,7,8,9,10,11,12,13,14].includes(item.keyboardPwdType)">
				<text>有效期设置时间:{{$moment.unix(parseInt(item.startDate)/1000).format('HH:mm')}}</text>
				<text style="padding: 0 20rpx;">至</text>
				<text>{{$moment.unix(parseInt(item.endDate)/1000).format('HH:mm')}}</text>
			</view>

		</view>
		<view class="" style="height: 60rpx;">

		</view>

		<!-- 生成系统密码弹窗 -->
		<m-popup :show="pop" @closePop="closePop" mode="center">
			<view class="ready_time">
				<p style="font-weight: 600;font-size: 44rpx;padding: 30rpx 0;">新增随机{{passType}}密码</p>
				<view class="" style="display: flex;align-items: center;" v-if="type.keyboardPwdType != 4">
					<text style="padding-right: 14rpx;">密码名:</text>
					<input type="text" v-model="passName" placeholder="请输入密码名字,默认未类型名字"
						style="height: 80rpx;width: 500rpx;border-radius: 14rpx;border: 1px solid #525dff;padding: 0 10rpx;">
				</view>
				<view class="" v-if="type.keyboardPwdType == 3">
					<p style="margin-bottom: 10rpx;">有效时间:锁有效时间只精确到小时</p>
					<uni-datetime-picker :hide-second="true" v-model="datetimerange" type="datetimerange"
						rangeSeparator="至" />
				</view>
				<view class="" v-if="type.keyboardPwdType == 4">
					<p>在锁上使用后会删除之前在锁上使用过的密码</p>
				</view>
				<view class="" v-if="[5,6,7,8,9,10,11,12,13,14].includes(type.keyboardPwdType)">
					<p style="margin-bottom: 10rpx;">有效时间:锁有效时间只精确到小时</p>
					<view class="" style="display: flex;align-items: center;">
						<picker :value="start_time" :range="timeArray" @change="bindTimeChange">
							<view class="" style="color: #525dff;">{{start_time}}</view>
						</picker>
						<view class="" style="padding: 0 20rpx;">
							到
						</view>
						<picker :value="end_time" :range="timeArray" @change="bindTimeChange1">
							<view class="" style="color: #525dff;">{{end_time}}</view>
						</picker>
					</view>

				</view>
				<button type="primary" @click="createPass">生成密码</button>
			</view>
		</m-popup>

		<!-- 生成自定义密码弹窗 -->
		<m-popup :show="pop1" @closePop="closePop1" mode="center">
			<view class="ready_time">
				<p style="font-weight: 600;font-size: 44rpx;padding: 30rpx 0;">新增自定义密码</p>
				<view class="" style="display: flex;align-items: center;">
					<text style="padding-right: 14rpx;">密码名:</text>
					<input type="text" v-model="passName" placeholder="请输入密码名字,默认未类型名字"
						style="height: 80rpx;width: 500rpx;border-radius: 14rpx;border: 1px solid #525dff;padding: 0 10rpx;">
				</view>
				<view class="" v-if="type.keyboardPwdType == 3" style="display: flex;align-items: center;">
					<text style="padding-right: 14rpx;">密码:</text>
					<input type="number" v-model="keyboardPwd" maxlength="9" placeholder="请输入6-9位密码"
						style="height: 80rpx;width: 500rpx;border-radius: 14rpx;border: 1px solid #525dff;padding: 0 10rpx;">
				</view>
				<view class="" v-if="type.keyboardPwdType == 3">
					<p style="margin-bottom: 10rpx;">有效时间:锁有效时间只精确到小时</p>
					<uni-datetime-picker :hide-second="true" v-model="datetimerange" type="datetimerange"
						rangeSeparator="至" />
				</view>
				<button type="primary" @click="createCustomPass">生成密码</button>
			</view>
		</m-popup>

		<!-- 修改密码弹窗 -->
		<m-popup :show="pop2" @closePop="closePop2" mode="center">
			<view class="ready_time" style="height: 760rpx;">
				<p style="font-weight: 600;font-size: 44rpx;padding: 30rpx 0;">修改密码</p>
				<view class="" style="display: flex;align-items: center;">
					<text style="padding-right: 14rpx;">新密码名:</text>
					<input type="text" v-model="passName" placeholder="请输入密码名字,默认未类型名字"
						style="height: 80rpx;width: 500rpx;border-radius: 14rpx;border: 1px solid #525dff;padding: 0 10rpx;">
				</view>
				<view class="" v-if="type.keyboardPwdType == 3" style="display: flex;align-items: center;">
					<text style="padding-right: 14rpx;">旧密码:</text>
					<input type="number" :disabled="true" v-model="passDetail.keyboardPwd" maxlength="9"
						style="height: 80rpx;width: 500rpx;border-radius: 14rpx;border: 1px solid #525dff;padding: 0 10rpx;">
				</view>
				<view class="" v-if="type.keyboardPwdType == 3" style="display: flex;align-items: center;">
					<text style="padding-right: 14rpx;">新密码:</text>
					<input type="number" v-model="keyboardPwdEdit" maxlength="9" placeholder="请输入6-9位密码"
						style="height: 80rpx;width: 500rpx;border-radius: 14rpx;border: 1px solid #525dff;padding: 0 10rpx;">
				</view>
				<view class="" v-if="type.keyboardPwdType == 3">
					<p style="margin-bottom: 10rpx;">有效时间:锁有效时间只精确到小时</p>
					<uni-datetime-picker :hide-second="true" v-model="datetimerange" type="datetimerange"
						rangeSeparator="至" />
				</view>
				<button type="primary" @click="toModifyPasscode">修改密码</button>
			</view>
		</m-popup>

	</view>
</template>

<script>
	// import MxDatePicker from "@/components/mx-datepicker/mx-datepicker.vue";
	const plugin = requirePlugin("myPlugin");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		data() {
			return {
				password: [{}],
				keyboardPwd: '', //键盘密码
				keyboardPwdEdit: '',
				start_time: '选择开始时间',
				end_time: '选择结束时间',
				passTime: '',
				passType: '',
				datetimerange: [],
				showTime: '选择开始时间',
				timeArray: ['1点', '2点', '3点', '4点', '5点', '6点', '7点', '8点', '9点', '10点', '11点', '12点', '13点', '14点', '15点',
					'16点', '17点', '18点', '19点', '20点', '21点', '22点', '23点'
				],
				params: {
					page: 1,
					limit: 20,
					id: '',
					password_type: ''
				},
				bool: true,
				pop: false,
				pop1: false,
				pop2: false,
				passName: '',
				passDetail: {}
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail', 'type']),
		},
		watch: {

		},
		onLoad(options) {
			this.passType = this.type.name
			this.passTime = this.type.remark
			this.params.page = 1
			this.params.id = this.lockDetail.id
			this.params.password_type = this.type.keyboardPwdType
			this.findPass()
			this.datetimerange = [this.$moment().format('YYYY/MM/DD HH:mm'), this.$moment().add(60,'minute').format('YYYY/MM/DD HH:mm')]
		},
		methods: {
			copy(e) {
				wx.setClipboardData({
					data: e,
					success: (res) => {

					}
				})
			},
			closePop() {
				this.pop = false
			},
			closePop1() {
				this.pop1 = false
			},
			closePop2() {
				this.pop2 = false
			},
			editPass(e) {
				this.passDetail = e
				let start = this.$moment(e.startDate).format('YYYY/MM/DD HH:mm')
				let end = this.$moment(e.endDate).format('YYYY/MM/DD HH:mm')
				this.datetimerange.push(start)
				this.datetimerange.push(end)
				this.pop2 = true
			},
			deletePassword(e) {
				uni.showLoading({
					title: 'loading...'
				})
				let deviceId = ""
				let that = this
				console.log(e,'shan');
				// 删除密码
				plugin.deletePasscode(e.keyboardPwd, this.lockDetail.lock_data, res => {
					if (res.errorCode === 10003) {
						console.log("监控到设备连接已断开", res)
					}
				}, deviceId).then(res => {
					wx.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {

						uni.showModal({
							title: '删除密码',
							content: '是否删除此条密码？',
							success: (res) => {
								if (res.confirm) {
									that.$iBox.http('delPassword', {
											id: that.lockDetail.id,
											password_id: e.keyboardPwdId
										})({
											method: 'post'
										})
										.then(res => {
											uni.showToast({
												title: '删除密码成功'
											})
											that.findPass()

											uni.hideLoading()
										})
								} else if (res.cancel) {
									console.log('用户点击取消')
								}
							}
						})


					} else {
						uni.showModal({
							title: '提示',
							content: '密码删除失败！请检查锁是否连接！'
						})
					}
				}, deviceId)
			},

			// 修改密码
			toModifyPasscode() {
				let deviceId = ''
				if (this.datetimerange.length < 2) {
					uni.showToast({
						icon: 'none',
						title: '请选择开始结束时间'
					})
					return
				}
				uni.showLoading({
					title: `正在修改密码`,
				})
				console.log(this.datetimerange[0],this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm').unix(),'修改',this.$moment().unix());
				if(this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm:ss').unix()  < this.$moment().unix()){
					uni.showToast({
						title:'开始时间不能小于现在'
					})
					return
				}
				const startTime = Date.now();
				console.log(this.passDetail.keyboardPwd,this.keyboardPwdEdit,this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm:ss').unix() * 1000,this.$moment(this.datetimerange[1], 'YYYY/MM/DD HH:mm:ss').unix() * 1000,this.lockDetail.lock_data)
				// 修改密码
				  // plugin.modifyPasscode(passcode, newPasscode, start, end, this.lockDetail.lock_data, res => {
				plugin.modifyPasscode(this.passDetail.keyboardPwd, this.keyboardPwdEdit, this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm:ss').unix() * 1000, this.$moment(this.datetimerange[1], 'YYYY/MM/DD HH:mm:ss').unix() * 1000, this.lockDetail.lock_data, res => {
						if (res.errorCode === 10003) {
							console.log("监控到设备连接已断开", res)
						}
					}, deviceId).then(res => {
					wx.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'none',
							title: `自定义密码已修改--密码:${res.passcode}--操作时间::${Date.now() - startTime}`
						})
						let params = {
							id: this.lockDetail.id,
							password_name: this.passName,
							stat_date: this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm').unix(),
							end_date: this.$moment(this.datetimerange[1], 'YYYY/MM/DD HH:mm:').unix(),
							password_id: this.passDetail.keyboardPwdId,
							new_password: this.keyboardPwdEdit,
							password_type: 1
						}
						this.$iBox.http('changeKeyboardPwd', params)({
							method: 'post'
						}).then(res => {
							if (!!res) {
								this.params.page = 1
								this.findPass()
								this.pop2 = false
								this.passName = ''
								this.keyboardPwdEdit = ''
								this.datetimerange = []
								uni.showToast({
									icon: 'none',
									title: `自定义密码已上传--密码:${this.keyboardPwdEdit}`
								})
							} else {
								uni.showToast({
									icon: 'none',
									title: `自定义密码添加失败`
								})
							}
						})



					} else {
						uni.showToast({
							icon: 'none',
							title: `密码修改失败:${res.errorMsg}`
						})
					}
				}, deviceId)
			},


			bindTimeChange(e) {
				console.log(e);
				this.start_time = this.timeArray[e.detail.value]
			},
			bindTimeChange1(e) {
				this.end_time = this.timeArray[e.detail.value]
			},
			// 生成随机密码
			createPass() {
				uni.showLoading({
					title: 'loading...'
				})

				if (this.type.keyboardPwdType == 1) {
					this.$iBox.http('createTempPassword', {
						id: this.lockDetail.id,
						password_name: this.passName
					})({
						method: 'post'
					}).then(res => {
						this.params.page = 1
						this.findPass()
						this.pop = false
						this.passName = ''
						uni.hideLoading()
					})
				} else if (this.type.keyboardPwdType == 2) {
					this.$iBox.http('createPermanentPassword', {
						id: this.lockDetail.id,
						password_name: this.passName
					})({
						method: 'post'
					}).then(res => {
						this.params.page = 1
						this.findPass()
						this.pop = false
						this.passName = ''
						uni.hideLoading()
					})
				} else if (this.type.keyboardPwdType == 3) {
					if (this.datetimerange.length < 2) {
						uni.showToast({
							icon: 'none',
							title: '请选择开始结束时间'
						})
						return
					}
					this.$iBox.http('createPassword', {
						id: this.lockDetail.id,
						password_name: this.passName,
						start_time: this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm').unix(),
						end_time: this.$moment(this.datetimerange[1], 'YYYY/MM/DD HH:mm:').unix(),
						password_type: 3
					})({
						method: 'post'
					}).then(res => {
						this.params.page = 1
						this.findPass()
						this.pop = false
						this.passName = ''
						this.datetimerange = []
						uni.hideLoading()
					})
				} else if ([5, 6, 7, 8, 9, 10, 11, 12, 13, 14].includes(this.type.keyboardPwdType)) {

					console.log(this.start_time.split(':'), this.end_time);

					if (this.start_time == "选择开始时间") {
						uni.showToast({
							icon: 'none',
							title: '请选择开始时间'
						})
						return
					}

					if (this.end_time == "选择结束时间") {
						uni.showToast({
							icon: 'none',
							title: '选择结束时间'
						})
						return
					}

					if (Number(this.start_time.split('点')[0]) >= Number(this.end_time.split('点')[0])) {
						uni.showToast({
							icon: 'none',
							title: '结束时间需要大于开始时间'
						})
						return
					}

					let startTime = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${this.start_time}`,
						'YYYY/MM/DD HH:mm').unix()

					let endTime = this.$moment(this.$moment().format('YYYY/MM/DD') + ` ${this.end_time}`,
						'YYYY/MM/DD HH:mm').unix()
					this.$iBox.http('createPassword', {
						id: this.lockDetail.id,
						password_name: this.passName,
						start_time: startTime,
						end_time: endTime,
						password_type: this.type.keyboardPwdType
					})({
						method: 'post'
					}).then(res => {
						this.params.page = 1
						this.findPass()
						this.pop = false
						this.passName = ''
						this.start_time = '选择开始时间'
						this.end_time = '选择结束时间'
						uni.hideLoading()
					})
				} else {
					this.$iBox.http('createClearPassword', {
						id: this.lockDetail.id
					})({
						method: 'post'
					}).then(res => {
						this.pop = false
						this.params.page = 1

						this.findPass()
						uni.hideLoading()
					})
				}
			},

			addCustomPass() {
				this.pop1 = true
			},
			// 生成自定义密码
			createCustomPass() {
				let deviceId = ""
				if (this.keyboardPwd.length < 6) {
					uni.showToast({
						icon: 'none',
						title: '密码最少为6位数'
					})
					return
				}

				if (this.datetimerange.length < 2) {
					uni.showToast({
						icon: 'none',
						title: '请选择开始结束时间'
					})
					return
				}

				wx.showLoading({
					title: `正在设置自定义密码`,
				})
				const startTime = Date.now();
				// 添加自定义密码
				plugin.createCustomPasscode(this.keyboardPwd, this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm')
					.unix() * 1000, this.$moment(this.datetimerange[1], 'YYYY/MM/DD HH:mm').unix() * 1000, this
					.lockDetail.lock_data, res => {
						if (res.errorCode === 10003) {
							console.log("监控到设备连接已断开", res)
						}
					}, deviceId).then(res => {
					wx.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'none',
							title: `自定义密码已添加--密码:${res.passcode}--操作时间::${Date.now() - startTime}`
						})
						let params = {
							id: this.lockDetail.id,
							password_name: this.passName,
							stat_date: this.$moment(this.datetimerange[0], 'YYYY/MM/DD HH:mm').unix(),
							end_date: this.$moment(this.datetimerange[1], 'YYYY/MM/DD HH:mm:').unix(),
							password: this.keyboardPwd
						}
						this.$iBox.http('createKeyboardPwd', params)({
							method: 'post'
						}).then(res => {
							if (!!res) {
								this.params.page = 1
								this.findPass()
								this.pop1 = false
								this.passName = ''
								this.keyboardPwd = ''
								this.datetimerange = []
								uni.showToast({
									icon: 'none',
									title: `自定义密码已上传--密码:${keyboardPwd}`
								})
							} else {
								uni.showToast({
									icon: 'none',
									title: `自定义密码添加失败`
								})
							}
						})
					} else {
						uni.showToast({
							icon: 'none',
							title: `自定义密码添加失败:${res.errorMsg},请蓝牙连接锁`
						})
					}
				})
			},

			findPass() {
				uni.showLoading({
					title: 'loading...'
				})
				this.$iBox.http('getTtLockPassword', this.params)({
						method: 'post'
					})
					.then(res => {
						this.password = res.data.list

						uni.hideLoading()
					})
			},
			// 读取操作记录
			toReadRecord() {
				let deviceId = ""
				// let type = event.currentTarget.dataset.type === 1 ? plugin.RecordReadType.ALL : plugin.RecordReadType.NEW;
				uni.showLoading({
					title: `正在读取锁内操作记录`,
				})
				const start = Date.now();
				// 获取操作记录
				plugin.getOperationLog(plugin.RecordReadType.NEW, this.lockDetail.lock_data, res => {
					if (res.errorCode === 10003) {
						console.log("监控到设备连接已断开", res)
					}
				}, deviceId).then(res => {
					uni.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'success',
							title: `操作记录已获取--操作时间::${Date.now() - start}`
						})
						this.$iBox.http('uploadOpenRecord', {
							id: this.lockDetail.id,
							records: res.log
						})({
							method: 'post'
						}).then(res => {

						})
					} else {
						uni.showToast({
							icon: 'success',
							title: "读取操作记录失败:" + res.errorMsg
						})

					}
				})
			},
			addPass() {
				this.pop = true
			},
		},
		// // 上拉加载
		onReachBottom() {
			if (this.bool&&this.password.length > this.params.limit) {
				++this.params.page
				this.$iBox.http('getTtLockPassword', this.params)({
					method: 'post'
				}).then(res => {
					console.log('我是返回', res.data)
					let new_list = this.password.concat(res.data.list)
					this.password = new_list
					console.log(this.password.length,res.data.total);
					if (this.password.length == res.data.total) {
						this.bool = false
					}

					uni.hideLoading()
				}).catch(function(error) {
					console.log('网络错误', error)
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		width: 100%;

		.title {
			width: 100%;
			height: 176rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: space-between;
			padding: 20rpx;
			position: fixed;
			top: 0;
			z-index: 9;
			width: 100vw;
			background: #FFFFFF;
			box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
		}

		.passBox {
			width: 90%;
			padding: 30rpx;
			margin: 40rpx auto;
			border-radius: 20rpx;
			// background-color: #FFFFFF;
			box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 6px -1px, rgba(0, 0, 0, 0.06) 0px 2px 4px -1px;
			display: flex;
			flex-direction: column;
		}

	}

	.ready_time {
		height: 600rpx;
		width: 730rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: space-between;
		padding: 30rpx;
	}

	.useable {
		background: #00ab5f;
		;
	}

	.unuseable {
		background: #c7cdd9;
	}
</style>
