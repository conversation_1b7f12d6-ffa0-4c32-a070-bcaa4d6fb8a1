// API
const API = {
	'getLockTypeList':'Boss/RoomLockType/getLockTypeList',//查询门锁类型
	'getTtLockList':'Boss/TtLock/getTtLockList',//获取通通锁列表
	'getRoomByRoomNumber':'Boss/Room/getRoomByRoomNumber', //查询房间门锁绑定
	'addTtLock':'Boss/TtLock/addTtLock',//添加通通锁
	'delTtLock':'Boss/TtLock/delTtLock',//删除通通锁
	'getClearPassword':'Boss/TtLock/getClearPassword',//生成清除锁密码
	'delPassword':'Boss/TtLock/delPassword',//删除密码
	'uploadOpenRecord':'Boss/TtLock/uploadOpenRecord',//上传开锁记录
	'getOpenRecord':'Boss/TtLock/getOpenRecord',//查询开锁记录
	'updateElectricQuantity':'Boss/TtLock/updateElectricQuantity',//更新电量
	'createKeyboardPwd':'Boss/TtLock/createKeyboardPwd',//生成键盘密码
	'updateLockeSetting':'Boss/TtLock/updateLockeSetting',//修改锁设置
	'getTtLockPassword':'Boss/TtLock/getTtLockPassword',//查询密码
	'changeKeyboardPwd':'Boss/TtLock/changeKeyboardPwd',//修改密码
	'getPasswordType':'Boss/TtLock/getPasswordType',//获取限时密码类型
	'createTempPassword':'Boss/TtLock/createTempPassword',//生成临时单次密码
	'createPassword':'Boss/TtLock/createPassword',//生成限时密码
	'createClearPassword':'Boss/TtLock/createClearPassword',//生成删除密码
	'createPermanentPassword':'Boss/TtLock/createPermanentPassword',//生成永久密码
	'getLockDetailConf':'Boss/TtLock/getLockDetailConf',//查询锁信息
	'changeAdminKeyboardPwd':'Boss/TtLock/changeAdminKeyboardPwd',//修改管理员密码
	'cuploadOpenRecord':'wx/TtLock/uploadOpenRecord',//客户端传记录
	'getLockSetting':'Boss/TtLock/getLockSetting',//
}

export default API
