<template>
	<view>
		<view class="billBox">
			<view class="" style="background: radial-gradient(54.13% 63.12% at 21.73% 0%, rgba(81, 222, 222, 0.4) 0%, rgba(81, 222, 222, 0) 100%);border-radius: 60rpx;
			min-height: 184rpx;width: 100%;padding: 32rpx;" >
				<view class="" style="display: flex;align-items: center;height: 40rpx;">
					<text>{{billInfo.link_man}}</text>
					<text style="margin: 0 10rpx;">|</text>
					<text>{{billInfo.room_type_name}}</text>
					<text style="margin: 0 10rpx;">|</text>
					<text>{{billInfo.room_number?billInfo.room_number:'未排房'}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;height: 40rpx;margin-top: 20rpx;" v-if="!online_cash&&(isMainOrPay && (billInfo.bill_source_sign === 'wxxcx' || billInfo.bill_source_sign === 'buru')) || (thirdBill && (billInfo.accnt || billInfo.other_bill_code))">
					<p style="padding-right: 0;width: 240rpx;">押金( <text
							:style="cashAll.total > 0&& cashAll.cash_pledge!=cashAll.already_pay_cash_pledge?'color:#7d5a31':'color:#00aa7f'">
							{{cashAll.total > 0&&cashAll.already_pay_cash_pledge!=cashAll.cash_pledge?'待支付':'已支付'}}</text>)</p>
					<view class="" style="padding: 0;display: flex;align-items: center;">
						<text style="color: red;font-size: 28rpx;"
							:style="cashAll.total > 0&&cashAll.already_pay_cash_pledge!=cashAll.cash_pledge?'color:#aa0000':'color:#00aa7f'">￥{{cashAll.cash_pledge}}/每间房</text>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;height: 40rpx;margin-top: 20rpx;">
					<view class="" style="display: flex;align-items: center;"
						v-if="(isMainOrPay && (billInfo.bill_source_sign === 'wxxcx' || billInfo.bill_source_sign === 'buru')) || (thirdBill && (billInfo.accnt || billInfo.other_bill_code))">
						<p style="padding-right: 0;width: 240rpx;">房费(<text
								:style="cashAll.total > 0&& cashAll.bill_amount!=cashAll.already_pay?'color:#7d5a31':'color:#00aa7f'">{{billInfo.booking_type==3?'先住后付':(cashAll.total > 0&& cashAll.bill_amount!=cashAll.already_pay?'待支付':'已支付')}}</text>)
						</p>
						<view class="" style="padding: 0;display: flex;align-items: center;">
							<text style="font-size: 28rpx;"
								:style="cashAll.total > 0&& cashAll.bill_amount!=cashAll.already_pay?'color:#aa0000':'color:#00aa7f'">￥{{cashAll.bill_amount}}/每间房x</text>
								<text v-if="billInfo.room_sale_type_sign=='standard'" :style="cashAll.total > 0&& cashAll.bill_amount!=cashAll.already_pay?'color:#aa0000':'color:#00aa7f'">{{billInfo.stay_time}}晚</text>
								<text v-if="billInfo.room_sale_type_sign=='long_standard'" :style="cashAll.total > 0&& cashAll.bill_amount!=cashAll.already_pay?'color:#aa0000':'color:#00aa7f'">{{billInfo.stay_time}}月</text>
								<text v-if="billInfo.room_sale_type_sign=='conference_room'" :style="cashAll.total > 0&& cashAll.bill_amount!=cashAll.already_pay?'color:#aa0000':'color:#00aa7f'">{{billInfo.stay_time}}小时</text>
								<text v-if="billInfo.room_sale_type_sign=='hour'" :style="cashAll.total > 0&& cashAll.bill_amount!=cashAll.already_pay?'color:#aa0000':'color:#00aa7f'">{{billInfo.stay_time}}小时</text>
						</view>
					</view>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;" v-if="cashAll.already_pay&&(isMainOrPay && (billInfo.bill_source_sign === 'wxxcx' || billInfo.bill_source_sign === 'buru')) || (thirdBill && (billInfo.accnt || billInfo.other_bill_code))">
					<p style="padding-right: 0rpx;width: 240rpx;">已支付房费:</p>
					<text>￥{{cashAll.already_pay}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;" v-if="cashAll.already_pay_cash_pledge&&!online_cash&&(isMainOrPay && (billInfo.bill_source_sign === 'wxxcx' || billInfo.bill_source_sign === 'buru')) || (thirdBill && (billInfo.accnt || billInfo.other_bill_code))">
					<p style="padding-right: 0rpx;width: 240rpx;">已支付押金:</p>
					<text>￥{{cashAll.already_pay_cash_pledge}}</text>
				</view>
				<view class="" style="display: flex;align-items: center;margin-top: 20rpx;" v-if="(isMainOrPay && (billInfo.bill_source_sign === 'wxxcx' || billInfo.bill_source_sign === 'buru')) || (thirdBill && (billInfo.accnt || billInfo.other_bill_code))">
					<p style="padding-right: 0rpx;width: 240rpx;">总计待付:</p>
					<text>￥{{cashAll.bill_amount - cashAll.already_pay - cashAll.already_pay_cash_pledge + cashAll.cash_pledge}}</text>
				</view>
			</view>
			<view class="" style="padding:0 0 32rpx 32rpx;">
				<view style="display: flex;align-items: center;margin-top: 20rpx;">
					<view style="background: #51DEDE1A;width: 100rpx;height: 48rpx;border-radius: 8rpx;color: #FFFFFF;display: flex;align-items: center;justify-content: center;">
						<text style="font-size: 30rpx;color: #51DEDE;">入住</text>
					</view> 
					<text style="color::#000000E0;font-size: 30rpx;margin-left: 30rpx;" v-if="billInfo">{{billInfo.enter_time_plan | moment1}}</text>
					
				</view>
				<view style="display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color::#000000E0;font-size: 30rpx;">联系人</text>
					<text style="color::#000000E0;font-size: 30rpx;margin-left: 30rpx;">{{billInfo.link_man}}</text>
					
				</view>
				<view style="display: flex;align-items: center;margin-top: 20rpx;">
					<text style="color::#000000E0;font-size: 30rpx;">预订电话</text>
					<text style="color::#000000E0;font-size: 30rpx;margin-left: 30rpx;"  v-if="billInfo">{{formatPass(billInfo.link_phone)}}</text>
				</view>
				
			</view>
			
			<view class="roomBox" v-if="!billInfo.room_number&&isMainOrPay">
				<view class="roomContent">
					<view class="" style="width: 100%;margin-top: 30rpx;" v-for="(item, index) in roomList"
						:key="index">
						<p class="title" style="color: #00000066;font-size: 30rpx;" >{{item.building}}({{item.room_count}})
						</p>
						<view class="roomContentBox" v-for="(item1, index1) in item.floor_list" :key="index1">
							<p>{{item1.floor}}L({{item1.room_count}})</p>
							<view class="" style="display: flex;align-items: center;flex-wrap: wrap;background-color: #F5F5F5;width: 700rpx;padding: 16rpx;border-radius: 32rpx;margin-top: 16rpx;">
								<view class="itemContent" v-for="(item2, index2) in item1.room_list" :key="index2" 
									@click="chooseRoom(item2)">
									<view class="itemBox" :style="item2.id === chooseId?'background:'+themeColor.main_color+';color:#FFFFFF;':'background:'+themeColor.main_color+'1A;color:#000000E0;border:1px solid '+themeColor.main_color+'A3'">
										<p style="font-size: 40rpx;">{{item2.room_number}}</p>
										<p style="font-size: 24rpx;">{{item2.room_type_name}}</p>
										<!-- <view class="" v-if="item2.id === chooseId"
											style="position: absolute;top: 0;right: 0;width: 100%;height: 100%;background-color: #00000099;display: flex;align-items: center;justify-content: center;">
											<text style="color: #FFFFFF;">已选择</text>
										</view> -->
									</view>
								</view>
							</view>
						</view>

					</view>

				</view>
			</view>

		</view>
		<view class="" style="height: 120rpx;">

		</view>
		<view class="btnBox">
			<view class="sureBtn" @click="upThrottle"
				:style="{background:themeColor.main_color,color:themeColor.bg_color}">
				<text>{{billInfo.bill_status==4?'下一步':(isMainOrPay?'下一步':'下一步')}}</text>
			</view>
		</view>

		<mFenceDevice :if_show="blueDeviceSetting"></mFenceDevice>

		<!-- 支付弹窗 -->
		<m-popup :show="pop" @closePop="closePop">
			<m-payCard v-if="hackReset" @toPay="payFor" :payType="payList"></m-payCard>
		</m-popup>

		<mChooseMan v-if="hackReset1" @selectMan="chooseMan" :if_show="ifChoose"></mChooseMan>

		<!-- 同住人二维码 -->
		<m-popup :show="popManQr" mode="center">
			<view class=""
				style="height: 600rpx;width: 700rpx;border-radius: 30rpx;display: flex;flex-direction: column;justify-content: space-between;align-items: center;padding: 30rpx;">
				<p>请将本码截图保存或出示给同住人扫码认证！</p>
				<image :src="qrcode" mode="" style="height: 300rpx;width: 300rpx;"></image>
				<view class="" style="width: 100%;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="toAuth" :style="{background:themeColor.main_color,color:themeColor.bg_color}"
						style="height: 80rpx;width: 400rpx;border-radius: 40rpx;display: flex;align-items: center;justify-content: center;">
						去认证
					</view>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import mFenceDevice from '../components/m-fenceDevice.vue'
	import mChooseMan from '../components/m-chooseMan'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				roomList: [],
				chooseId: '',
				pop: false,
				hackReset: true,
				hackReset1: true,
				cashAll: null,
				payList: ['weixin', 'tongyong', 'duli'], //支付方式
				online_cash: '',
				room_id: '',
				isMainOrPay: false, //是否是主入住人且需要支付
				ifChoose: false,
				popManQr: false, //入住码
				qrcode: '',
				blueDeviceSetting: false,
				thirdBill: 1,
				billInfo:null
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting']),
			...mapState('room', ['billDetail', 'roomBillUser']),
		},
		components: {
			mFenceDevice,
			mChooseMan
		},
		watch: {
			roomBillUser: {
				handler(oldData, newData) {
					console.log(this.roomBillUser.length, 'd');
					if (this.roomBillUser && this.roomBillUser.length == 0) {
						this.isMainOrPay = true
					} else if (this.roomBillUser && this.roomBillUser.length > 0) {
						let user = this.roomBillUser.filter(item => {
							return item.common_code == this.userInfo.common_code
						})

						if (user.length > 0) {
							if (user[0].is_main) {
								this.isMainOrPay = true
							} else {
								this.isMainOrPay = false
							}
						} else {
							this.isMainOrPay = false
						}
					} else if (!this.roomBillUser) {
						this.isMainOrPay = true
					}
				},
				immediate: true,
				deep: true
			},
			billDetail: {
				handler(newData) {
					
					if (newData.select_user_count || newData.max_user_count == 1) {
						this.ifChoose = false
					}
				},
				immediate: true,
				deep: true
			}
		},
		async onShow() {
			await this.$onLaunched;
			this.hackReset = false
			this.hackReset1 = false
			this.$nextTick(() => {
				this.hackReset = true
			})

			this.$nextTick(() => {
				this.hackReset1 = true
			})

			this.blueDeviceSetting = this.shopSetting.filter(item => {
				return item.sign == 'u_dun_device'
			})[0].property.status

			this.thirdBill = this.shopSetting.filter(item => {
				return item.sign == 'third_bill_show_price'
			})[0].property.status

			// 查询是否支持在线收押金
			this.online_cash = this.shopSetting.filter(item => {
				return item.sign == 'self_check_in_pay_cash_pledge'
			})[0].property.status
			// 刷新订单
			this.$iBox.http('getRoomBillInfo', {
				bill_id: this.billDetail.id
			})({
				method: 'post'
			}).then(res => {
				this.billInfo = res.data
				// 查询房费
				
				this.$iBox.http('getSelfCheckInAmount', {
					bill_id: this.billInfo.id
				})({
					method: 'post'
				}).then(res => {
					this.cashAll = res.data
				})
				
				if (!this.billInfo.room_number) {
					this.$iBox.http('wxSelfCheckInSelectRoomList', {
						bill_id: this.billInfo.id
					})({
						method: 'post'
					}).then(res => {
						this.roomList = res.data
					}).catch(err => {
						uni.hideLoading()
					})
				}
			})
			
			
			



		},
		methods: {
			formatPass(e) {
				return e.substring(0, 3) + '****' + e.substring(7, 11)
			},
			chooseRoom(e) {
				this.chooseId = e.id
			},
			upInfo() {
				// 判断订单是否已经认证，认证但未排房代表机器过来，排房后直接去房卡页
				let user = this.roomBillUser.filter(item => {
					return item.common_code == this.userInfo.common_code
				})[0]
				
				// 刷新订单
				this.$iBox.http('getRoomBillInfo', {
					bill_id: this.billDetail.id
				})({
					method: 'post'
				}).then(res => {
					this.billInfo = res.data
					
					
					if (this.billInfo.bill_status == 4) {
						uni.navigateTo({
							url: '/packageA/autoRoom/autoRoom'
						})
					} else {
						// 判断订单状态后再判断是否为主入住人或者是第一个办理的，然后是否选了房间 再分为两种情况，订单带房间和订单不带房间
						if (this.isMainOrPay) {
							// 订单带了房间
							if (this.billInfo.room_number) {
								this.room_id = this.billInfo.room_id
							} else {
								// 订单未带房间
								if (!this.chooseId) {
									uni.showToast({
										icon: 'error',
										title: '请选择一间房间！',
										duration: 1600
									})
									return
								} else {
									this.room_id = this.chooseId
								}
							}
							console.log((!this.billInfo.select_user_count && this.billInfo.max_user_count == 1) || this
								.billInfo.select_user_count, 'do');
							// 2.再判断是否需要在线支付押金
							if (!this.online_cash && this.cashAll.cash_pledge > 0) {
								// 需要在线支付判断是否已经支付了押金和房费，>0则代表需要支付
					
								// 再判断是否是先住后付，
								if ((this.cashAll.total > 0 && this.billInfo.booking_type != 3) || (this.billInfo
										.booking_type == 3 && this.cashAll.already_pay_cash_pledge == 0)) {
									this.pop = true
								} else {
					
									// ==0则代表已经支付，传房号直接去认证
									let params = {
										bill_id: this.billInfo.id,
										pay_type: 1,
										room_id: this.room_id
									}
									this.$iBox
										.http('paySelfCheckInAmount', params)({
											method: 'post'
										})
										.then(res => {
											console.log(user, 'user');
											if (((!this.billInfo.select_user_count && this.billInfo.max_user_count ==
													1) || this.billInfo.select_user_count) && (user && user
													.authentication == 0 || !user)) {
												uni.navigateTo({
													url: '/packageA/autoRoom/autoRoom'
												})
											} else if (((!this.billInfo.select_user_count && this.billInfo
													.max_user_count == 1) || (this.billInfo.select_user_count)) && (
													user && user.authentication == 1)) {
												uni.reLaunch({
													url: '/pages/myRoom/myRoom'
												})
											} else {
												this.ifChoose = true
											}
											uni.hideLoading()
										})
								}
					
					
					
							} else {
					
								// 不在线交押金则判断房费是否交清
								if (this.cashAll.bill_amount - this.cashAll.already_pay > 0 && this.billInfo
									.booking_type != 3) {
									this.pop = true
								} else {
					
									// ==0则代表已经支付，传房号直接去认证
									let params = {
										bill_id: this.billInfo.id,
										pay_type: 1,
										room_id: this.room_id
									}
									this.$iBox
										.http('paySelfCheckInAmount', params)({
											method: 'post'
										})
										.then(res => {
											uni.hideLoading()
											console.log(user, 'user');
											if (((!this.billInfo.select_user_count && this.billInfo.max_user_count ==
													1) || this.billInfo.select_user_count) && (user && user
													.authentication == 0 || !user)) {
												uni.navigateTo({
													url: '/packageA/autoRoom/autoRoom'
												})
											} else if (((!this.billInfo.select_user_count && this.billInfo
													.max_user_count == 1) || (this.billInfo.select_user_count)) && (
													user && user.authentication == 1)) {
												uni.reLaunch({
													url: '/pages/myRoom/myRoom'
												})
											} else {
												this.ifChoose = true
											}
										})
								}
							}
						} else {
							// 非主入住人扫码进入直接去认证界面
							if (user.authentication == 0) {
								uni.navigateTo({
									url: '/packageA/autoRoom/autoRoom'
								})
							} else if (user.authentication == 1) {
								uni.reLaunch({
									url: '/pages/myRoom/myRoom'
								})
							}
						}
					}
				})
				
			},
			upThrottle(e) {
				this.hackReset = false
				this.$nextTick(() => {
					this.hackReset = true
				})
				this.$iBox.throttle1(() => {
					this.upInfo()
				}, 2000);
			},
			payFor(e) {
				
				this.$iBox.throttle(() => {
					this.fnPay(e)
				}, 2000);
			},
			fnPay(e) {
				uni.showLoading({
					title: '等待支付...'
				})
				let user = this.roomBillUser.filter(item => {
					return item.common_code == this.userInfo.common_code
				})[0]
				console.log(e, 'xufang');
				this.payType = e
				let params = {
					bill_id: this.billInfo.id,
					booking_type: '',
					pay_type: '',
					room_id: this.room_id
				}
				if (e == 'weixin') {
					params.pay_type = 1
					params.booking_type = 1
					this.$iBox
						.http('paySelfCheckInAmount', params)({
							method: 'post'
						})
						.then(res => {
							if (res.data) {
								
								if(res.data.bizCode=='0000'){
									// 随行付
									uni.requestPayment({
										provider: 'wxpay',
										AppId:res.data.payAppId,
										timeStamp: res.data.payTimeStamp,
										nonceStr: res.data.paynonceStr,
										package: res.data.payPackage,
										signType: res.data.paySignType,
										paySign: res.data.paySign,
										success: (res) => {
											uni.hideLoading()
											this.pop = false
											if (((!this.billInfo.select_user_count && this.billInfo
														.max_user_count == 1) || this.billInfo
													.select_user_count) && (user && user.authentication == 0 ||
													!user)) {
												uni.navigateTo({
													url: '/packageA/autoRoom/autoRoom'
												})
											} else if (((!this.billInfo.select_user_count && this.billInfo
													.max_user_count == 1) || (this.billInfo
													.select_user_count)) && (user && user.authentication ==
												1)) {
												uni.reLaunch({
													url: '/pages/myRoom/myRoom'
												})
											} else {
												this.ifChoose = true
											}
									
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
									
									
								}else{
									// 微信支付
									uni.requestPayment({
										provider: 'wxpay',
										timeStamp: res.data.timeStamp,
										nonceStr: res.data.nonceStr,
										package: res.data.package,
										signType: 'MD5',
										paySign: res.data.paySign,
										success: (res) => {
											uni.hideLoading()
											this.pop = false
											if (((!this.billInfo.select_user_count && this.billInfo
														.max_user_count == 1) || this.billInfo
													.select_user_count) && (user && user.authentication == 0 ||
													!user)) {
												uni.navigateTo({
													url: '/packageA/autoRoom/autoRoom'
												})
											} else if (((!this.billInfo.select_user_count && this.billInfo
													.max_user_count == 1) || (this.billInfo
													.select_user_count)) && (user && user.authentication ==
												1)) {
												uni.reLaunch({
													url: '/pages/myRoom/myRoom'
												})
											} else {
												this.ifChoose = true
											}
										
										},
										fail: function(err) {
											uni.hideLoading()
										}
									});
								}
								
						
							} else {
								uni.hideLoading()
								this.pop = false
								if (((!this.billInfo.select_user_count && this.billInfo.max_user_count == 1) ||
										this.billInfo.select_user_count) && (user && user.authentication == 0 || !
										user)) {
									uni.navigateTo({
										url: '/packageA/autoRoom/autoRoom'
									})
								} else if (((!this.billInfo.select_user_count && this.billInfo.max_user_count ==
										1) || (this.billInfo.select_user_count)) && (user && user.authentication ==
										1)) {
									uni.reLaunch({
										url: '/pages/myRoom/myRoom'
									})
								} else {
									this.ifChoose = true
								}
							}

						})
				} else {
					params.booking_type = e != 'daodian' ? 1 : 2
					params.pay_type = e == 'tongyong' ? 2 : (e == 'duli' ? 3 : '')

					this.$iBox
						.http('paySelfCheckInAmount', params)({
							method: 'post'
						})
						.then(res => {
							uni.hideLoading()
							this.pop = false
							// 支付成功提示!
							uni.showModal({
								title: '支付提示',
								content: '支付成功!',
								showCancel: false,
								success: res => {
									if (res.confirm) {
										if (((!this.billInfo.select_user_count && this.billInfo
													.max_user_count == 1) || this.billInfo
												.select_user_count) && user.authentication == 0) {
											uni.navigateTo({
												url: '/packageA/autoRoom/autoRoom'
											})
										} else if (((!this.billInfo.select_user_count && this.billInfo
												.max_user_count == 1) || (this.billInfo
												.select_user_count)) && user.authentication == 1) {
											uni.reLaunch({
												url: '/pages/myRoom/myRoom'
											})
										} else {
											this.ifChoose = true
										}
									}
								}
							})
						})
				}

			},
			closePop() {
				this.pop = false
			},
			chooseMan(e) {
				console.log(e);
				this.$iBox
					.http('selectUserCount', {
						bill_id: this.billInfo.id,
						user_count: e
					})({
						method: 'post'
					})
					.then(res => {
						if (e == 1) {
							uni.navigateTo({
								url: '/packageA/autoRoom/autoRoom'
							})

						} else {
							this.$iBox
								.http('createRoomBillQrCode', {
									bill_id: this.billInfo.id,
									type: 2
								})({
									method: 'post'
								})
								.then(res => {
									this.qrcode = res.data.url
									this.popManQr = true

								}).catch(err => {
									console.log('err');
									uni.navigateTo({
										url: '/packageA/autoRoom/autoRoom'
									})
								})

						}
					})

			},
			toAuth() {
				uni.navigateTo({
					url: '/packageA/autoRoom/autoRoom'
				})
			}
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style scoped lang="scss">
	.billBox {
		width: 750rpx;
		min-height: 90vh;
		border-radius: 60rpx;
		margin: 30rpx auto;
		background: #FFFFFF;

		.billItem {
			width: 50%;
			height: 25%;
			font-size: 26rpx;
		}

		.billItem1 {
			width: 100%;
			height: 25%;
			font-size: 26rpx;
		}
	}

	.cashBox {
		width: 720rpx;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;
		padding: 0rpx 30rpx;

		.room {
			padding: 30rpx 0;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 1px solid #e4e7ed;

		}
	}


	.roomBox {
		padding-left: 30rpx;
		width: 100%;

		.roomContent {
			width: 100%;
			display: flex;
			align-items: center;
			flex-wrap: wrap;

			.title {
				// font-weight: 600;
			}

			.roomContentBox {
				width: 700rpx;
				font-size: 38rpx;
				color: #000000E0;
				margin-top: 16rpx;
				.itemContent {
					width: 33%;
					height: 200rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 16rpx;
					margin-top: 10rpx;
					.itemBox {
						width: 200rpx;
						height: 200rpx;
						// background-color: #21860b;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						// color: #FFFFFF;
						border-radius: 16rpx;
						position: relative;
					}
				}
			}

		}
	}

	.btnBox {
		position: fixed;
		bottom: 0rpx;
		width: 100%;
		height: 120rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #FFFFFF;
		.sureBtn {
			width: 600rpx;
			height: 100rpx;
			border-radius: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>