<script>
	import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	export default {
		data(){
			return {
				du_hotel:''
			}
		},
		onLaunch(options) {
			// 监听网络状态
			
			// 	if(options.query && options.query.hasOwnProperty('q') ){//普通链接二维码进入小程序内部信息在 q 里

			//       let str = decodeURIComponent(options.query.q);

			//     }
			let du = '' //分销id参数
			let shop_id = ''
			let scene = wx.getEnterOptionsSync()
			console.log(options,'options',scene);
			if (scene.query.scene && (scene.scene == 1047 || scene.scene == 1048 || scene.scene == 1049 || scene.scene == 1007 || scene.scene == 1008)) {
				let query = decodeURIComponent(scene.query.scene)
				//解析参数
				if (query.includes("du")) {
					du = this.$iBox.linkFormat(query, "du")
					console.log(du,'1du');
				}
				
				if(query.includes("shop_id")){
					shop_id = this.$iBox.linkFormat(query, "shop_id")
				}
			
			}
			
			
			// 小程序二维码带参数第二种补充,
			if (options.query && options.query.scene) {
				let query = decodeURIComponent(options.query.scene)
				//解析参数,判断参数是一个且确定是du的
				if (query.includes("du")) {
					du = this.$iBox.linkFormat(query, "du")
				} 
			}
			
			if(options.query&&options.query.share_id){
				this.getShareId(options.query.share_id)
			}

			wx.login({
				success: res => {
					if (res.code) {
						let {
							appId
						} = wx.getAccountInfoSync().miniProgram

						this.$iBox.http('login', {
							code: res.code,
							app_id: appId,
							shop_id:shop_id
						})({
							method: 'post'
						}).then(userInfoData => {
							// 是否被拉黑
							console.log(userInfoData,'userInfoData.block');
							if(userInfoData.data.block){
								uni.reLaunch({
									url:'/pages/mainPage/mainPage'
								})
							}
							
							// 是否有酒店id
							let last_hotel = userInfoData.data.last_hotel_id
							// 存储用户信息
							this.toLogin(userInfoData.data)
							console.log(du, '分销ID');
							// 更新分销人
							if (du) {
								this.$iBox.http('updateDistributionUserId', {
									distribution_user_id: du
								})({
									method: 'post'
								}).then(res => {
									this.du_hotel = res.data.shop_id
								})

							}
							
							// 获取全局UI
							this.$iBox.http('getUI', {})({
								method: 'post'
							}).then(res => {
								res.data.control_component.forEach(item => {
									if (item.sign == 'tab') {
										let tabbar = item.property.filter(item1 => {
											return item1.status == 1
										})
										this.toTabbar(tabbar)
									}

									if (item.sign == 'popover') {
										this.toPop(item.property)
									}
								})

								this.toThemeColor(res.data.subject.style)
								this.toCopyRight(res.data.copy_right)
							}).catch(err => {
								
							})

							// 获取全局配置
							this.$iBox.http('getSetting', {})({
								method: 'post'
							}).then(res => {
								this.getSetting(res.data)
								// this.$isResolve();
							})

							// 获取个人信息
							this.$iBox.http('getUserInfo', {
								simple: false
							})({
								method: 'post'
							}).then(res => {
								let userInfo = res.data
								userInfo.session_key = userInfoData.data.session_key
								this.updateUserInfo(userInfo)
								// this.$isResolve();
							})
							
						
							
							// 获取定位权限

							uni.getLocation({
								type: 'wgs84',
								success: (res) => {

									const latitude = res.latitude
									const longitude = res.longitude
									this.getCityAndHotel(latitude,
										longitude, last_hotel)
								},
								fail: res => {
									console.log(res,'dddss');
									// this.$iBox.auth('getLocation',
									// 	'小程序需要获取您的位置，为您推荐合适的入住酒店')
									this.getCityAndHotel('', '',
										last_hotel)
								}
							})

						}).catch(function(error) {
							console.log('网络错误', error)
						})
					}
				}
			})
		},
		onShow(options) {
			console.log("onshow--------", options);
			uni.getNetworkType({
				success: function (res) {
					if(res.networkType=='none'){
						uni.showModal({
							title:'请检查手机网络!',
							content:'手机网络断开，请重新启动小程序',
							confirmText:'重新启动',
							showCancel:false,
							success:res=>{
								if(res.confirm){
									wx.exitMiniProgram()
								}
							}
						})
					}
					console.log(res.networkType=='none','net');
					
				}
			});

			const updateManager = wx.getUpdateManager()

			updateManager.onUpdateReady(() => {
				wx.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					success: (res) => {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate()
						}
					}
				})
			})

			updateManager.onUpdateFailed(function() {
				// 新版本下载失败
				wx.showModal({
					title: '已经有新版本',
					content: '新版本已经准备好，请删除当前小程序重新进入！'
				})
			})
		},
		methods: {
			...mapActions('login', ['toLogin', 'updateUserInfo']),
			...mapActions('ui', ['toTabbar', 'toThemeColor', 'toPop', 'toCopyRight']),
			...mapActions('hotel', ['getUnit', 'getChooseDate', 'getHotelList', 'getHotel', 'getCity', 'getCityList',
				'getSetting', 'getShopSetting','getShareId'
			]),
			getCityAndHotel(latitude, longitude, last_hotel) {
				// 查询城市
				// uni.showLoading({
				// 	title: '加载中...'
				// })
				this.$iBox.http('getShopCities', {
					latitude: latitude,
					longitude: longitude
				})({
					method: 'post'
				}).then(res => {
					if (res.data.length > 0) {
						let arr = []
						res.data.forEach(item=>{
							item.data.forEach(item1=>{
								arr.push(item1)
							})
						})
						
						const minValue = Math.min(...arr.map(obj => obj.distance));
						console.log(minValue,'minValue',arr);
						let city= arr.filter(item=>{
							return item.distance?item.distance== minValue:!item.distance
						})[0]
						console.log(city);
						this.getCity(city)
						this.getCityList(res.data)
					}

				})

				// 查询酒店列表
				this.$iBox.http('getShopList', {
					latitude: latitude,
					longitude: longitude,
					page: 1,
					limit: 10
				})({
					method: 'post'
				}).then(res => {
					if (res.data.list.length > 0) {
						this.getHotelList(res.data.list)
						
						console.log(last_hotel, 'last_hotel',this.du_hotel);
						if(this.du_hotel){
							let hotel = res.data.list.filter(item => {
								return item.id == this.du_hotel
							})[0]
							if (hotel) {
								this.getHotel(hotel)
							} else {
								this.getHotel(res.data.list[0])
							}
							
						}else{
							
							if (last_hotel) {
								let hotel = res.data.list.filter(item => {
									return item.id == last_hotel
								})[0]
								if (hotel) {
									this.getHotel(hotel)
								} else {
									this.getHotel(res.data.list[0])
								}
							
							} else {
								this.getHotel(res.data.list[0])
							}
						}
					

						//
						this.$iBox.http('getSaleType', {
							shop_id: this.du_hotel?this.du_hotel:( last_hotel ? last_hotel : res.data.list[0].id)
						})({
							method: 'post'
						}).then(res => {
							if (res.data.length > 0) {
								this.getUnit(res.data[0].sign)
							} else {
								// 如果没有销售类型，默认设置为标准
								this.getUnit('standard')
							}
							

							uni.hideLoading()
						})

						// 查询酒店设置
						this.$iBox
							.http('getShopSetting', {
								shop_id: this.du_hotel?this.du_hotel:( last_hotel ? last_hotel : res.data.list[0].id)
							})({
								method: 'post'
							})
							.then(res => {

								let enter_time = res.data.filter(item => {

									return item.sign == 'enter_time'
								})[0].property.value

								enter_time = Number(enter_time.split(':')[0])
								console.log(enter_time, 'ente');
								// 初始化日期
								// 获取每天的实时时间，如果时间小于当天这代表过期弹框提示重新刷新页面
								let date = {
									startDate: '',
									endDate: ''
								}
								// 判断是否为凌晨
								if (0 <= this.$moment().get('hours') && this.$moment().get('hours') <=
									enter_time) {
									date.startDate = this.$moment().subtract(1, 'day').startOf('day').unix()
									date.endDate = this.$moment().startOf('day').unix()
								} else {
									date.startDate = this.$moment().startOf('day').unix()
									date.endDate = this.$moment().add(1, 'day').startOf('day').unix()
								}

								this.getChooseDate(date)


								this.getShopSetting(res.data)
								this.$isResolve();
							})
					} else {
						uni.showModal({
							title: '提示',
							content: '酒店暂未上线',
							showCancel: false,
							success() {

							}
						})
					}


				})
			}
		},
		onHide: function() {
			// console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	@import "./static/iconfont.css";

	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	view {
		box-sizing: border-box;
	}

	//设置圆角
	checkbox.round .wx-checkbox-input,
	checkbox.round .uni-checkbox-input {
		border-radius: 100upx;
	}

	//设置背景色
	checkbox.red[checked] .wx-checkbox-input,
	checkbox.red.checked .uni-checkbox-input {
		background-color: #ffffff !important; //背景
		border-color: #ffffff !important; //边框
		color: #2681FB !important; //对勾
	}
</style>