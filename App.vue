<script>
	import {
		mapState,
		mapGetters,
		mapMutations,
		mapActions
	} from 'vuex';
	import { batchRequest, getRequestCache, setRequestCache } from './flyio/request'
	import loadingManager from './utils/LoadingManager'

	export default {
		data(){
			return {
				du_hotel:'',
				isInitialized: false, // 是否已初始化
				initPromise: null // 初始化Promise，避免重复初始化
			}
		},
		async onLaunch(options) {
			// 避免重复初始化
			if (this.isInitialized || this.initPromise) {
				return this.initPromise
			}

			// 创建初始化Promise
			this.initPromise = this.initializeApp(options)

			try {
				await this.initPromise
				this.isInitialized = true
				console.log('应用初始化完成')
			} catch (error) {
				console.error('应用初始化失败:', error)
				// 初始化失败时重置状态，允许重试
				this.isInitialized = false
				this.initPromise = null
			}
		},

		// 优化后的应用初始化方法
		async initializeApp(options) {
			console.log('开始应用初始化...')

			// 解析启动参数
			const { du, shop_id } = this.parseStartupParams(options)

			// 处理分享ID
			if (options.query && options.query.share_id) {
				this.getShareId(options.query.share_id)
			}

			// 显示初始化loading
			loadingManager.show({ title: '初始化中...', delay: 100 })

			try {
				// 第一步：登录认证
				const userInfoData = await this.performLogin(shop_id)

				// 检查用户状态
				if (userInfoData.data.block) {
					uni.reLaunch({ url: '/pages/mainPage/mainPage' })
					return
				}

				// 存储用户信息
				this.toLogin(userInfoData.data)
				const last_hotel = userInfoData.data.last_hotel_id

				// 第二步：并行执行非关键请求
				const parallelTasks = [
					{ url: 'getUI', params: {}, config: { skipLoading: true } },
					{ url: 'getSetting', params: {}, config: { skipLoading: true } },
					{ url: 'getUserInfo', params: { simple: false }, config: { skipLoading: true } }
				]

				// 处理分销用户
				if (du) {
					parallelTasks.push({
						url: 'updateDistributionUserId',
						params: { distribution_user_id: du },
						config: { skipLoading: true }
					})
				}

				const results = await batchRequest(parallelTasks)

				// 处理并行请求结果
				this.processParallelResults(results, userInfoData.data, du)

				// 第三步：获取位置和酒店信息
				await this.initializeLocationAndHotels(last_hotel)

			} catch (error) {
				console.error('初始化过程中出错:', error)
				// 尝试使用缓存数据
				this.loadFromCache()
				throw error
			} finally {
				loadingManager.hide()
			}
		},

		// 解析启动参数
		parseStartupParams(options) {
			let du = '' // 分销id参数
			let shop_id = ''

			const scene = wx.getEnterOptionsSync()
			console.log('启动参数:', options, scene)

			// 处理扫码进入的场景
			if (scene.query.scene && [1047, 1048, 1049, 1007, 1008].includes(scene.scene)) {
				const query = decodeURIComponent(scene.query.scene)
				if (query.includes("du")) {
					du = this.$iBox.linkFormat(query, "du")
				}
				if (query.includes("shop_id")) {
					shop_id = this.$iBox.linkFormat(query, "shop_id")
				}
			}

			// 处理二维码带参数
			if (options.query && options.query.scene) {
				const query = decodeURIComponent(options.query.scene)
				if (query.includes("du")) {
					du = this.$iBox.linkFormat(query, "du")
				}
			}

			return { du, shop_id }
		},

		// 执行登录
		async performLogin(shop_id) {
			return new Promise((resolve, reject) => {
				wx.login({
					success: res => {
						if (res.code) {
							const { appId } = wx.getAccountInfoSync().miniProgram

							this.$iBox.http('login', {
								code: res.code,
								app_id: appId,
								shop_id: shop_id
							})({
								method: 'post',
								skipLoading: true
							}).then(resolve).catch(reject)
						} else {
							reject(new Error('获取登录code失败'))
						}
					},
					fail: reject
				})
			})
		},

		// 处理并行请求结果
		processParallelResults(results, userInfoData, du) {
			results.forEach((result, index) => {
				if (result.status === 'fulfilled' && result.value && !result.value.error) {
					const data = result.value

					switch (index) {
						case 0: // getUI
							this.processUIData(data)
							break
						case 1: // getSetting
							this.getSetting(data.data)
							break
						case 2: // getUserInfo
							const userInfo = data.data
							userInfo.session_key = userInfoData.session_key
							this.updateUserInfo(userInfo)
							break
						case 3: // updateDistributionUserId (如果存在)
							if (du && data.data) {
								this.du_hotel = data.data.shop_id
							}
							break
					}
				} else {
					console.warn(`并行请求失败 [${index}]:`, result.reason || result.value?.error)
				}
			})
		},

		// 处理UI数据
		processUIData(data) {
			if (data.data && data.data.control_component) {
				data.data.control_component.forEach(item => {
					if (item.sign === 'tab') {
						const tabbar = item.property.filter(item1 => item1.status === 1)
						this.toTabbar(tabbar)
					}
					if (item.sign === 'popover') {
						this.toPop(item.property)
					}
				})

				if (data.data.subject) {
					this.toThemeColor(data.data.subject.style)
				}
				if (data.data.copy_right) {
					this.toCopyRight(data.data.copy_right)
				}
			}
		},

		// 初始化位置和酒店信息
		async initializeLocationAndHotels(last_hotel) {
			return new Promise((resolve) => {
				uni.getLocation({
					type: 'wgs84',
					success: (res) => {
						console.log('获取位置成功:', res)
						this.getCityAndHotel(res.latitude, res.longitude, last_hotel)
						resolve()
					},
					fail: (error) => {
						console.log('获取位置失败:', error)
						// 位置获取失败时使用空坐标
						this.getCityAndHotel('', '', last_hotel)
						resolve()
					}
				})
			})
		},

		// 从缓存加载数据（降级方案）
		loadFromCache() {
			console.log('尝试从缓存加载数据...')

			// 尝试加载缓存的UI配置
			const cachedUI = getRequestCache('getUI', {})
			if (cachedUI) {
				this.processUIData(cachedUI)
				console.log('从缓存加载UI配置成功')
			}

			// 尝试加载缓存的设置
			const cachedSetting = getRequestCache('getSetting', {})
			if (cachedSetting) {
				this.getSetting(cachedSetting.data)
				console.log('从缓存加载设置成功')
			}

			// 尝试加载缓存的用户信息
			const cachedUserInfo = getRequestCache('getUserInfo', { simple: false })
			if (cachedUserInfo) {
				this.updateUserInfo(cachedUserInfo.data)
				console.log('从缓存加载用户信息成功')
			}
		},
		onShow(options) {
			console.log("onshow--------", options);
			uni.getNetworkType({
				success: function (res) {
					if(res.networkType=='none'){
						uni.showModal({
							title:'请检查手机网络!',
							content:'手机网络断开，请重新启动小程序',
							confirmText:'重新启动',
							showCancel:false,
							success:res=>{
								if(res.confirm){
									wx.exitMiniProgram()
								}
							}
						})
					}
					console.log(res.networkType=='none','net');
					
				}
			});

			const updateManager = wx.getUpdateManager()

			updateManager.onUpdateReady(() => {
				wx.showModal({
					title: '更新提示',
					content: '新版本已经准备好，是否重启应用？',
					success: (res) => {
						if (res.confirm) {
							// 新的版本已经下载好，调用 applyUpdate 应用新版本并重启
							updateManager.applyUpdate()
						}
					}
				})
			})

			updateManager.onUpdateFailed(function() {
				// 新版本下载失败
				wx.showModal({
					title: '已经有新版本',
					content: '新版本已经准备好，请删除当前小程序重新进入！'
				})
			})
		},
		methods: {
			...mapActions('login', ['toLogin', 'updateUserInfo']),
			...mapActions('ui', ['toTabbar', 'toThemeColor', 'toPop', 'toCopyRight']),
			...mapActions('hotel', ['getUnit', 'getChooseDate', 'getHotelList', 'getHotel', 'getCity', 'getCityList',
				'getSetting', 'getShopSetting','getShareId'
			]),
			async getCityAndHotel(latitude, longitude, last_hotel) {
				console.log('开始获取城市和酒店信息...')

				try {
					// 并行请求城市和酒店列表
					const locationTasks = [
						{
							url: 'getShopCities',
							params: { latitude, longitude },
							config: { skipLoading: true }
						},
						{
							url: 'getShopList',
							params: { latitude, longitude, page: 1, limit: 10 },
							config: { skipLoading: true }
						}
					]

					const [cityResult, hotelResult] = await batchRequest(locationTasks)

					// 处理城市数据
					if (cityResult && !cityResult.error && cityResult.data?.length > 0) {
						this.processCityData(cityResult.data)
					}

					// 处理酒店数据
					if (hotelResult && !hotelResult.error && hotelResult.data?.list?.length > 0) {
						await this.processHotelData(hotelResult.data.list, last_hotel)
					} else {
						// 没有酒店数据时显示提示
						uni.showModal({
							title: '提示',
							content: '酒店暂未上线',
							showCancel: false
						})
					}

				} catch (error) {
					console.error('获取城市和酒店信息失败:', error)
					// 尝试使用缓存数据
					this.loadLocationFromCache()
				}
			},

			// 处理城市数据
			processCityData(cityData) {
				const allCities = []
				cityData.forEach(item => {
					if (item.data) {
						item.data.forEach(city => allCities.push(city))
					}
				})

				if (allCities.length > 0) {
					// 找到距离最近的城市
					const minDistance = Math.min(...allCities.map(obj => obj.distance || 0))
					const nearestCity = allCities.find(item =>
						item.distance ? item.distance === minDistance : !item.distance
					)

					if (nearestCity) {
						this.getCity(nearestCity)
					}
					this.getCityList(cityData)
				}
			},

			// 处理酒店数据
			async processHotelData(hotelList, last_hotel) {
				this.getHotelList(hotelList)

				// 选择酒店逻辑
				let selectedHotel = null
				const targetHotelId = this.du_hotel || last_hotel

				if (targetHotelId) {
					selectedHotel = hotelList.find(hotel => hotel.id == targetHotelId)
				}

				// 如果没找到指定酒店，使用第一个
				if (!selectedHotel && hotelList.length > 0) {
					selectedHotel = hotelList[0]
				}

				if (selectedHotel) {
					this.getHotel(selectedHotel)

					// 并行获取酒店相关信息
					await this.loadHotelRelatedData(selectedHotel.id)
				}
			},

			// 加载酒店相关数据
			async loadHotelRelatedData(shopId) {
				try {
					const hotelTasks = [
						{
							url: 'getSaleType',
							params: { shop_id: shopId },
							config: { skipLoading: true }
						},
						{
							url: 'getShopSetting',
							params: { shop_id: shopId },
							config: { skipLoading: true }
						}
					]

					const [saleTypeResult, shopSettingResult] = await batchRequest(hotelTasks)

					// 处理销售类型
					if (saleTypeResult && !saleTypeResult.error) {
						if (saleTypeResult.data?.length > 0) {
							this.getUnit(saleTypeResult.data[0].sign)
						} else {
							this.getUnit('standard')
						}
					}

					// 处理酒店设置
					if (shopSettingResult && !shopSettingResult.error) {
						this.processShopSetting(shopSettingResult.data)
					}

				} catch (error) {
					console.error('加载酒店相关数据失败:', error)
					// 使用默认值
					this.getUnit('standard')
				} finally {
					// 标记初始化完成
					this.$isResolve && this.$isResolve()
				}
			},

			// 处理酒店设置
			processShopSetting(settingData) {
				try {
					const enterTimeSetting = settingData.find(item => item.sign === 'enter_time')
					let enterTime = 14 // 默认下午2点

					if (enterTimeSetting && enterTimeSetting.property?.value) {
						enterTime = Number(enterTimeSetting.property.value.split(':')[0])
					}

					// 计算日期
					const currentHour = this.$moment().get('hours')
					let date = {}

					if (currentHour >= 0 && currentHour <= enterTime) {
						// 凌晨时段，日期向前推一天
						date.startDate = this.$moment().subtract(1, 'day').startOf('day').unix()
						date.endDate = this.$moment().startOf('day').unix()
					} else {
						// 正常时段
						date.startDate = this.$moment().startOf('day').unix()
						date.endDate = this.$moment().add(1, 'day').startOf('day').unix()
					}

					this.getChooseDate(date)
					this.getShopSetting(settingData)

				} catch (error) {
					console.error('处理酒店设置失败:', error)
					// 使用默认日期
					const defaultDate = {
						startDate: this.$moment().startOf('day').unix(),
						endDate: this.$moment().add(1, 'day').startOf('day').unix()
					}
					this.getChooseDate(defaultDate)
				}
			},

			// 从缓存加载位置信息（降级方案）
			loadLocationFromCache() {
				console.log('尝试从缓存加载位置信息...')

				const cachedCities = getRequestCache('getShopCities', {})
				const cachedHotels = getRequestCache('getShopList', { page: 1, limit: 10 })

				if (cachedCities && cachedCities.data) {
					this.processCityData(cachedCities.data)
				}

				if (cachedHotels && cachedHotels.data?.list) {
					this.processHotelData(cachedHotels.data.list, '')
				}
			}
		},
		onHide: function() {
			// console.log('App Hide')
		}
	}
</script>

<style lang="scss">
	@import "./static/iconfont.css";

	/* 注意要写在第一行，同时给style标签加入lang="scss"属性 */
	view {
		box-sizing: border-box;
	}

	//设置圆角
	checkbox.round .wx-checkbox-input,
	checkbox.round .uni-checkbox-input {
		border-radius: 100upx;
	}

	//设置背景色
	checkbox.red[checked] .wx-checkbox-input,
	checkbox.red.checked .uni-checkbox-input {
		background-color: #ffffff !important; //背景
		border-color: #ffffff !important; //边框
		color: #2681FB !important; //对勾
	}
</style>