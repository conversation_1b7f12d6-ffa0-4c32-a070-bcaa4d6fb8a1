<template>
	<view>
		<rich-text :nodes="strings"></rich-text>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name:"m-richBox",
		data() {
			return {
				strings:'',
			};
		},
		props:{
			contents:{
				type:String,
				default:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		mounted() {
			console.log(this.content);
			this.strings =  this.$iBox.formatRichText(this.contents)
		},
	}
</script>

<style lang="scss">

</style>