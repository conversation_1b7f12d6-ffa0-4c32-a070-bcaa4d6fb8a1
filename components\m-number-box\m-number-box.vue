<template>
	<view class="fx-numbox">
		<view class="fx-numbox__minus" @tap="_calcValue('minus')" @longpress="_longTapCalcValue('minus')"
			@touchend="_touchend" @touchcancel="_touchcancel ">
			<view class="icon-24gl-minusCircle"
				:style="inputValue > min?'color:'+themeColor.main_color:'color:#9d9d9d'"></view>
		</view>
		<text class="fx-numbox__value" type="digit" @click="onInput">{{inputValue}}间</text>
		<view class="fx-numbox__plus" @tap="_calcValue('plus')" @longpress="_longTapCalcValue('plus')"
			@touchend="_touchend" @touchcancel="_touchcancel ">
			<view class="icon-jiahao" :style="{color:themeColor.main_color}">
			</view>
		</view>

		<!-- 房间数量 -->
		<m-popup :show="pop" @closePop="closePop">
			<view class="roomNumberBox">
				<view class="icon-close" style="position: absolute;top: 20rpx;right: 20rpx;font-size: 44rpx;" @click="closePop">
					
				</view>
				<view class="" style="height: 60rpx;display: flex;justify-content: center;">
					<p style="font-size: 34rpx;font-weight: 500;">
						选择间数
					</p>
				</view>
				
				<scroll-view scroll-y="true" style="height: 540rpx;" :scroll-into-view="scrollId"
					scroll-with-animation="true">
					<view class="number__box">
						<view class="box__item" v-for="item in max" @click="chooseNum(item+1)">
							<view class="item__det" :class="item+1==inputValue?'choose':''" >
								{{item+1}}
							</view>
						</view>
						
					</view>
					<view class="" style="height: 80rpx;width:100% ;">
						
					</view>
				</scroll-view>
			</view>
		</m-popup>
	</view>
</template>
<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	var timer = null;
	export default {
		name: 'm-number-box',
		props: {
			min: {
				type: Number,
				default: 0
			},
			max: {
				type: Number,
				default: 999
			},
			step: {
				type: Number,
				default: 1
			}
		},
		data() {
			return {
				inputValue: 1,
				pop: false
			}
		},
		mounted() {
			console.log(this.min, this.max,this.inputValue);
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'unit', 'startDate', 'endDate', 'roomInfo', 'linkMan', 'shopSetting']),
		},
		watch: {

		},
		methods: {
			closePop() {
				this.pop = false
			},
			_calcValue(type) {
				const scale = this._getDecimalScale();
				let value = this.inputValue * scale
				let step = this.step * scale
				if (type === 'minus') {
					value -= step
					if (value < this.min) {
						value = this.min
						uni.showToast({
							icon:'none',
							title:'至少需要选择1间房间'
						})
					}
				} else if (type === 'plus') {
					value += step
					if (value > this.max) {
						value = this.max
						uni.showToast({
							icon:'none',
							title:'已选择最大房间数量'
						})
					}
				}
				this.inputValue = Math.floor((value / scale).toFixed(1) * 10) / 10;
				this.$emit('change', this.inputValue);
			},
			onInput(e) {
				this.pop = true
				
			},
			chooseNum(e){
				this.inputValue = Math.floor((e).toFixed(1) * 10) / 10;
				this.$emit('change', this.inputValue);
				this.closePop()
			},
			_longTapCalcValue(type) {
				var _this = this;
				timer = setInterval(function() {
					const scale = _this._getDecimalScale()
					let value = _this.inputValue * scale
					let step = _this.step * scale
					if (type === 'minus') {
						value -= step
						if (value < _this.min) {
							value = _this.min;
							clearInterval(timer)
							return false
						}
					} else if (type === 'plus') {
						value += step
						if (value > _this.max) {
							value = _this.max
							clearInterval(timer)
							return false
						}
					}
					_this.inputValue = Math.floor((value / scale).toFixed(1) * 10) / 10;
					_this.$emit('change', _this.inputValue);
				}, 150)
			},
			_touchend() {
				clearInterval(timer)
			},
			_touchcancel() {
				clearInterval(timer)
			},
			_getDecimalScale() {
				let scale = 1
				// 浮点型
				if (~~this.step !== this.step) {
					scale = Math.pow(10, (this.step + '').split('.')[1].length)
				}
				return scale
			},

		},
		created() {

		}
	}
</script>
<style lang="scss">
	.fx-numbox {
		display: inline-flex;
		flex-direction: row;
		justify-content: flex-start;
		height: 60rpx;
		position: relative;
		align-items: center;
		padding: 2rpx;
		border-radius: 30rpx;
		background-color: #f3f8fe;

		&__minus,
		&__plus {
			margin: 0;
			width: 60rpx;
			height: 60rpx;
			display: flex;
			justify-content: center;
			align-items: center;
			font-size: 40rpx;
			font-weight: 500;
		}

		&__value {
			width: fit-content;
			padding: 20rpx;
			text-align: center;
			padding: 0;
		}
	}

	.roomNumberBox {
		height: 600rpx;
		padding: 20rpx;
		
		position: relative;
		
		.number__box{
			height: auto;
			width: 100%;
			display: flex;
			flex-wrap: wrap;
			
			.box__item {
				width: 25%;
				height: 80rpx;
				border-radius: 6rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-top: 10rpx;
				.item__det {
					width: 90%;
					height: 94%;
					background: #f3f8fe;
					display: flex;
					align-items: center;
					justify-content: center;
				}
				
				.choose{
					width: 90%;
					height: 94%;
					background: #d2e6fe;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #77a0fe;
				}
			}
		}
	}
</style>
