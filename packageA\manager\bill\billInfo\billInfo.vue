<template>
	<view>
		<view class="roomListBox">
			<scroll-view scroll-x="true" style="width: 100%;height: 100%;">
				<view class="roomListBoxContent">
					<view class="roomListBoxContent_item"
						:style="item.bill_id==chooseId?'color:#FFF;background:'+themeColor.main_color:'background:#e6e6e6'"
						v-for="(item,index) in roomInfo" :key="index" @click="changeRoom(item)">
						<view
							style="font-weight: 600;display: flex;align-items: center;justify-content: center;width: 150rpx;">
							<view style="background: #00aa00;height: 18rpx;border-radius: 50%;"
								v-if="item.main_room==1"></view> <text>{{item.room_number}}</text><text
								v-if="item.main_room==1">(主)</text>
						</view>
					</view>
				</view>
			</scroll-view>
		</view>
		<view class="" style="height: 60rpx;width: 100%;padding: 20rpx;"
			v-if="billDetail&&billDetail.connect_bills&&billDetail.connect_bills.length > 1" @click="getConnectBill">
			<text>查看联房财务明细</text>
			<text style="padding-left: 20rpx;"
				:style="billDetail&&billDetail.connect_bills_balance>0?'color:#00aa00':'color:red'">余额:{{billDetail.connect_bills_balance.toFixed(2)}}</text>
		</view>
		<view class="typeList">
			<view class="" style="width: 400rpx;margin: 0 auto;">
				<uni-segmented-control activeColor="#227aff" :current="current" :values="items" @clickItem="onClickItem"
					styleType="button"></uni-segmented-control>
			</view>

			<view class="content">
				<view v-show="current === 0">
					<!-- 信息 -->
					<view class="roomInfo">
						<view class="item" style="width: 20%;color: darkgray;font-size: 20;">
							<text class="item_text">id:</text>
							<text>{{billDetail.id}}</text>
						</view>

						<view class="item"
							style="width: 70%;display: flex;justify-content: flex-end;color: darkgray;font-size: 20;">
							<text class="item_text">订单号:</text>
							<text>{{billDetail.bill_code}}</text>
						</view>
						<view class="item">
							<text class="item_text">订单状态:</text>
							<text>{{billFormat(billDetail&&billDetail.bill_status)}}</text>
						</view>
						<view class="item">
							<text class="item_text">房间号:</text>
							<text>{{billDetail.room_number}}</text>
						</view>

						<view class="item">
							<text class="item_text">房型:</text>
							<text>{{billDetail.room_type_name}}</text>
						</view>
						<view class="item">
							<text class="item_text">总房价:</text>
							<text>{{billDetail.room_amount}}</text>
						</view>
						<view class="item" style="width: 30%;">
							<text class="item_text">押金:</text>
							<text>{{billDetail.cash_pledge}}</text>
						</view>


						<view class="item" style="width: 30%;">
							<text class="item_text">入住人数:</text>
							<text>{{billDetail.user_count}}</text>
						</view>
						<view class="item" style="width: 30%;">
							<text class="item_text">余额:</text>
							<text>{{billDetail.bill_balance}}</text>
						</view>


						<view class="item" style="width: 100%;" v-for="item in billDetail.users">
							<text class="item_text">{{item.is_main?'入住人':'同住人'}}:</text>
							<text style="padding: 0 30rpx;">{{item.name}}</text>
							<text style="padding: 0 0 0 30rpx;">{{item.phone}}</text>
							<text style="padding: 0 30rpx 0 0;color: midnightblue;" @click="callPhone(item.phone)"
								class="icon-dianhua"></text>
							<text class="" style="color: #00aa00;font-weight: 600;" @click="pushPassWord(item)"
								v-if="billDetail&&billDetail.hardware_list.lock_list.length>0">
								发送密码
							</text>
						</view>
						<view class="item" style="width: 100%;"
							v-if="billDetail.bill_status==2||billDetail.bill_status==3||billDetail.bill_status==5">
							<text class="item_text">预住时间:</text>
							<text>{{billDetail.enter_time_plan | moment2}}</text>
						</view>
						<view class="item" style="width: 100%;"
							v-if="billDetail.bill_status==4||billDetail.bill_status==5">
							<text class="item_text">实住时间:</text>
							<text v-if="billDetail.enter_time>0">{{billDetail.enter_time | moment2}}</text>
							<text v-if="billDetail.enter_time == 0">暂未实住</text>
						</view>
						<view class="item" style="width: 100%;">
							<text class="item_text">预离时间:</text>
							<text>{{billDetail.leave_time_plan | moment2}}</text>
						</view>
						<view class="item" style="width: 70%;"
							v-if="billDetail.bill_status==5||billDetail.bill_status==10">
							<text class="item_text">实离时间:</text>
							<text v-if="billDetail.leave_time>0">{{billDetail.leave_time | moment2}}</text>
							<text v-if="billDetail.leave_time == 0">暂未离店</text>
						</view>
						<view class="item" style="width: 50%;display: flex;" v-if="(billDetail.room_sale_type_sign == 'standard' || billDetail.room_sale_type_sign ==
					'long_standard')">
							<text class="item_text">入住间夜:</text>
							<text>{{stayTimeNumber()}}</text><text
								v-if="billDetail.bill_status==4">/{{toDayNumber()}}</text>
						</view>
						<view class="item" tyle="width: 140rpx;">
							<text class="item_text">联系人:</text>
							<text>{{billDetail.link_man}}</text>
						</view>

						<view class="item" style="width: 350rpx;">
							<text class="item_text">联系电话:</text>
							<text>{{billDetail.link_phone}}</text>
						</view>
						<view class="item">
							<text class="item_text">订单来源:</text>
							<text>{{billDetail.bill_source_name}}</text>
						</view>
						<view class="item">
							<text class="item_text">销售类型:</text>
							<text>{{billDetail.room_sale_type_name}}</text>
						</view>
						<view class="item">
							<text class="item_text">优惠券:</text>
							<text>-{{billDetail.amount_reduction}}</text>
						</view>
						<view class="item">
							<text class="item_text">价格方案:</text>
							<text>{{billDetail.grade_name}}</text>
						</view>
						<view class="item">
							<text class="item_text">备注:</text>
							<text>{{billDetail.memo?billDetail.memo:''}}</text>
						</view>
						<view class="item">
							<text class="item_text">套餐:</text>
							<text>{{billDetail.room_service.service_name}}</text>
						</view>
					</view>

					<!-- 操作按钮 -->
					<view class="btn_list">

						<button size="mini" type="primary" @click.stop="sureAccess(billDetail)"
							v-if="billDetail.bill_status == 2" style="margin-right: 10rpx;">确认接受订单</button>
						<button size="mini" type="warn" @click.stop="cancelAccess(billDetail)"
							v-if="billDetail.bill_status == 2||billDetail.bill_status == 3"
							style="margin-right: 10rpx;">主动取消</button>
						<button size="mini" type="primary" @click.stop="selectRoom(billDetail)"
							v-if="billDetail.bill_status == 3" style="margin-right: 10rpx;">排房</button>
						<button size="mini" type="primary" @click.stop="ordingToCheckIn(billDetail)"
							v-if="billDetail.bill_status == 3" style="margin-right: 10rpx;">预订转入住</button>
						<button size="mini" type="warn" @click.stop="noShowAccess(billDetail)"
							v-if="billDetail.bill_status == 3"
							style="margin-right: 10rpx;margin-top: 10rpx;">noShow</button>
						<button size="mini" type="primary" @click.stop="continueRoom(billDetail)"
							v-if="billDetail.bill_status == 4" style="margin-right: 10rpx;">续房</button>
						<button size="mini" type="primary" @click.stop="checkOutRoom(billDetail)"
							v-if="billDetail.bill_status == 4" style="margin-right: 10rpx;">退房</button>
						<button size="mini" type="primary" @click.stop="exChangeRoom(billDetail)"
							v-if="(billDetail.bill_status == 3&&billDetail.room_number)||billDetail.bill_status == 4"
							style="margin-right: 10rpx;">换房</button>
					</view>

					<!-- 财务内容 -->
					<view class="fundBox">
						<view class="account">
							<view class="" style="font-size: 44rpx;font-weight: 600;color:#aa0000 ">
								余额: {{billDetail.bill_balance}}
							</view>
							<view class="amountBox">
								<p style="color:red;margin-bottom: 20rpx;">消费合计:{{fund.consume_amount}}</p>
								<p style="color:green">收款合计:{{fund.pay_amount}}</p>
							</view>
						</view>
						<view class="btnBoxList">
							<view class="btnClass btnbg" @click="toPayTh(billDetail.bill_balance)"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								收款
							</view>
							<view class="btnClass btnbg" @click="toGuaTh(billDetail.bill_balance)"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								挂账
							</view>
							<view class="btnClass btnbg" @click="toConsumeTh"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								记消费
							</view>
							<view class="btnClass1 btnbg" @click="toTuiTh(1)"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								原路退款
							</view>
							<view class="btnClass1 btnbg" @click="toSelfTuiTh(1)"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								自定义退款
							</view>
							<view class="btnClass btnbg" @click="toModifyTh()"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								冲调
							</view>
							<view class="btnClass btnbg" @click="toFreeChargeTh()"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								免单
							</view>
							<view class="btnClass btnbg" @click="toNoClearCheckOutTh()"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4">
								走结
							</view>
							<view class="btnClass btnbg" @click="clearBillTh()"
								v-if="billDetail.bill_status == 3||billDetail.bill_status == 4 || billDetail.bill_status == 10">
								结账
							</view>
							<view class="btnClass btnbg" style="background-color: #aa0000;" @click="finishBillTh()"
								v-if="billDetail.bill_status == 5">
								转为走结状态
							</view>
						</view>
					</view>
					<view class="" style="">
						<view class="" style="height: 160rpx;padding: 20rpx;display: flex;align-items: center;">
							<text>类型:</text>
							<view class="btnClass" @click="chooseType(item)" v-for="(item, index) in chooseTypeList"
								:key="index"
								:style="chooseTypeId==item.id?'border:1px solid #0073ff;color:#0073ff;':''">
								<text>{{item.name}}</text>
							</view>
						</view>
						<view class="itemBox" style="background-color: #ffffff;" v-if="chooseTypeId">
							<p style="font-size: 28rpx;">账务项目</p>
							<picker @change="bindChange" :value="changeIndex" range-key="name" :range="groupTypeList">
								<view class="pickerBox">
									{{groupTypeList[changeIndex].name}}
									<view class="icon-down"
										style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
									</view>
								</view>
							</picker>
						</view>
						<view class="itemBoxPay" v-for="(item, index) in fundList" :key="item.id">
							<view class=""
								style="display: flex;flex-wrap: wrap;width: 600rpx;font-size: 24rpx;margin: 20rpx 0;">
								<view class="itemText">流水ID：{{item.id}}</view>
								<view class="itemText">房间：{{item.room_number}}</view>
								<view class="itemText" v-if="item.account_name">
									动账账户：{{item.account_name?item.account_name:''}}</view>
								<view class="itemText">账务项目：{{item.detail_type_name}}</view>
								<view class="itemText" v-if="item.account_type">
									我方收款：{{item.account_type?item.amount:''}}</view>
								<view class="itemText" v-if="!item.account_type">
									客户消费：{{!item.account_type?item.amount:''}}</view>
								<view class="itemText">业务说明：{{item.remark}}</view>
								<view class="itemText">操作人：{{item.admin_name?item.admin_name:'系统生成'}}</view>
								<view class="itemText">时间：{{item.create_time | moment1}}</view>
								<view class="itemText">备注：{{item.memo}}</view>
							</view>
						</view>
					</view>
				</view>
				<view v-show="current === 1" style="position: relative;">
					<m-tabs :list="logList" style="" @tabClick="tab_click" :activeIndex="currentTab" :config="{color:themeColor.text_main_color,
									  fontSize:30,
									  activeColor:themeColor.com_color1,
									  underLineColor:themeColor.com_color1,
									  underLineWidth:0,
									  underLineHeight:0}">
					</m-tabs>

					<view class="logBox">
						<view class="logItem" v-for="(item, index) in logDetailList" :key="index">
							<p style="font-size:24rpx;color: #333;">{{item.create_time | moment1}}</p>
							<p>{{item.title}}</p>
							<p>{{item.content}}</p>
							<p>ip:{{item.ip}}</p>
							<p style="font-size: 30rpx;font-weight: 600;">操作人:{{item.admin_name}}</p>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 排房 -->
		<m-popup :show="popSelect" @closePop="closePopSelect" mode="center">
			<scroll-view scroll-y="true" class="selectRoom">
				<p>批量排房:</p>
				<view v-for="(item, index) in connectBill" :key="index"
					style="height: 100rpx;width: 100%;display: flex;align-items: center;justify-content: space-between;">
					<text>房型:{{item.room_type_name}}</text>
					<text>房号:{{item.room_number}}</text>
					<view class="" style="width: 120rpx;" @click="toSelect(item)">
						<view class="btnClass">
							排房
						</view>
					</view>
				</view>
			</scroll-view>


		</m-popup>
		


		<!-- 选房弹窗 -->
		<mExchangeRoom :roomList="selectRoomList" :ids="chooseIds" :num="itemPrice" :rooms="chooseRooms"
			:poprc="showExRc" @closeZj="closeExRc" @sureRc="getExRcIds"></mExchangeRoom>

		<!-- 换房弹窗 -->
		<mExchangeBillRoom :roomList="selectRoomList" :ids="chooseIds" :num="itemPrice" :rooms="chooseRooms"
			:poprc="showExBillRc" @closeZj="closeExBillRc" @sureRc="getExBillRcIds"></mExchangeBillRoom>

		<!-- 预订转入住 -->
		<m-popup :show="popBookToIn" @closePop="closePopBookToIn">
			<view class="bookIn" style="">
				<scroll-view scroll-y="true" style="height: 100%;width: 100%;">
					<p style="margin: 20rpx auto;font-weight: 600;display: flex;justify-content: center;">预订转入住</p>
					<checkbox-group @change="checkboxBookToIn">
						<view class="bookBill" :style="{background:themeColor.com_color1+'40'}">
							<view class="item" v-for="(item, index) in connectBill" :key="index"
								v-if="item.room_number">
								<label style="width: 80rpx;height: 100%;">
									<checkbox style="transform:scale(0.7)" :checked="false" :value="item.id" />
								</label>
								<view class="item_roomBox">
									<view class="item_roomBox_title">
										<view class="item_roomBox_title_text">{{item.room_type_name}}</view>
										<view class="item_roomBox_title_text">房号:{{item.room_number}}</view>
										<view class="item_roomBox_title_text" >房价:￥{{item.room_date_price[0].room_price}}
										</view>
										<text style="font-size: 24rpx;color: blue;"
											@click="changePrice(item)">多日房价</text>
									</view>
									<view class="">
										<view class="item_msg" v-for="(item1, index1) in item.userMsg" :key="index1">
											<input type="text" v-model="item1.name" style="width: 140rpx;"
												class="item_msg_inpt" placeholder="姓名">
											<view class="msgItem"
												@click.stop="getGender({'item':item,'index1':index1})">
												<picker @change.stop="bindChange1" :value="changeIndex1"
													range-key="name" :range="genderList">
													<view class="pickerBox">
														{{item1.gender==0?'保密':(item1.gender==1?'男':'女')}}
														<view class="icon-down"
															style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
														</view>
													</view>
												</picker>
											</view>
											<input type="number" v-model="item1.phone" style="width: 300rpx;"
												class="item_msg_inpt" placeholder="电话">
											<view class="msgItem"
												@click.stop="getGender({'item':item,'index1':index1})">
												<picker @change.stop="bindChange2" :value="changeIndex2"
													range-key="name" :range="cardList">
													<view class="pickerBox">
														{{item1.identification_type==1?'身份证':(item1.identification_type==2?'港澳通行证':(item1.identification_type==3?'驾驶证':(item1.identification_type==4?'军官证':(item1.identification_type==5?'护照':'台湾身份证'))))}}
													</view>
												</picker>
											</view>
											<input type="idcard" v-model="item1.identification_number"
												style="width: 340rpx;margin-left: 10rpx;" class="item_msg_inpt"
												placeholder="证件号码">
											<view class="icon-tianjia" style="font-size: 40rpx;margin:0 20rpx;"
												@click="addUserMsg(item)">
											</view>
											<image src="../../../../static/images/jian.png"
												style="width: 44rpx;height:44rpx"
												@click="reduceUserMsg({'item':item,'index1':index1})" mode=""
												v-if="item.userMsg&&item.userMsg.length>1"></image>
										</view>
									</view>

								</view>
							</view>
						</view>
					</checkbox-group>
					<view class="" style="height: 120rpx;">

					</view>
				</scroll-view>
				<view class=""
					style="position: fixed;bottom: 0;width: 100%;height: 120rpx;display: flex;align-items: center;justify-content: center;">
					<view class="" @click="sureCheckInTh"
						style="width: 500rpx;height: 80rpx;border-radius: 16rpx;display: flex;align-items: center;justify-content: center;font-size: 38rpx;"
						:style="{background:themeColor.main_color }">
						确认
					</view>
				</view>

			</view>
		</m-popup>
		
		<!-- 价格弹窗 -->
		<m-popup :show="popPrice" @closePop="closepopPrice">
			<view class="" style="height: 80vh;width: 100%;padding: 30rpx;">
				<scroll-view scroll-y="true" style="height:100%;position: relative;">
					<view class="" v-for="(item3, index3) in pricesBox" :key="index3" style="margin-top: 30rpx;">
						<view class=""
							style="display: flex;align-items: center;height: 50rpx;justify-content: space-between;">
							<text>{{item3.date}}:</text>
							<view class="" style="width: 400rpx;height: 44rpx;display: flex;align-items: center;">
								<uni-easyinput :disabled="true" type="digit" v-model="item3.room_price" trim="all" @change="changePrice(item)" :clearable="false"></uni-easyinput>
								<!-- <uni-number-box v-model="item3.room_price" :disabled="true" max="1000000" :step="1" /> -->
							</view>
						</view>
		
					</view>
					<view class=""
						style="width: 100%;height: 60rpx;display: flex;align-items: center;justify-content: center;margin-top: 80rpx;">
						<view class=""
							style="width: 480rpx;height: 70rpx;border-radius: 30rpx;background-color: #2c7f08;color: #fff;display: flex;align-items: center;justify-content: center;"
							@click="closepopPrice">
							<text>确认</text>
						</view>
		
					</view>
				</scroll-view>
		
			</view>
		</m-popup>
		
		<!-- 续房 -->
		<m-popup :show="popContinue" @closePop="closePopContinue" mode="center">
			<view class="continueBox">
				<p style="padding: 30rpx 0;margin: 0 auto;display: flex;justify-content: center;">续房</p>
				<p>原离店时间: <text style="margin:0 20rpx;">{{billDetail.leave_time_plan | moment1}}</text></p>
				<view class="">
					<!-- 小时 -->
					<view class="msgItem" v-if="billDetail&&(billDetail.room_sale_type_sign=='hour'||billDetail.room_sale_type_sign=='conference')">
						<p style="margin-right: 8rpx;width: 160rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">续房时长</text></p>
						<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<text>{{billDetail.stay_time}}小时</text>
						</view>
					</view>
					<!-- 天数 -->
					<view class="msgItem" v-if="billDetail&&billDetail.room_sale_type_sign=='standard'">
						<p style="margin-right: 8rpx;width: 160rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">续房时长</text></p>
						<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<uni-number-box :min="1" v-model="dayCounts" @change="dayChange" :max="100000000000"></uni-number-box>天
						</view>
					</view>
					<!-- 月租 -->
					<view class="msgItem" v-if="billDetail&&billDetail.room_sale_type_sign=='long_standard'">
						<p style="margin-right: 8rpx;width: 160rpx;"><text style="color: red;">*</text><text
								style="font-size: 34rpx;">续房时长</text></p>
						<view class="" style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
							<uni-number-box :min="1" v-model="monthCounts" @change="monthChange" :max="100000000000"></uni-number-box>月
						</view>
					</view>
				</view>
				<!-- 离店时间 -->
				<view class="msgItem">
					<p style="margin-right: 8rpx;width: 160rpx;"><text style="color: red;">*</text><text
							style="font-size: 34rpx;">离店时间</text></p>
					<view class="" v-if="billDetail&&billDetail.room_sale_type_sign=='hour'||billDetail.room_sale_type_sign=='conference'"
						style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
						<text>{{datetimesingle1 | moment1}}</text>
					</view>

					<view class="" style="width: 480rpx;" v-if="billDetail.room_sale_type_sign=='standard'">
						<uni-datetime-picker :clear-icon="false" :start="endTime" type="datetime"
							v-model="datetimesingle1" @change="changeLog1" />
					</view>
					<view class="" v-if="billDetail&&billDetail.room_sale_type_sign=='long_standard'"
						style="width: 480rpx;padding-left: 20rpx;display: flex;align-items: center;">
						<text>{{datetimesingle1 | moment1}}</text>
					</view>
				</view>
				<!-- 续房方式 -->
				<view class="msgItem" style="font-size: 24rpx;">
					<p style="width: 100rpx;font-size: 28rpx;"><text style="color: red;">*</text><text
							style="">方式</text></p>
					<uni-data-checkbox v-model="radio1" :localdata="continueType"></uni-data-checkbox>
				</view>
				<!-- 续房价格 -->
				<view class="msgItem" style="font-size: 24rpx;" v-if="radio1==3">
					<p style="margin-right: 8rpx;width: 190rpx;"><text style="color: red;">*</text><text
							style="">自定义价格</text></p>
					<uni-easyinput type="digit"  v-model="selfPrice"  placeholder="请输入价格"></uni-easyinput>
				</view>
				<view class=""
					style="width: 100%;display: flex;align-items: center;justify-content: center;margin-top: 50rpx;">
					<view class="btnClass" @click="sureContinueTh" style="background: #00aa00;">
						确认续房
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 退房 -->
		<m-popup :show="popCheckOut" @closePop="closePopCheckOut" mode="center">
			<view class="checkOut">
				<p style="margin: 20rpx auto;width: 100%;display: flex;align-items: center;justify-content: center;">退房
				</p>
				<uni-easyinput type="textarea" v-model="checkReason" placeholder="退房备注"></uni-easyinput>
				<view class="billMounte" v-if="billDetail&&billDetail.bill_balance<0"
					:style="ifArrange?'border: 1px solid #55aa7f;background: #55aa7f33;':'border: 1px solid #ff5500;background: #ff550033;'">
					<p style="width: 335rpx;">本订单需要收款: {{Math.abs(billDetail.bill_balance)}}</p>
					<view class="btnClass" @click="toPayTh(billDetail.bill_balance)">
						收款
					</view>
					<view class="btnClass" @click="toGuaTh(billDetail.bill_balance)">
						挂账
					</view>
				</view>
				<view class="billMounte" v-if="billDetail&&billDetail.bill_balance>0"
					:style="ifArrange?'border: 1px solid #55aa7f;background: #55aa7f33;':'border: 1px solid #ff5500;background: #ff550033;'">
					<p style="width: 335rpx;">本订单需要退款: {{billDetail.bill_balance}}</p>
					<view class="btnClass1" @click="toTuiTh(1)">
						原路退款
					</view>
					<view class="btnClass1" @click="toSelfTuiTh(1)">
						自定义退款
					</view>
				</view>

				<view class="billMounte"
					v-if="billDetail&&billDetail.connect_bills&&billDetail.connect_bills.length > 1&&billDetail.connect_bills_balance<0"
					:style="ifArrange?'border: 1px solid #55aa7f;background: #55aa7f33;':'border: 1px solid #ff5500;background: #ff550033;'">
					<p style="width: 335rpx;">联房订单当前总计需要收款: {{Math.abs(billDetail.connect_bills_balance)}}
					</p>
					<view class="btnClass" @click="toPayTh(billDetail.bill_balance)">
						收款
					</view>
					<view class="btnClass" @click="toGuaTh(billDetail.bill_balance)">
						挂账
					</view>
				</view>
				<view class="billMounte"
					v-if="billDetail&&billDetail.connect_bills&&billDetail.connect_bills.length > 1&&billDetail.connect_bills_balance>0"
					:style="ifArrange?'border: 1px solid #55aa7f;background: #55aa7f33;':'border: 1px solid #ff5500;background: #ff550033;'">
					<p style="width: 335rpx;">联房订单当前总计需要退款: {{billDetail.connect_bills_balance}}
					</p>
					<view class="btnClass1" @click="toTuiTh(2)">
						原路退款
					</view>
					<view class="btnClass1" @click="toSelfTuiTh(2)">
						自定义退款
					</view>
				</view>
				<view class="billMounte"
					v-if="billDetail&&billDetail.connect_bills&&billDetail.connect_bills.length > 1&&ifArrange"
					:style="ifArrange?'border: 1px solid #55aa7f;background: #55aa7f33;':'border: 1px solid #ff5500;background: #ff550033;'">
					<p style="width: 100%;">当前为联房订单，还有其他房间在住，当前可直接退房，由其他房间结账
					</p>
				</view>
				<view class="" style="display: flex;justify-content: flex-end;">
					<view class="btnClass" @click="sureCheckOutTh"
						style="background: #00aa00;color: #ffffff;margin-top: 30rpx;">
						确认退房
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 记消费 -->
		<mconsume :poprc="showConsume" @upBillDetail="updateBill" @closePay="consumeClose" :bill="billDetail">
		</mconsume>

		<!-- 收银台 -->
		<mpayCenter :poprc="showPayCcenter" @upBillDetail="updateBill" @closePay="payClose" :money="billMoney"
			:bill="billDetail"></mpayCenter>

		<!-- 挂账 -->
		<monAccount :poprc="showOnAccount" @upBillDetail="updateBill" @closeAccount="onAccountClose" :money="billMoney"
			:bill="billDetail"></monAccount>

		<!-- 原路退款 -->
		<monCheckOutPay :poprc="showCheckOutPay" @upBillDetail="updateBill" @closeRefund="checkOutPayClose"
			:refundType="checkType" :money="billMoney" :bill="billDetail"></monCheckOutPay>

		<!-- 自定义退款 -->
		<mrefundSelf :poprc="showRefundSelf" @upBillDetail="updateBill" @closeRefund="refundSelfClose"
			:refundType="checkType" :bill="billDetail"></mrefundSelf>

		<!-- 冲调 -->
		<mmodifyBox :poprc="showModifyBox" @upBillDetail="updateBill" @closeRefund="modifyBoxClose" :money="billMoney"
			:bill="billDetail"></mmodifyBox>

		<!-- 免单 -->
		<mfreeCharge :poprc="showfreeCharge" @upBillDetail="updateBill" @closeRefund="freeChargeClose"
			:money="billMoney" :bill="billDetail"></mfreeCharge>

		<!-- 走结 -->
		<m-popup :show="popNoClearCheckOut" @closePop="closePopNoClearCheckOut" mode="center">
			<view class="noCheckOut">
				<p>走结</p>
				<view style="height: 500rpx;width: 700rpx;display: flex;flex-direction: column;margin-top: 30rpx;">
					<text>房型:{{billDetail.room_type_name}}</text>
					<text style="margin: 20rpx 0;">房号:{{billDetail.room_number}}</text>
					<uni-easyinput type="textarea" v-model="remarkNoCheck" placeholder="请输入原因"></uni-easyinput>
					<view class="btnClass" style="margin: 0 auto;" @click="sureNoCheckTh()">
						走结
					</view>
				</view>
			</view>
		</m-popup>

		<!-- 联房财务明细 -->
		<m-popup :show="popConnectBill" @closePop="closePopConnectBill" mode="center">
			<scroll-view scroll-y="true" style="height: 90vh;" @scrolltolower="onRefresh">
				<view class="connectBox">
					<view class="fundBox">
						<view class="account">
							<view class="" style="font-size: 44rpx;font-weight: 600;color:#aa0000 ">
								余额: {{fundConn.pay_amount - fundConn.consume_amount}}
							</view>
							<view class="amountBox">
								<p style="color:red;margin-bottom: 20rpx;">消费合计:{{fundConn.consume_amount}}</p>
								<p style="color:green">收款合计:{{fundConn.pay_amount}}</p>
							</view>
						</view>
					</view>
					<view class="" style="height: 160rpx;padding: 20rpx;display: flex;align-items: center;">
						<text>类型:</text>
						<view class="btnClass" @click="chooseConnType(item)" v-for="(item, index) in chooseTypeList"
							:key="index" :style="chooseTypeId1==item.id?'border:1px solid #0073ff;color:#0073ff;':''">
							<text>{{item.name}}</text>
						</view>
					</view>
					<view class="itemBox" style="background-color: #ffffff;" v-if="chooseTypeId1">
						<p style="font-size: 28rpx;">账务项目</p>
						<picker @change="bindChange4" :value="changeIndex4" range-key="name" :range="groupConnectList">
							<view class="pickerBox">
								{{groupConnectList[changeIndex4].name}}
								<view class="icon-down"
									style="position: absolute;margin: auto 0;top: 0;bottom: 0;right: 10rpx;display: flex;align-items: center;font-size: 38rpx;">
								</view>
							</view>
						</picker>
					</view>
					<view class="itemBoxPay" v-for="item in fundConnList" :key="item.id">
						<view class=""
							style="display: flex;flex-wrap: wrap;width: 600rpx;font-size: 24rpx;margin: 20rpx 0;">
							<view class="itemText">流水ID：{{item.id}}</view>
							<view class="itemText">房间：{{item.room_number}}</view>
							<view class="itemText">动账账户：{{item.account_name}}</view>
							<view class="itemText">账务项目：{{item.detail_type_name}}</view>
							<view class="itemText">我方收款：{{!item.account_type?item.amount:''}}</view>
							<view class="itemText">客户消费：{{item.account_type?item.amount:''}}</view>
							<view class="itemText">业务说明：{{item.remark}}</view>
							<view class="itemText">操作人：{{item.admin_name?item.admin_name:'系统生成'}}</view>
							<view class="itemText">时间：{{item.create_time | moment1}}</view>
							<view class="itemText">备注：{{item.memo}}</view>
						</view>
					</view>
				</view>
			</scroll-view>

		</m-popup>
	</view>
</template>

<script>
	import mExchangeRoom from '../../../components/m-exChangeRoom.vue'
	import mExchangeBillRoom from '../../../components/m-exChangeBillRoom.vue'
	import mpayCenter from '../../../components/m-payCenter.vue'
	import monAccount from '../../../components/m-onAccount.vue'
	import monCheckOutPay from '../../../components/m-checkOutPay.vue'
	import mrefundSelf from '../../../components/m-refundSelf.vue'
	import mconsume from '../../../components/m-consumeBox.vue'
	import mmodifyBox from '../../../components/m-modifyBox.vue'
	import mfreeCharge from '../../../components/m-freeCharge.vue'
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				billDetail: '',
				chooseId: 0,
				roomInfo: null,
				current: 0,
				items: ['详情', '日志'],
				params: {
					bill_id: '',
					group_id: "",
					limit: 10,
					page: 1,
					pid: 0,
					room_bill_type_id: '',
					type: ''
				},
				fund: null,
				fundList: [],
				popSelect: false, //选房
				connectBill: [],

				// 房态
				params1: {
					room_clear_status: [],
					room_record_status: [],
					floor_id: '',
					building_id: "",
					room_type_id: "",
					room_number: "",
					room_sale_type: "",
					grade_id: "",
					intermediaries_id: "",
					start_time: "",
					end_time: ""
				},
				selectRoomList: [],
				showExRc: false,
				showExBillRc: false,
				chooseIds: [],
				chooseRooms: [],
				itemPrice: null, //暂时存储点击的item
				// 预订转入住
				popBookToIn: false,
				genderList: [{
					id: 0,
					name: '保密'
				}, {
					id: 1,
					name: '男'
				}, {
					id: 2,
					name: '女'
				}],
				cardList: [],
				changeIndex1: 0,
				changeIndex2: 0,
				chooseConn: null,
				msgIndex: 0,
				chooseConnBill: [], //多选要办理入住得
				isCheckingIn: false, // 防止重复入住操作

				// 续房
				popContinue: false,
				checkOutTime: '',
				dayCounts: 1,
				monthCounts: 1,
				datetimesingle1: '', //
				endTime: '',
				radio1: 2,
				continueType: [ {
					text: '最后一天',
					value: 2
				},{
					text: '日历价',
					value: 1
				}, {
					text: '自定义',
					value: 3
				}],
				selfPrice:0,//自定义价格
				// 退房
				popCheckOut: false,
				checkReason: '',
				
				//价格弹窗
				popPrice: false,
				pricesBox: [],
				itemPrice: null, //暂时存储点击的item
				
				//收银台
				showPayCcenter: false,
				ifArrange: false, //是否需要平账
				billMoney: 0,

				// 挂账
				showOnAccount: false,

				// 退房
				showCheckOutPay: false,
				checkType: 1, //退款类型1.本订单，2联房财务

				// 自定义退房
				showRefundSelf: false,

				// 及消费
				showConsume: false,

				// 冲调
				showModifyBox: false,

				// 免单
				showfreeCharge: false,

				// 走结
				popNoClearCheckOut: false,
				remarkNoCheck: '',

				chooseTypeList: [{
					id: 0,
					name: '全部'
				}, {
					id: 1,
					name: '消费'
				}, {
					id: 2,
					name: '收款'
				}],
				chooseTypeId: 0,
				changeIndex: 0,
				groupTypeList: [],
				bool: true,

				//联房财务明细
				popConnectBill: false,
				bool1: true,
				params2: {
					page: 1,
					limit: 10,
					pid: 0,
					type: '',
					connect_code: ''
				},
				chooseTypeList1: [{
					id: 0,
					name: '全部'
				}, {
					id: 1,
					name: '消费'
				}, {
					id: 2,
					name: '收款'
				}],
				chooseTypeId1: 0,
				changeIndex4: 0,
				groupConnectList: [],
				fundConn: null,
				fundConnList: [],

				// 日志
				bool2: true,
				logList: [{
						id: 1,
						name: '全部',
						tip: 0
					},
					{
						id: 2,
						name: '订房',
						tip: 0
					},
					{
						id: 3,
						name: '财务',
						tip: 0
					},
					{
						id: 4,
						name: '其他',
						tip: 0
					}
				],
				currentTab: 0,
				params3: {
					page: 1,
					limit: 10,
					type: '',
					bill_id: ''
				},
				logDetailList: []
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		components: {
			mExchangeRoom,
			mpayCenter,
			monAccount,
			monCheckOutPay,
			mrefundSelf,
			mconsume,
			mmodifyBox,
			mfreeCharge,
			mExchangeBillRoom
		},
		watch: {
			changeIndex1() {
				this.connectBill.forEach(item => {
					if (this.chooseConn.id == item.id) {
						item.userMsg.forEach((item1, index1) => {
							if (this.msgIndex == index1) {
								item1.gender = this.genderList[this.changeIndex1].id
							}

						})
					}
				})
			},
			changeIndex2() {
				this.connectBill.forEach(item => {
					if (this.chooseConn.id == item.id) {
						item.userMsg.forEach((item1, index1) => {
							if (this.msgIndex == index1) {
								item1.identification_type = this.cardList[this.changeIndex2].id
							}

						})
					}
				})
			},
			monthCounts() {
				this.datetimesingle1 = this.$moment(this.billDetail.leave_time_plan * 1000).add(this.monthCounts, 'months')
					.unix()
			},
			dayCounts() {
				this.datetimesingle1 = this.$moment(this.billDetail.leave_time_plan * 1000).add(this.dayCounts, 'days')
					.unix() * 1000
			},
			connectBill: {
				handler(newVal, oldVal) {
					this.connectBill = newVal
					console.log('ddddd');
				},
				immediate: true,
				deep: true
			},
			billDetail: {
				handler(newVal, oldVal) {

					if (this.billDetail && this.billDetail.connect_bills && this.billDetail.connect_bills.length > 1) {
						let connect_bills = 0
						connect_bills = this.billDetail.connect_bills.filter(item => {
							return item.bill_status == 4
						})
						if (connect_bills.length == 1) {
							this.ifArrange = false
						} else {
							this.ifArrange = true
						}
						console.log(connect_bills.length, 'kength');
					} else {
						this.ifArrange = false
					}
				},
				immediate: true,
				deep: true
			}
		},
		onLoad(option) {
			uni.showLoading({
				title: 'loading...'
			})
			console.log(option, 'option')
			let bill_id = ''
			if (option && option.id) {
				bill_id = option.id
			}
			this.$iBox
				.http('getBossRoomBillDetail', {
					bill_id: bill_id
				})({
					method: 'post'
				})
				.then(res => {
					this.billDetail = res.data
					this.chooseId = bill_id
					this.roomInfo = this.billDetail.connect_bills
					this.params.page = 1
					this.params.bill_id = this.billDetail.id
					this.$iBox
						.http('getRoomBillFund', this.params)({
							method: 'post'
						})
						.then(res => {
							this.fund = res.data
							this.fundList = res.data.list


							uni.hideLoading()
						})
				})

		},
		methods: {
			stayTimeNumber() {

				if (this.billDetail.room_sale_type_sign == 'standard' || this.billDetail.room_sale_type_sign ==
					'long_standard') {
					let s = this.$moment(this.$moment(this.billDetail.enter_time_plan * 1000).format('YYYY-MM-DD'))
					let e = ''
					if(this.billDetail.leave_time){
						 e = this.$moment(this.$moment(this.billDetail.leave_time * 1000).format('YYYY-MM-DD'))
					}else{
						 e = this.$moment(this.$moment(this.billDetail.leave_time_plan * 1000).format('YYYY-MM-DD'))
						console.log(e,'wode');
					}
					
					
					let c = e.diff(s, 'hours')/24
					return Math.ceil(c) +'晚'
				} else {
					let s = this.$moment(this.$moment(this.billDetail.enter_time_plan * 1000).format(
						'YYYY-MM-DD HH:mm:ss'))
					let e = ''
					if(this.billDetail.leave_time){
						 e = this.$moment(this.$moment(this.billDetail.leave_time * 1000).format('YYYY-MM-DD'))
					}else{
						 e = this.$moment(this.$moment(this.billDetail.leave_time_plan * 1000).format('YYYY-MM-DD'))
					}
					let c = e.diff(s, 'minutes')
					console.log((c/60).toString(2),'yuliu ');
					
					return Math.ceil(c/60) +'小时'
				}


			},
			toDayNumber() {

				if (this.billDetail.room_sale_type_sign == 'standard' || this.billDetail.room_sale_type_sign ==
					'long_standard') {

					let s = this.$moment(this.$moment().format('YYYY-MM-DD'))
					let e = this.$moment(this.$moment(this.billDetail.leave_time_plan * 1000).format('YYYY-MM-DD'))
					let c = e.diff(s, 'days')
					return c +'晚'
				} else {

					let s = this.$moment(this.$moment().format('YYYY-MM-DD HH:mm:ss'))
					let e = this.$moment(this.$moment(this.billDetail.leave_time_plan * 1000).format(
						'YYYY-MM-DD HH:mm:ss'))
					let c = e.diff(s, 'minutes')/60
					
					return Math.ceil(c/60) +'小时'
				}

			},
			callPhone(e) {
				uni.makePhoneCall({
					phoneNumber: e
				})
			},
			pushPassWord(e) {
				this.$iBox
					.http('sendPassword', {
						bill_id: e.bill_id,
						room_bill_user_id: e.id
					})({
						method: 'post'
					})
					.then(res => {
						uni.showToast({
							icon: 'success',
							title: '密码发送成功'
						})
					})
			},
			closePopSelect() {
				this.popSelect = false
			},
			closeExRc() {
				this.showExRc = false
			},
			closeExBillRc() {
				this.showExBillRc = false
			},
			changeRoom(e) {
				this.chooseId = e.bill_id
				this.getBillDetail(e.bill_id)

				this.params3.page = 1
				this.params3.bill_id = e.bill_id
				this.$iBox
					.http('getRoomLog', this.params3)({
						method: 'post'
					})
					.then(res => {
						this.logDetailList = res.data.list

						uni.hideLoading()
					})
			},
			bindChange1(e) {
				this.changeIndex1 = e.detail.value[0]
			},
			bindChange2(e) {
				this.changeIndex2 = e.detail.value[0]
			},
			dayChange(e) {
				console.log(e, '00');
				this.dayCounts = e
				// this.datetimesingle1 = this.$moment(this.billDetail.leave_time_plan*1000).add(e,'days').unix()*1000
				console.log(e, this.datetimesingle1);
			},

			monthChange(e) {
				this.monthCounts = e
				this.datetimesingle1 = this.$moment(this.billDetail.leave_time_plan * 1000).add(e, 'months').unix()
			},
			changeLog1(a) {
				this.datetimesingle1 = a
				let s = this.$moment(this.billDetail.leave_time_plan * 1000)
				let e = this.$moment(this.datetimesingle1)
				let c = e.diff(s, 'days')
				this.dayCounts = c

			},
			sureContinueTh(e) {
				this.$iBox.throttle(() => {
					this.sureContinue()
				}, 2000);
			},
			sureContinue() {
				let params = {
					times: '',
					bill_id: this.billDetail.id,
					extend_type:this.radio1,
					type:''
				}
				if(this.radio1==3){
					params.price = this.selfPrice
				}
				if (this.billDetail.room_sale_type_sign == 'hour') {
					params.times = this.billDetail.stay_time
					params.type = 1
					this.$iBox
						.http('HourextendRoom', params)({
							method: 'post'
						}).then(res => {
							this.popContinue = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '续房成功!'
							})
							uni.hideLoading()
						})
				} else if (this.billDetail.room_sale_type_sign == 'standard') {
					params.times = this.dayCounts
					params.type = 2
					this.$iBox
						.http('extendRoom', params)({
							method: 'post'
						}).then(res => {
							this.popContinue = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '续房成功!'
							})
							uni.hideLoading()
						})
				} else if (this.billDetail.room_sale_type_sign == 'long_standard') {
					params.times = this.monthCounts
					params.type = 3
					this.$iBox
						.http('LongextendRoom', params)({
							method: 'post'
						}).then(res => {
							this.popContinue = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '续房成功!'
							})
							uni.hideLoading()
						})
				} else {
					params.times = this.billDetail.stay_time
					params.type = 1
					this.$iBox
						.http('ConferenceextendRoom', params)({
							method: 'post'
						}).then(res => {
							this.popContinue = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '续房成功!'
							})
							uni.hideLoading()
						})
				}
			},
			changePrice(e) {
				this.itemPrice = e
				this.pricesBox = e.room_date_price
				this.popPrice = true
			},
			closepopPrice() {
				this.popPrice = false
			},
			sureCheckOutTh() {
				this.$iBox.throttle(() => {
					this.sureCheckOut()
				}, 2000);
			},
			sureCheckOut() {
				// 先判断是否是联房且是最后一间
				let a = this.billDetail.connect_bills.filter(item => {
					return item.bill_status == 4
				})

				if (a.length == 1) {
					console.log(a, this.billDetail);
					if (this.billDetail.bill_balance != 0) {
						uni.showToast({
							icon: 'none',
							title: '账务未平'
						})
						return
					}
				}
				if (this.billDetail.room_sale_type_sign == 'hour') {
					this.$iBox
						.http('HourcheckOut', {
							check_out_remark: this.checkReason,
							bill_id: this.billDetail.id
						})({
							method: 'post'
						}).then(res => {
							this.popCheckOut = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '退房成功!'
							})
							uni.hideLoading()
						})
				} else if (this.billDetail.room_sale_type_sign == 'standard') {

					this.$iBox
						.http('checkOut', {
							check_out_remark: this.checkReason,
							bill_id: this.billDetail.id
						})({
							method: 'post'
						}).then(res => {
							this.popCheckOut = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '退房成功!'
							})
							uni.hideLoading()
						})
				} else if (this.billDetail.room_sale_type_sign == 'long_standard') {
					this.$iBox
						.http('LongcheckOut', {
							check_out_remark: this.checkReason,
							bill_id: this.billDetail.id
						})({
							method: 'post'
						}).then(res => {
							this.popCheckOut = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '退房成功!'
							})
							uni.hideLoading()
						})
				} else {
					this.$iBox
						.http('ConferencecheckOut', {
							check_out_remark: this.checkReason,
							bill_id: this.billDetail.id
						})({
							method: 'post'
						}).then(res => {
							this.popCheckOut = false
							this.getBillDetail(this.billDetail.id)
							uni.showToast({
								icon: 'none',
								title: '退房成功!'
							})
							uni.hideLoading()
						})
				}
			},
			getGender(e) {
				console.log(e, 'chooseConn');
				this.chooseConn = e.item
				this.msgIndex = e.index1
			},
			ordingToCheckIn(e) {
				this.$iBox.http('getConnectBill', {
						bill_id: this.billDetail.id
					})({
						method: 'post'
					})
					.then(res => {
						this.connectBill = res.data.filter(item => {
							return item.bill_status == 3
						})
						
						this.connectBill.forEach(item => {
							if(item.bill_user.length>0){
								item.userMsg = item.bill_user
							}else {
								let userInfo = []
								let msg = {
									name: item.link_man ? item.link_man : '',
									gender: 0,
									phone: item.link_phone ? item.link_phone : '',
									identification_type: 1,
									identification_number: ''
								}
								userInfo.push(msg)
								item.userMsg = userInfo
							}
							
						})
						console.log(this.connectBill, 'this.connectBill');
						// 查询证件类型
						this.$iBox.http('getIdentificationTypeList', {

							})({
								method: 'post'
							})
							.then(res => {
								this.cardList = res.data
							})

						let enter_time = this.$moment(this.billDetail.enter_time_plan * 1000).format('YYYY-MM-DD')
						let today = this.$moment().format('YYYY-MM-DD')
						if (this.$moment(enter_time, 'YYYY-MM-DD').unix() > this.$moment(today, 'YYYY-MM-DD').unix()) {
							uni.showModal({
								title: '提示',
								content: '请注意!此订单正在提前办理入住!',
								success: res => {
									if (res.confirm) {
										this.popBookToIn = true
									} else {

									}
								}
							})
						} else {
							this.popBookToIn = true
						}


					})

			},
			addUserMsg(e) {
				let connectBill = JSON.parse(JSON.stringify(this.connectBill))
				connectBill.forEach(item => {
					if (e.id == item.id) {
						let msg = {
							name: '',
							gender: 0,
							phone: '',
							identification_type: 1,
							identification_number: ''
						}
						item.userMsg.push(msg)
					}
				})
				this.connectBill = JSON.parse(JSON.stringify(connectBill))

				// 同步更新 chooseConnBill 中对应的数据
				this.syncChooseConnBill()
			},
			reduceUserMsg(e) {
				let connectBill = JSON.parse(JSON.stringify(this.connectBill))
				connectBill.forEach(item => {
					if (e.item.id == item.id) {
						item.userMsg.splice(e.index1, 1)
					}
				})
				this.connectBill = connectBill

				// 同步更新 chooseConnBill 中对应的数据
				this.syncChooseConnBill()
			},
			// 同步 chooseConnBill 数据，确保添加/删除入住人后数据实时更新
			syncChooseConnBill() {
				if (this.chooseConnBill && this.chooseConnBill.length > 0) {
					// 根据已选择的房间ID，从最新的 connectBill 中获取对应数据
					let updatedChooseConnBill = []
					this.chooseConnBill.forEach(chosenItem => {
						let updatedItem = this.connectBill.find(item => item.id === chosenItem.id)
						if (updatedItem) {
							updatedChooseConnBill.push(updatedItem)
						}
					})
					this.chooseConnBill = updatedChooseConnBill
					console.log('同步更新 chooseConnBill:', this.chooseConnBill)
				}
			},
			checkboxBookToIn(e) {
				console.log(e, 'ssss');
				let bills = []
				this.connectBill.forEach(item => {
					if (e.detail.value.includes(item.id.toString())) {
						bills.push(item)
					}
				})

				this.chooseConnBill = bills
			},
			sureCheckInTh() {
				this.$iBox.throttle1(() => {
					this.sureCheckIn()
				}, 2000);
			},
			sureCheckIn() {
				// 防止重复请求
				if (this.isCheckingIn) {
					console.log('正在处理入住请求，请勿重复操作');
					return;
				}

				// 验证是否选择房间
				if (this.chooseConnBill.length == 0) {
					uni.showToast({
						icon: 'none',
						title: '请选择房间'
					})
					return
				}

				console.log(this.chooseConnBill, 'lll', this.connectBill);

				// 验证入住人信息 - 第一个入住人需要验证手机号，其他入住人只验证姓名和证件号
				for (let item of this.chooseConnBill) {
					for (let index = 0; index < item.userMsg.length; index++) {
						const item1 = item.userMsg[index];
						console.log(item1);

						// 第一个入住人需要验证姓名、手机号和证件号
						if (index === 0) {
							if (!item1.name || !item1.phone || !item1.identification_number) {
								uni.showToast({
									icon: 'none',
									title: '请完善第一个入住人的姓名、手机号和证件号'
								})
								return;
							}
						} else {
							// 其他入住人只需要验证姓名和证件号
							if (!item1.name || !item1.identification_number) {
								uni.showToast({
									icon: 'none',
									title: '请完善入住人的姓名和证件号'
								})
								return;
							}
						}
					}
				}

				// 构建请求参数
				let params = []
				this.chooseConnBill.forEach(item => {
					let bill = {
						bill_id: '',
						room_id: '',
						user_info: []
					}

					bill.bill_id = item.id
					bill.room_id = item.room_id
					bill.user_info = item.userMsg
					params.push(bill)
				})
				console.log(this.chooseConnBill,'this.chooseConnBill');

				// 设置处理状态
				this.isCheckingIn = true;

				// 显示加载状态
				uni.showLoading({
					title: '转入住中...'
				});

				if (this.chooseConnBill[0].room_sale_type_sign == 'hour') {
					this.$iBox.http('HourbookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							this.handleCheckInSuccess();
						})
						.catch(err => {
							this.handleCheckInError(err);
						})
				} else if (this.chooseConnBill[0].room_sale_type_sign == 'standard') {
					this.$iBox.http('bookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							this.handleCheckInSuccess();
						})
						.catch(err => {
							this.handleCheckInError(err);
						})
				} else if (this.chooseConnBill[0].room_sale_type_sign == 'long_standard') {
					this.$iBox.http('LongbookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							this.handleCheckInSuccess();
						})
						.catch(err => {
							this.handleCheckInError(err);
						})
				} else {
					this.$iBox.http('ConferencebookToCheckIn', params)({
							method: 'post'
						})
						.then(res => {
							this.handleCheckInSuccess();
						})
						.catch(err => {
							this.handleCheckInError(err);
						})
				}



			},
			// 入住成功统一处理
			handleCheckInSuccess() {
				uni.hideLoading();
				this.isCheckingIn = false; // 重置处理状态
				this.getBillDetail(this.chooseConnBill[0].id);
				this.popBookToIn = false;
				uni.showToast({
					title: '转入住成功',
					icon: 'success'
				});
			},
			// 入住失败统一处理
			handleCheckInError(err) {
				uni.hideLoading();
				this.isCheckingIn = false; // 重置处理状态
				console.error('预定转入住失败:', err);

				// 向用户显示具体错误信息
				let errorMessage = '转入住失败，请重试';
				if (err && err.message) {
					errorMessage = err.message;
				} else if (err && typeof err === 'string') {
					errorMessage = err;
				}

				uni.showToast({
					icon: 'none',
					title: errorMessage,
					duration: 3000
				});
			},
			exChangeRoom() {
				this.params1.start_time = this.billDetail.enter_time_plan
				this.params1.end_time = this.billDetail.leave_time_plan
				this.params1.room_sale_type = this.billDetail.sale_type_id
				this.params1.room_type_id = ''
				this.params1.intermediaries_id = this.billDetail.tintermediary_id
				// this.params1.grade_id = this.manInfo ? this.manInfo.grade : ''

				this.$iBox.http('getUserInfoBoss', {
					common_code: this.billDetail.common_code
				})({
					method: 'post'
				}).then(res => {
					this.params1.grade_id = res.data.grade ? res.data.grade : ''
				})

				this.$iBox.http('selectRoom', this.params1)({
					method: 'post'
				}).then(res => {
					this.chooseRooms = []
					this.selectRoomList = res.data

					this.connectBill.forEach(billItem => {
						this.selectRoomList.forEach(item => {
							item.floor_list.forEach(item1 => {
								item1.room_list.filter(item2 => {
									return item2.id != billItem.room_id
								})
							})
						})
					})

					this.showExBillRc = true
				})

			},
			getExBillRcIds(e) {
				let room = e.rooms[0]

				if (this.billDetail.room_sale_type_sign == 'hour') {
					this.$iBox.http('HourchangeRoom', {
							bill_id: this.billDetail.id,
							room_id: room.id
						})({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.billDetail.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.billDetail.id)

						})
				} else if (this.billDetail.room_sale_type_sign == 'standard') {
					this.$iBox.http('changeRoom', {
							bill_id: this.billDetail.id,
							room_id: room.id
						})({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.billDetail.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.billDetail.id)

						})
				} else if (this.billDetail.room_sale_type_sign == 'long_standard') {
					this.$iBox.http('LongchangeRoom', {
							bill_id: this.billDetail.id,
							room_id: room.id
						})({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.billDetail.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.billDetail.id)

						})
				} else {
					this.$iBox.http('ConferencechangeRoom', {
							bill_id: this.billDetail.id,
							room_id: room.id
						})({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.billDetail.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.billDetail.id)

						})
				}

			},
			getExRcIds(e) {
				console.log(e, 'rrpm');
				let room = e.rooms[0]

				if (this.billDetail.room_sale_type_sign == 'hour') {
					this.$iBox.http('HourarrangedRoom', [{
							bill_id: this.itemPrice.id,
							room_id: room.id
						}])({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.itemPrice.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.itemPrice.id)

						})
				} else if (this.billDetail.room_sale_type_sign == 'standard') {
					this.$iBox.http('arrangedRoom', [{
							bill_id: this.itemPrice.id,
							room_id: room.id
						}])({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.itemPrice.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.itemPrice.id)

						})
				} else if (this.billDetail.room_sale_type_sign == 'long_standard') {
					this.$iBox.http('LongarrangedRoom', [{
							bill_id: this.itemPrice.id,
							room_id: room.id
						}])({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.itemPrice.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.itemPrice.id)

						})
				} else {
					this.$iBox.http('ConferencearrangedRoom', [{
							bill_id: this.itemPrice.id,
							room_id: room.id
						}])({
							method: 'post'
						})
						.then(res => {
							this.$iBox.http('getConnectBill', {
									bill_id: this.itemPrice.id
								})({
									method: 'post'
								})
								.then(res => {
									this.connectBill = res.data.filter(item => {
										return item.bill_status == 3
									})
								})
							this.getBillDetail(this.itemPrice.id)

						})
				}


			},
			getBillDetail(e) {
				uni.showLoading({
					title: 'loading...'
				})
				this.$iBox
					.http('getBossRoomBillDetail', {
						bill_id: e
					})({
						method: 'post'
					})
					.then(res => {
						this.billDetail = res.data

						this.roomInfo = this.billDetail.connect_bills

						this.params.bill_id = this.billDetail.id
						this.$iBox
							.http('getRoomBillFund', this.params)({
								method: 'post'
							})
							.then(res => {
								this.fund = res.data
								this.fundList = res.data.list
								uni.hideLoading()
							})
					})
			},
			onClickItem(e) {
				console.log(e);
				if (this.current != e.currentIndex) {
					this.current = e.currentIndex;
				}
				if (e.currentIndex == 1) {
					this.params3.page = 1
					this.params3.bill_id = this.billDetail.id
					this.$iBox
						.http('getRoomLog', this.params3)({
							method: 'post'
						})
						.then(res => {
							this.logDetailList = res.data.list

							uni.hideLoading()
						})
				}

			},
			closePopBookToIn() {
				this.popBookToIn = false
			},
			continueRoom(e) {
				this.popContinue = true

				this.$iBox
					.http('getCheckOutTime', {})({
						method: 'post'
					})
					.then(res => {
						this.checkOutTime = res.data
						if (this.billDetail.room_sale_type_sign=='hour'||this.billDetail.room_sale_type_sign=='conference') {
							this.datetimesingle1 = this.$moment(this.billDetail.leave_time_plan * 1000).add(1, 'hours')
								.unix()
						} else if (this.billDetail.room_sale_type_sign == 'standard') {
							this.endTime = (this.billDetail.leave_time_plan + 24 * 3600) * 1000
							this.datetimesingle1 = this.$moment(this.billDetail.leave_time_plan * 1000).add(1, 'days')
								.unix() * 1000
						} else if (this.billDetail.room_sale_type_sign == 'long_standard') {
							this.datetimesingle1 = this.$moment(this.billDetail.leave_time_plan * 1000).add(1,
								'months').unix()
						}

						uni.hideLoading()
					})
			},
			closePopContinue() {
				this.popContinue = false
			},
			closePopCheckOut() {
				this.popCheckOut = false
			},
			checkOutRoom() {

				let leave_time = this.$moment(this.billDetail.leave_time_plan * 1000).format('YYYY-MM-DD')
				let today = this.$moment().format('YYYY-MM-DD')
				if (this.$moment(leave_time, 'YYYY-MM-DD').unix() > this.$moment(today, 'YYYY-MM-DD').unix()) {
					uni.showModal({
						title: '提示',
						content: '请注意!此订单正在提前办理退房!',
						success: res => {
							if (res.confirm) {
								this.popCheckOut = true
							} else {

							}
						}
					})
				} else {
					this.popCheckOut = true
				}

			},
			// 收款 - 防重复触发版本
			toPayTh(e) {
				this.$iBox.throttle1(() => {
					this.toPay(e)
				}, 2000);
			},

			toPay(e) {
				this.billMoney = Math.abs(e)
				this.showPayCcenter = true

				console.log('pay', e, this.billMoney);
			},
			payClose(e) {
				console.log(e, 'ee');
				this.showPayCcenter = false
			},
			updateBill() {
				this.getBillDetail(this.billDetail.id)
			},
			selectRoom() {
				this.popSelect = true
				this.$iBox.http('getConnectBill', {
						bill_id: this.billDetail.id
					})({
						method: 'post'
					})
					.then(res => {
						this.connectBill = res.data.filter(item => {
							return item.bill_status == 3
						})
					})
			},
			toSelect(e) {
				this.itemPrice = e
				this.params1.start_time = this.billDetail.enter_time_plan
				this.params1.end_time = this.billDetail.leave_time_plan
				this.params1.room_sale_type = this.billDetail.sale_type_id
				this.params1.room_type_id = e.room_type_id
				this.params1.intermediaries_id = this.billDetail.tintermediary_id
				// this.params1.grade_id = this.manInfo ? this.manInfo.grade : ''

				this.$iBox.http('getUserInfoBoss', {
					common_code: this.billDetail.common_code
				})({
					method: 'post'
				}).then(res => {
					this.params1.grade_id = res.data.grade ? res.data.grade : ''
				})

				this.$iBox.http('selectRoom', this.params1)({
					method: 'post'
				}).then(res => {
					this.chooseRooms = []
					this.selectRoomList = res.data

					this.connectBill.forEach(billItem => {
						this.selectRoomList.forEach(item => {
							item.floor_list.forEach(item1 => {
								item1.room_list.filter(item2 => {
									return item2.id != billItem.room_id
								})
							})
						})
					})

					this.showExRc = true
				})

			},
			sureAccess(e) {
				// confirmRoomBill
				console.log(e);
				uni.showModal({
					title: '提示',
					content: '是否确认接受订单',
					success: (res) => {
						if (res.confirm) {
							if (e.room_sale_type_sign == 'hour') {
								this.$iBox
									.http('HourconfirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										this.getBillDetail(this.billDetail.id)
									})
							} else if (e.room_sale_type_sign == 'standard') {
								this.$iBox
									.http('confirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										this.getBillDetail(this.billDetail.id)
									})
							} else if (e.room_sale_type_sign == 'long_standard') {
								this.$iBox
									.http('LongconfirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {

										this.getBillDetail(this.billDetail.id)
									})
							} else {
								this.$iBox
									.http('ConferenceconfirmRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {

										this.getBillDetail(this.billDetail.id)
									})
							}


						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});


			},
			// 取消订单 - 防重复触发版本
			cancelAccessTh(e) {
				this.$iBox.throttle1(() => {
					this.cancelAccess(e)
				}, 2000);
			},

			cancelAccess(e) {
				uni.showModal({
					title: '提示',
					content: '是否取消订单',
					success: (res) => {
						if (res.confirm) {
							// 显示加载状态
							uni.showLoading({
								title: '取消中...'
							});

							if (e.room_sale_type_sign == 'hour') {
								this.$iBox
									.http('HourcancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.getBillDetail(this.billDetail.id)
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							} else if (e.room_sale_type_sign == 'standard') {
								this.$iBox
									.http('cancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.getBillDetail(this.billDetail.id)
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							} else if (e.room_sale_type_sign == 'long_standard') {
								this.$iBox
									.http('LongcancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.getBillDetail(this.billDetail.id)
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							} else {
								this.$iBox
									.http('ConferencecancelRoomBill', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {
										uni.hideLoading();
										this.getBillDetail(this.billDetail.id)
										uni.showToast({
											title: '取消成功',
											icon: 'success'
										});
									})
									.catch(err => {
										uni.hideLoading();
										console.error('取消订单失败:', err);
									})
							}

						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			noShowAccess(e) {
				uni.showModal({
					title: '提示',
					content: '是否处置为noShow?',
					success: (res) => {
						if (res.confirm) {
							if (e.room_sale_type_sign == 'hour') {
								this.$iBox
									.http('HourtoNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {

										this.getBillDetail(this.billDetail.id)
									})
							} else if (e.room_sale_type_sign == 'standard') {
								this.$iBox
									.http('toNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {

										this.getBillDetail(this.billDetail.id)
									})
							} else if (e.room_sale_type_sign == 'long_standard') {
								this.$iBox
									.http('LongtoNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {

										this.getBillDetail(this.billDetail.id)
									})
							} else {
								this.$iBox
									.http('ConferencetoNoShow', {
										bill_id: e.id
									})({
										method: 'post'
									})
									.then(res1 => {

										this.getBillDetail(this.billDetail.id)
									})
							}
						} else if (res.cancel) {
							console.log('用户点击取消');
						}
					}
				});
			},
			// 挂账 - 防重复触发版本
			toGuaTh(e) {
				this.$iBox.throttle1(() => {
					this.toGua(e)
				}, 2000);
			},

			toGua(e) {
				this.billMoney = Math.abs(e)
				console.log(e, 'e');
				this.showOnAccount = true
			},
			onAccountClose() {
				this.showOnAccount = false
			},
			// 原路退款 - 防重复触发版本
			toTuiTh(e) {
				this.$iBox.throttle1(() => {
					this.toTui(e)
				}, 2000);
			},

			toTui(e) {
				this.checkType = e
				this.showCheckOutPay = true
			},

			checkOutPayClose() {
				this.showCheckOutPay = false
			},
			// 自定义退款 - 防重复触发版本
			toSelfTuiTh(e) {
				this.$iBox.throttle1(() => {
					this.toSelfTui(e)
				}, 2000);
			},

			toSelfTui(e) {
				this.checkType = e
				this.showRefundSelf = true
			},
			refundSelfClose() {
				this.showRefundSelf = false
			},
			// 记消费 - 防重复触发版本
			toConsumeTh() {
				this.$iBox.throttle1(() => {
					this.toConsume()
				}, 2000);
			},

			toConsume() {
				this.showConsume = true
			},
			consumeClose() {
				this.showConsume = false
			},
			// 冲调 - 防重复触发版本
			toModifyTh() {
				this.$iBox.throttle1(() => {
					this.toModify()
				}, 2000);
			},

			toModify() {
				this.showModifyBox = true
			},
			modifyBoxClose() {
				this.showModifyBox = false
			},
			// 免单 - 防重复触发版本
			toFreeChargeTh() {
				this.$iBox.throttle1(() => {
					this.toFreeCharge()
				}, 2000);
			},

			toFreeCharge() {
				this.showfreeCharge = true
			},
			freeChargeClose() {
				this.showfreeCharge = false
			},
			// 走结 - 防重复触发版本
			toNoClearCheckOutTh() {
				this.$iBox.throttle1(() => {
					this.toNoClearCheckOut()
				}, 2000);
			},

			toNoClearCheckOut() {
				this.popNoClearCheckOut = true
			},
			closePopNoClearCheckOut() {
				this.popNoClearCheckOut = false
			},
			sureNoCheckTh() {
				this.$iBox.throttle(() => {
					this.sureNoCheck()
				}, 2000);
			},
			sureNoCheck() {
				if (!this.remarkNoCheck) {
					uni.showToast({
						icon: 'none',
						title: '请填写走结原因'
					})
					return
				}

				let params = {
					bill_id: this.billDetail.id,
					check_out_remark: this.remarkNoCheck
				}

				if (this.billDetail.room_sale_type_sign == 'hour') {
					this.$iBox
						.http('HournoClearCheckOut', params)({
							method: 'post'
						})
						.then(res => {
							this.popNoClearCheckOut = false
							this.remarkNoCheck = ''
							this.getBillDetail(this.billDetail.id)
						})
				} else if (this.billDetail.room_sale_type_sign == 'standard') {
					this.$iBox
						.http('noClearCheckOut', params)({
							method: 'post'
						})
						.then(res => {
							this.popNoClearCheckOut = false
							this.remarkNoCheck = ''
							this.getBillDetail(this.billDetail.id)
						})
				} else if (this.billDetail.room_sale_type_sign == 'long_standard') {
					this.$iBox
						.http('LongnoClearCheckOut', params)({
							method: 'post'
						})
						.then(res => {
							this.popNoClearCheckOut = false
							this.remarkNoCheck = ''
							this.getBillDetail(this.billDetail.id)
						})
				} else {
					this.$iBox
						.http('ConferencenoClearCheckOut', params)({
							method: 'post'
						})
						.then(res => {
							this.popNoClearCheckOut = false
							this.remarkNoCheck = ''
							this.getBillDetail(this.billDetail.id)
						})
				}


			},
			// 结账 - 防重复触发版本
			clearBillTh() {
				this.$iBox.throttle1(() => {
					this.clearBill()
				}, 2000);
			},

			clearBill() {
				// 显示加载状态
				uni.showLoading({
					title: '结账中...'
				});

				let params = {
					bill_id: this.billDetail.id,
					pid: 0
				}
				this.$iBox
					.http('clearingBillCheckOut', params)({
						method: 'post'
					})
					.then(res => {
						uni.hideLoading();
						this.getBillDetail(this.billDetail.id)
						uni.showToast({
							title: '结账成功',
							icon: 'success'
						});
					})
					.catch(err => {
						uni.hideLoading();
						console.error('结账失败:', err);
					})
			},
			// 转为走结状态 - 防重复触发版本
			finishBillTh() {
				this.$iBox.throttle1(() => {
					this.finishBill()
				}, 2000);
			},

			finishBill() {
				// 显示加载状态
				uni.showLoading({
					title: '处理中...'
				});

				let params = {
					bill_id: this.billDetail.id
				}
				this.$iBox
					.http('finishBillToNoClear', params)({
						method: 'post'
					})
					.then(res => {
						uni.hideLoading();
						this.getBillDetail(this.billDetail.id)
						uni.showToast({
							title: '操作成功',
							icon: 'success'
						});
					})
					.catch(err => {
						uni.hideLoading();
						console.error('操作失败:', err);
					})
			},
			chooseType(e) {
				this.chooseTypeId = e.id
				this.params.page = 1
				this.params.bill_id = this.billDetail.id
				this.params.type = e.id ? e.id : ''
				this.$iBox
					.http('getRoomBillFund', this.params)({
						method: 'post'
					})
					.then(res => {
						this.fund = res.data
						this.fundList = res.data.list
						uni.hideLoading()
					})

				if (e.id) {
					this.$iBox
						.http('getDetailTypeByGroupId', {
							type: e.id
						})({
							method: 'post'
						})
						.then(res => {
							let a = [{
								id: '',
								name: '全部'
							}]
							this.groupTypeList = [...a, ...res.data]
						})
				}
			},
			chooseConnType(e) {
				this.chooseTypeId1 = e.id
				this.params2.type = e.id ? e.id : ''
				this.$iBox
					.http('getRoomBillFundByConnectCode', this.params2)({
						method: 'post'
					})
					.then(res => {
						this.fundConn = res.data
						this.fundConnList = res.data.list
						uni.hideLoading()
					})

				if (e.id) {
					this.$iBox
						.http('getDetailTypeByGroupId', {
							type: e.id
						})({
							method: 'post'
						})
						.then(res => {
							let a = [{
								id: '',
								name: '全部'
							}]
							this.groupConnectList = [...a, ...res.data]
						})
				}
			},
			bindChange(e) {
				this.changeIndex = e.detail.value[0]
				this.params.room_bill_type_id = this.groupTypeList[this.changeIndex].id

				this.$iBox
					.http('getRoomBillFund', this.params)({
						method: 'post'
					})
					.then(res => {
						this.fund = res.data
						this.fundList = res.data.list
						uni.hideLoading()
					})
			},
			bindChange4(e) {
				this.changeIndex4 = e.detail.value[0]
				this.params2.room_bill_type_id = this.groupConnectList[this.changeIndex4].id

				this.$iBox
					.http('getRoomBillFundByConnectCode', this.params2)({
						method: 'post'
					})
					.then(res => {
						this.fundConn = res.data
						this.fundConnList = res.data.list
						uni.hideLoading()
					})
			},
			getConnectBill() {
				this.popConnectBill = true
				this.params2.connect_code = this.billDetail.connect_code
				this.$iBox
					.http('getRoomBillFundByConnectCode', this.params2)({
						method: 'post'
					})
					.then(res => {
						this.fundConn = res.data
						this.fundConnList = res.data.list

						uni.hideLoading()
					})

			},
			closePopConnectBill() {
				this.popConnectBill = false
			},
			onRefresh() {
				if (this.bool1) {
					++this.params2.page
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getRoomBillFundByConnectCode', this.params2)({
						method: 'post'
					}).then(res => {
						this.fundConn = res.data
						let new_list = this.fundConnList.concat(res.data.list)
						this.fundConnList = new_list
						if (this.fundConnList.length == res.data.count) {
							this.bool1 = false
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}
			},
			tab_click(e) {
				console.log(e);
				this.currentTab = e
				if (e == 0) {
					this.params3.type = ''
					this.$iBox
						.http('getRoomLog', this.params3)({
							method: 'post'
						})
						.then(res => {
							this.logDetailList = res.data.list

							uni.hideLoading()
						})
				} else if (e == 1) {
					this.params3.type = 1
					this.$iBox
						.http('getRoomLog', this.params3)({
							method: 'post'
						})
						.then(res => {
							this.logDetailList = res.data.list

							uni.hideLoading()
						})
				} else if (e == 2) {
					this.params3.type = 2
					this.$iBox
						.http('getRoomLog', this.params3)({
							method: 'post'
						})
						.then(res => {
							this.logDetailList = res.data.list

							uni.hideLoading()
						})
				} else if (e == 3) {
					this.params3.type = 3
					this.$iBox
						.http('getRoomLog', this.params3)({
							method: 'post'
						})
						.then(res => {
							this.logDetailList = res.data.list

							uni.hideLoading()
						})
				}

			},

			billFormat(e) {
				switch (e) {
					case 1:
						return '待付款'
						break;
					case 2:
						return '待确认'
						break;
					case 3:
						return '待入住'
						break;
					case 4:
						return '入住中'
						break;
					case 5:
						return '已完成'
						break;
					case 6:
						return '待取消'
						break;
					case 7:
						return '已取消'
						break;
					case 8:
						return '申请退房'
						break;
					case 9:
						return '预定未到'
						break;
					case 10:
						return '走结'
						break;
					case 11:
						return 'noShow'
						break;
					default:
						break;
				}
			},
		},
		// // 上拉加载
		onReachBottom() {
			if (this.current == 0) {
				if (this.bool) {
					++this.params.page
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox.http('getRoomBillFund', this.params)({
						method: 'post'
					}).then(res => {
						this.fund = res.data
						let new_list = this.fundList.concat(res.data.list)
						this.fundList = new_list
						if (this.fundList.length == res.data.count) {
							this.bool = false
						}
						uni.hideLoading()
					}).catch(function(error) {
						console.log('网络错误', error)
					})
				}
			} else {
				if (this.bool2) {
					++this.params3.page
					uni.showLoading({
						title: '加载中...'
					})
					this.$iBox
						.http('getRoomLog', this.params3)({
							method: 'post'
						})
						.then(res => {
							let new_list = this.logDetailList.concat(res.data.list)
							this.logDetailList = new_list
							if (this.logDetailList.length == res.data.count) {
								this.bool2 = false
							}
							uni.hideLoading()
						})
				}

			}

		}
	}
</script>

<style lang="scss">
	.roomListBox {
		width: 100%;
		height: 100rpx;
		background: #FFFFFF;

		.roomListBoxContent {
			display: flex;
			align-items: center;
			padding: 20rpx;

			.roomListBoxContent_item {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 150rpx;
				height: 50rpx;
				font-size: 26rpx;
				padding: 4rpx;
				border-radius: 8rpx;
				border: 1px solid #eee;
				margin-right: 20rpx;
			}
		}
	}

	.typeList {
		margin-top: 40rpx;

		.content {
			width: 700rpx;
			// min-height: 100rpx;
			border-radius: 20rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			padding: 20rpx;

			.itemBox {
				width: 98%;
				background: #f6f6f6;
				margin: 0 auto;
				padding: 20rpx 10rpx;

				.pickerBox {
					position: relative;
					height: 60rpx;
					width: 380rpx;
					border-radius: 14rpx;
					border: 1px solid #eee;
					display: flex;
					margin-top: 14rpx;
					font-size: 30rpx;
					align-items: center;
					padding: 0 10rpx;
				}
			}

			.itemBoxPay {
				margin: 20rpx 0;
				display: flex;
				align-items: center;
				border: 1px solid #878787;
				border-radius: 6rpx;
				padding: 20rpx 10rpx;

				.itemText {
					display: flex;
					flex-wrap: wrap;
					padding: 0 20rpx;
				}
			}

			.roomInfo {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;

				.item {
					line-height: 45rpx;
					font-size: 26rpx;
					padding: 0 15rpx;

					.item_text {
						color: #727272;
					}
				}
			}

			.btn_list {
				// justify-content: flex-start;
				direction: rtl;
				margin-top: 50rpx;
			}

			.fundBox {
				margin-top: 30rpx;

				.account {
					display: flex;
					align-items: center;
					height: 160rpx;
					width: 100%;
					padding: 10rpx;
					border-radius: 10rpx;
					background-color: #c1d7ff;

					.amountBox {
						margin-left: 30rpx;
						font-size: 24rpx;
					}
				}

				.btnBoxList {
					display: flex;
					flex-wrap: wrap;
					margin-top: 30rpx;

					.btnbg {
						background-color: #0869de;
						color: #FFFFFF;
						border: 0px;
					}
				}
			}
		}
	}

	.selectRoom {

		width: 700rpx;
		height: 500rpx;
		border-radius: 20rpx;
		padding: 20rpx 20rpx;

		.btnClass {
			width: fit-content;
			padding: 10rpx 22rpx;
			// height: 60rpx;
			// border: 1px solid #727272;
			border-radius: 12rpx;
			margin-left: 14rpx;
			background-color: cornflowerblue;
			margin: 10rpx auto;
		}
	}

	.bookIn {
		height: 80vh;
		position: relative;
		display: flex;
		flex-direction: column;
		padding: 10rpx;

		.bookBill {
			display: flex;
			flex-direction: column;
			align-items: center;

			.item {
				margin: 20rpx 0;
				display: flex;
				// flex-direction: column;
				align-items: center;

				// border-bottom: 1px solid #eee;
				.item_roomBox {
					width: 640rpx;

					.item_roomBox_title {
						width: 100%;
						display: flex;
						align-items: center;

						.item_roomBox_title_text {
							width: fit-content;
							font-size: 28rpx;
							padding: 0 20rpx;

						}
					}
				}

				.item_msg {
					width: 640rpx;
					display: flex;
					flex-wrap: wrap;
					align-items: center;

					.item_msg_inpt {
						border: 1px solid #eee;
						padding: 0 10rpx;
						border-radius: 4rpx;
						height: 40rpx;
						background: #fff;
						margin: 4rpx;
					}

					.msgItem {
						display: flex;
						align-items: center;
						padding: 10rpx 0;
						width: 120rpx;

						.pickerBox {
							position: relative;
							height: 43rpx;
							width: 110rpx;
							border-radius: 4rpx;
							border: 1px solid #eee;
							display: flex;
							font-size: 30rpx;
							align-items: center;
							background-color: #fff;
							padding-left: 6rpx;
							font-size: 26rpx;

							.arrow {
								animation-name: to_bottom_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}

							.arrow_ac {
								animation-name: to_up_show;
								animation-duration: 0.2s;
								animation-timing-function: linear;
								/* animation-delay: 1s; */
								/* animation-iteration-count: infinite; */
								animation-direction: normal;
								animation-play-state: running;
								animation-fill-mode: forwards;
							}


						}
					}
				}
			}
		}
	}

	.continueBox {
		min-height: 700rpx;
		width: 730rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-top: 40rpx;

		.msgItem {
			display: flex;
			align-items: center;
			padding: 20rpx 0;
		}

	}

	.checkOut {
		min-height: 700rpx;
		width: 680rpx;
		border-radius: 20rpx;
		padding: 20rpx;

		.billMounte {
			min-height: 120rpx;
			padding: 20rpx;
			display: flex;
			align-items: center;

			border-radius: 10rpx;
			margin-top: 20rpx;
		}
	}

	.btnClass {
		width: fit-content;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		margin-top: 10rpx;
	}

	.btnClass1 {
		width: fit-content;
		padding: 10rpx 6rpx;
		// height: 60rpx;
		font-size: 22rpx;
		border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		margin-top: 10rpx;
	}

	.noCheckOut {
		padding: 30rpx;
	}

	.connectBox {
		height: 90vh;
		width: 700rpx;
		padding: 10rpx;

		.fundBox {
			margin-top: 30rpx;

			.account {
				display: flex;
				align-items: center;
				height: 160rpx;
				width: 90%;
				padding: 10rpx;
				border-radius: 10rpx;
				// background-color: #c1d7ff;

				.amountBox {
					margin-left: 30rpx;
					font-size: 24rpx;
				}
			}
		}

		.itemBox {
			width: 92%;
			background: #f6f6f6;
			margin: 0 auto;
			padding: 20rpx 10rpx;

			.pickerBox {
				position: relative;
				height: 60rpx;
				width: 380rpx;
				border-radius: 14rpx;
				border: 1px solid #eee;
				display: flex;
				margin-top: 14rpx;
				font-size: 30rpx;
				align-items: center;
				padding: 0 10rpx;
			}
		}

		.itemBoxPay {
			margin: 20rpx 0;
			display: flex;
			align-items: center;
			border: 1px solid #878787;
			border-radius: 6rpx;
			padding: 20rpx 10rpx;

			.itemText {
				display: flex;
				flex-wrap: wrap;
				padding: 0 20rpx;
			}
		}
	}

	.logBox {
		display: flex;
		flex-direction: column;

		.logItem {
			min-height: 160rpx;
			width: 700rpx;
			margin: 20rpx auto;
			background-color: #FFFFFF;
			border-radius: 20rpx;
			line-height: 50rpx;
			border: 1px solid #eee;
			border-radius: 10rpx;
			padding: 14rpx;
		}
	}
</style>
