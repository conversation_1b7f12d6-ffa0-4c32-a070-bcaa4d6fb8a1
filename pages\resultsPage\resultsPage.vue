<template>
	<view class="box">
		<view class="resBox">
			<view class="resTitle">
				<view class="icon-chenggong" style="font-size: 80rpx;" :style="{color:themeColor.main_color}"></view>
				<text style="font-size: 48rpx;font-weight: 600;padding-left: 20rpx;">{{payType=='daodian'?'预定成功(到店付)':'付款成功'}}</text>
			</view>
			<p style="margin-top: 30rpx;color: dimgray;font-size: 24rpx;">{{payType=='daodian'?'您选择的是到店付方式！请到酒店后支付房费办理入住！':'您已成功预订房间，可以通过「订单详情」查看！'}}</p>
		</view>
		<view class="btnClass">
			<view class="ording" @click="goOrding">
				继续订房
			</view>
			
			<view class="goDetail" @click="goDetail" :style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}">
				查看订单
			</view>
			
		</view>
		
		<view class="goAuto" v-if="autoRoomSetting" @click="goAuto" :style="{color:themeColor.main_color,border:'1px solid '+themeColor.main_color}">
			立即办理入住
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				autoRoomSetting:false,
				payType:''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'unit', 'startDate', 'endDate', 'roomInfo', 'linkMan', 'shopSetting']),
			},
		
		onLoad(options) {
			// 获取自助入住设置，
			this.autoRoomSetting = this.shopSetting.filter(item => {
				return item.sign == 'self_check_in'
			})[0].property.status
			console.log(options,'type');
			this.payType = options.payType
		},
			methods:{
				goDetail(){
					uni.navigateTo({
						url:'/packageA/hotelBill/hotelBill'
					})
				},
				goOrding(){
					uni.switchTab({
						url:'/pages/index/index'
					})
				},
				goAuto(){
					uni.switchTab({
						url:'/pages/myRoom/myRoom'
					})
				}
			}
	}
</script>

<style lang="scss" scoped>
	page {
		background: #fff;
	}
	.box {
		height: 100vh;
		background: #fff;
	}
	
	.resBox{
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		
		.resTitle{
			margin-top: 140rpx;
			display: flex;
			align-items: center;
		}
	}
	
	.btnClass{
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 80rpx 50rpx;
		.ording{
			height: 100rpx;
			border: 1px solid #7d7d7d;
			color: #7d7d7d;
			width: 300rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		
		.goDetail {
			height: 100rpx;
			width: 300rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
		

	}
	.goAuto {
		height: 100rpx;
		width: 650rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		margin: 0 auto;
	}
</style>
