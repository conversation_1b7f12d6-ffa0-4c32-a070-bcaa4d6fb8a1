<template>
	<view class="box" style="position: relative;height: auto;" :style="{color:themeColor.bg_color}">
		<image src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/centerpg.png"
			style="position: absolute;top: 0;width: 630rpx;height: 380rpx;left: 0;right: 0;margin:  0 auto;z-index: 1;;"
			mode=""></image>
		<image v-if="mode==1" :src="modeStyle.property.bg_image"
			style="position: absolute;width: 100%;height: 100%;top: 0;left: 0;z-index: 0;" mode=""></image>
		<view class="" style="position: absolute;width: 100%;height: 600rpx;top: 0;left: 0;z-index: 0;" v-if="mode==2"
			:style="{background: 'linear-gradient(180deg, '+ themeColor.bg_main_color+'40'+' 0%, '+ '#F5F5F5 100%)' }">

		</view>
		<view class="" style="position: absolute;width: 100%;height: 500rpx;top: 0;left: 0;z-index: 0;" v-if="mode==3"
			:style="{background: 'linear-gradient(180deg, '+ themeColor.bg_main_color+'40'+' 0%, '+ '#F5F5F5 100%)'}">

		</view>
		<view class="noStyle"
			v-if="userInfo.grade==-1||(userInfo.grade_info&&userInfo.grade_info.upgrade_growth_value==-1)"
			:style="{color:modeStyle.property.text_color}">
			<p style="font-size: 44rpx;margin-top: 120rpx;z-index: 3;">Hello,欢迎来到{{hotel.shop_name}}</p>
			<p style="font-size: 24rpx;padding-top: 10rpx;z-index: 3;">
				加入酒店会员即可享受入住优惠，体验优质得会员服务</p>
			<p style="font-size: 30rpx;padding-top: 20rpx;z-index: 3;">
				您当前为散客，点击注册会员享受更多订房优惠！</p>
			<view class="btn" style="z-index: 3;" :style="{background:themeColor.com_color2,color:themeColor.bg_color}"
				@click="toLogin">
				<text>注册会员</text>
			</view>
		</view>
		<!-- 是会员 -->
		<view class="" v-else>
			<view class="style1" v-if="mode==1">
				<view class="nameBox">
					<image :src="userInfo.avatar_url"
						style="z-index: 2;height: 80rpx;width: 80rpx;margin-right: 20rpx;border-radius: 50%;" mode=""
						@click="editInfo">
					</image>
					<view class="" style="z-index: 2" :style="{color:modeStyle.property.text_main_color}">
						<p style="font-size: 30rpx;font-weight: 600;">{{userInfo.nickname}}</p>
						<p style="z-index: 2;font-size: 28rpx;" :style="{color:modeStyle.property.text_color}"
							v-if="growthShow">成长值:{{userInfo.growth}}</p>
					</view>

					<view class="smBox" style="z-index: 2;"
						:style="{background:themeColor.com_color2,color:modeStyle.property.bg_color}" @click="editInfo">
						<image src="../../../static/images/edit.png"
							style="width: 24rpx;height: 24rpx;padding-right: 6rpx;border-radius: 50%;"></image>
						<text>编辑信息</text>
					</view>
				</view>

				<view class="vipCard">
					<swiper class="swiper" :current="swCurrent" circular next-margin="50rpx" @change="itemCurrent">
						<swiper-item v-for="(item, index) in vipList" :key="index">
							<view class="swiper-item">
								<image :src="item.card_image"
									style="position: absolute;top:0;right:0;width: 670rpx;height: 100%;z-index: 2;border-radius: 30rpx;"
									mode="">
								</image>

								<view class="tips" v-if="userInfo.grade==item.id&&hotel.id!=956">
									<text>当前等级</text>
								</view>

								<view class="progress">
									<!-- <progress :percent="preLevel(item)" v-if="hotel.id!=956"  stroke-width="3" :activeColor="themeColor.com_color3"
										backgroundColor="#b3c1d2" /> -->
									<!-- 	<p style="font-size: 22rpx;margin-top: 20rpx;" :style="{color:themeColor.main_color}" v-if="index!=vipList.length-1">再{{settingValue(item)}}可以升级到{{nextLevel(item)}}</p>
									
									<text style="font-size: 22rpx;margin-top: 20rpx;" :style="{color:themeColor.main_color}" v-if="index==vipList.length-1">成为{{vipList[vipList.length-1].grade_name}}，享受更多优惠</text> -->
								</view>


								<view class="saleBox" @click="toSale" v-if="if_buy">
									<text>购买等级{{nextZk(item)?(nextZk(item)==10?'享受更多优惠':'享受'+nextZk(item)+"折优惠"):`享受更多优惠`}}</text>
								</view>

							</view>

						</swiper-item>
					</swiper>

					<view class="vipSign"
						style="height: 380rpx;background: linear-gradient(360deg, rgba(243, 248, 255, 1) 0%, rgba(198, 207, 231, 1) 100%);border-radius: 32rpx;backdrop-filter: blur(16rpx);border-bottom-left-radius: 0rpx;border-bottom-right-radius: 0rpx;"
						@click="toRights" :style="{color:modeStyle.property.text_color}">
						<view class=""
							style="display: flex;align-items: center;justify-content: space-between;padding: 20rpx 0;">
							<p>会员权益</p>
							<view class="icon-jiantou">
							</view>
						</view>

						<view class="signBox" style="height: 380rpx;">
							<view class="signItem" v-for="(item, index) in rightList" @click.stop="getRight(item)"
								:key="index" v-if="index<7">
								<image :src="item.icon" style="width: 60rpx;height: 60rpx;"
									:style="item.status==0?'opacity:0.25':''" mode="">
								</image>
								<text style="font-size: 24rpx;"
									:style="item.status==0?'color:'+modeStyle.property.text_color+'99':''">{{item.name}}</text>
							</view>
							<view class="signItem" style="opacity: 0.8;">
								<view class="icon-gengduogongneng" style="font-size: 54rpx;color: #939ac1;">
								</view>
								<text style="font-size: 24rpx;"
									:style="item.status==0?'color:'+modeStyle.property.text_color:''">更多权益</text>
							</view>
						</view>

					</view>
				</view>
			</view>
			<!-- 样式2 -->
			<view class="" style="z-index: 2;position: relative;" v-if="mode==2"
				:style="{color:modeStyle.property.text_color}">
				<view class="" :style="{'height':navBarHeight+'px'}"></view>
				<view class="" style="display: flex;align-items: center;justify-content: space-between;"
					@click="editInfo">
					<view class="" style="padding: 0rpx 32rpx;display: flex;align-items: center;z-index: 2;">
						<image :src="userInfo&&userInfo.avatar_url" v-if="userInfo.avatar_url"
							style="width: 128rpx;height: 128rpx;border-radius: 50%;" mode=""></image>
						<view class="" v-else style="width: 128rpx;height: 128rpx;border-radius: 50%;font-size: 28rpx;
						background-color: aliceblue;display: flex;align-items: center;justify-content: center;color: #8c8c8c;">
							无头像
						</view>
						<view class=""
							style="display: flex;height: 78rpx;margin-left: 20rpx;flex-direction: column;justify-content: space-between">
							<text>{{userInfo.user_name?userInfo.user_name:'暂无'}}</text>
							<text style="font-size: 30rpx;"
								v-if="userInfo.phone">{{userInfo.phone?userInfo.phone:''}}</text>
						</view>
					</view>
					<p style="margin-right: 30rpx;display: flex;align-items: center;color: #00000080;"><uni-icons
							type="compose" size="28"></uni-icons>编辑信息</p>
				</view>
				<!-- 会员卡样式 -->
				<view class="" @click="toVip"
					style="height: 300rpx;width: 750rpx; margin: -30rpx auto;border-radius: 24rpx;display: flex;align-items: center;padding: 48rpx;justify-content: space-between;position: relative;">
						<image :src="userInfo.grade_info.card_image" style="height: 300rpx;width: 686rpx;position: absolute;top: 0;left: 0;right: 0;margin: 0 auto;z-index: 0;" mode=""></image>
						<view class="" style="padding: 24rpx;z-index: 9;display: flex;flex-direction: column;margin-top: 60rpx;color: #ffffff;">
							<text style="font-size: 52rpx;">{{userInfo.grade_name}}</text>
							<text style="font-size: 24rpx;margin-top: 14rpx;">会员体验升级，享受酒店特权</text>
						</view>
				</view>
				<view class="vipSign1" @click="toRights" :style="{color:modeStyle.property.text_color}"
					style="width: 100%;padding: 0;height: 200rpx;">
					<view class="signBox1"
						style="height: 200rpx;background: linear-gradient(360deg, rgba(243, 248, 255, 0) 0%, rgba(198, 207, 231, 0.5) 100%);border-radius: 32rpx;backdrop-filter: blur(16rpx);">
						<view class="signItem1" v-for="(item, index) in rightList" @click.stop="getRight(item)"
							:key="index" v-if="index<7">
							<image :src="item.icon" style="width: 60rpx;height: 60rpx;"
								:style="item.status==0?'opacity:0.25':''" mode="">
							</image>
							<text style="font-size: 24rpx;"
								:style="item.status==0?'color:'+modeStyle.property.text_color:''">{{item.name}}</text>
						</view>
						<view class="signItem1" style="opacity: 0.8;">
							<view class="icon-gengduogongneng" style="font-size: 54rpx;color: #939ac1;">
							</view>
							<text style="font-size: 24rpx;"
								:style="item.status==0?'color:'+modeStyle.property.text_color:''">更多权益</text>
						</view>
					</view>

				</view>
			</view>

			<!-- 样式3 -->
			<view class="" style="z-index: 2;position: relative;" v-if="mode==3"
				:style="{color:modeStyle.property.text_color}">
				<view class="" :style="{'height':navBarHeight+'px'}"></view>
				<view class="" style="display: flex;align-items: center;justify-content: space-between;"
					@click="editInfo">
					<view class="" style="padding: 0rpx 32rpx;display: flex;align-items: center;z-index: 2;">
						<image :src="userInfo&&userInfo.avatar_url" v-if="userInfo.avatar_url"
							style="width: 128rpx;height: 128rpx;border-radius: 50%;" mode=""></image>
						<view class="" v-else style="width: 128rpx;height: 128rpx;border-radius: 50%;font-size: 28rpx;
						background-color: aliceblue;display: flex;align-items: center;justify-content: center;color: #8c8c8c;">
							无头像
						</view>
						<view class=""
							style="display: flex;margin-left: 20rpx;flex-direction: column;justify-content: space-between">
							<text>{{userInfo.user_name?userInfo.user_name:'暂无'}}</text>
							<text style="font-size: 30rpx;"
								v-if="userInfo.phone">{{userInfo.phone?userInfo.phone:''}}</text>
							<text style="font-size: 30rpx;">{{userInfo.grade_info.grade_name}}</text>
						</view>
					</view>
					<p style="margin-right: 30rpx;display: flex;align-items: center;color: #00000080;"><uni-icons
							type="compose" size="28"></uni-icons>编辑信息</p>
				</view>
				<!-- 会员卡样式 -->
				<view class="" @click="toVip"
					style="box-sizing: border-box;height: 120rpx;width: 700rpx; margin: 30rpx auto;margin-bottom:0;background: #33d1a770;border-top-left-radius: 24rpx;border-top-right-radius: 24rpx;display: flex;align-items: center;padding: 30rpx;justify-content: space-between;"
					:style="{background:modeStyle.property.text_color}">
					<view class="" :style="{color:themeColor.bg_color}">
						<p
							style="font-size: 32rpx;font-weight: 600;background: linear-gradient(to right,#ffffff, #f7f9ff);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">
							升级成为{{nextLevel(userInfo.grade_info)}}</p>
						<view class="" style="display: flex;align-items: center;margin-top: 8rpx;" v-if="buyVip">
							<view class="" :style="{background:themeColor.com_color3}"
								style="font-size: 22rpx;border-radius: 8rpx;padding: 6rpx 10rpx;">
								{{fx}}%返现
							</view>
							<view class="" :style="{background:themeColor.com_color3}"
								style="font-size: 22rpx;border-radius: 8rpx;padding: 6rpx 10rpx;margin: 0 14rpx;">
								{{memRights.total_count}}项暖心
							</view>
							<view class="" :style="{background:themeColor.com_color3}"
								style="font-size: 22rpx;border-radius: 8rpx;padding: 6rpx 10rpx;">
								赠送优惠券
							</view>
						</view>
					</view>
					<view class=""
						style="height: 60rpx;padding: 0 20rpx;;border-radius: 12rpx;display: flex;align-items: center;justify-content: center;font-size: 26rpx;"
						:style="'background:'+themeColor.bg_color+';color:'+themeColor.title_mian_color">
						<view v-if="buyVip" style="display: flex;align-items: center;justify-content: space-between;">
							<view><text style="font-size: 22rpx;">￥</text><text>{{buyVip.amount}}</text></view>
							<text>立即升级</text>
						</view>
						<text v-else>查看会员</text>
					</view>
				</view>
				<view class="vipSign" @click="toRights" :style="{color:modeStyle.property.text_color}"
					style="width: 100%;padding: 0;">
					<view class="signBox"
						style="background: linear-gradient(360deg, rgba(243, 248, 255, 0) 0%, rgba(198, 207, 231, 0.5) 100%);border-radius: 32rpx;backdrop-filter: blur(16rpx);">
						<view class="signItem" v-for="(item, index) in rightList" @click.stop="getRight(item)"
							:key="index" v-if="index<4">
							<image :src="item.icon" style="width: 60rpx;height: 60rpx;"
								:style="item.status==0?'opacity:0.25':''" mode="">
							</image>
							<text style="font-size: 24rpx;"
								:style="item.status==0?'color:'+modeStyle.property.text_color:''">{{item.name}}</text>
						</view>
						<!-- <view class="signItem" style="opacity: 0.8;">
							<view class="icon-gengduogongneng" style="font-size: 54rpx;color: #939ac1;">
							</view>
							<text style="font-size: 24rpx;"
								:style="item.status==0?'color:'+modeStyle.property.text_color:''">更多权益</text>
						</view> -->
					</view>

				</view>
			</view>
		</view>

		<!-- 详细 -->
		<m-popup :show="ifShow" mode="bottom" @closePop="closePop1">
			<view class="" style="height: 500rpx;width: 100%;;padding: 30rpx;color:#000">
				<view
					style="font-weight: 600;display: flex;align-items: center;justify-content: center;font-size: 40rpx;">
					{{rights.name}}
				</view>
				<view class="" style="margin-top: 20rpx;">
					{{rights.desc}}
					<text v-if="rights.sign=='zk'">,享受{{rights.value}}%折扣</text>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				vipList: [],
				rightList: [],
				swCurrent: 0,
				pop: false,
				ifShow: false,
				rights: null,
				growthShow: false,
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				buyVip: null,
				memRights: null,
				zk: '',
				fx: ''
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'setting']),
		},
		props: {
			if_buy: {
				type: Boolean,
				default: false
			},
			modeStyle: {
				type: Object,
				default: null
			},
			mode: {
				type: Number,
				default: 1
			}
		},

		watch: {
			rightList: {
				handler(newVal) {
					newVal.forEach(item => {
						if (item.sign == 'xffx') {
							this.fx = item.value
						} else if (item.sign == 'zk') {
							this.zk = item.value
						}
					})
				}
			}
		},
		async mounted() {
			await this.$onLaunched;
			const systemInfo = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.menuButtonInfo = menuButtonInfo
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo.statusBarHeight + 44;
			this.growthShow = this.setting.filter(item => {
				return item.sign == 'show_user_growth'
			})[0].property.status


			this.$iBox.http('getMemberGrade', {})({
				method: 'post'
			}).then(res => {
				this.getGrade(res.data)
				this.vipList = res.data

				for (var i = 0; i < this.vipList.length; i++) {
					if (this.vipList[i].id == this.userInfo.grade) {
						this.swCurrent = i
						this.rightList = this.vipList[i].right_itererest
						break
					}
				}

			})

			this.$iBox.http('getMemberGradeBuySetting', {})({
				method: 'post'
			}).then(res => {
				if (res.data.length > 0) {
					this.buyVip = res.data[0]
					console.log(this.buyVip, 'this.buyVip');
				}
			})

			this.$iBox.http('getMemberRightGoodsCount', {
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {
				this.memRights = res.data
			})

		},
		methods: {
			...mapActions('hotel', ['getGrade']),
			editInfo() {
				uni.navigateTo({
					url: '/pages/supplementInfo/supplementInfo'
				})
			},

			getRight(e) {
				console.log(e);
				this.rights = e
				if (e.status == 1) {
					this.ifShow = true
				} else {
					uni.showToast({
						icon: 'none',
						title: '升级会员享受此权益'
					})
				}

			},
			toVip() {
				uni.navigateTo({
					url: '/pages/vipLevel/vipLevel'
				})
			},
			closePop1() {
				this.ifShow = false
			},
			closePop() {
				this.pop = false
			},
			itemCurrent(e) {
				this.swCurrent = e.detail.current
				this.rightList = this.vipList[this.swCurrent].right_itererest
			},
			preLevel(e) {
				let level = this.setting.filter(item => {
					return item.sign == 'upgrade_model'
				})[0].property
				let mode = {}
				let upValue = 0
				if (level.type == 1) {
					mode = level.growth
					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id && i != this.vipList.length - 1) {
							upValue = (this.userInfo.growth / this.vipList[i + 1].upgrade_growth_value) * 100 * Number(mode
								.amount / mode.growth)
							break;
						}
					}
				} else {
					mode = level.night

					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id && i != this.vipList.length - 1) {
							upValue = (this.userInfo.growth / this.vipList[i + 1].upgrade_growth_value) * 100 * Number(mode
								.growth / mode.night)
							break;
						}
					}

				}

				return upValue.toFixed(0)
			},
			settingValue(e) {

				let level = this.setting.filter(item => {
					return item.sign == 'upgrade_model'
				})[0].property
				let mode = {}
				let upValue = 0
				if (level.type == 1) {
					mode = level.growth

					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id) {
							console.log(this.vipList[i + 1].upgrade_growth_value, mode, 'mode');
							upValue = '消费' + (this.vipList[i + 1].upgrade_growth_value / Number(mode.amount / mode.growth))
								.toFixed(0) + '元'
							break;
						}
					}
				} else {
					mode = level.night
					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id) {
							upValue = '住' + (this.vipList[i + 1].upgrade_growth_value / Number(mode.growth / mode.night))
								.toFixed(0) + '晚'
							break;
						}
					}

				}


				return upValue
			},

			nextLevel(e) {
				let upValue = ''
				for (var i = 0; i < this.vipList.length; i++) {
					if (this.vipList[i].id == e.id && i != this.vipList.length - 1) {

						upValue = this.vipList[i + 1].grade_name
						break;
					} else {
						upValue = '最高'
					}

				}
				return upValue

			},
			nextZk(e) {
				let upValue = ''
				for (var i = 0; i < this.vipList.length; i++) {
					if (this.vipList[i].id == e.id && i != this.vipList.length - 1) {

						upValue = this.vipList[i + 1].right_itererest.filter(item => {
							return item.sign == 'zk'
						})[0]?this.vipList[i + 1].right_itererest.filter(item => {
							return item.sign == 'zk'
						})[0].value / 10:0
						break;
					} else {
						upValue = '最高'
					}

				}
				return upValue
			},
			toRights() {
				uni.navigateTo({
					url: '/pages/vipRights/vipRights'
				})
			},
			toSale() {
				uni.navigateTo({
					url: '/pages/vipLevel/vipLevel'
				})
			},
			toLogin() {
				let set = this.setting.filter(item => {
					return item.sign == 'auto_register_member'
				})
				if (set[0].property) {
					let a = set[0].property.value
					if (a == 2) {
						uni.navigateTo({
							url: '/pages/login/login'
						})

					} else if (a == 1) {
						// this.pop = true
						uni.navigateTo({
							url: '/packageA/memberInfo/memberInfo'
						})
					}
				}
			}
		}
	}
</script>

<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.noStyle {
		height: 500rpx;
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		z-index: 3;

		.btn {
			height: 80rpx;
			border-radius: 60rpx;
			width: 240rpx;
			margin-top: 50rpx;
			display: flex;
			align-items: center;
			justify-content: space-around;
		}
	}


	.style1 {
		z-index: 2;

		.nameBox {

			height: 160rpx;
			width: 100%;
			display: flex;
			align-items: center;
			padding: 120rpx 30rpx 0 30rpx;

			.smBox {
				height: 40rpx;
				width: fit-content;
				padding: 14rpx 14rpx;
				background-color: #fff;
				border-radius: 20rpx;
				display: flex;
				font-size: 22rpx;
				align-items: center;
				justify-content: center;
				margin-left: 20rpx;
			}

		}

		.vipCard {
			width: 100%;
			// padding: 20rpx 0 20rpx 20rpx;
			display: flex;
			flex-direction: column;
			margin-top: 30rpx;

			.swiper {
				width: 100%;
				height: 350rpx;
				padding: 20rpx 0;

				.swiper-item {
					width: 680rpx;
					height: 350rpx;
					// background-color: #bdd7ef;
					border-radius: 20rpx;
					display: flex;
					flex-direction: column;
					position: relative;

					&_name {
						display: flex;
						align-items: center;
						position: relative;
					}

					.progress {
						position: absolute;
						bottom: 20rpx;
						left: 40rpx;
						padding: 30rpx 0;
						width: 500rpx;
						display: flex;
						flex-direction: column;
						z-index: 4;
					}

					.saleBox {
						position: absolute;
						display: flex;
						align-items: center;
						justify-content: center;
						z-index: 4;
						width: fit-content;
						padding: 6rpx 14rpx;
						border-radius: 30rpx;
						font-size: 20rpx;
						color: #e3d1b0;
						background: #363434;
						right: 30rpx;
						bottom: 30rpx;
					}

					.tips {
						display: flex;
						align-items: center;
						justify-content: center;
						position: absolute;
						width: 140rpx;
						height: 50rpx;
						top: 0;
						right: 0;
						border-top-right-radius: 20rpx;
						border-bottom-left-radius: 20rpx;
						background-color: #4d5257;
						font-size: 26rpx;
						z-index: 4;
					}
				}
			}


		}
	}

	.vipSign {
		padding: 0 30rpx;
		z-index: 3;
		height: 90rpx;

		.signBox {
			display: flex;
			flex-wrap: wrap;
			height: 90rpx;
			padding: 20rpx 0; 
			.signItem {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 25%;
				// padding: 20rpx;
			}
		}

	}

	.vipSign1 {
		padding: 0 30rpx;
		z-index: 3;
		height: 150rpx;

		.signBox1 {
			display: flex;
			flex-wrap: wrap;
			height: 150rpx;

			.signItem1 {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 25%;
				padding: 20rpx;
			}
		}

	}
</style>