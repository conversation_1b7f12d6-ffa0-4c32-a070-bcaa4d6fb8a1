/**
 * 缓存配置文件
 * 管理哪些接口需要缓存，哪些需要实时数据
 */

// 需要实时数据的接口列表（不使用缓存）
export const REAL_TIME_APIS = [
	'getShopList',    // 酒店列表 - 需要实时库存和价格信息
	'getUI'           // UI配置 - 需要实时的界面配置和活动信息
]

// 缓存策略配置
export const CACHE_STRATEGIES = {
	// 用户相关 - 较长缓存时间
	'getUserInfo': 24 * 60 * 60 * 1000,        // 24小时
	'login': 24 * 60 * 60 * 1000,              // 24小时
	
	// 设置相关 - 中等缓存时间
	'getSetting': 60 * 60 * 1000,              // 1小时
	'getShopSetting': 60 * 60 * 1000,          // 1小时
	'getSaleType': 60 * 60 * 1000,             // 1小时
	
	// 地理位置相关 - 较长缓存时间
	'getShopCities': 7 * 24 * 60 * 60 * 1000,  // 7天
	
	// 分销相关 - 中等缓存时间
	'updateDistributionUserId': 60 * 60 * 1000, // 1小时
	
	// 首页UI相关 - 短缓存时间
	'getHomePageUi': 30 * 60 * 1000,           // 30分钟
	
	// 默认缓存时间
	'default': 30 * 60 * 1000                  // 30分钟
}

// 检查接口是否需要实时数据
export function isRealTimeApi(apiName) {
	return REAL_TIME_APIS.includes(apiName)
}

// 获取接口的缓存时间
export function getCacheTime(apiName) {
	return CACHE_STRATEGIES[apiName] || CACHE_STRATEGIES.default
}

// 缓存配置说明
export const CACHE_CONFIG_DOCS = {
	description: '缓存配置管理',
	realTimeApis: {
		'getShopList': '酒店列表需要实时库存、价格、可用性信息',
		'getUI': 'UI配置需要实时的界面布局、活动信息、主题配置'
	},
	cacheReasons: {
		'getUserInfo': '用户基本信息变化频率低',
		'getSetting': '系统设置相对稳定',
		'getShopCities': '城市列表基本不变',
		'getShopSetting': '酒店设置变化频率低'
	},
	notes: [
		'实时接口确保数据准确性，提升用户体验',
		'缓存接口减少网络请求，提升响应速度',
		'可根据业务需求调整缓存策略'
	]
}
