<template>
	<view class="container">
		<view class="mainPage" style="position: fixed;top:0;z-index: -1;width: 750rpx;height: 100vh;">

		</view>
		<view class="" style="position: fixed;top: 0;width: 750rpx;z-index:9;">
			<view class="" :style="{marginTop:searchBarTop + 'px',height:searchBarHeight + 'px'}"
				style="display: flex;align-items: center;justify-content: center;width: 100%;z-index: 9;position: relative;">
				<view class="" @click="goMain"
					style="position: absolute;left: 30rpx;top: 0;bottom:0;margin: 0 auto;height: 100%;display: flex;align-items: center;">
					<uni-icons type="left" size="24"></uni-icons>
				</view>
				<text style="color: #000000;">超市服务</text>
			</view>

		</view>
		<view class="" style="" :style="{height:navBarHeight+'px'}"></view>
		<view class="" style="box-sizing: border-box;display: flex;align-items: center;justify-content: center;">
			<view class="" style="min-height: 176rpx;width: 686rpx;box-sizing: border-box;">
				<view class="" style="background: linear-gradient(94.86deg, #CFE8FF 0%, #D3EAFD 51%, #95A5DA 100%);padding:0 32rpx;
width: 100%;height: 88rpx;border: 1px solid #FFFFFFCC;border-top-right-radius: 32rpx;
border-top-left-radius: 32rpx;display: flex;align-items: center;">
					<p style="font-size: 32rpx;font-weight: 600;background: linear-gradient(to right,#96979B, #5D5E63);-webkit-background-clip: text;-webkit-text-fill-color: transparent;max-width: 400rpx;overflow: hidden;
white-space: nowrap;text-overflow: ellipsis;">{{userInfo.grade_info.grade_name}}</p>
					<p style="margin-left: 16rpx;font-size: 28rpx;"><text
							style="background: linear-gradient(to right,#96979B, #5D5E63);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">今日已订</text><text
							style="color: #dc8ea2;">{{memRights.use_count}}项</text><text
							style="background: linear-gradient(to right,#96979B, #5D5E63);-webkit-background-clip: text;-webkit-text-fill-color: transparent;">服务，还可以免费享</text><text
							style="color: #dc8ea2;">{{memRights.useable_count}}项</text></p>
				</view>
				<view class="" style="min-height: 96rpx;background-color: #EFF8FF;padding: 16rpx 32rpx;">
					<view class="" style="height: 48rpx;display: flex;align-items: center;">
						<view class="" style="min-width: 152rpx;height: 48rpx;display: flex;align-items: center;">
							<image src="http://doc.hanwuxi.cn/wp-content/uploads/2024/11/Group-92.png"
								style="width: 48rpx;height: 48rpx;" mode=""></image>
							<text style="margin-left: 8rpx;font-size: 24rpx;color: #000000E0;">服务{{fw_count}}项</text>
						</view>
					</view>
					<view class="" @click="toBuy" v-if="ifBuy&&pageSource=='form'"
						style="height: 68rpx;display: flex;align-items: center;justify-content: space-between;width: 100%;">
						<p style="color:#00000066;font-size: 24rpx;">{{buyVip.amount}}元立享{{buyVip.grade_name}}权益</p>
						<view class=""
							style="height: 60rpx;width: 198rpx;border-radius: 12rpx;display: flex;align-items: center;justify-content: center;font-size: 26rpx;"
							:style="'background:'+themeColor.main_color+'26;color:'+themeColor.main_color">
							升级会员
						</view>
					</view>
					<view class="" @click="toRoom" v-else
						style="height: 68rpx;display: flex;align-items: center;justify-content: space-between;width: 100%;">
						<p style="color:#00000066;font-size: 24rpx;">订房立享免费权益</p>
						<view class=""
							style="height: 60rpx;width: 198rpx;border-radius: 12rpx;display: flex;align-items: center;justify-content: center;font-size: 26rpx;"
							:style="'background:'+themeColor.main_color+'26;color:'+themeColor.main_color">
							立即订房
						</view>
					</view>
				</view>
			</view>
		</view>
		<!-- 滚动公告栏 end -->
		<view class="main">
			<!-- 左侧菜单 begin -->
			<scroll-view class="menu-bar" scroll-y scroll-with-animation :scroll-top="productsScrollTop">
				<view class="wrapper">
					<view class="menu-item" @tap="handleMenuSelected(category.id)"
						:style="currentCategoryId == category.id?'border-left:7rpx solid '+ themeColor.main_color:''"
						:class="{active: currentCategoryId == category.id}" v-for="(category, index) in categories"
						:key="index">
						<!-- <image :src="category.category_image_url" class="image" mode="widthFix"></image> -->
						<view class="title">{{ category.type_name }}</view>
					</view>
				</view>
			</scroll-view>
			<!-- 左侧菜单 end -->
			<!-- 右侧商品列表 begin -->
			<scroll-view class="product-section" scroll-y scroll-with-animation :scroll-into-view="viewName"
				@scroll="productsScroll">
				<view class="wrapper">
					<!-- <view id="ads">
					
						<swiper class="ads1" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000"
							circular>
							<swiper-item v-for="(ad, index) in ads1" :key="index">
								<image :src="ad" class="w-100" mode="widthFix"></image>
							</swiper-item>
						</swiper>
					
						<swiper class="ads2" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000"
							circular>
							<swiper-item v-for="(ad, index) in ads2" :key="index">
								<image :src="ad" class="w-100" mode="widthFix"></image>
							</swiper-item>
						</swiper>
						
					</view> -->
					<!-- 商品 begin -->
					<view class="products-list" v-for="(category, index) in categories" :key="index"
						:id="`products-${category.id}`">
						<view class="category-name">{{ category.type_name }}</view>
						<view class="products">
							<view class="product" v-for="(product, key) in category.goods_list" :key="key"
								style="height: 182rpx;" @tap="showProductDetailModal(product)">
								<view class="" style="display: flex;height: 182rpx;align-items: flex-start;">
									<image :src="product.cover_pic" mode="aspectFill" class="image"></image>
								</view>

								<view class="content">
									<view class="name">{{ product.name }}</view>
									<view class="labels">
										<view class="label" style="border-radius: 6rpx;"
											:style="'color:' + themeColor.main_color + ';border:1px solid ' + themeColor.main_color+';background:'+themeColor.main_color+'26;'"
											v-for="label in product.pay_type_list" :key="label.id">
											{{ label.pay_type_name }}支付
										</view>
									</view>
									<!-- <view class="description">{{ product.description }}</view> -->
									<!-- :materials-btn="!product.is_single"多规格 -->
									<view class="price">
										<view v-if="!product.ifMem">￥{{ product.price }} </view>
										<view :style="'color:' + themeColor.main_color" v-if="product.ifMem">
											<text>免费</text>
											<text
												style="font-size: 28rpx;color: #00000066;text-decoration: line-through;margin-left: 20rpx;">￥{{product.price}}</text>
										</view>
										<actions v-if="hackReset&&product.stock>0"
											@materials="showProductDetailModal(product)"
											:number="productCartNum(product.id)" @add="handleAddToCart(product)"
											@minus="handleMinusFromCart(product)" />
										<text v-if="product.stock==0"
											style="font-size: 26rpx;color: #5D5E63;">商品已售罄</text>
									</view>
									<view class="">
										<text
											style="font-size:24rpx;color: #00000066;font-weight: 200;">库存:{{product.stock}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<!-- 商品 end -->
				</view>
			</scroll-view>
			<!-- 右侧商品列表 end -->
		</view>
		<!-- 商品详情 modal begin -->
		<product-modal v-if="hackReset" :product="product" :visible="productModalVisible"
			@cancel="closeProductDetailModal" @add-to-cart="handleAddToCartInModal" />
		<!-- 商品详情 modal end -->
		<!-- 购物车栏 begin -->
		<cart-bar v-if="hackReset" :source="pageSource" :cart="cart" @add="handleAddToCart" @minus="handleMinusFromCart"
			@clear="clearCart" @pay="pay" />
		<!-- 购物车栏 end -->
		<!-- <search :show="showSearch" :categories="categories" @hide="showSearch=false" @choose="showProductDetailModal">
		</search> -->
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	import Actions from './components/actions/actions.vue'
	import CartBar from './components/cartbar/cartbar.vue'
	import ProductModal from './components/product-modal/product-modal.vue'
	import cartPopup from './components/cart-popup/cart-popup.vue'
	// import Search from './components/search/search.vue'

	export default {
		components: {
			Actions,
			CartBar,
			ProductModal,
			cartPopup,
			// Search
		},
		data() {
			return {
				params: {
					page: 1,
					limit: 10,
					shop_id: ''
				},
				categories: [],
				cart: [],
				product: {},
				currentCategoryId: 0,
				notices: [],
				ads1: [],
				ads2: [],
				productModalVisible: false,
				cartPopupShow: false,
				productsScrollTop: 0,
				showSearch: false,
				viewName: '',
				verticalNavTop: 0,
				navBarHeight: 0,
				searchBarTop: 0,
				searchBarHeight: 0,
				fw_count: 0,
				centerUser: null,
				ifBuy: false, //是否能够购买会员
				nextName: '',
				buyVip: '',
				memRights: null,
				pageSource: '',
				hackReset: true,
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'startDate', 'endDate', 'unit', 'setting']),
			...mapState('market', ['cartList']),
			productCartNum() { //计算单个添加到购物车的数量
				return id => this.cart.reduce((acc, cur) => {
					if (cur.id === id) {
						return acc += cur.number
					}
					return acc
				}, 0)
			},

		},
		watch: {
			cart: {
				handler(newVal, oldVal) {
					// 判断购物车的变动，是否免费服务商品大于可用服务数量,有的话将列表的服务全部改成不免费

				},
				immediate: true,
				deep: true
			}
		},
		async onShow() {
			await this.$onLaunched;
			this.hackReset = false
			this.$nextTick(() => {
				this.hackReset = true
			})
			let pages = getCurrentPages(); //获取当前页面js里面的pages里的所有信息。
			let currentPage = pages[pages.length - 2]; //获取当前页面的对象
			let url = '' //当前页面url
			if (currentPage) {
				url = currentPage.route //当前页面url
				if (url.includes('/roomForm')) {
					this.pageSource = "form"
					if (this.cartList.length > 0) {
						this.cart = JSON.parse(JSON.stringify(this.cartList))
					}
					// 
				} else {
					this.pageSource = ''
				}
			}
			console.log(this.cartList, 'this.cartList');


			const systemInfo = wx.getSystemInfoSync();
			let menuButtonInfo = uni.getMenuButtonBoundingClientRect();
			this.searchBarTop = menuButtonInfo.top;
			this.searchBarHeight = menuButtonInfo.height;
			this.navBarHeight = systemInfo.statusBarHeight + 44;
			this.$iBox.http('getUserInfo', {
				simple: false
			})({
				method: 'post'
			}).then(res => {
				let userInfo = res.data
				userInfo.session_key = this.userInfo.session_key
				this.centerUser = userInfo
				this.updateUserInfo(userInfo)
				this.$iBox.http('getMemberGrade', {})({
					method: 'post'
				}).then(res => {
					this.vipList = res.data

					let upValue = ''
					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == this.centerUser.grade_info.id && i != this.vipList
							.length - 1) {

							upValue = this.vipList[i + 1].grade_name
							break;
						} else {
							upValue = '最高'
						}

					}

					this.nextName = upValue
					// 购买等级
					this.$iBox.http('getMemberGradeBuySetting', {})({
						method: 'post'
					}).then(res => {
						if (res.data.length > 0) {
							this.ifBuy = true
							this.buyVip = res.data[0]
						} else {
							this.ifBuy = false
						}

					})


				})

				this.$iBox.http('getMemberRightGoodsCount', {
					shop_id: this.hotel.id
				})({
					method: 'post'
				}).then(res => {
					this.memRights = res.data
					let count = ''
					if (userInfo.grade_info) {
						count = userInfo.grade_info.right_itererest.filter(item => {
							return item.sign == 'nxff'
						})
					}

					if (count && count.length > 0) {
						this.fw_count = count[0].value
					}
					this.fw_count
					this.params.page = 1
					this.params.shop_id = this.hotel.id
					this.$iBox
						.http('getStoreGoodsTypeList', this.params)({
							method: 'post'
						})
						.then(res => {
							let list = res.data.list
							let memcount = 0

							list.forEach(item => {
								item.goods_list.forEach(item1 => {
									item1.number = 0
									// 先检查购物车中是否包含权益物品，若超过数量则不显示免费字样，若不超过则显示免费
									if (this.cart.length > 0) {
										this.cart.forEach(item2 => {
											//判断购物车中是否有数量大于2的同一件服务商品，大于2只算一件
											if (item2.ifMem) {
												memcount++
											}
										})
									}

								})
							})

							if (this.pageSource == "form") {

								list.forEach(item => {
									item.goods_list.forEach(item1 => {
										if (item1.member_right == 1) {
											item1.ifMem = true
										}
									})
								})

							} else {
								list.forEach(item => {
									item.goods_list.forEach(item1 => {
										item1.ifMem = false
									})
								})
							}



							this.categories = list
							console.log(this.categories, 'sp');
							this.currentCategoryId = res.data.list[0].id
							this.viewName = 'products-' + res.data.list[0].id
							this.$nextTick(() => this.calcSize())
						})

					uni.hideLoading()
				})



			})

			// this.notices = await this.$api('notices')

		},
		methods: {
			...mapActions('login', ['updateUserInfo']),
			...mapActions('market', ['getCart']),
			goMain() {
				uni.navigateBack({})
			},
			toRoom() {
				uni.navigateTo({
					url: '/pages/hotelDetail/hotelDetail'
				})
			},
			toBuy() {
				uni.navigateTo({
					url: '/pages/vipLevel/vipLevel'
				})
			},
			switchOrderType() {
				// if (this.orderType === 'takein') {
				// 	uni.navigateTo({
				// 		url: '/pages/addresses/addresses'
				// 	})
				// } else {
				// 	this.SET_ORDER_TYPE('takein')
				// }
			},
			handleAddToCart(product) { //添加到购物车

				let set = this.setting.filter(item => {
					return item.sign == 'auto_register_member'
				})
				if (set[0].property) {
					let a = set[0].property.value
					if (a == 2) {
						if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
							.upgrade_growth_value > -1) {
							this.if_login = false
							// 查询活动
							//商品是服务属性，先查询购物车是否有
							const index = this.cart.findIndex(item => {
								if (!product.is_single) {
									return (item.id == product.id) && (item.materials_text == product
										.materials_text)
								} else {
									return item.id === product.id
								}
							})
							if (index > -1) {
								if (this.cart[index].number >= product.stock) {
									uni.showToast({
										icon: 'none',
										title: '该商品库存已不足,剩余' + product.stock
									})
									return
								}

							} else {
								if (product.number >= product.stock) {
									uni.showToast({
										icon: 'none',
										title: '该商品库存已不足,剩余' + product.stock
									})
									return
								}
							}
							if (this.cart.length == 0) {
								if (product.number > 1) {
									if (product.ifMem) {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: true
										})

										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number - 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: false
										})
									} else {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number || 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: false
										})
									}
								} else {
									if (product.ifMem) {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number || 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: true
										})
									} else {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number || 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: false
										})
									}
								}

							} else {
								let memCount = 0
								this.cart.forEach(item => {
									if (item.ifMem) {
										memCount++
									}
								})
								console.log(this.memRights.useable_count, memCount);
								if (this.memRights.useable_count > memCount) {
									if (product.number > 1) {
										// 当添加服务商品时
										if (product.ifMem) {
											let ifHave = 0
											// 当剩余服务小于已选服务时
											for (let item of this.cart) {
												if (item.id == product.id) {
													ifHave++
												}
											}
											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: true
												})

												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: product.number - 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: false
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: product.number,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number = item.number + product.number
														break;
													}
												}
											}
										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += (product.number)
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: product.number,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})
										}

									} else {
										// 当添加服务商品时
										if (product.ifMem) {
											let ifHave = 0
											// 当剩余服务小于已选服务时
											for (let item of this.cart) {
												if (item.id == product.id) {
													ifHave++
												}
											}
											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: true
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: 1,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number++
														break;
													}
												}
											}
										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += 1
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: 1,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})
										}
									}



								} else {
									if (product.number > 1) {
										if (product.ifMem) {
											let ifHave = 0
											// 当等于服务小于已选服务时,所有新增的服务商品都要变成付费商品
											for (let item of this.cart) {
												if (item.id == product.id && item.ifMem) {
													ifHave = 1
												} else if (item.id == product.id && !item.ifMem) {
													ifHave = 2
												}
											}

											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: product.number,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: false
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: product.number,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number += product.number
														break;
													}
												}
											}

										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += (product.number || 1)
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: product.number || 1,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})


										}
									} else {
										if (product.ifMem) {
											let ifHave = 0
											// 当等于服务小于已选服务时,所有新增的服务商品都要变成付费商品
											for (let item of this.cart) {
												if (item.id == product.id && item.ifMem) {
													ifHave = 1
												} else if (item.id == product.id && !item.ifMem) {
													ifHave = 2
												}
											}

											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: product.number || 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: false
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: product.number || 1,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number++
														break;
													}
												}
											}

										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += (product.number || 1)
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: product.number || 1,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})


										}
									}


								}

							}
							let list = []
							let list1 = []

							list = this.cart.filter(item => {
								return item.ifMem === true
							})
							list1 = this.cart.filter(item => {
								return item.ifMem === false
							})
							this.cart = [...list, ...list1]

						} else {
							uni.navigateTo({
								url: '/pages/login/login'
							})
						}

					} else if (a == 1) {
						// this.pop = true
						if (this.userInfo.grade_info && this.userInfo
							.grade_info.upgrade_growth_value > -1) {
							// 查询活动
							//商品是服务属性，先查询购物车是否有
							const index = this.cart.findIndex(item => {
								if (!product.is_single) {
									return (item.id == product.id) && (item.materials_text == product
										.materials_text)
								} else {
									return item.id === product.id
								}
							})
							if (index > -1) {
								if (this.cart[index].number >= product.stock) {
									uni.showToast({
										icon: 'none',
										title: '该商品库存已不足,剩余' + product.stock
									})
									return
								}

							} else {
								if (product.number >= product.stock) {
									uni.showToast({
										icon: 'none',
										title: '该商品库存已不足,剩余' + product.stock
									})
									return
								}
							}
							if (this.cart.length == 0) {
								if (product.number > 1) {
									if (product.ifMem) {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: true
										})

										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number - 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: false
										})
									} else {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number || 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: false
										})
									}
								} else {
									if (product.ifMem) {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number || 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: true
										})
									} else {
										this.cart.push({
											id: product.id,
											cate_id: product.category_id,
											name: product.name,
											price: product.price,
											number: product.number || 1,
											image: product.cover_pic,
											pay_type: product.pay_type,
											ifMem: false
										})
									}
								}

							} else {
								let memCount = 0
								this.cart.forEach(item => {
									if (item.ifMem) {
										memCount++
									}
								})
								console.log(this.memRights.useable_count, memCount);
								if (this.memRights.useable_count > memCount) {
									if (product.number > 1) {
										// 当添加服务商品时
										if (product.ifMem) {
											let ifHave = 0
											// 当剩余服务小于已选服务时
											for (let item of this.cart) {
												if (item.id == product.id) {
													ifHave++
												}
											}
											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: true
												})

												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: product.number - 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: false
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: product.number,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number = item.number + product.number
														break;
													}
												}
											}
										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += (product.number)
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: product.number,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})
										}

									} else {
										// 当添加服务商品时
										if (product.ifMem) {
											let ifHave = 0
											// 当剩余服务小于已选服务时
											for (let item of this.cart) {
												if (item.id == product.id) {
													ifHave++
												}
											}
											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: true
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: 1,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number++
														break;
													}
												}
											}
										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += 1
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: 1,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})
										}
									}



								} else {
									if (product.number > 1) {
										if (product.ifMem) {
											let ifHave = 0
											// 当等于服务小于已选服务时,所有新增的服务商品都要变成付费商品
											for (let item of this.cart) {
												if (item.id == product.id && item.ifMem) {
													ifHave = 1
												} else if (item.id == product.id && !item.ifMem) {
													ifHave = 2
												}
											}

											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: product.number,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: false
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: product.number,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number += product.number
														break;
													}
												}
											}

										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += (product.number || 1)
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: product.number || 1,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})


										}
									} else {
										if (product.ifMem) {
											let ifHave = 0
											// 当等于服务小于已选服务时,所有新增的服务商品都要变成付费商品
											for (let item of this.cart) {
												if (item.id == product.id && item.ifMem) {
													ifHave = 1
												} else if (item.id == product.id && !item.ifMem) {
													ifHave = 2
												}
											}

											if (ifHave == 0) {
												this.cart.push({
													id: product.id,
													cate_id: product.category_id,
													name: product.name,
													price: product.price,
													number: product.number || 1,
													image: product.cover_pic,
													pay_type: product.pay_type,
													ifMem: false
												})
											} else if (ifHave == 1) {
												for (let item of this.cart) {
													if (item.id == product.id) {
														this.cart.push({
															id: product.id,
															cate_id: product.category_id,
															name: product.name,
															price: product.price,
															number: product.number || 1,
															image: product.cover_pic,
															pay_type: product.pay_type,
															ifMem: false
														})
														break;
													}
												}
											} else if (ifHave > 1) {
												for (let item of this.cart) {
													if (!item.ifMem && item.id == product.id) {
														item.number++
														break;
													}
												}
											}

										} else {
											// 当添加非服务商品
											const index = this.cart.findIndex(item => {
												return item.id === product.id
											})

											if (index > -1) {
												this.cart[index].number += (product.number || 1)
												return
											}
											this.cart.push({
												id: product.id,
												cate_id: product.category_id,
												name: product.name,
												price: product.price,
												number: product.number || 1,
												image: product.cover_pic,
												pay_type: product.pay_type,
												ifMem: false
											})


										}
									}


								}

							}
							let list = []
							let list1 = []

							list = this.cart.filter(item => {
								return item.ifMem === true
							})
							list1 = this.cart.filter(item => {
								return item.ifMem === false
							})
							this.cart = [...list, ...list1]

						} else {
							uni.navigateTo({
								url: '/packageA/memberInfo/memberInfo'
							})
						}
					}
				}



			},
			handleMinusFromCart(product) { //从购物车减商品
				// 如果是减服务商品
				let index
				console.log(product, 'l');
				if (product.ifMem) {
					let num = 0
					this.cart.forEach(item => {
						if (item.id == product.id && item.ifMem == false) {
							num++
						}
					})
					console.log(num);
					for (let item of this.cart) {
						if (num > 0) {
							if (item.id == product.id && item.ifMem === false) {
								item.number--
								break
							}
						} else {
							if (item.id == product.id && item.ifMem === true) {
								item.number--
								break
							}
						}
					}
					console.log(this.cart, 'car');
					this.cart = this.cart.filter(item => {
						return item.number > 0
					})

					// index = this.cart.findIndex(item => (item.id == product.id && item.ifMem))
				} else {

					let num = 0
					this.cart.forEach(item => {
						if (item.id == product.id && product.ifMem === false) {
							num++
						}
					})
					console.log(num);
					for (let item of this.cart) {
						if (num > 0) {
							if (item.id == product.id && item.ifMem === false) {
								item.number--
								break
							}
						} else {
							if (item.id == product.id) {
								item.number--
								break
							}
						}
					}
					console.log(this.cart, 'car');
					this.cart = this.cart.filter(item => {
						return item.number > 0
					})

					// index = this.cart.findIndex(item => (item.id == product.id))
					// this.cart[index].number -= 1
					// if (this.cart[index].number <= 0) {
					// 	this.cart.splice(index, 1)
					// }
				}


			},
			showProductDetailModal(product) {
				this.product = product
				this.productModalVisible = true
			},
			handleAddToCartInModal(product) {

				let set = this.setting.filter(item => {
					return item.sign == 'auto_register_member'
				})
				if (set[0].property) {
					let a = set[0].property.value
					if (a == 2) {
						if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
							.upgrade_growth_value > -1) {
							this.if_login = false
							
							this.handleAddToCart(product)
							this.closeProductDetailModal()

						} else {
							this.if_login = true
						}

					} else if (a == 1) {
						// this.pop = true
						if (this.userInfo.grade_info && this.userInfo
							.grade_info.upgrade_growth_value > -1) {
							
							this.handleAddToCart(product)
							this.closeProductDetailModal()

						} else {
							uni.navigateTo({
								url: '/packageA/memberInfo/memberInfo'
							})
						}
					}
				}

			},
			closeProductDetailModal() {
				this.productModalVisible = false
				this.product = {}
			},
			openCartDetailsPopup() {
				this.$refs['cartPopup'].open()
			},
			clearCart() {
				this.cart = []
				this.categories.forEach(item => {
					item.goods_list.forEach(item1 => {
						item1.number = 0
					})
				})
			},
			handleMenuSelected(id) {
				this.productsScrollTop = this.categories.find(item => item.id == id).top
				this.currentCategoryId = id
				this.viewName = 'products-' + id
				console.log(this.viewName, 'this.viewName');
			},
			productsScroll({
				detail
			}) {
				const {
					scrollTop
				} = detail
				let tabs = this.categories.filter(item => item.top <= scrollTop).reverse()
				console.log(this.categories, 'tabs');
				if (tabs.length > 0) {
					this.currentCategoryId = tabs[0].id
				}
			},
			calcSize() {
				let h = 0

				this.categories.forEach(item => {
					let view = uni.createSelectorQuery().select(`#products-${item.id}`)
					view.fields({
						size: true
					}, data => {
						console.log(data);
						item.top = h
						h += Math.floor(data.height)
						item.bottom = h
					}).exec()
				})
				console.log('cal', this.categories);
			},
			pay() {
				console.log('ww', this.pageSource);
				this.getCart(this.cart)
				if (this.pageSource == 'form') {
					uni.navigateBack({})
				} else {
					uni.navigateTo({
						url: '/packageA/market/pay/pay'
					})
				}

			}
		},
		onUnload() {
			console.log('hide', this.cart);

		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	@import './index.scss';

	.mainPage {
		background: linear-gradient(180deg, #c2e5e0 0%, #FFFFFF 20%);

	}
</style>