<template>
	<view>
		<view class="elseItem" :style="{color:themeColor.text_main_color}">
			<view class="head">
				<view class="icon-gengduogongneng" :style="{color:themeColor.com_color3}"></view>
				<text style="padding-left: 10rpx;">管理端服务</text>
			</view>
			<view class="body" :style="{background:themeColor.bg_color}">
				<view class="bodyItem" @click="goPages()">
					<view class="icon-shebeisheshi" style="font-size: 54rpx;" :style="{color:themeColor.main_color}">
						
					</view>
					<text style="font-size: 30rpx;margin-top: 6rpx;">管理端登录</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			};
		},
		props:{
			style:{
				type:Number
			},
			list:{
				type:Array,
				default:[]
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		methods:{
			goPages(e){
				uni.navigateTo({
					url:'/packageA/manager/manager'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.elseItem {
		width: 700rpx;
		min-height: 200rpx;
		border-radius: 20rpx;
		margin: 60rpx auto;
		// background-color: #FFFFFF;
		.head {
			padding: 20rpx;
			width: 100%;
			// border-bottom:1px solid #EBEDF0;
			display: flex;
			align-items: center;
		}
		
		.body{
			display: flex;
			align-items: center;
			padding: 0 48rpx;
			border-radius: 32rpx;
			width: 100%;
			flex-wrap: wrap;
			.bodyItem{
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 200rpx;
				width: 33%;
			}
		}
	}
</style>
