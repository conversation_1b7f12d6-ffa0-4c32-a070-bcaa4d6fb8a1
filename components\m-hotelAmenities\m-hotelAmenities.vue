<template>
	<view class="box" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
		<view class="title">
			<text :style="{color:themeColor.text_color}">酒店设施</text>
			<view class="detail" :style="{color:themeColor.text_title_color}" @click="toAmen" v-if="detailShow">
				查看详情
				<view class="icon-jiantou" style="font-size: 22rpx;padding-left: 6rpx;">
				</view>
			</view>
		</view>
		<view class="content" :style="{background:themeColor.bg1_color}">
			<view class="item" v-for="(item,index) in hotel.facility" :key="item.id" v-if="index<10">
				<image :src="item.icon" style="height: 60rpx;width: 60rpx;" mode=""></image>
				<text class="item_content">{{item.title}}</text>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-amenities",
		props:{
			detailShow:{
				type:Boolean,
				default:true
			}
		},
		data() {
			return {
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
			
		},
		methods:{
			toAmen(){
				uni.navigateTo({
					url:'/packageA/amenities/amenities'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.box {
		// width: 750rpx;
		box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;
		// padding: 20rpx;
		.title {
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 30rpx;
			// border-bottom: 1px solid #e4e7ed;
			.detail {
				display: flex;
				align-items: center;
				color: #585858;
				font-size: 22rpx;
			}
		}

		.content {
			padding:0 20rpx;
			// width: 700rpx;
			margin: 0 auto;
			// background-color: #f8f9fd;
			border-radius: 20rpx;
			margin-bottom: 30rpx;
			display: flex;
			flex-wrap: wrap;
			
			.item {
				width: 20%;
				height: 160rpx;
				display: flex;
				flex-direction: column;
				align-items: center;
				padding: 30rpx 0;
				font-size: 22rpx;
				
				.i_title{
					width: 23%;
					display: flex;
					flex-direction: column;
					align-items: center;
				}
				
				.item_content{
					width: 100%;
					text-align: center;
					padding: 3rpx;
					white-space:nowrap;
					overflow:hidden;
					text-overflow:ellipsis;
				}
			}
			
		}
	}
</style>
