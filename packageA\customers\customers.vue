<template>
	<view>
		<view class="box" v-for="(item, index) in list" :key="index">
			<image src="/static/images/customs.png" style="height: 120rpx;width: 120rpx;" mode=""></image>
			<view class="nameBox">
				<text>{{item.name}}</text>
				<view class="" style="display: flex;align-items: center;">
					<view class="icon-dianhua"></view>
					<text style="margin-left: 8rpx;">{{item.phone}}</text>
				</view>
			</view>
			<view class="" style="position: absolute;right: 30rpx;height: 100%;display: flex;align-items: center;justify-content: center;">
				<p style="font-size: 54rpx;color: #5555ff;" class="icon-dianhua" @click="makePhone(item.phone)"></p>
				<view class="" style="position: relative;">
					<p style="font-size: 54rpx;color: #00aa00;margin-left: 60rpx;"  class="icon-weixin"></p>
					<button class='contact-button' open-type='contact'>a</button>
				</view>
				
			</view>
			
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				params:{
					page:1,
					limit:100,
					shop_id:''
				},
				list:[]
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'cityModel'])
		},
		onLoad() {
			this.params.page=1
			this.params.shop_id = this.hotel.id
			this.$iBox.http('getCustomerServiceList', this.params)({
				method: 'post'
			}).then(res => {
				this.list = res.data.list
				uni.hideLoading()
			})
		},
		methods:{
			makePhone(e){
				uni.makePhoneCall({
					phoneNumber:e
				})
			}
		}
	}
</script>

<style lang="scss">
	.box {
		padding: 20rpx;
		display: flex;
		align-items: center;
		width: 700rpx;
		margin: 20rpx auto;
		height: 180rpx;
		border-radius: 20rpx;
		background: #fff;
		position: relative;
		.nameBox{
			display: flex;
			flex-direction: column;
			height: 100%;
			justify-content: space-around;
			padding: 14rpx;
		}
		
		
		.contact-button { 
		 display: inline-block; 
		 position: absolute; 
		 width: 100%; 
		 background: salmon; 
		 opacity: 0; 
		 top: 0;
		}
	}
</style>
