/**
 * 性能监控工具
 * 用于监控应用启动时间、请求性能、缓存命中率等指标
 */
class PerformanceMonitor {
	constructor() {
		this.metrics = {
			appLaunchTime: 0,
			pageLoadTime: 0,
			requestCount: 0,
			cacheHitCount: 0,
			errorCount: 0,
			networkRequests: []
		}
		this.startTime = Date.now()
		this.isEnabled = true // 可以通过配置控制是否启用监控
	}

	// 记录应用启动时间
	recordAppLaunch() {
		if (!this.isEnabled) return
		
		this.metrics.appLaunchTime = Date.now() - this.startTime
		console.log(`📊 应用启动时间: ${this.metrics.appLaunchTime}ms`)
		
		// 如果启动时间超过3秒，记录为性能问题
		if (this.metrics.appLaunchTime > 3000) {
			console.warn('⚠️ 应用启动时间过长，建议优化')
		}
	}

	// 记录页面加载时间
	recordPageLoad(pageName, loadTime) {
		if (!this.isEnabled) return
		
		this.metrics.pageLoadTime = loadTime
		console.log(`📊 ${pageName} 页面加载时间: ${loadTime}ms`)
		
		if (loadTime > 2000) {
			console.warn(`⚠️ ${pageName} 页面加载时间过长`)
		}
	}

	// 记录网络请求
	recordNetworkRequest(url, duration, fromCache = false) {
		if (!this.isEnabled) return
		
		this.metrics.requestCount++
		
		if (fromCache) {
			this.metrics.cacheHitCount++
		}

		this.metrics.networkRequests.push({
			url,
			duration,
			fromCache,
			timestamp: Date.now()
		})

		console.log(`📊 请求: ${url}, 耗时: ${duration}ms, 缓存: ${fromCache ? '命中' : '未命中'}`)
	}

	// 记录错误
	recordError(error, context = '') {
		if (!this.isEnabled) return
		
		this.metrics.errorCount++
		console.error(`❌ 错误 [${context}]:`, error)
	}

	// 计算缓存命中率
	getCacheHitRate() {
		if (this.metrics.requestCount === 0) return 0
		return (this.metrics.cacheHitCount / this.metrics.requestCount * 100).toFixed(2)
	}

	// 获取性能报告
	getPerformanceReport() {
		const report = {
			...this.metrics,
			cacheHitRate: this.getCacheHitRate() + '%',
			averageRequestTime: this.getAverageRequestTime(),
			totalRunTime: Date.now() - this.startTime
		}

		console.log('📊 性能报告:', report)
		return report
	}

	// 计算平均请求时间
	getAverageRequestTime() {
		if (this.metrics.networkRequests.length === 0) return 0
		
		const totalTime = this.metrics.networkRequests.reduce((sum, req) => sum + req.duration, 0)
		return (totalTime / this.metrics.networkRequests.length).toFixed(2)
	}

	// 检查性能指标是否达标
	checkPerformanceTargets() {
		const targets = {
			appLaunchTime: 2000,    // 应用启动时间 < 2秒
			pageLoadTime: 1500,     // 页面加载时间 < 1.5秒
			cacheHitRate: 80,       // 缓存命中率 > 80%
			errorRate: 5            // 错误率 < 5%
		}

		const results = {
			appLaunchTime: this.metrics.appLaunchTime <= targets.appLaunchTime,
			pageLoadTime: this.metrics.pageLoadTime <= targets.pageLoadTime,
			cacheHitRate: parseFloat(this.getCacheHitRate()) >= targets.cacheHitRate,
			errorRate: (this.metrics.errorCount / this.metrics.requestCount * 100) <= targets.errorRate
		}

		console.log('🎯 性能目标达成情况:', results)
		return results
	}

	// 导出性能数据（用于分析）
	exportMetrics() {
		const data = {
			timestamp: new Date().toISOString(),
			metrics: this.metrics,
			report: this.getPerformanceReport(),
			targets: this.checkPerformanceTargets()
		}

		// 在开发环境下可以将数据发送到分析服务
		if (process.env.NODE_ENV === 'development') {
			console.log('📤 导出性能数据:', JSON.stringify(data, null, 2))
		}

		return data
	}

	// 重置监控数据
	reset() {
		this.metrics = {
			appLaunchTime: 0,
			pageLoadTime: 0,
			requestCount: 0,
			cacheHitCount: 0,
			errorCount: 0,
			networkRequests: []
		}
		this.startTime = Date.now()
		console.log('🔄 性能监控数据已重置')
	}

	// 启用/禁用监控
	setEnabled(enabled) {
		this.isEnabled = enabled
		console.log(`📊 性能监控已${enabled ? '启用' : '禁用'}`)
	}
}

// 创建全局实例
const performanceMonitor = new PerformanceMonitor()

// 导出实例和类
export default performanceMonitor
export { PerformanceMonitor }
