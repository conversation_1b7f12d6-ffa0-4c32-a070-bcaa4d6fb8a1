<template>
	<view class="box" :style="{background:themeColor.bg_color,color:themeColor.text_main_color}">
		<view class="title">
			<text :style="{color:themeColor.text_main_color}">酒店设施</text>
			<view class="detail" :style="{color:themeColor.text_second_color}" @click="toAmen" v-if="detailShow">
				查看详情
				<view class="icon-jiantou" style="font-size: 22rpx;padding-left: 6rpx;">
				</view>
			</view>
		</view>
		<view class="content" :style="{background:themeColor.bg_color}">
			<view class="item" v-for="(item,index) in list" :key="item.id">
				<view class="i_title">
					<view class="icon-shebeisheshi" style="font-size: 60rpx;">
						
					</view>
					<!-- <image src="../../static/images/rate/rate1_1.png" mode="" style="width: 80rpx;height: 80rpx;"></image> -->
					<text style="font-size: 24rpx;">{{item.label}}</text>
				</view>
				<view class="i_content">
					<view class="i_content_i" v-for="item1 in item.des" :key="item1.id">
							<view class="icon-duigouzhong" :style="{color:themeColor.main_color}">
								
							</view>
						
						<text :style="{color:themeColor.text_second_color}">{{item1}}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		name: "m-amenities",
		props:{
			detailShow:{
				type:Boolean,
				default:true
			},
			list:{
				type:Array,
				default:true
			}
		},
		data() {
			return {
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		methods:{
			toAmen(){
				uni.navigateTo({
					url:'/packageA/amenities/amenities'
				})
			}
		}
	}
</script>
<style>
	view{
		box-sizing: border-box;
	}
</style>
<style lang="scss" scoped>
	.box {
		// width: 750rpx;
		// box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px;
		margin: 20rpx auto;
		border-radius: 20rpx;
		padding: 20rpx;
		.title {
			height: 100rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			padding: 0 30rpx;
			// border-bottom: 1px solid #e4e7ed;
			.detail {
				display: flex;
				align-items: center;
				color: #585858;
				font-size: 22rpx;
			}
		}

		.content {
			padding:0 20rpx;
			// width: 700rpx;
			margin: 0 auto;
			// background-color: #f8f9fd;
			border-radius: 20rpx;
			margin-bottom: 30rpx;
			.item {
				width: 100%;
				min-height: 100rpx;
				display: flex;
				align-items: center;
				padding: 30rpx 0;
				.i_title{
					width: 23%;
					display: flex;
					flex-direction: column;
					align-items: center;
				}
				
				.i_content{
					width: 76%;
					display: flex;
					flex-wrap: wrap;
					padding: 30rpx;
					
					.i_content_i {
						// flex:1
						width: 48%;
						margin: 10rpx 0;
						display: flex;
						
						align-items: center;
						font-size: 28rpx;
						// text-overflow: ellipsis;
						// white-space: nowrap;
						//  overflow: hidden;
					}
				}
			}
			
		}
	}
</style>
