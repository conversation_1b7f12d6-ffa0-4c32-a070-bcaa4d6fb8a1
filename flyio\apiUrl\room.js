// API
const API = {
	'getSaleType': 'wx/Room/getRoomSellType', // 登录
	'getRoomType':'wx/Room/getRoomType',//查询房型
	'getStandardRoomPriceArrByDate':'wx/RoomBill/getStandardRoomPriceArrByDate',//查询全日房每日房价
	'getHourRoomPriceArrByDate':'wx/RoomBill/getHourRoomPriceArrByDate',//查询钟点房房价
	'getConferenceRoomPriceArrByDate':'wx/RoomBill/getConferenceRoomPriceArrByDate',//查询会议室房价
	'getLongStandardPriceArrByDate':'wx/RoomBill/getLongStandardPriceArrByDate',//查询月租房房价
	'standardBookRoom':'wx/StandardRoomBill/bookRoom',//全日房下单
	'standardApplyCancelRoomBill':'wx/StandardRoomBill/applyCancelRoomBill',//全日房申请取消
	'standardApplyCheckOut':'wx/StandardRoomBill/applyCheckOut',//全日房申请退房
	'standardExtendRoomBill':'wx/StandardRoomBill/extendRoomBill',//申请续房
	'hourBookRoom':'wx/HourRoomBill/bookRoom',//时租房预订下单
	'hourApplyCancelRoomBill':'wx/HourRoomBill/applyCancelRoomBill', //时租房取消
	'hourApplyCheckOut':'wx/HourRoomBill/applyCheckOut',//时租房退房
	'conferenceBookRoom':'wx/ConferenceRoom/bookRoom',//会议室下单
	'conferenceApplyCancelRoomBill':'wx/ConferenceRoom/applyCancelRoomBill',//会议室取消
	'conferenceApplyCheckOut':'wx/ConferenceRoom/applyCheckOut',
	'longStandardBookRoom':'wx/LongStandardRoomBill/bookRoom',//月租房下单
	'longStandardApplyCancelRoomBill':'wx/LongStandardRoomBill/applyCancelRoomBill',//月租房取消
	'longStandardApplyCheckOut':'wx/LongStandardRoomBill/applyCheckOut',//月租房退房
	'getRoomBillList':'wx/RoomBill/getRoomBillList',//查询订单
	'getRoomBillInfo':'wx/RoomBill/getRoomBillInfo',//订单详情
	'getMemberRechargeSettingList':'wx/MemberRecharge/getMemberRechargeSettingList',//充值设置
	'memberRecharge':'wx/MemberRecharge/memberRecharge',//确认充值
	'getUserBalanceRecord':'wx/User/getUserBalanceRecord',//余额记录
	'getUserPointRecord':'wx/User/getUserPointRecord',//积分记录
	'getBreakfastCouponList':'wx/BreakfastCoupon/getBreakfastCouponList',//早餐券
	'getWifiList':'wx/Wifi/getWifiList',//获取wifi列表
	'getWifiById':'wx/Wifi/getWifiById',//根据id查询wifi
	'addRepairs':'wx/Repairs/addRepairs',//添加报修
	'getRepairsList':'wx/Repairs/getRepairsList',//查询报修
	'getRoomBillCarNumber':'wx/RoomBill/getRoomBillCarNumber',//查询车
	'updateRoomBillCarNumber':'wx/RoomBill/updateRoomBillCarNumber',//添加车牌
	'getStayingRoom':'wx/RoomBill/getStayingRoom'
	
}

export default API
