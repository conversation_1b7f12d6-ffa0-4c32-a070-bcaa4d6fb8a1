<template>
	<view>
		<view class="" style="width: 100vw;display: flex;flex-direction: column;align-items: center;justify-content: center;margin-top: 50rpx;" >
			<view class="nameBox">
			
				<text style="padding-right: 120rpx;">价格:</text>
				<view class="" style="width: 400rpx;">
					<input type="digit"  placeholder="请输入核销价格" v-model="price" />
				</view>
			</view>
			
			<view class="nameBox">
			
				<text style="padding-right: 90rpx;">扣费名称:</text>
				<view class="" style="width: 400rpx;">
					<input type="text" placeholder="请输入扣费名称" v-model="memo" />
				</view>
			</view>
			<view class="" style="width: 500rpx;margin: 80rpx auto;">
				<view class="btn_register"
					:style="{background:themeColor.com_color1,color:themeColor.bg_color}" @click="scanCode">
					<text class="icon-saoma" style="font-size: 38rpx;"></text>扫码收款</view>
			</view>
		</view>
		
	</view>
</template>

<script>
	import { mapState, mapGetters, mapActions } from 'vuex';
	export default {
		data() {
			return {
				price:'',
				memo:''
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel'])
		},
		methods: {
			scanCode(){
				if(!this.price){
					uni.showToast({
						icon:'error',
						title:'请输入价格'
					})
					return
				}
				
				if(!this.memo){
					uni.showToast({
						icon:'error',
						title:'请输入扣费名称'
					})
					return
				}
				
				wx.scanCode({
				  onlyFromCamera: false,
				  success :(res)=> {
				    console.log(res)
					let qr = res.result
					uni.showLoading({
						title:'扫描中...'
					})
					this.$iBox
						.http('updateMemberBalance', {member_code:qr,amount:this.price,remark:this.memo})({
							method: 'post'
						})
						.then(res => {
							uni.showModal({
								title:'提示',
								content:'扣费成功',
								showCancel:false,
								success:(res)=> {
									this.price = ''
									this.memo = ''
								}
							})
							uni.hideLoading()
						})
						.catch(function(error) {
							uni.hideLoading()
							console.log('33434', error);
						});
					
					
				  }
				})
			},
		
		}
	}
</script>
	
<style>
	page {
		background: #f3f4f6;
	}
</style>
<style scoped lang="scss">
	.nameBox {
		padding: 30rpx 0;
		display: flex;
		align-items: center;
		border-bottom: 1px solid #e4e7ed;
		// justify-content: space-between;
	}
	.btn_register {
		display: flex;
		align-items: center;
		justify-content: center;
		width: 500rpx;
		height: 90rpx;
		border-radius: 20rpx;
	}
</style>
