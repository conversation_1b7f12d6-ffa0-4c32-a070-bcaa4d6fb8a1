<template>
	<view>
		<!-- 隐私提醒 -->
		<m-popup mode="center" :show="anthorShow" @closePop="closeAnthorShow">
			<view class="popupWrap">
				<view class="popupTxt">
					在您使用小程序之前，请仔细阅读
					<text class="blueColor" @click="handlerOpenPrivacyContract">{{name}}</text>。如您同意{{name}},请点击“同意”开始使用小程序。
				</view>
				<view class="popupBot" >
				<button id="disagree-btn" type="default" @click="handleDisagree">拒绝</button>
				<button id="agree-btn" type="primary" open-type="agreePrivacyAuthorization" @agreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
				</view>
			</view>
		</m-popup>
	</view>
</template>

<script>
	export default {
		name: "m-needAuthor",
		data() {
			return {
				anthorShow: false,
				name:''
			};
		},
		mounted() {
			// 获取隐私设置
			wx.getPrivacySetting({
				success: res => {
					console.log(res);
					if(res.needAuthorization){
						this.name = res.privacyContractName
						this.anthorShow = true
					}
				}
			})

		},
		methods:{
			handleDisagree(){
				//直接退出小程序
				wx.exitMiniProgram()
			},
			closeAnthorShow(){
				this.anthorShow = false
			},
			handlerOpenPrivacyContract(){
				wx.openPrivacyContract({
					fail:res=>{
						wx.showToast({
							title:'遇到错误',
							icon:'error'
						})
					}
				})
			},
			handleAgreePrivacyAuthorization(){
				this.anthorShow = false
			}
		}
	}
</script>

<style lang="scss" scoped>
	.popupWrap {
		width: 540rpx;
		box-sizing: border-box;
		padding: 42rpx;
		background: white;
		border-radius: 8rpx;
		z-index: 999999999;
		.blueColor {
			color: rgba(39,152, 24, 1);
		}

		.popupTxt {
			line-height: 48rpx;
		}

		.popupBot {
			display: flex;
			justify-content: space-around;
			align-items: center;
			margin-top: 3erpx;
		}
	}
</style>