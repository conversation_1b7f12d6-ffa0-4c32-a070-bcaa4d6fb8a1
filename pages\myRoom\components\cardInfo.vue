<template>
	<view>
		<image v-if="!billDetail" src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/6aae463f63cf32c84c8b60694de1071.png"
		style="width: 750rpx;position: absolute;top: 0;left: 0;height: 100vh;z-index: -1;" mode=""></image>
		<image v-if="billDetail&&!billDetail.room_number" src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/23240d4ba69a19f4c323dd037f67031-1.png"
		style="width: 750rpx;position: absolute;top: 0;left: 0;height: 100vh;z-index: -1;" mode=""></image>
		<!-- 样式一 -->
		<view class="" v-if="styleModel==1&&!billDetail">
			
			<view style="font-size: 24rpx;margin: 20rpx 40rpx;z-index: 99;margin-top: 220rpx;" :style="{color:themeColor.text_title_color}" >
				<text v-if="billDetail">*您正在入住，如遇到问题请联系前台</text>
				<text v-else style="font-size: 52rpx;color: #000000E0;z-index: 999;">暂未查询到您的入住信息，您还可以：</text>
				
			</view>
			<view class="" style="display: flex;width: 100%;justify-content: center;flex-direction: column;
			margin:0 auto;position: absolute;bottom: 220rpx;">
				<view class="noBillBox" style="display: flex;flex-direction: column;">
					<view class="searchBoxBtn">
						<view class="search_btn" style="font-size: 32rpx;" @click="searchBill" :style="{'color': themeColor.bg_color,'background': 'linear-gradient(90deg, '+themeColor.bg_main_color +' 0%,'+ themeColor.bg_main1_color +' 100%)'}">
							查预定
						</view>
					</view>
					<view class="noBillBtn" @click="chooseRoom" style="background: #FFFFFF4D;font-size: 30rpx;">
						预订房间
					</view>
				</view>
			</view>
			
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		props: {
			city: Boolean,
			billDetail: Object,
			styleModel: {
				type: Number,
				default:1
			}
		},
		data() {
			return {

			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['hotel','cityModel']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		async mounted() {
			await this.$onLaunched;
			console.log(this.billDetail, this.city, 'ddt');
		},
		methods: {
			changeHotel() {
				uni.navigateTo({
					url: '/pages/hotelList/hotelList'
				})
			},
			chooseRoom(){
				uni.navigateTo({
					url:'/pages/hotelDetail/hotelDetail'
				})
			},
			searchBill(){
				uni.navigateTo({
					url:'/packageA/autoRoom/searchPage/searchPage'
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.noBillBox {
		margin: 20rpx;
		width: 680rpx;
		// min-height: 300rpx;
		display: flex;
		// align-items: center;
	
		.noBillBtn {
			height: 96rpx;
			min-width: 622rpx;
			border-radius: 56rpx;
			margin: 32rpx auto;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
			background: #FFFFFF4D;
		}
	
	}
	
	
	.searchBoxBtn {
		margin: 10rpx auto;
		width: 680rpx;
		// min-height: 300rpx;
		display: flex;
		align-items: center;
		.search_btn {
			height: 96rpx;
			width: 622rpx;
			border-radius: 56rpx;
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: center;
			box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
			font-size: 24rpx;
		}
	}
</style>
