<template>
	<view class="" style="display: flex;flex-direction: column;align-items: center;">
		<text style="margin-top: 60rpx;">操作成功</text>
		
		<text style="margin-top: 60rpx;">名称：{{lockDetail.lock_alias}}</text>
		<text style="margin-top: 60rpx;">锁编号：{{lockDetail.lock_name}}</text>
		<view class="" style="margin-top: 60rpx;">
			<text>锁电量：</text><text style="color: #39B54A;">{{lockDetail.electric_quantity}}%</text>
		</view>

		<view style="width: 400rpx;margin: 100rpx auto;height: 500rpx;display: flex;flex-direction: column;justify-content: space-around;" class="">
			<!-- <button type="warn" @click="openLock">开锁测试</button> -->
			<button type="primary" @click="over">完成</button>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	const plugin = requirePlugin("myPlugin");
	export default {
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'roles_list']),
			...mapState('room', ['lockDetail']),
		},
		data() {
			return {

			}
		},
		async onLoad() {
			await this.$onLaunched;
			console.log(this.lockDetail,'dddd');
		},
		methods: {
			// 点击开锁  
			toOpenDoor() {
				uni.showLoading({
					title: '正在开启智能锁'
				})
				let deviceId = ""
				const start = Date.now();
				// 调用开锁接口
				plugin.controlLock(plugin.ControlAction.OPEN, this.lockDetail.lock_data, res => {
					if (res.errorCode === 10003) {
						console.log("获取版本信息时设备连接已断开", res)
					}
				}, null, deviceId).then(res => {
					uni.hideLoading({});
					console.log(res)
					if (!!res.deviceId) deviceId = res.deviceId;
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'none',
							title: `已开锁--操作时间:${Date.now() - start}ms.`
						})

						this.toReadRecord()
						uni.navigateTo({
							url: './lockDetail'
						})
					} else {
						uni.showToast({
							icon: 'none',
							title: `开锁失败: ${res.errorMsg}`
						})
					}
				})
			},
			// 校准锁时间
			toCheckLockTime() {
			
				uni.showLoading({
					title: "正在校准锁时间",
				})
				let deviceId = ''
				const start = Date.now();
				// 调用设置锁时间接口，（！！为安全考虑，开锁时间请传入服务器时间）
				// 2.7.0版本开始，开锁接口成功后自动校准本地锁时间
				plugin.setLockTime(Date.now(), this.lockDetail.lock_data, res => {
					if (res.errorCode === 10003) {
						console.log("获取版本信息时设备连接已断开", res)
					}
				}, deviceId).then(res => {
					wx.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'none',
							title: `锁时间已校准--操作时间:${Date.now() - start}ms`
						})
						uni.hideLoading()
					} else {
						uni.showToast({
							icon: 'none',
							title: `校准锁时间失败:${res.errorMsg}`
						})
						uni.hideLoading()
					}
				}, deviceId)
			},
			// 读取操作记录
			toReadRecord() {
				let deviceId = ""
				// let type = event.currentTarget.dataset.type === 1 ? plugin.RecordReadType.ALL : plugin.RecordReadType.NEW;
				uni.showLoading({
					title: `正在读取锁内操作记录`,
				})
				const start = Date.now();
				// 获取操作记录
				plugin.getOperationLog(plugin.RecordReadType.NEW, this.lockDetail.lock_data, res => {
					if (res.errorCode === 10003) {
						console.log("监控到设备连接已断开", res)
					}
				}, deviceId).then(res => {
					uni.hideLoading({});
					if (!!res.deviceId) deviceId = res.deviceId;
					console.log(res)
					if (res.errorCode === 0) {
						uni.showToast({
							icon: 'success',
							title: `操作记录已获取--操作时间::${Date.now() - start}`
						})
						this.$iBox.http('uploadOpenRecord', {
								id: this.lockDetail.id,
								records: res.log
							})({
								method: 'post'
							}).then(res => {

							})
					} else {
						uni.showToast({
							icon: 'success',
							title: "读取操作记录失败:" + res.errorMsg
						})

					}
				})
			},
			openLock(e) {
				// 开锁前校准锁时间
				this.toCheckLockTime()
				this.toOpenDoor()
			},
			over() {
				uni.navigateTo({
					url: './lockDetail'
				})
			}
		}
	}
</script>

<style>

</style>
