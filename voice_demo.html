<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>🎤 语音输入楼层控制演示</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 24px rgba(0,0,0,0.1);
        }
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 30px;
            color: #333;
        }
        .voice-section {
            margin-top: 20px;
            padding: 25px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
        .voice-title {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 25px;
            color: #2979ff;
            font-weight: 600;
        }
        .voice-input-container {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .voice-button {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: #2979ff;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(41, 121, 255, 0.3);
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .voice-button:active {
            transform: scale(0.95);
        }
        .voice-button.recording {
            background: #F44336;
            animation: pulse 1s infinite;
        }
        .voice-button.processing {
            background: #FF9800;
        }
        .voice-tips {
            text-align: center;
            color: #666;
            font-size: 14px;
        }
        .voice-result {
            margin-top: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 12px;
            border-left: 4px solid #2979ff;
        }
        .result-text {
            margin-bottom: 15px;
            font-size: 14px;
            color: #333;
        }
        .floor-result {
            color: #2979ff;
            font-weight: 600;
            margin-left: 8px;
        }
        .result-actions {
            display: flex;
            gap: 10px;
        }
        .action-button {
            flex: 1;
            height: 40px;
            border-radius: 20px;
            border: none;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
        }
        .action-button.confirm {
            background: #4CAF50;
            color: white;
        }
        .action-button.retry {
            background: #f0f0f0;
            color: #666;
        }
        .demo-section {
            margin-top: 30px;
            padding: 20px;
            background: #e3f2fd;
            border-radius: 15px;
        }
        .demo-section h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .demo-commands {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 15px;
        }
        .demo-command {
            padding: 8px 12px;
            background: white;
            border-radius: 8px;
            text-align: center;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .demo-command:hover {
            background: #2979ff;
            color: white;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        .rotating {
            animation: rotate 1s linear infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎤 语音输入楼层控制</h1>
        
        <div class="voice-section">
            <div class="voice-title">
                🎤 语音输入
            </div>
            
            <div class="voice-input-container">
                <button 
                    class="voice-button" 
                    id="voiceButton"
                    onmousedown="startVoice()" 
                    onmouseup="stopVoice()"
                    onmouseleave="cancelVoice()"
                >
                    <span id="voiceIcon">🎤</span>
                </button>
                
                <div class="voice-tips">
                    <span id="voiceTips">长按说出楼层数字</span>
                </div>
            </div>
            
            <div class="voice-result" id="voiceResult" style="display: none;">
                <div class="result-text">
                    <span>识别结果：</span><span id="resultText"></span>
                    <span class="floor-result" id="floorResult"></span>
                </div>
                <div class="result-actions">
                    <button class="action-button confirm" onclick="confirmFloor()">确认前往</button>
                    <button class="action-button retry" onclick="clearResult()">重新录音</button>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>🗣️ 语音指令示例</h3>
            <p>点击下方示例来模拟语音识别：</p>
            <div class="demo-commands">
                <div class="demo-command" onclick="simulateVoice('去三楼', 3)">去三楼</div>
                <div class="demo-command" onclick="simulateVoice('到十五层', 15)">到十五层</div>
                <div class="demo-command" onclick="simulateVoice('我要去二十楼', 20)">我要去二十楼</div>
                <div class="demo-command" onclick="simulateVoice('十三楼', 13)">十三楼</div>
                <div class="demo-command" onclick="simulateVoice('五楼', 5)">五楼</div>
                <div class="demo-command" onclick="simulateVoice('二十三层', 23)">二十三层</div>
                <div class="demo-command" onclick="simulateVoice('33层', 33)">33层</div>
                <div class="demo-command" onclick="simulateVoice('一楼', 1)">一楼</div>
            </div>
        </div>
        
        <div class="demo-section">
            <h3>✨ 功能特性</h3>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>🎤 长按录音，松开识别</li>
                <li>🔢 支持中文和阿拉伯数字</li>
                <li>✅ 智能楼层提取</li>
                <li>🔄 识别错误可重试</li>
                <li>📱 与蓝牙控制无缝集成</li>
            </ul>
        </div>
    </div>

    <script>
        let isRecording = false;
        let isProcessing = false;
        let currentFloor = null;

        function startVoice() {
            if (isRecording || isProcessing) return;
            
            isRecording = true;
            const button = document.getElementById('voiceButton');
            const icon = document.getElementById('voiceIcon');
            const tips = document.getElementById('voiceTips');
            
            button.classList.add('recording');
            icon.textContent = '🎤';
            tips.textContent = '正在录音，请说话...';
            
            // 隐藏之前的结果
            document.getElementById('voiceResult').style.display = 'none';
            
            console.log('开始录音...');
        }

        function stopVoice() {
            if (!isRecording) return;
            
            isRecording = false;
            isProcessing = true;
            
            const button = document.getElementById('voiceButton');
            const icon = document.getElementById('voiceIcon');
            const tips = document.getElementById('voiceTips');
            
            button.classList.remove('recording');
            button.classList.add('processing');
            icon.textContent = '⏳';
            icon.classList.add('rotating');
            tips.textContent = '正在识别语音...';
            
            // 模拟语音识别过程
            setTimeout(() => {
                const mockResults = [
                    { text: '去三楼', floor: 3 },
                    { text: '到十五层', floor: 15 },
                    { text: '我要去二十楼', floor: 20 },
                    { text: '十三楼', floor: 13 },
                    { text: '五楼', floor: 5 },
                    { text: '二十三层', floor: 23 },
                    { text: '33层', floor: 33 },
                    { text: '一楼', floor: 1 }
                ];
                
                const result = mockResults[Math.floor(Math.random() * mockResults.length)];
                showVoiceResult(result.text, result.floor);
            }, 2000);
        }

        function cancelVoice() {
            if (isRecording) {
                isRecording = false;
                resetVoiceButton();
                alert('录音已取消');
            }
        }

        function simulateVoice(text, floor) {
            if (isRecording || isProcessing) return;
            
            isProcessing = true;
            const button = document.getElementById('voiceButton');
            const icon = document.getElementById('voiceIcon');
            const tips = document.getElementById('voiceTips');
            
            button.classList.add('processing');
            icon.textContent = '⏳';
            icon.classList.add('rotating');
            tips.textContent = '正在识别语音...';
            
            setTimeout(() => {
                showVoiceResult(text, floor);
            }, 1500);
        }

        function showVoiceResult(text, floor) {
            isProcessing = false;
            currentFloor = floor;
            
            resetVoiceButton();
            
            const resultDiv = document.getElementById('voiceResult');
            const resultText = document.getElementById('resultText');
            const floorResult = document.getElementById('floorResult');
            
            resultText.textContent = text;
            floorResult.textContent = floor ? `→ ${floor}层` : '→ 未识别到楼层';
            floorResult.style.color = floor ? '#2979ff' : '#F44336';
            
            resultDiv.style.display = 'block';
            
            if (floor) {
                // 模拟成功识别的反馈
                navigator.vibrate && navigator.vibrate(100);
            }
        }

        function confirmFloor() {
            if (currentFloor) {
                alert(`确认前往 ${currentFloor} 层！\n正在启动蓝牙连接电梯...`);
                clearResult();
            }
        }

        function clearResult() {
            document.getElementById('voiceResult').style.display = 'none';
            currentFloor = null;
        }

        function resetVoiceButton() {
            const button = document.getElementById('voiceButton');
            const icon = document.getElementById('voiceIcon');
            const tips = document.getElementById('voiceTips');
            
            button.className = 'voice-button';
            icon.textContent = '🎤';
            icon.classList.remove('rotating');
            tips.textContent = '长按说出楼层数字';
        }
    </script>
</body>
</html>
