<template>
	<view>
		<!-- 样式一 -->
		<view class="bannerBox" v-if="list.style==2">
			<image :src="list.bg_image" @click="goChoose"
				style="height: 160rpx;width: 100%;"></image>
		</view>
		<!-- 样式二 -->
		<view class="bannerBox1 " :style="{background:themeColor.main_color}" v-if="list.style==1">
			<image class="alert-btn" src="http://doc.hanwuxi.cn/wp-content/uploads/2025/03/a578ae87b828259ebe5e440caf8c6d4.png" @click="goChoose"
				style="height: 160rpx;width: 600rpx;"></image>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		props: {
			list: {
				type: Object
			},
			billDetail: {
				type: Object
			}
		},
		data() {
			return {
				authInfo:[],
				online_cash:false
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['hotel','cityModel','shopSetting']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		mounted() {
			console.log(this.list,'list');
		},
		methods: {
			goChoose(){
					//是否是会员
					
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -1) {
					
						// 查询房费
						console.log(this.billDetail, 'billDetail');
						this.$iBox.http('getSelfCheckInAmount', {
							bill_id: this.billDetail.id
						})({
							method: 'post'
						}).then(res => {
							this.cashAll = res.data
							// 查询是否支持在线收押金
							this.online_cash = this.shopSetting.filter(item => {
								return item.sign == 'self_check_in_pay_cash_pledge'
							})[0].property.status
							
							if(!this.billDetail.team_id){
								if (!this.online_cash) {
									// 需要在线支付判断是否已经支付了押金和房费，>0则代表需要支付,或者没有选择入住人数在房间人数大于1的情况下
									if (!this.billDetail.room_number||this.cashAll.bill_amount + this.cashAll.cash_pledge - this.cashAll.already_pay - this.cashAll.already_pay_cash_pledge > 0) {
										uni.navigateTo({
											url:'/packageA/autoRoom/chooseRoom/chooseRoom'
										})
									} else {
										uni.navigateTo({
											url:'/packageA/autoRoom/autoRoom'
										})
										
									}
								} else {
									
									// 不在线交押金则判断房费是否交清
									if (!this.billDetail.room_number||this.cashAll.bill_amount - this.cashAll.already_pay > 0) {
										uni.navigateTo({
											url:'/packageA/autoRoom/chooseRoom/chooseRoom'
										})
									} else {  
										uni.navigateTo({
											url:'/packageA/autoRoom/autoRoom'
										})
										
									}
								}
							}else{
								uni.navigateTo({
									url: '/packageB/teamCheckIn/confirmTeam?team_id=' + this.billDetail.team_id
								})
							}
							
						})
					} else {
						console.log('dsdsdsdbbbbbbbbbbb');
						uni.reLaunch({
							url:'/pages/myRoom/myRoom'
						})
						
					}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.bannerBox {
		width: 100%;
		padding: 20rpx;
		height: 160rpx;
	}
	
	.bannerBox1 {
		width: 700rpx;
		padding: 20rpx;
		height: 160rpx;
		margin: 20rpx auto;
		border-radius: 32rpx;
		display: flex;
		align-items: center;justify-content: center;
	}
	
	@keyframes superPulse {
	  0% {
	    transform: scale(1) translateZ(0);
	    box-shadow: 0 0 0 rgba(255,87,51,0.3);
	  }
	  50% {
	    transform: scale(1.02) translateZ(20rpx);
	    box-shadow: 0 15rpx 30rpx rgba(255,87,51,0.5);
	    filter: hue-rotate(10deg);
	  }
	  100% {
	    transform: scale(0.99) translateZ(0);
	    box-shadow: 0 5rpx 15rpx rgba(255,87,51,0.2);
	  }
	}
	
	.alert-btn {
	  animation: superPulse 1.2s cubic-bezier(0.4,0,0.2,1) infinite;
	  transform-style: preserve-3d;
	}
</style>
