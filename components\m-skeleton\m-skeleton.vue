<template>
	<!-- 骨架屏的高度宽度和背景，用绝对定位提高层级 -->
	<view v-if="show">
		<view :style="{
		width: windowWidth,
		height: selector!=0?selector*320 +'rpx':'300rpx',
		backgroundColor: bgColor,
		position: 'relative',
		'margin-bottom':'140rpx',
		zIndex: 9
	}">


			<view v-for="(item,index) in rectNodes" :key="index + 'Rect'" class="skeleton-fade" :style="{
			width: '750rpx',
			height: '280rpx',
			position: 'relative',
		}">
				<view class="" v-for="item1 in item.img" :style="{
						width: item1.width + 'rpx',
						height: item1.height + 'rpx',
						backgroundColor: elColor,
						position: 'absolute',
						top: item1.top + 'rpx',
						left: item1.left + 'rpx'
		}">

				</view>
			</view>
			<!-- 
			<view v-for="(item,index) in circleNodes" :key="index" class="skeleton-fade" :style="{
			width: item.width + 'rpx',
			height: item.height + 'rpx',
			backgroundColor: elColor,
			borderRadius: item.width/2 + 'rpx', 
			position: 'absolute',
			top: item.top + 'rpx',
			left: item.left + 'rpx'
		}">
			</view> -->
		</view>
	</view>
</template>

<script>
	let systemInfo = uni.getSystemInfoSync();
	export default {
		name: 'm-skeleton',
		props: {
			show: {
				type: Boolean,
				default: true
			},
			selector: {
				type: Number
			}
		},
		data() {
			return {
				windowWidth: systemInfo.windowWidth + 'px',
				windowHeight: systemInfo.windowHeight + 'px',
				bgColor: '#fff',
				elColor: '#e7e7e7',
				top: 0,
				left: 0,
				borderRadius: 10,
				rectNodes: [{
					id: '',
					img: [{
							"id": "",
							"dataset": {},
							"left": 30,
							"right": 170,
							"top": 28,
							"bottom": 60,
							"width": 200,
							"height": 240
						},
						{
							"id": "",
							"dataset": {},
							"left": 260,
							"right": 720,
							"top": 28,
							"bottom": 60,
							"width": 400,
							"height": 50
						},
						{
							"id": "",
							"dataset": {},
							"left": 260,
							"right": 720,
							"top": 88,
							"bottom": 60,
							"width": 400,
							"height": 50
						},
						{
							"id": "",
							"dataset": {},
							"left": 260,
							"right": 720,
							"top": 158,
							"bottom": 60,
							"width": 400,
							"height": 50
						}
					]
				}]
			}
		},
		mounted() {
			
			let a = this.rectNodes[0]
			for (var i = 1; i < this.selector; i++) {
				console.log(this.selector,a, 'selector',this.rectNodes[i]);
				a.top = 158 + 158*i + 20
				this.rectNodes.push(a)
			}
			console.log(this.rectNodes,'rectNodes');
			// 矩形骨架元素
			this.getRectEls();
			// 圆形骨架元素
			this.getCircleEls();
		},
		methods: {
			getRectEls() {
				let query = uni.createSelectorQuery().in(this.$parent)
				query.selectAll('.skeleton-rect').boundingClientRect(res => {
					console.log('rect', JSON.stringify(res));
				}).exec(function() {

				})
			},
			getCircleEls() {
				let query = uni.createSelectorQuery().in(this.$parent)
				query.selectAll('.skeleton-circle').boundingClientRect(res => {
					console.log('circle', JSON.stringify(res));
				}).exec(function() {

				})
			}
		},
	}
</script>

<style>
	.skeleton-fade {
		width: 100%;
		height: 100%;
		background: rgb(255, 255, 255);
		animation-duration: 1.5s;
		animation-name: blink;
		animation-timing-function: ease-in-out;
		animation-iteration-count: infinite;
	}

	@keyframes blink {
		0% {
			opacity: .4;
		}

		50% {
			opacity: 1;
		}

		100% {
			opacity: .4;
		}
	}
</style>
