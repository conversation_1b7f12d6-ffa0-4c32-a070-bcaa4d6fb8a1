/**
 * 全局动画管理器
 * 在小程序中管理全局的初始化动画状态
 */
class GlobalAnimationManager {
	constructor() {
		this.isVisible = false
		this.progress = 0
		this.currentMessage = ''
		this.callbacks = []
		
		// 在小程序中使用全局数据存储
		if (typeof getApp === 'function') {
			const app = getApp()
			if (app) {
				app.globalData = app.globalData || {}
				app.globalData.initAnimation = {
					visible: false,
					progress: 0,
					message: ''
				}
			}
		}
	}

	// 显示初始化动画
	show() {
		this.isVisible = true
		this.progress = 0
		this.currentMessage = '初始化中...'
		
		this.updateGlobalData()
		this.notifyCallbacks('show')
		
		console.log('🎬 显示全局初始化动画')
		return this
	}

	// 更新进度
	updateProgress(progress, message = '') {
		this.progress = Math.min(100, Math.max(0, progress))
		if (message) {
			this.currentMessage = message
		}
		
		this.updateGlobalData()
		this.notifyCallbacks('progress', { progress: this.progress, message: this.currentMessage })
		
		console.log(`📊 初始化进度: ${this.progress}%`, this.currentMessage)
		return this
	}

	// 隐藏动画
	hide() {
		// 确保进度达到100%
		this.updateProgress(100, '初始化完成')
		
		// 延迟隐藏，让用户看到完成状态
		setTimeout(() => {
			this.isVisible = false
			this.progress = 0
			this.currentMessage = ''
			
			this.updateGlobalData()
			this.notifyCallbacks('hide')
			
			console.log('🎬 隐藏全局初始化动画')
		}, 500)
		
		return this
	}

	// 注册回调
	onStateChange(callback) {
		if (typeof callback === 'function') {
			this.callbacks.push(callback)
		}
		return this
	}

	// 移除回调
	offStateChange(callback) {
		const index = this.callbacks.indexOf(callback)
		if (index > -1) {
			this.callbacks.splice(index, 1)
		}
		return this
	}

	// 获取当前状态
	getState() {
		return {
			visible: this.isVisible,
			progress: this.progress,
			message: this.currentMessage
		}
	}

	// 更新全局数据
	updateGlobalData() {
		if (typeof getApp === 'function') {
			const app = getApp()
			if (app && app.globalData) {
				app.globalData.initAnimation = {
					visible: this.isVisible,
					progress: this.progress,
					message: this.currentMessage
				}
			}
		}
	}

	// 通知所有回调
	notifyCallbacks(type, data = {}) {
		this.callbacks.forEach(callback => {
			try {
				callback(type, {
					...this.getState(),
					...data
				})
			} catch (error) {
				console.error('动画回调执行失败:', error)
			}
		})
	}

	// 从全局数据恢复状态
	restoreFromGlobalData() {
		if (typeof getApp === 'function') {
			const app = getApp()
			if (app && app.globalData && app.globalData.initAnimation) {
				const data = app.globalData.initAnimation
				this.isVisible = data.visible || false
				this.progress = data.progress || 0
				this.currentMessage = data.message || ''
			}
		}
		return this
	}

	// 重置状态
	reset() {
		this.isVisible = false
		this.progress = 0
		this.currentMessage = ''
		this.updateGlobalData()
		this.notifyCallbacks('reset')
		console.log('🔄 重置全局动画状态')
		return this
	}
}

// 创建全局实例
const globalAnimationManager = new GlobalAnimationManager()

// 在小程序环境中，将管理器挂载到全局
if (typeof getApp === 'function') {
	// 等待App实例创建后挂载
	setTimeout(() => {
		const app = getApp()
		if (app) {
			app.globalAnimationManager = globalAnimationManager
		}
	}, 0)
}

export default globalAnimationManager
