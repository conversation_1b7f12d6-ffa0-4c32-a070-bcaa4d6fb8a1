<template>
	<view>
		<m-popup mode="center" :show="showDevice" :closeable="false">
			<view class="deviceBox">
				<p>您好，{{showDevice?'正在检测':'未检测到'}}酒店前台电子围栏设备！{{showDevice?'请前往酒店前台操作!':'请到酒店进行操作'}}!</p>
				<view class="icon-wuxinhao" style="font-size: 240rpx;color: brown;margin-top: 30rpx;">
				</view>
				<view class="deBtn" @click="reload()" :style="{background:themeColor.main_color}">
					重新检测
				</view>
				<p style="margin-top: 20rpx;color: darkgrey;" @click="goMain">暂不认证，回到首页</p>
			</view>
		</m-popup>
	</view>
</template>

<script>
	const bgAudioManager = uni.getBackgroundAudioManager();
	const plugin = requirePlugin("yayaLock");
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				blueList: [],
				showDevice: true
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate']),
		},
		mounted() {
			this.$iBox.http('getBlueTooth', {
				shop_id: this.hotel.id
			})({
				method: 'post'
			}).then(res => {
				let blueList = res.data.filter(item => {
					return item.bluetooth_type == 1
				})
				this.blueList = blueList
				if (this.blueList.length > 0) {
					this.peiDui()
				} else {
					this.showDevice = false
				}

				console.log(this.blueList, 'this.blueList');
			})

		},
		methods: {
			goMain() {
				uni.reLaunch({
					url: '/pages/myRoom/myRoom'
				})
			},
			reload() {
				this.stopBluetoothDevicesDiscovery()
				uni.showLoading({
					title:'正在查找设备...'
				})
				setTimeout(res=>{
					this.peiDui()
				},1000)
				
				setTimeout(res=>{
					uni.hideLoading()
				},3000)
				
			},

			////////////////////////=============================蓝牙U Key盾检测==========================
			peiDui() {
				//在页面加载时候初始化蓝牙适配器
				
				uni.openBluetoothAdapter({
					success: e => {
						console.log('初始化蓝牙成功:' + e.errMsg);
						// 初始化完毕开始搜索
						this.startBluetoothDeviceDiscovery()
						
					},
					fail: e => {
						
						console.log('初始化蓝牙失败，错误码：' + (e.errCode || e.errMsg));
						bgAudioManager.title = '提醒'
						bgAudioManager.epname = '提醒'
						bgAudioManager.singer = '提醒'
						bgAudioManager.src =
							'http://hwx-hotel.oss-cn-beijing.aliyuncs.com/common_mp3/%E8%AF%B7%E6%89%8B%E5%8A%A8%E6%89%93%E5%BC%80%E6%89%8B%E6%9C%BA%E8%93%9D%E7%89%99.mp3'
						uni.showToast({
							icon: "none",
							title: "查找设备失败！请检查手机是否打开蓝牙！",
							duration: 3000
						})
					}
				});
			},
			startBluetoothDeviceDiscovery() {
				//在页面显示的时候判断是都已经初始化完成蓝牙适配器若成功，则开始查找设备
				let self = this;
				console.log("开始搜寻智能设备");
				// setTimeout(res => {
				uni.startBluetoothDevicesDiscovery({
					success: res => {
						self.onBluetoothDeviceFound();
					},
					fail: res => {
						console.log("查找设备失败!");
						uni.showToast({
							icon: "none",
							title: "查找设备失败！",
							duration: 3000
						})
					}
				});
				// }, 300)
			},
			/**
			 * 停止搜索蓝牙设备
			 */
			stopBluetoothDevicesDiscovery() {
				uni.stopBluetoothDevicesDiscovery({
					success: e => {
						console.log('停止搜索蓝牙设备:' + e.errMsg);
					},
					fail: e => {
						console.log('停止搜索蓝牙设备失败，错误码：' + e.errCode);
					}
				});
			},
			/**
			 * 发现外围设备
			 */
			onBluetoothDeviceFound() {
				let self = this
				self.showDevice = true
				uni.onBluetoothDeviceFound(devices => {
					let mac = ''
					let mac1 = ''
					let macname = ''
					for (var i = 0; i < devices.devices.length; i++) {
						if (devices.devices[i].localName) {
							macname = devices.devices[i].localName.slice(-12)
							mac = ""
							for (let i = 0, len = macname.length; i < len; i++) {
								mac += macname[i];
								if (i % 2 == 1 && i <= len - 2) mac += ":";
							}
						}
						
						if(devices.devices[i].deviceId){
							mac1 = devices.devices[i].deviceId
						}
						
						console.log('devices',self.blueList,devices);
						// 循环判断是否存在,并且信号值达标
						for (let item of self.blueList) {
							console.log(mac, 'dddd' ,item, devices.devices[i].RSSI);
							if (item.mac == mac || item.mac == mac1) {
								if(item.max_value != 0){
									if(devices.devices[i].RSSI > item.max_value){
										self.showDevice = false
										self.stopBluetoothDevicesDiscovery()
										uni.closeBluetoothAdapter({
											success(res) {
												console.log(res)
											}
										})
										break
									}
								}else {
									self.showDevice = false
									self.stopBluetoothDevicesDiscovery()
									uni.closeBluetoothAdapter({
										success(res) {
											console.log(res)
										}
									})
									break
								}
								
							}
						}
					}
				})

			},

		}
	}
</script>

<style scoped lang="scss">
	.deviceBox {
		height: 50vh;
		width: 700rpx;
		border-radius: 20rpx;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		.deBtn {
			width: 500rpx;
			height: 80rpx;
			border-radius: 40rpx;

			color: #FFFFFF;
			margin-top: 50rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}
	}
</style>