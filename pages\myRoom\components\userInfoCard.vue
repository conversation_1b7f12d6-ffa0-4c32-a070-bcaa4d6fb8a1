<template>
	<view>
		<!-- 样式一 -->
		<view class="userBox">
			<view class="infoBox">
				<view class="" style="display: flex;flex-direction: column;align-items: center;position: relative;">
					<image :src="userInfo.avatar_url" style="height: 120rpx;width: 120rpx;border-radius: 50%;box-shadow: rgba(50, 50, 93, 0.25) 0px 30px 60px -12px, rgba(0, 0, 0, 0.3) 0px 18px 36px -18px;"></image>
					
				</view>
				
				<view class="" style="padding: 0 20rpx;width: 500rpx;">
					<view class="" style="font-size: 34rpx;font-weight: 600;display: flex;align-items: center;">
						<text>{{`${manInfo.name?manInfo.name:'住客'}`}}</text>
						
					</view>
					<view class="" style="display: flex;align-items: center;">
						<view class="" style="display: flex;align-items: center;margin-top: 10rpx;">
							<image src="/static/images/level.png" style="height: 40rpx;width: 40rpx;z-index: 2;" mode=""></image>
							<p style="padding: 4rpx 20rpx;border-radius: 14rpx;background-color: #ecb47b;color: #FFFFFF;margin-left: -16rpx;z-index: 1;display: flex;align-items: center;">
								<text style="font-size: 20rpx;color: #ffffff;font-weight: 600;text-align: center;">{{userInfo.grade_name?userInfo.grade_name.split('会员')[0]:''}}</text>
							</p>
							
						</view>
						
						<!-- <view class="" style="margin-left: 20rpx;width: fit-content;height: 40rpx;border-radius: 10rpx;padding: 14rpx;display: flex;align-items: center;justify-content: center;"
						 :style="{background:themeColor.com_color1,color:themeColor.button_text_color}" @click="recharge">
							<text class="icon-shouye" ></text> <text style="font-size: 22rpx;">去充值</text> 
						</view> -->
					</view>
				</view>
				<view class="" style="width: 160rpx;height: 100%;display: flex;align-items: center;justify-content: center;" @click="goCounpon">
					<image :src="styleModel.bg_image" style="height: 120rpx;width:120rpx"></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		props: {
			styleModel:{
				type:Object
			},
			manInfo:{
				type:Object
			}
		},
		data() {
			return {

			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('hotel', ['hotel','cityModel']),
			...mapState('ui', ['tabbar', 'themeColor'])
		},
		mounted() {
		
		},
		methods: {
			recharge(){
				uni.navigateTo({
					url:'/pages/recharge/recharge'
				})
			},
			goCounpon(){
				uni.navigateTo({
					url:'/packageA/couponCenter/couponCenter'
				})
			},
			goQr(){
				uni.navigateTo({
					url:"/pages/payCode/payCode"
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.userBox {
		// height: 180rpx;
		width: 100%;
		padding: 20rpx;
		.infoBox {
			display: flex;
			align-items: center;
		}
	}
</style>
