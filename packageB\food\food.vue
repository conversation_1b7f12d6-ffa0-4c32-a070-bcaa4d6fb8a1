<template>
	<view class="container">
		<view class="header">
			<!-- 搜索栏 begin -->
			<!-- <view class="search-box">
				<view class="search-input" @tap="showSearch=true">
					<image src="/static/images/common/search-icon.png" class="search-icon"></image>
					<view>搜索</view>
				</view>
			</view> -->
			<!-- 搜索栏 end -->
			<view class="center">
				<view class="store">
					<view class="" style="">
						<view class="title">
							<view class="icon-ditu-dibiao left-icon"></view>
							<view class="address">{{hotel.shop_name}}</view>
						</view>
						
					</view>
					<!-- 外卖&自取switch begin -->
					<view class="buttons" v-if="table_num">
						桌号:{{table_num}}|{{Number(this.manIndex) + 1}}人用餐
					</view>
					<!-- 外卖&自取switch end -->
				</view>
				
				<view class="" style="margin-top: 6rpx;padding-left: 10rpx;font-size: 26rpx;color:#6666666">
					{{timeLong}}
				</view>
				<!-- 	<text v-if="hotel.distance">距您{{hotel.distance*1<1000?hotel.distance+'m':(hotel.distance/1000).toFixed(1)+'km'}}</text>
				<text v-if="!hotel.distance">暂无距离</text> -->
			</view>
			<!-- 滚动公告栏 begin -->
			<!-- <view class="notices">
				<swiper class="swiper" autoplay vertical :interval="3000" :duration="1000" circular>
					<swiper-item v-for="(notice, index) in notices" :key="index">
						<view class="swiper-item">
							<image :src="notice.image" class="image"></image>
							<view class="content">{{ notice.content }}</view>
						</view>
					</swiper-item>
				</swiper>
				<view class="more">
					<text>更多</text>
					<image src="/static/images/common/gray_arrow_down.png" class="down-icon"></image>
				</view>
			</view> -->
		</view>
		<!-- 滚动公告栏 end -->
		<view class="main">
			<!-- 左侧菜单 begin -->
			<scroll-view class="menu-bar" scroll-y scroll-with-animation :scroll-into-view="viewName">
				<view class="wrapper">
					<view class="menu-item" @tap="handleMenuSelected(category.id)"
						:class="{active: currentCategoryId == category.id}" v-for="(category, index) in categories"
						:key="index">
						<!-- <image :src="category.category_image_url" class="image" mode="widthFix"></image> -->
						<view class="title">{{ category.type_name }}</view>
					</view>
				</view>
			</scroll-view>
			<!-- 左侧菜单 end -->
			<!-- 右侧商品列表 begin -->
			<scroll-view class="product-section" scroll-y scroll-with-animation :scroll-into-view="typeId">
				<view class="wrapper">
					<!-- <view id="ads">
					
						<swiper class="ads1" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000"
							circular>
							<swiper-item v-for="(ad, index) in ads1" :key="index">
								<image :src="ad" class="w-100" mode="widthFix"></image>
							</swiper-item>
						</swiper>
					
						<swiper class="ads2" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000"
							circular>
							<swiper-item v-for="(ad, index) in ads2" :key="index">
								<image :src="ad" class="w-100" mode="widthFix"></image>
							</swiper-item>
						</swiper>
						
					</view> -->
					<!-- 商品 begin -->
					<view class="products-list"  v-for="(category, index) in categories" :key="index"
						:id="`products-${category.id}`">
						<view class="category-name">{{ category.type_name }}</view>
						<view class="products">
							<view class="product" style="border-bottom:1px solid #eeeeee ;" v-for="(product, key) in category.goods_list" :key="key"
								@tap="showProductDetailModal(product)">
								<image :src="product.cover_pic" mode="widthFix" class="image"></image>
								<view class="content">
									<view class="name">{{ product.name }}</view>
									<view class="labels">
										<view class="label" style="border-radius: 6rpx;"
											:style="'color:' + themeColor.main_color + ';border:1px solid ' + themeColor.main_color+';background:'+themeColor.main_color+'33;'"
											v-for="label in product.pay_type_list" :key="label.id">
											{{ label.pay_type_name }}支付
										</view>
									</view>
									<!-- <view class="description">{{ product.description }}</view> -->
									<view class="price" style="width: 380rpx;padding: 0">
										<view>￥{{ product.allPrice}}</view>
										<actions v-if="hackReset&&product.stock>0" :materials-btn="product.specification_types.length >0"
											@materials="showProductDetailModal(product)"
											:number="productCartNum(product.id)" @add="handleAddToCart(product)"
											@minus="handleMinusFromCart(product)" />
										<text v-if="product.stock==0" style="font-size: 26rpx;color: #5D5E63;">菜品已售罄</text>
									</view>
									<view class="">
										<text style="font-size:24rpx;color: #00000066;font-weight: 200;">库存:{{product.stock}}</text>
									</view>
								</view>
							</view>
						</view>
					</view>
					<!-- 商品 end -->
				</view>
			</scroll-view>
			<!-- 右侧商品列表 end -->
		</view>
		<!-- 商品详情 modal begin -->
		<product-modal v-if="hackReset" :product="product" :visible="productModalVisible" @cancel="closeProductDetailModal"
			@add-to-cart="handleAddToCartInModal" />
		<!-- 商品详情 modal end -->
		<!-- 购物车栏 begin -->
		<cart-bar v-if="hackReset" :cart="cart" @add="handleAddToCart" @minus="handleMinusFromCart" @clear="clearCart" @pay="pay" />
		<!-- 购物车栏 end -->
		<!-- 	<search :show="showSearch" :categories="categories" @hide="showSearch=false" @choose="showProductDetailModal">
		</search> -->
		<!-- 选择人数弹窗 -->
		<m-popup :show="chooseNum" mode="center">
			<view class=""
				style="width: 700rpx;height: 660rpx;display: flex;flex-direction: column;align-items: center;padding: 20rpx 0;">
				<p>选择用餐人数</p>
				<p style="font-size: 26rpx;color: #afafaf;margin-top: 14rpx;">桌号: {{table_num}}</p>
				<view class="" style="display: flex;flex-wrap: wrap;margin-top: 30rpx;">
					<view class="" v-for="(item,index) in manNum" @click="chooseMan(index)"
						style="width: 140rpx;height: 100rpx;margin: 14rpx;display: flex;align-items: center;justify-content: center;">
						<view class="" :style="manIndex==index?'background:'+themeColor.main_color:''"
							style="width: 120rpx;height: 100rpx;background-color: #FFFFFF;display: flex;align-items: center;justify-content: center;border: 1px solid #afafaf;border-radius: 10rpx;">
							<text>{{index+1}}</text>
						</view>
					</view>
				</view>
				<view class="" @click="sureMan"
					style="width: 500rpx;border-radius: 36rpx;height: 80rpx;margin-top: 20rpx;display: flex;align-items: center;justify-content: center;"
					:style="{background:themeColor.main_color}">
					去点餐
				</view>
			</view>
		</m-popup>
		<m-login v-if="hackReset&&if_login" @loginTo="loginSucess" @closeToLogin="toCloseLogin"></m-login>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	import Actions from './components/actions/actions.vue'
	import CartBar from './components/cartbar/cartbar.vue'
	import ProductModal from './components/product-modal/product-modal.vue'
	import cartPopup from './components/cart-popup/cart-popup.vue'
	// import Search from './components/search/search.vue'

	export default {
		components: {
			Actions,
			CartBar,
			ProductModal,
			cartPopup,
			// Search
		},
		data() {
			return {
				params: {
					page: 1,
					limit: 10,
					shop_id: '',
					table_id: ''
				},
				categories: [],
				cart: [],
				product: {},
				currentCategoryId: 0,
				notices: [],
				ads1: [],
				ads2: [

				],
				productModalVisible: false,
				cartPopupShow: false,
				productsScrollTop: 0,
				showSearch: false,
				viewName: '',
				table_num: '',
				chooseNum: false,
				manNum: 12,
				manIndex: '',
				typeId: '',
				if_login: false,
				hackReset: false,
				timeLong:'',
				hackReset: true,
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting','setting']),
			productCartNum() { //计算单个饮品添加到购物车的数量
				return id => this.cart.reduce((acc, cur) => {
					if (cur.id === id) {
						return acc += cur.number
					}
					return acc
				}, 0)
			},

		},
		async onLoad(options) {
			await this.$onLaunched;
			// 获取营业时间住设置，
			let time = this.shopSetting.filter(item => {
				return item.sign == 'catering_open_time'
			})[0].property
			
			let a = ''
			time.forEach(item=>{
				a += (item.start_time+'-'+item.end_time+' | ')
			})
			this.$nextTick(() => {
				this.hackReset = true
			})
			this.timeLong = '餐厅营业时间为:'+a
			
			let set = this.setting.filter(item => {
				return item.sign == 'auto_register_member'
			})
			if (set[0].property) {
				let a = set[0].property.value
				if (a == 2) {
					
					if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info.upgrade_growth_value > -
						1) {
						this.if_login = false
						let scene = wx.getEnterOptionsSync()
					
						if (scene.query.scene && (scene.scene == 1047 || scene.scene == 1048 || scene.scene == 1049 || scene
								.scene == 1007 || scene.scene == 1008)) {
							let query = decodeURIComponent(scene.query.scene)
							console.log(query, 'q');
							//解析参数
							if (query.includes("table_id")) {
								this.chooseNum = true
								this.params.table_id = this.$iBox.linkFormat(query, "table_id")
								this.params.shop_id = this.$iBox.linkFormat(query, "shop_id")
								this.$iBox
									.http('getTableById', {
										table_id: this.$iBox.linkFormat(query, "table_id")
									})({
										method: 'post'
									})
									.then(res => {
										this.table_num = res.data.table_number
										this.getTableNum(res.data.table_number)
										this.getTableId(this.params.table_id)
									})
							}
					
						} else {
							this.params.shop_id = this.hotel.id
						}
					
						this.params.page = 1
					
						this.$iBox
							.http('getCateringGoodsTypeList', this.params)({
								method: 'post'
							})
							.then(res => {
								let list = res.data.list
								if (res.data.list.length > 0) {
									list.forEach(item => {
										item.goods_list.forEach(item1 => {
											item1.number = 0
											item1.typePrice = 0
											if (item1.specification_types.length > 0) {
												item1.is_single = false
					
												item1.specification_types.forEach((itemTypes) => {
													itemTypes.specifications.forEach((itemPrice,index) => {
														if (index == 0) {
															item1.typePrice += itemPrice.price
														}
													})
												})
												item1.allPrice = item1.typePrice + item1.price
											} else {
												item1.allPrice =  item1.price
												item1.is_single = true
											}
										})
					
									})
									this.categories = list
									console.log(list,'list');
									this.currentCategoryId = res.data.list[0].id
									this.viewName = 'products-' + res.data.list[0].id
								} else {
									uni.showModal({
										title: '提示',
										content: '餐厅暂无菜品'
									})
								}
					
							})
					} else {
						this.if_login = true
					}
							
				} else if (a == 1) {
					// this.pop = true
					
					if (this.userInfo.phone) {
						this.if_login = false
						let scene = wx.getEnterOptionsSync()
					
						if (scene.query.scene && (scene.scene == 1047 || scene.scene == 1048 || scene.scene == 1049 || scene
								.scene == 1007 || scene.scene == 1008)) {
							let query = decodeURIComponent(scene.query.scene)
							console.log(query, 'q');
							//解析参数
							if (query.includes("table_id")) {
								this.chooseNum = true
								this.params.table_id = this.$iBox.linkFormat(query, "table_id")
								this.params.shop_id = this.$iBox.linkFormat(query, "shop_id")
								this.$iBox
									.http('getTableById', {
										table_id: this.$iBox.linkFormat(query, "table_id")
									})({
										method: 'post'
									})
									.then(res => {
										this.table_num = res.data.table_number
										this.getTableNum(res.data.table_number)
										this.getTableId(this.params.table_id)
									})
							}
					
						} else {
							this.params.shop_id = this.hotel.id
						}
					
						this.params.page = 1
					
						this.$iBox
							.http('getCateringGoodsTypeList', this.params)({
								method: 'post'
							})
							.then(res => {
								let list = res.data.list
								if (res.data.list.length > 0) {
									list.forEach(item => {
										item.goods_list.forEach(item1 => {
											item1.number = 0
											item1.typePrice = 0
											if (item1.specification_types.length > 0) {
												item1.is_single = false
					
												item1.specification_types.forEach((itemTypes) => {
													itemTypes.specifications.forEach((itemPrice,index) => {
														if (index == 0) {
															item1.typePrice += itemPrice.price
														}
													})
												})
												item1.allPrice = item1.typePrice + item1.price
											} else {
												item1.allPrice =  item1.price
												item1.is_single = true
											}
										})
					
									})
									this.categories = list
									console.log(list,'list');
									this.currentCategoryId = res.data.list[0].id
									this.viewName = 'products-' + res.data.list[0].id
								} else {
									uni.showModal({
										title: '提示',
										content: '餐厅暂无菜品'
									})
								}
					
							})
					} else {
						this.if_login = true
					}
				}
			}
			


			// this.$nextTick(() => this.calcSize())
		},
		methods: {
			...mapActions('food', ['getCart', 'getTableNum', 'getUserCount','getTableId']),
			switchOrderType() {
				// if (this.orderType === 'takein') {
				// 	uni.navigateTo({
				// 		url: '/pages/addresses/addresses'
				// 	})
				// } else {
				// 	this.SET_ORDER_TYPE('takein')
				// }
			},
			sureMan() {
				this.getUserCount(Number(this.manIndex) + 1)
				this.chooseNum = false
			},
			toCloseLogin() {
				uni.showModal({
					title: '提示！',
					content: '为了获得更完整的会员服务请您授权您的手机号！',
					showCancel: false,
					success: res => {
						this.hackReset1 = false
						this.$nextTick(() => {
							this.hackReset1 = true
						})
						this.if_login = true
					}
				})
			},
			loginSucess() {
				this.hackReset = false
				
				
				
				this.$nextTick(() => {
					this.hackReset = true
					
					let set = this.setting.filter(item => {
						return item.sign == 'auto_register_member'
					})
					if (set[0].property) {
						let a = set[0].property.value
						if (a == 2) {
							if (this.userInfo.phone && this.userInfo.grade_info && this.userInfo.grade_info
								.upgrade_growth_value > -1) {
								this.if_login = false
							
								let scene = wx.getEnterOptionsSync()
							
								if (scene.query.scene && (scene.scene == 1047 || scene.scene == 1048 || scene.scene ==
										1049 || scene
										.scene == 1007 || scene.scene == 1008)) {
									let query = decodeURIComponent(scene.query.scene)
									console.log(query, 'q');
									//解析参数
									if (query.includes("table_id")) {
										this.chooseNum = true
										this.params.table_id = this.$iBox.linkFormat(query, "table_id")
										this.params.shop_id = this.$iBox.linkFormat(query, "shop_id")
										this.$iBox
											.http('getTableById', {
												table_id: this.$iBox.linkFormat(query, "table_id")
											})({
												method: 'post'
											})
											.then(res => {
												this.table_num = res.data.table_number
												this.getTableNum(res.data.table_number)
												this.getTableId(this.params.table_id)
											})
									}
							
								} else {
									this.params.shop_id = this.hotel.id
								}
							
								this.params.page = 1
							
								this.$iBox
									.http('getCateringGoodsTypeList', this.params)({
										method: 'post'
									})
									.then(res => {
										let list = res.data.list
										if (res.data.list.length > 0) {
											list.forEach(item => {
												item.goods_list.forEach(item1 => {
													item1.number = 0
													item1.typePrice = 0
													if (item1.specification_types.length > 0) {
														item1.is_single = false
													
														item1.specification_types.forEach((itemTypes) => {
															itemTypes.specifications.forEach((itemPrice,index) => {
																if (index == 0) {
																	item1.typePrice += itemPrice.price
																}
															})
														})
														item1.allPrice = item1.typePrice + item1.price
													} else {
														item1.allPrice =  item1.price
														item1.is_single = true
													}
												})
							
											})
											this.categories = list
							
											this.currentCategoryId = res.data.list[0].id
											this.viewName = 'products-' + res.data.list[0].id
										} else {
											uni.showModal({
												title: '提示',
												content: '餐厅暂无菜品'
											})
										}
									})
							} else {
								this.if_login = true
							}
					
						} else if (a == 1) {
							// this.pop = true
							if (this.userInfo.phone) {
								this.if_login = false
							
								let scene = wx.getEnterOptionsSync()
							
								if (scene.query.scene && (scene.scene == 1047 || scene.scene == 1048 || scene.scene ==
										1049 || scene
										.scene == 1007 || scene.scene == 1008)) {
									let query = decodeURIComponent(scene.query.scene)
									console.log(query, 'q');
									//解析参数
									if (query.includes("table_id")) {
										this.chooseNum = true
										this.params.table_id = this.$iBox.linkFormat(query, "table_id")
										this.params.shop_id = this.$iBox.linkFormat(query, "shop_id")
										this.$iBox
											.http('getTableById', {
												table_id: this.$iBox.linkFormat(query, "table_id")
											})({
												method: 'post'
											})
											.then(res => {
												this.table_num = res.data.table_number
												this.getTableNum(res.data.table_number)
												this.getTableId(this.params.table_id)
											})
									}
							
								} else {
									this.params.shop_id = this.hotel.id
								}
							
								this.params.page = 1
							
								this.$iBox
									.http('getCateringGoodsTypeList', this.params)({
										method: 'post'
									})
									.then(res => {
										let list = res.data.list
										if (res.data.list.length > 0) {
											list.forEach(item => {
												item.goods_list.forEach(item1 => {
													item1.number = 0
													item1.typePrice = 0
													if (item1.specification_types.length > 0) {
														item1.is_single = false
													
														item1.specification_types.forEach((itemTypes) => {
															itemTypes.specifications.forEach((itemPrice,index) => {
																if (index == 0) {
																	item1.typePrice += itemPrice.price
																}
															})
														})
														item1.allPrice = item1.typePrice + item1.price
													} else {
														item1.allPrice =  item1.price
														item1.is_single = true
													}
												})
							
											})
											this.categories = list
							
											this.currentCategoryId = res.data.list[0].id
											this.viewName = 'products-' + res.data.list[0].id
										} else {
											uni.showModal({
												title: '提示',
												content: '餐厅暂无菜品'
											})
										}
									})
							} else {
								this.if_login = true
							}
						}
					}
				})
			},
			handleAddToCart(product) { //添加到购物车
				
				const index = this.cart.findIndex(item => {
					if (!product.is_single) {
						return (item.id == product.id) && (item.materials_text == product.materials_text)
					} else {
						return item.id === product.id
					}
				})
				if (index > -1) {
					if(this.cart[index].number>=product.stock){
						uni.showToast({
							icon:'none',
							title:'该菜品库存已不足,剩余'+product.stock
						})
						return
					}else{
						this.cart[index].number += (product.number || 1)
						return
					}
					
				}else {
					if(product.number>=product.stock){
						uni.showToast({
							icon:'none',
							title:'该菜品库存已不足,剩余'+product.stock
						})
						return
					}
				}


				this.cart.push({
					id: product.id,
					cate_id: product.category_id,
					name: product.name,
					price: product.allPrice,
					number: product.number || 1,
					image: product.cover_pic,
					pay_type: product.pay_type,
					is_single: product.is_single,
					materials_text: product.materials_text || '',
					specification_ids: product.specification_ids ? product.specification_ids.split(',') : []
				})
			},
			handleMinusFromCart(product) { //从购物车减商品
				console.log(product);
				let index
				if (product.is_single) {
					index = this.cart.findIndex(item => item.id == product.id)
				} else {
					index = this.cart.findIndex(item => (item.id == product.id) && (item.materials_text == product
						.materials_text))
				}
				console.log(this.cart, 'this.cart', index);
				this.cart[index].number -= 1
				if (this.cart[index].number <= 0) {
					this.cart.splice(index, 1)
				}
			},
			showProductDetailModal(product) {
				console.log(product, 'product');

				this.product = product
				this.product.specification_types.forEach((values) => {
					values.specifications.forEach((value, index) => {
						if (index == 0) {
							value.is_selected = true
						} else {
							value.is_selected = false
						}
						// value.is_exclusive = true
					})
				})

				this.productModalVisible = true
			},
			handleAddToCartInModal(product) {
				console.log(product,'kkkl');
				this.handleAddToCart(product)
				this.closeProductDetailModal()
			},
			closeProductDetailModal() {
				this.productModalVisible = false
				this.product = {}
			},
			openCartDetailsPopup() {
				this.$refs['cartPopup'].open()
			},
			clearCart() {
				this.cart = []
				this.categories.forEach(item => {
					item.goods_list.forEach(item1 => {
						item1.number = 0
					})
				})
			},
			handleMenuSelected(id) {

				this.productsScrollTop = this.categories.find(item => item.id == id).top

				this.currentCategoryId = id
				this.typeId = `products-${id}`
				console.log();
			},
			// productsScroll({
			// 	detail
			// }) {
			// 	const {
			// 		scrollTop
			// 	} = detail
			// 	let tabs = this.categories.filter(item => item.top <= scrollTop).reverse()
			// 	if (tabs.length > 0) {
			// 		this.currentCategoryId = tabs[0].id
			// 	}
			// },
			// calcSize() {
			// 	let h = 0
			// 	this.categories.forEach(item => {
			// 		let view = uni.createSelectorQuery().select(`#products-${item.id}`)
			// 		view.fields({
			// 			size: true
			// 		}, data => {
			// 			item.top = h
			// 			h += Math.floor(data.height)
			// 			item.bottom = h
			// 		}).exec()
			// 	})
			// },
			pay() {
				console.log('ww');
				this.getCart(this.cart)
				uni.navigateTo({
					url: '/packageB/food/pay/pay?table_num=' + this.table_num
				})
			},
			chooseMan(e) {
				this.manIndex = e
			}
		}
	}
</script>
<style>
	view {
		box-sizing: border-box;
	}
</style>
<style lang="scss">
	@import './index.scss';
</style>