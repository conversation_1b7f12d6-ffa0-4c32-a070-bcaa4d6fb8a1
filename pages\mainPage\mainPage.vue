<template>
	<view class="" style="margin-top: 360rpx;display: flex;flex-direction: column;align-items: center;">
		<image :src="hotel.pic_list[0]" style="width: 260rpx;height: 260rpx;border-radius: 50%;" mode=""></image>
		<p style="margin-top: 30rpx;font-size: 34rpx;font-weight: 700;">{{hotel?hotel.shop_name:'酒店预定'}}</p>
	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				
			}
		},
		components: {},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting','setting']),
		},
		async onLoad() {
			await this.$onLaunched;
			
			if(this.userInfo.block){
				uni.showModal({
					title:'提示',
					content:'账号状态异常，请联系酒店处理!\n联系电话:'+this.hotel.link_phone,
					cancelText:'退出',
					confirmText:'联系酒店',
					success:(res)=> {
						if(res.confirm){
							uni.makePhoneCall({
								phoneNumber:this.hotel.link_phone
							})
						}else{
							uni.exitMiniProgram()
						}

					}
				})
				return
			}

			// 管理员判断逻辑
			this.checkAdminAccess();
			
			
			
		},
		methods: {
			...mapActions('ui', ['toTabbar', 'toThemeColor', 'toPop', 'toCopyRight']),
			...mapActions('hotel', ['getHotelList', 'getHotel', 'getCityModel', 'getSaleTypes']),

			// 检查管理员权限
			checkAdminAccess() {
				// 判断用户是否为管理员
				const isAdmin = this.isUserAdmin();

				if (isAdmin) {
					// 如果是管理员，显示弹窗询问是否进入管理界面
					this.goToAdminPanel();
					// uni.showModal({
					// 	title: '🔑 管理员权限检测',
					// 	content: '检测到您拥有管理员权限，是否直接进入酒店管理界面？',
					// 	cancelText: '客户模式',
					// 	confirmText: '管理模式',
					// 	success: (res) => {
					// 		if (res.confirm) {
					// 			// 用户选择进入管理界面
					// 			console.log('管理员选择进入管理模式');
					// 			this.goToAdminPanel();
					// 		} else {
					// 			// 用户选择继续使用普通界面
					// 			console.log('管理员选择继续使用客户端模式');
					// 			this.continueNormalFlow();
					// 		}
					// 	},
					// 	fail: () => {
					// 		// 弹窗失败时继续正常流程
					// 		console.log('管理员权限弹窗显示失败，继续正常流程');
					// 		this.continueNormalFlow();
					// 	}
					// });
				} else {
					// 不是管理员，继续正常流程
					this.continueNormalFlow();
				}
			},

			// 判断用户是否为管理员
			isUserAdmin() {
				// 检查用户信息是否存在
				console.log(this.userInfo,'mainpage',this.userInfo.is_boss);

				// 根据is_boss字段判断是否为管理员
				if (this.userInfo.is_boss) {
					console.log('通过is_boss字段判断为管理员:', this.userInfo.is_boss);
					return true;
				}

				console.log('is_boss字段值为:', this.userInfo.is_boss, '，判断为普通用户');
				return false;
			},

			// 进入管理员界面
			goToAdminPanel() {
				console.log('管理员选择进入管理界面');

				// 显示加载提示
				uni.showLoading({
					title: '正在进入管理模式...'
				});

				// 延迟一下，让用户看到加载提示
				setTimeout(() => {
					// 跳转到管理员页面
					// 这里需要根据实际的管理员页面路径来修改
					uni.reLaunch({
						url: '/packageA/manager/manager',
						success: () => {
							console.log('成功跳转到管理员界面');
							uni.hideLoading();
						},
						fail: (err) => {
							console.error('跳转管理员界面失败:', err);
							uni.hideLoading();
							uni.showToast({
								title: '跳转失败，请重试',
								icon: 'none'
							});
							// 跳转失败时继续正常流程
							this.continueNormalFlow();
						}
					});
				}, 500);
			},

			// 继续正常流程
			continueNormalFlow() {
				console.log('继续正常的页面流程');
				this.loadUIComponents();
			},

			// 加载UI组件（原来的逻辑）
			loadUIComponents() {
				// 获取全局UI
				this.$iBox.http('getUI', {})({
					method: 'post'
				}).then(res => {
					this.$iBox.http('getHomePageUi', {
						path: 'pages/index/index',
						shop_id: this.hotel.id
					})({
						method: 'post'
					}).then(res => {
						uni.hideLoading()
						// 判断是否是选择城市模式，判断组件
						res.data.forEach(item => {
							if (item.sign == 'book_room_1') {
								if (item.property.style == 4) {
									this.getCityModel(true)
								} else {
									this.getCityModel(false)
								}
							}
						})
					})
					res.data.control_component.forEach(item => {
						if (item.sign == 'tab') {
							let tabbar = item.property.filter(item1 => {
								return item1.status == 1
							})
							if(tabbar.length > 0){
								uni.switchTab({
									url:'/'+tabbar[0].path
								})
							}
							this.toTabbar(tabbar)
						}

						if (item.sign == 'popover') {
							this.toPop(item.property)
						}
					})

					this.toThemeColor(res.data.subject.style)
					this.toCopyRight(res.data.copy_right)

				}).catch(err => {
					console.error('加载UI组件失败:', err);
				})
			},


		}
	}
</script>

<style>

</style>
