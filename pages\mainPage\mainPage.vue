<template>
	<view class="main-page">
		<!-- 初始化动画 -->
		<InitAnimation
			v-if="initAnimationVisible"
			:visible="initAnimationVisible"
			:progress="initProgress"
		/>

		<!-- 主页面内容 -->
		<view v-else style="margin-top: 360rpx;display: flex;flex-direction: column;align-items: center;">
			<!-- 显示骨架屏或实际内容 -->
			<HotelSkeleton v-if="isLoading" />

			<!-- 酒店信息 -->
			<view v-else class="hotel-info">
				<image
					:src="hotel.pic_list && hotel.pic_list[0] ? hotel.pic_list[0] : defaultHotelImage"
					style="width: 260rpx;height: 260rpx;border-radius: 50%;"
					mode="aspectFill"
					@error="onImageError"
				/>
				<p style="margin-top: 30rpx;font-size: 34rpx;font-weight: 700;">
					{{ hotel ? hotel.shop_name : '酒店预定' }}
				</p>
			</view>
		</view>
	</view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'vuex';
	import HotelSkeleton from '@/components/HotelSkeleton/HotelSkeleton.vue'
	import InitAnimation from '@/components/InitAnimation/InitAnimation.vue'
	import { getRequestCache } from '@/flyio/request'
	import initAnimationMixin from '@/mixins/initAnimationMixin'

	export default {
		mixins: [initAnimationMixin],
		components: {
			HotelSkeleton,
			InitAnimation
		},
		data() {
			return {
				isLoading: true, // 加载状态
				defaultHotelImage: '/static/images/hotelImg.png', // 默认酒店图片
				loadingTimer: null, // 加载超时定时器
				maxLoadingTime: 5000 // 最大加载时间5秒
			}
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['themeColor', 'pop']),
			...mapState('hotel', ['city', 'hotel', 'startDate', 'shopSetting','setting']),
		},
		async onLoad() {
			console.log('mainPage onLoad 开始')

			// 设置加载超时
			this.setLoadingTimeout()

			try {
				// 首先尝试从缓存加载数据
				this.loadFromCache()

				// 等待应用初始化完成
				await this.$onLaunched

				// 检查用户状态
				if (this.userInfo.block) {
					this.handleBlockedUser()
					return
				}

				// 检查管理员权限
				this.checkAdminAccess()

			} catch (error) {
				console.error('mainPage 加载失败:', error)
				this.handleLoadError(error)
			} finally {
				// 确保loading状态被清除
				this.clearLoadingState()
			}
		},
		methods: {
			...mapActions('ui', ['toTabbar', 'toThemeColor', 'toPop', 'toCopyRight']),
			...mapActions('hotel', ['getHotelList', 'getHotel', 'getCityModel', 'getSaleTypes']),

			// 设置加载超时
			setLoadingTimeout() {
				this.loadingTimer = setTimeout(() => {
					if (this.isLoading) {
						console.warn('页面加载超时，停止loading')
						this.isLoading = false
					}
				}, this.maxLoadingTime)
			},

			// 清除加载状态
			clearLoadingState() {
				if (this.loadingTimer) {
					clearTimeout(this.loadingTimer)
					this.loadingTimer = null
				}
				this.isLoading = false
			},

			// 从缓存加载数据
			loadFromCache() {
				// 如果已经有酒店数据，立即显示
				if (this.hotel && this.hotel.shop_name) {
					this.isLoading = false
					console.log('使用已有酒店数据')
					return
				}

				// 尝试从缓存加载酒店列表
				const cachedHotels = getRequestCache('getShopList', { page: 1, limit: 10 })
				if (cachedHotels && cachedHotels.data?.list?.length > 0) {
					// 使用缓存中的第一个酒店
					this.getHotel(cachedHotels.data.list[0])
					this.isLoading = false
					console.log('从缓存加载酒店数据成功')
				}
			},

			// 处理被拉黑用户
			handleBlockedUser() {
				const phoneNumber = this.hotel?.link_phone || '客服电话'
				uni.showModal({
					title: '提示',
					content: `账号状态异常，请联系酒店处理!\n联系电话: ${phoneNumber}`,
					cancelText: '退出',
					confirmText: '联系酒店',
					success: (res) => {
						if (res.confirm && this.hotel?.link_phone) {
							uni.makePhoneCall({
								phoneNumber: this.hotel.link_phone
							})
						} else {
							uni.exitMiniProgram()
						}
					}
				})
			},

			// 处理加载错误
			handleLoadError(error) {
				console.error('页面加载错误:', error)
				uni.showToast({
					title: '加载失败，请重试',
					icon: 'none',
					duration: 2000
				})
			},

			// 图片加载错误处理
			onImageError() {
				console.log('酒店图片加载失败，使用默认图片')
			},

			// 优化后的管理员权限检测
			checkAdminAccess() {
				// 快速检查用户是否为管理员
				if (!this.isUserAdmin()) {
					// 不是管理员，继续正常流程
					this.continueNormalFlow()
					return
				}

				// 是管理员，直接跳转（移除弹窗减少等待时间）
				console.log('检测到管理员权限，准备跳转')
				this.goToAdminPanel()
			},

			// 优化后的管理员判断
			isUserAdmin() {
				// 快速检查，减少日志输出
				return !!(this.userInfo && this.userInfo.is_boss)
			},

			// 优化后的管理员界面跳转
			goToAdminPanel() {
				// 显示简洁的跳转提示
				uni.showToast({
					title: '进入管理模式',
					icon: 'loading',
					duration: 1000,
					mask: true
				})

				// 减少延迟时间，提升响应速度
				setTimeout(() => {
					uni.reLaunch({
						url: '/packageA/manager/manager',
						success: () => {
							console.log('成功跳转到管理员界面')
						},
						fail: (err) => {
							console.error('跳转管理员界面失败:', err)
							uni.showToast({
								title: '跳转失败，请重试',
								icon: 'none'
							})
							// 跳转失败时继续正常流程
							this.continueNormalFlow()
						}
					})
				}, 200) // 减少到200ms
			},

			// 继续正常流程
			continueNormalFlow() {
				console.log('继续正常的页面流程')
				this.loadUIComponents()
			},

			// 优化后的UI组件加载
			async loadUIComponents() {
				try {
					// 首先尝试从缓存加载UI配置
					let uiData = getRequestCache('getUI', {})

					if (!uiData) {
						// 缓存中没有数据，发起请求
						uiData = await this.$iBox.http('getUI', {})({
							method: 'post',
							skipLoading: true
						})
					}

					if (uiData && uiData.data) {
						this.processGlobalUI(uiData.data)
					}

					// 加载首页UI配置
					if (this.hotel && this.hotel.id) {
						await this.loadHomePageUI()
					}

				} catch (error) {
					console.error('加载UI组件失败:', error)
					// 使用默认配置
					this.useDefaultUIConfig()
				}
			},

			// 处理全局UI配置
			processGlobalUI(uiData) {
				if (uiData.control_component) {
					uiData.control_component.forEach(item => {
						if (item.sign === 'tab') {
							const tabbar = item.property.filter(item1 => item1.status === 1)
							if (tabbar.length > 0) {
								// 延迟跳转，避免阻塞当前页面
								setTimeout(() => {
									uni.switchTab({
										url: '/' + tabbar[0].path
									})
								}, 100)
							}
							this.toTabbar(tabbar)
						}

						if (item.sign === 'popover') {
							this.toPop(item.property)
						}
					})
				}

				if (uiData.subject) {
					this.toThemeColor(uiData.subject.style)
				}
				if (uiData.copy_right) {
					this.toCopyRight(uiData.copy_right)
				}
			},

			// 加载首页UI配置
			async loadHomePageUI() {
				try {
					const homePageUI = await this.$iBox.http('getHomePageUi', {
						path: 'pages/index/index',
						shop_id: this.hotel.id
					})({
						method: 'post',
						skipLoading: true
					})

					if (homePageUI && homePageUI.data) {
						// 处理首页UI配置
						homePageUI.data.forEach(item => {
							if (item.sign === 'book_room_1') {
								const isCityMode = item.property.style === 4
								this.getCityModel(isCityMode)
							}
						})
					}

				} catch (error) {
					console.error('加载首页UI失败:', error)
					// 使用默认城市模式配置
					this.getCityModel(false)
				}
			},

			// 使用默认UI配置
			useDefaultUIConfig() {
				console.log('使用默认UI配置')
				this.getCityModel(false) // 默认非城市模式
			}


		},

		// 页面显示时的优化
		onShow() {
			// 如果页面已经加载完成，检查是否需要刷新数据
			if (!this.isLoading && this.hotel) {
				console.log('页面重新显示，数据已存在')
			}
		},

		// 页面隐藏时清理资源
		onHide() {
			this.clearLoadingState()
		},

		// 页面卸载时清理资源
		onUnload() {
			this.clearLoadingState()
		}
	}
</script>

<style>

</style>
