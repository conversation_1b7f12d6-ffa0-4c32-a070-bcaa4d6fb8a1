<template>
	<view class="">
		<view class="box"
			:style="{'background':'linear-gradient(180deg, '+themeColor.com_color2+' 0%, '+themeColor.com_color3+' 100%)' }">
			<view class="style1">
				<view class="nameBox" @click="editInfo">
					<image :src="userInfo.avatar_url"
						style="height: 80rpx;width: 80rpx;margin-right: 20rpx;border-radius: 50%;" mode="">
					</image>
					<view style="display: flex;flex-direction: column;">
						<text>{{userInfo.nickname}}</text>
						<text style="margin: 20rpx 0;">{{userInfo.grade_name}}</text>
						<p style="font-size: 22rpx;" v-if="userInfo.grade_name!=maxLevel">
							再{{settingValue(vipInfo)}}可以升级到{{nextLevel(vipInfo)}}</p>
						<p v-else style="font-size: 22rpx;margin-top: 20rpx;">您已经是本酒店等级最高的会员！</p>
					</view>
					<!-- <view class="smBox" :style="{color:themeColor.com_color2}">
						<image src="../../static/images/anth.png" style="width: 24rpx;height: 24rpx;padding-right: 6rpx;">
						</image>
						<text>已实名</text>
					</view> -->

				</view>
			</view>

		</view>

		<view class="saleBox">

			<p style="font-size: 36rpx;font-weight: 600;" v-if="showAdroid">选择购买的会员等级</p>
			<view class="lvBox" v-if="showAdroid">
				<view class="item" v-for="(item, index) in buyList" @click="choose(item)">
					<view :class="item.member_grade_id==current?'item_box':'no_box'">
						<text :style="current!=item.member_grade_id?'color:#303139':''">{{item.grade_name}}</text>
						<view class="" style="font-weight: 600;">
							<text style="font-size: 22rpx;">￥</text>
							<text style="font-size: 44rpx;">{{item.amount.toFixed(2)}}</text>
							<text style="font-size: 22rpx;"
								:style="current!=item.member_grade_id?'color:#303139;font-weight:200':''">/{{item.usable_month}}月</text>
						</view>
					</view>
				</view>
			</view>
			<checkbox-group @change="checkboxChange" v-if="showAdroid">
				<label class="radio" style="font-size: 22rpx;color:#c0c4cc">
					<checkbox :value="cb" checked="true" color="#d0b99e" style="transform:scale(0.7)" />
					<text>购买即视为同意</text><text style="text-decoration:underline;"
						@click.stop="agreement">{{hotel.shop_name}}会员用户协议</text>
				</label>
			</checkbox-group>

			<view class="" style="width: 500rpx;margin: 80rpx auto;" v-if="showAdroid">
				<view class="btn_register" :style="{background:themeColor.com_color1,color:themeColor.bg_color}"
					@click="register">
					购买会员</view>
			</view>


			<view class="rightInfo">
				<view class="" style="display: flex;justify-content: space-between;">
					<p style="font-size: 36rpx;font-weight: 600;">权益介绍</p>
					<text style="font-size: 24rpx;color: #c0c4cc;" @click="goMore">更多等级介绍</text>
				</view>

				<view class="" style="display: flex;justify-content: space-between;margin-top: 20rpx;">
					<view class="InfoBox" v-for="(item, index) in rightItererest" v-if="index<2">
						<view class="title1">
							<view class="tips" v-if="index==0">
								当前
							</view>
							<text>{{index==0?userInfo.grade_name:nextLevel(vipInfo)}}</text>
						</view>
						<view class="items" v-for="item1 in item" v-if="item1.status==1">
							<text class="item_t">{{item1.name}}</text>
							<text class="item_c">{{ info(item1)}}</text>
						</view>
					</view>
				</view>

			</view>
		</view>


	</view>

</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex';
	export default {
		data() {
			return {
				current: '',
				vipList: [],
				vipInfo: {},
				maxLevel: '',
				buyList: [],
				rightItererest: [],
				cb: '0',
				if_check: true,
				showAdroid: false
			};
		},
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['hotel', 'setting'])
		},
		async onShow() {
			await this.$onLaunched;
			const deviceInfo = wx.getDeviceInfo()
			console.log(deviceInfo)
			let platform = deviceInfo.platform
			if (platform === 'android') {
				this.showAdroid = true
				// 在这里执行安卓设备相关的逻辑或设置样式
			} else if (platform === 'ios') {
				this.showAdroid = false
				// 在这里执行iOS设备相关的逻辑或设置样式
			} else {
				this.showAdroid = true
			}

			this.$iBox.http('getMemberGrade', {})({
				method: 'post'
			}).then(res => {
				this.vipList = res.data
				this.vipInfo = res.data.filter(item => {
					return item.id == this.userInfo.grade
				})[0]
				this.maxLevel = res.data[res.data.length - 1].grade_name

				let buy_list = []
				let flag = 0
				// 循环获取下一等级购买条件
				for (var i = 0; i < this.vipList.length; i++) {
					if (this.userInfo.grade == this.vipList[i].id) {
						flag = i + 1;
						break;
					}
				}

				this.current = this.vipList[flag].buy_setting.member_grade_id


				// 获取权益列表
				let inList = []
				for (var i = flag - 1; i < this.vipList.length; i++) {
					inList.push(this.vipList[i].right_itererest)
				}
				console.log(inList, 'flag');
				this.rightItererest = inList
				console.log(inList);
			})


			this.$iBox.http('getMemberGradeBuySetting', {})({
				method: 'post'
			}).then(res => {
				this.buyList = res.data
			})


		},
		methods: {
			choose(e) {
				this.current = e.member_grade_id
			},
			editInfo() {
				uni.navigateTo({
					url: '/pages/supplementInfo/supplementInfo'
				})
			},
			agreement() {
				uni.navigateTo({
					url: '/pages/agreement/agreement'
				})
			},
			preLevel(e) {
				let level = this.setting.filter(item => {
					return item.sign == 'upgrade_model'
				})[0].property
				let mode = {}
				let upValue = 0
				if (level.type == 1) {
					mode = level.growth
					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id && i != this.vipList.length - 1) {
							upValue = (this.userInfo.growth / this.vipList[i + 1].upgrade_growth_value) * 100 * Number(mode
								.amount / mode.growth)
							break;
						}
					}
				} else {
					mode = level.night

					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id && i != this.vipList.length - 1) {
							upValue = (this.userInfo.growth / this.vipList[i + 1].upgrade_growth_value) * 100 * Number(mode
								.growth / mode.night)
							break;
						}
					}

				}

				return upValue.toFixed(0)

			},
			settingValue(e) {
				let level = this.setting.filter(item => {
					return item.sign == 'upgrade_model'
				})[0].property
				let mode = {}
				let upValue = 0
				if (level.type == 1) {
					mode = level.growth
					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id) {

							upValue = '消费' + (this.vipList[i + 1].upgrade_growth_value / Number(mode.amount / mode.growth))
								.toFixed(0) + '元'
							break;
						}
					}
				} else {
					mode = level.night

					for (var i = 0; i < this.vipList.length; i++) {
						if (this.vipList[i].id == e.id) {

							upValue = '住' + (this.vipList[i + 1].upgrade_growth_value / Number(mode.growth / mode.night))
								.toFixed(0) + '晚'
							break;
						}
					}

				}


				return upValue
			},

			nextLevel(e) {
				let upValue = ''
				for (var i = 0; i < this.vipList.length; i++) {
					if (this.vipList[i].id == e.id && i != this.vipList.length - 1) {

						upValue = this.vipList[i + 1].grade_name
						break;
					} else {
						upValue = '最高'
					}

				}
				return upValue

			},
			checkboxChange(e) {
				console.log(e, 'll');
				if (e.detail.value.length == 0) {
					this.if_check = false
				} else {
					this.if_check = true
				}
			},
			info(e) {
				if (e.sign == 'zk') {
					return e.value == 100 ? '-' : e.value / 10 + '折'
				} else if (e.sign == "tqrz") {
					return e.value ? e.value + '小时' : '-'
				} else if (e.sign == "yhq") {
					return e.value ? e.value.length + '张' : '-'
				} else {
					return e.value ? e.value : '-'
				}
			},
			goMore() {
				uni.navigateTo({
					url: '/pages/vipRights/vipRights'
				})
			},
			register() {
				if (!this.if_check) {
					uni.showToast({
						icon: 'none',
						title: '请先阅读并同意酒店用户协议',
						duration: 1000
					})
					return
				} else {
					this.$iBox.http('agreeMemberAgreement', {})({
						method: 'post'
					}).then(res => {

					})
				}

				this.$iBox.http('buyMemberGrade', {
					setting_id: this.current,
					shop_id: this.hotel.id,
				})({
					method: 'post'
				}).then(res => {
					if (res.data.bizCode == '0000') {
						// 随行付
						uni.requestPayment({
							provider: 'wxpay',
							AppId: res.data.payAppId,
							timeStamp: res.data.payTimeStamp,
							nonceStr: res.data.paynonceStr,
							package: res.data.payPackage,
							signType: res.data.paySignType,
							paySign: res.data.paySign,
							success: (res) => {
								uni.showModal({
									title: '提示',
									content: '支付成功!',
									showCancel: false,
									success: () => {
										uni.navigateBack()
									}
								})

							},
							fail: function(err) {
								uni.hideLoading()
							}
						});


					} else {
						// 微信支付
						uni.requestPayment({
							provider: 'wxpay',
							timeStamp: res.data.timeStamp,
							nonceStr: res.data.nonceStr,
							package: res.data.package,
							signType: 'MD5',
							paySign: res.data.paySign,
							success: (res) => {
								uni.showModal({
									title: '提示',
									content: '支付成功!',
									showCancel: false,
									success: () => {
										uni.navigateBack()
									}
								})

							},
							fail: function(err) {
								uni.hideLoading()
							}
						});
					}
				})
				// buyMemberGrade


			}

		}
	}
</script>

<style>
	page {
		background-color: #FFFFFF;
	}
</style>

<style lang="scss" scoped>
	.box {
		position: relative;
		z-index: 1;
		margin: 30rpx auto;
		height: 200rpx;
		width: 700rpx;
		border-radius: 24rpx;

		.style1 {
			.nameBox {
				height: 160rpx;
				width: 100%;
				display: flex;
				align-items: center;
				padding: 30rpx 30rpx 0rpx 30rpx;

				.smBox {
					height: 30rpx;
					width: fit-content;
					padding: 14rpx 14rpx;
					background-color: #fff;
					border-radius: 20rpx;
					display: flex;
					font-size: 22rpx;
					align-items: center;
					justify-content: center;
					margin-left: 20rpx;
				}

			}

			.vipCard {
				width: 100%;
				display: flex;
				flex-direction: column;
				z-index: -3;
				// height: 350rpx;

				.swiper-item {
					position: relative;
					width: 700rpx;
					height: 350rpx;
					margin-left: 20rpx;
					background-color: #bdd7ef;
					border-radius: 20rpx;
					padding: 60rpx 30rpx;
					display: flex;
					flex-direction: column;
					z-index: 0;

					&_name {
						display: flex;
						align-items: center;
					}



				}


				.vipSign {
					padding: 0 30rpx;

					.signBox {
						display: flex;
						flex-wrap: wrap;

						.signItem {
							display: flex;
							flex-direction: column;
							align-items: center;
							height: 140rpx;
							width: 25%;
							padding: 20rpx;
						}
					}

				}
			}

		}

	}

	.progress {
		padding: 30rpx 0;
		width: 400rpx;
		display: flex;
		flex-direction: column;
		z-index: 4;
		color: #000000;
	}

	.saleBox {
		width: 100vw;
		// min-height: 300rpx;
		border-radius: 10rpx;
		// margin-top: -30rpx;
		background-color: #fff;
		// position: absolute;
		// top: 200rpx;
		z-index: 6;
		padding: 0 30rpx;

		.progress {
			padding: 30rpx 0;
			width: 400rpx;
			display: flex;
			flex-direction: column;
			z-index: 4;
			color: #000000;
		}

		.lvBox {
			display: flex;
			flex-wrap: wrap;
			align-items: center;
			width: 100%;
			margin-top: 20rpx;

			.item {
				width: 33%;
				height: 140rpx;

				.item_box {
					width: 90%;
					height: 90%;
					background-color: #d0b99e;
					border-radius: 14rpx;
					color: #5c3b23;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
				}

				.no_box {
					width: 90%;
					height: 90%;
					background-color: #ffffff;
					border-radius: 14rpx;
					color: #b8946d;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					border: 1px solid #e5e5e5;
				}
			}
		}

		.btn_register {
			display: flex;
			align-items: center;
			justify-content: center;
			width: 500rpx;
			height: 90rpx;
			border-radius: 20rpx;
		}

		.rightInfo {
			margin-top: 100rpx;
			background-color: #ffffff;
			padding: 30rpx;

			.InfoBox {
				width: 46%;
				min-height: 400rpx;
				border-radius: 16rpx;
				background-color: #f7f7f7;

				.title {
					height: 80rpx;
					width: 100%;
					border-top-left-radius: 16rpx;
					border-top-right-radius: 16rpx;
					background-image: linear-gradient(120deg, #e0c3fc 0%, #8ec5fc 100%);
					display: flex;
					align-items: center;
					justify-content: center;
				}

				.title1 {
					height: 80rpx;
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: center;
					border-top-left-radius: 16rpx;
					border-top-right-radius: 16rpx;
					background-image: linear-gradient(120deg, #c5ddfc 0%, #8ec5fc 100%);
					position: relative;

					.tips {
						height: 40rpx;
						width: fit-content;
						padding: 10rpx;
						font-size: 22rpx;
						color: #ffffff;
						background-color: #8592a1;
						display: flex;
						align-items: center;
						justify-content: center;
						position: absolute;
						top: 0;
						right: 0;
						border-top-right-radius: 16rpx;
						border-bottom-left-radius: 16rpx;
					}
				}

				.items {
					width: 100%;
					min-height: 80rpx;
					border: 1px solid #eee;
					display: flex;
					align-items: center;
					justify-content: space-around;
					padding: 10rpx;
					font-size: 24rpx;

					.item_t {
						color: #7d7d7d;
					}

					.item_c {
						font-weight: 500;
					}
				}
			}
		}
	}
</style>