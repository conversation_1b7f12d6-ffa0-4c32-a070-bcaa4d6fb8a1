<template>
	<view>
		<m-popup :show="poprc" @closePop="closePopPay">
			<view class="payBox">
				<p style="margin: 10rpx 0;">联房</p>
				<view class="chooseRoom">
					<scroll-view scroll-y="true" style="height: 100%;">
						<view>
							<p>可选择联房</p>
							<view class="" style="display: flex;flex-wrap: wrap;">
								<view class="roomBox" v-for="(item, index) in list" :key="index"  @click="conn(item)">
									{{item.room_number}}
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="userableBox">
					<scroll-view scroll-y="true" style="height: 100%;">
						<view>
							<p>当前联房中</p>
							<view class="" style="display: flex;flex-wrap: wrap;">
								<view class="roomBox" v-for="(item, index) in connectList" :key="index"  @click="unconn(item)">
									{{item.room_number}} <text
										style="color: brown;padding-left: 6rpx;font-size: 24rpx;">{{item.is_main==1?'主':''}}</text>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
				<view class="" style="width: 100%;height: 80rpx;direction: rtl;margin-top: 30rpx;">
					<button size="mini" type="default" @click="cancel" style="margin-right: 10rpx;">取消</button>
					<button size="mini" type="primary" @click.stop="sure(item)" style="margin-right: 10rpx;">确认</button>

				</view>
			</view>
		</m-popup>

	</view>
</template>

<script>
	import {
		mapState,
		mapGetters,
		mapActions
	} from 'vuex'
	export default {
		computed: {
			...mapState('login', ['userInfo']),
			...mapState('ui', ['tabbar', 'themeColor']),
			...mapState('hotel', ['roles_list', 'manager']),
		},
		props: {
			poprc: {
				type: Boolean,
				default: false
			},
			room: {
				type: Object
			}
		},
		data() {
			return {
				list: [],
				connectList: []
			}
		},
		watch: {
			poprc: {
				handler(newVal, oldVal) {

				},
				immediate: true,
				deep: true
			},
			room: {
				handler(newVal, oldVal) {
				
					if (this.room.bill_id) {
						this.$iBox.http('getConnectBill', {
								bill_id: this.room.bill_id
							})({
								method: 'post'
							})
							.then(res1 => {
								let room = []
								res1.data.forEach(item => {
									let info = {
										bill_id: '',
										room_number: '',
										is_main: ''
									}
									info.bill_id = item.id
									info.room_number = item.room_number
									info.is_main = item.is_main
									room.push(info)

								})
								this.connectList = room
								let params = {
									room_type_id: "",
									room_record_status: [],
									room_clear_status: [],
									building_id: "",
									floor_id: "",
								}

								this.$iBox.http('getUsableRoom', params)({
									method: 'post'
								}).then(res => {
									let list = []
									res.data.list.forEach(item => {
										item.floor_list.forEach(item1 => {
											item1.room_list.forEach(item2 => {
												if (item2.room_status_record_id == 1 ||item2.room_status_record_id == 4) {
													if (!item2.bill_info.connect_code&&item2.id != this.room.id) {
														list.push(item2)
													}
   
												}
											})
										})
									})

									let room1 = []
									list.forEach(item => {
										let info = {
											bill_id: '',
											room_number: '',

										}
										info.bill_id = item.bill_id
										info.room_number = item.room_number

										room1.push(info)

									})

									this.list = room1
								})
								uni.hideLoading()
							})
					}
				},
				immediate: false,
				deep: true
			}
		},
		mounted() {

		},

		methods: {
			closePopPay() {
				this.$emit('closeConnect', '')
			},
			cancel() {
				this.$emit('closeConnect', '')
			},
			conn(e) {
				console.log(this.list);
				this.list = this.list.filter(item => {
					return item.bill_id != e.bill_id
				})

				this.connectList.push(e)
			},
			unconn(e) {
				console.log(this.connectList);
				if (e.is_main) {
					uni.showToast({
						icon: 'none',
						title: '主房间不可取消联房'
					})
					return
				}

				this.connectList = this.connectList.filter(item => {
					return item.bill_id != e.bill_id
				})

				this.list.push(e)
			},
			sure() {
				let ids = []
				this.connectList.forEach(item => {
					ids.push(item.bill_id)
				})
				let params = {
					bill_ids: ids
				}

				this.$iBox.http('connectRoomBill', params)({
					method: 'post'
				}).then(res => {
					this.$emit('closeConnect', '')
					this.$emit('sureConnect', '')
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.payBox {
		height: 80vh;
		width: 100vw;
		padding: 20rpx;

		.chooseRoom {
			height: 30vh;
			width: 100%;
			border: 1px solid #eee;
			padding: 20rpx;

			.roomBox {
				width: fit-content;
				padding: 8rpx 14rpx;
				border: 1px solid #eee;
				margin: 10rpx;
			}
		}

		.userableBox {
			height: 30vh;
			width: 100%;
			border: 1px solid #eee;
			padding: 20rpx;

			.roomBox {
				width: fit-content;
				padding: 8rpx 14rpx;
				border: 1px solid #eee;
				margin: 10rpx;
			}
		}

	}

	.btnClass {
		width: fit-content;
		padding: 10rpx 22rpx;
		// height: 60rpx;
		// border: 1px solid #727272;
		border-radius: 12rpx;
		margin-left: 14rpx;
		background-color: cornflowerblue;
		margin: 10rpx auto;
	}
</style>
